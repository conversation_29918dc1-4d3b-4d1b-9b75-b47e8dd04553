# Zod Localization Guide

This document provides comprehensive guidance on using the Zod localization utilities in the project. These utilities allow you to display validation error messages in the user's selected language (English or Estonian).

## Table of Contents

1. [Client-Side Localization](#client-side-localization)
2. [Server-Side Localization](#server-side-localization)
3. [Working with Enums](#working-with-enums)
4. [Form Integration](#form-integration)
5. [Real-World Examples](#real-world-examples)
6. [Language Detection](#language-detection)
7. [Troubleshooting](#troubleshooting)
8. [Implementation Details](#implementation-details)
9. [Benefits](#benefits)

## Client-Side Localization

For client components, use the `lz` utility and `localizedMessage` function from `@/utils/zod-localization`.

```typescript
import { localizedMessage, lz } from "@/utils/zod-localization"
import { z } from "zod"

// Create a schema with localized error messages
const userSchema = lz.object({
  // Basic field with default error messages
  username: lz
    .string()
    .min(3, localizedMessage("Username must be at least 3 characters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> peab olema vähemalt 3 tähemärki")),

  // Email field with custom error message
  email: lz
    .string()
    .email(localizedMessage("Please enter a valid email address", "Palun sisestage kehtiv e-posti aadress")),

  // Number field with validation
  age: lz.number().min(18, localizedMessage("You must be at least 18 years old", "Te peate olema vähemalt 18-aastane")),

  // Enum field with predefined options
  role: lz.enum(["admin", "user", "guest"]),

  // Boolean field
  acceptTerms: lz.boolean().refine((val) => val === true, {
    message: localizedMessage("You must accept the terms and conditions", "Te peate nõustuma tingimustega"),
  }),

  // Array field
  interests: lz
    .array(lz.string())
    .min(1, localizedMessage("Please select at least one interest", "Palun valige vähemalt üks huvi")),

  // Nested object
  address: lz
    .object({
      street: lz.string().min(1, localizedMessage("Street is required", "Tänav on kohustuslik")),
      city: lz.string().min(1, localizedMessage("City is required", "Linn on kohustuslik")),
      country: lz.string().min(1, localizedMessage("Country is required", "Riik on kohustuslik")),
    })
    .optional(),
})
```

### Available Client-Side Methods

The `lz` object provides localized versions of all common Zod methods:

- `lz.string()` - For string validation
- `lz.number()` - For number validation
- `lz.boolean()` - For boolean validation
- `lz.date()` - For date validation
- `lz.enum()` - For enum validation
- `lz.object()` - For object validation
- `lz.array()` - For array validation

Each method returns a Zod schema with localized error messages based on the user's language.

## Server-Side Localization

For server components and server actions, use the `slz` utility from `@/utils/server-zod-localization`.

```typescript
import { slz } from "@/utils/server-zod-localization"

// In a server action or route handler
async function validateUserData(data) {
  // Note: server-side methods are async
  const schema = lz.object({
    name: (await slz.string()).min(3),
    email: (await slz.string()).email(),
    age: (await slz.number()).min(18),
    role: await slz.enum(["admin", "user", "guest"]),
  })

  return schema.safeParse(data)
}
```

### Available Server-Side Methods

The `slz` object provides async versions of all common Zod methods:

- `slz.string()` - Returns a Promise that resolves to a Zod string schema
- `slz.number()` - Returns a Promise that resolves to a Zod number schema
- `slz.boolean()` - Returns a Promise that resolves to a Zod boolean schema
- `slz.date()` - Returns a Promise that resolves to a Zod date schema
- `slz.enum()` - Returns a Promise that resolves to a Zod enum schema
- `slz.object()` - For object validation
- `slz.array()` - For array validation

## Working with Enums

When working with enums that need to support multiple languages, include all possible values:

```typescript
// Include both English and Estonian values
const statusSchema = lz.enum(["Yes", "No", "Jah", "Ei"])

// This allows the schema to validate both "Yes" and "Jah" as valid values
```

This is particularly important for forms that might be submitted in either language.

## Form Integration

### React Hook Form

```typescript
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { lz, localizedMessage } from "@/utils/zod-localization";

const formSchema = lz.object({
  name: lz.string().min(1, localizedMessage(
    "Name is required",
    "Nimi on kohustuslik"
  )),
  email: lz.string().email(localizedMessage(
    "Please enter a valid email",
    "Palun sisestage kehtiv e-post"
  )),
});

function MyForm() {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
    }
  });

  // Now form errors will be displayed in the user's language
  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <input {...form.register("name")} />
      {form.formState.errors.name && (
        <p>{form.formState.errors.name.message}</p>
      )}

      <input {...form.register("email")} />
      {form.formState.errors.email && (
        <p>{form.formState.errors.email.message}</p>
      )}

      <button type="submit">Submit</button>
    </form>
  );
}
```

## Real-World Examples

### Survey Form Schema

```typescript
import { lz } from "@/utils/zod-localization"

// Example from the survey form
export const greenTransitionSchema = lz.object({
  sustainability_strategy: lz.object({
    // Include both English and Estonian values for enums
    has_strategy: lz.enum(["Yes", "No", "Jah", "Ei"]),
    strategy_document: lz.string().optional(),
    plan_to_develop: lz.enum(["Yes", "No", "Jah", "Ei"]).optional(),
    additional_documents: lz
      .array(
        lz.object({
          key: lz.string(),
          mimeType: lz.string(),
          name: lz.string(),
          url: lz.string().optional(),
        })
      )
      .optional(),
  }),
  sustainability_report: lz.enum(["Yes", "No", "Jah", "Ei"]),
  other_sustainability_reports: lz.object({
    has_reports: lz.enum(["Yes", "No", "Jah", "Ei"]),
    report_document: lz.string().optional(),
    // ... more fields
  }),
})
```

### Send Survey Dialog

```typescript
import { localizedMessage, lz } from "@/utils/zod-localization"
import { z } from "zod"

export const sendSurveySchema = lz.object({
  company: z.object(
    {
      aggregateId: z.string(),
      name: z.string().min(1),
    },
    {
      message: localizedMessage("Company is required", "Ettevõte on kohustuslik"),
    }
  ),
  email: lz
    .string()
    .email(localizedMessage("Please enter a valid email address", "Palun sisestage kehtiv e-posti aadress")),
  message: lz.string().optional(),
})
```

## Language Detection

### Client-Side

The client-side localization uses the `next-intl` library to detect the current language:

```typescript
import { useLocale } from "next-intl"

// In the zod-localization.ts file
export function getCurrentLanguage(): Language {
  try {
    // Get the locale from next-intl
    const locale = useLocale()
    return locale === "et" ? Language.ET : Language.EN
  } catch (e) {
    // If there's an error, return English as a default
    return Language.EN
  }
}
```

### Server-Side

The server-side localization uses the server version of `next-intl` to detect the language:

```typescript
import { getLocale } from "next-intl/server"

// In the server-zod-localization.ts file
export async function getCurrentLanguage(): Promise<Language> {
  try {
    const locale = await getLocale()
    return locale === "et" ? Language.ET : Language.EN
  } catch (e) {
    // If there's an error, return English as a default
    return Language.EN
  }
}
```

## Troubleshooting

### Enum Values in Different Languages

When using enums that need to accept values in multiple languages, include all possible values:

```typescript
// CORRECT: Include both language options
const statusSchema = lz.enum(["Yes", "No", "Jah", "Ei"]);

// INCORRECT: This will fail if Estonian values are submitted
const statusSchema = lz.enum(["Yes", "No"]);
```

### Type Errors with Generated Schemas

If you're using generated schemas, you may need to update the type definitions to include both language options:

```typescript
// Update type definitions to include both language options
type Status = "Yes" | "No" | "Jah" | "Ei"

// Use the updated type in your schema
const statusSchema = lz.enum(["Yes", "No", "Jah", "Ei"] as const)
```

### Server Component Issues

Remember that server components need to use the `slz` utility, which returns Promises:

```typescript
// CORRECT: Await the schema methods
const schema = lz.object({
  name: (await slz.string()).min(3),
});

// INCORRECT: This will cause errors
const schema = lz.object({
  name: slz.string().min(3), // Error: slz.string() returns a Promise
});
```

## Implementation Details

### Client-Side Implementation

The client-side implementation is in `@/utils/zod-localization.ts`. It provides:

1. `getCurrentLanguage()` - Gets the current language from next-intl
2. `localizedMessage(en, et)` - Returns the appropriate message based on language
3. `lz` - An object with localized versions of Zod's schema creation functions

### Server-Side Implementation

The server-side implementation is in `@/utils/server-zod-localization.ts`. It provides:

1. `getCurrentLanguage()` - Async function that gets the language from next-intl/server
2. `localizedMessage(en, et)` - Async function that returns the appropriate message
3. `slz` - An object with async versions of Zod's schema creation functions

## Benefits

- **Consistent User Experience**: Error messages appear in the user's preferred language
- **Maintainability**: Centralized localization logic makes updates easier
- **Developer Friendly**: Simple API that's similar to standard Zod
- **Flexibility**: Works with both client and server components
- **Type Safety**: Fully typed with TypeScript
- **Form Library Integration**: Works seamlessly with React Hook Form and other libraries
- **Minimal Changes**: Requires minimal changes to existing code
