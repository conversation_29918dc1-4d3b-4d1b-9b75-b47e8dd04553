/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { Language } from "@/utils/constants"

import universalQuestionnaireSchemaEn from "./self-assesment-questionnaire-en"
import universalQuestionnaireSchemaEt from "./self-assesment-questionnaire-et"

/**
 * Returns the appropriate schema based on the language.
 * @param language The language to get the schema for
 * @returns The schema for the specified language
 */
export const getUniversalQuestionnaireSchema = (language: Language) => {
  return language === Language.EN ? universalQuestionnaireSchemaEn : universalQuestionnaireSchemaEt
}

export { universalQuestionnaireSchemaEn, universalQuestionnaireSchemaEt }
