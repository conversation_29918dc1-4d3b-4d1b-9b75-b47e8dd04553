/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type {
  BooleanRadioField,
  CheckboxField,
  DateField,
  FieldType,
  FileField,
  FormField,
  MultiSelectField,
  NumberField,
  RadioSelectField,
  SchemaField,
  SelectField,
  TableField,
  TextareaField,
  TextField,
} from "./types"

// Determines the field type based on the schema field properties
export function getFieldType(field: SchemaField): FieldType {
  if (field["x-display"] === "file-upload") return "file"
  if (field["x-display"] === "textarea") return "textarea"
  if (field["x-display"] === "multiselect") return "multiselect"
  if (field["x-display"] === "radio") return "radio"
  if (field["x-display"] === "boolean-radio") return "boolean-radio"
  if (field.format === "date") return "date"
  if (field.enum) return "select"
  if (field.type === "string") return "text"
  if (field.type === "integer") return "number"
  if (field.type === "boolean") return "checkbox"
  return "text"
}

// Creates a form field based on the schema field, question number, field key, and required status
export function createFormField(
  field: SchemaField,
  questionNumber: string,
  fieldKey: string,
  isRequired: boolean
): FormField {
  // Common properties for all form fields
  const base = {
    questionNumber,
    field: fieldKey,
    title: `${fieldKey}.title`,
    description: `${fieldKey}.description`,
    placeholder: `${fieldKey}.placeholder`,
    helpText: `${fieldKey}.helpText`,
    isYearField: field.isYearField,
    required: isRequired,
    ...(field.helpText ? { helpText: field.helpText } : {}),
    ...(field.dependency
      ? {
          dependency: field.dependency.map((dep) =>
            dep.includes(".") ? dep : `${fieldKey.split(".").slice(0, -1).join(".")}.${dep}`
          ),
        }
      : {}),
  }

  const fieldType = getFieldType(field)

  switch (fieldType) {
    case "date":
      return { ...base, type: "date", format: field.format } as DateField
    case "select":
      return { ...base, type: "select", enum: field.enum ? field.enum : [] } as SelectField
    case "radio":
      return { ...base, type: "radio", enum: field.enum ? field.enum : [] } as RadioSelectField
    case "number":
      return { ...base, type: "number", minimum: field.minimum, maximum: field.maximum } as NumberField
    case "checkbox":
      return { ...base, type: "checkbox" } as CheckboxField
    case "file":
      return { ...base, type: "file" } as FileField
    case "textarea":
      return { ...base, type: "textarea" } as TextareaField
    case "boolean-radio":
      return { ...base, type: "boolean-radio" } as BooleanRadioField
    case "multiselect":
      return {
        ...base,
        type: "multiselect",
        enum: field.items?.enum ?? field.enum ?? [],
      } as MultiSelectField
    default:
      return { ...base, type: "text" } as TextField
  }
}

// Handles the creation of a yearly-table field
export function handleYearlyTable(
  schemaField: SchemaField,
  questionNumber: string,
  fullFieldKey: string,
  isRequired: boolean,
  prefix: string,
  fieldKey: string
): TableField {
  const yearColumns = schemaField["x-year-columns"]!
  const yearFields = yearColumns.map((year: string) => `year_${year}`)

  const rows: { key: string; title: string; description?: string; questionNumber: string; isHeader?: boolean }[] = []
  let subCounter = 0

  Object.entries(schemaField.properties!).forEach(([scopeKey, scopeValue]) => {
    subCounter++
    const subQuestionNumber = `${questionNumber}.${subCounter}`
    if (scopeValue.type === "object" && scopeValue.properties) {
      if (scopeKey.endsWith("scope_3")) {
        rows.push({
          key: scopeKey,
          title: scopeValue.title ?? scopeKey,
          description: scopeValue.description,
          questionNumber: subQuestionNumber,
          isHeader: true,
        })

        let subSubCounter = 0
        Object.entries(scopeValue.properties).forEach(([subKey, subValue]) => {
          subSubCounter++
          const subSubQuestionNumber = `${questionNumber}.${subCounter}.${subSubCounter}`
          rows.push({
            key: `${scopeKey}.${subKey}`,
            title: subValue.title ?? subKey,
            description: subValue.description,
            questionNumber: subSubQuestionNumber,
            isHeader: false,
          })
        })
      } else {
        rows.push({
          key: scopeKey,
          title: scopeValue.title ?? scopeKey,
          description: scopeValue.description,
          questionNumber: subQuestionNumber,
          isHeader: false,
        })
      }
    }
  })

  const columns: FormField[] = yearFields.map((yearField: string, index: number) => ({
    questionNumber: `${questionNumber}.${index + 1}`,
    field: yearField,
    title: yearColumns[index],
    type: "number" as const,
    minimum: 0,
  }))

  return {
    questionNumber,
    field: fullFieldKey,
    type: "table",
    title: `${fullFieldKey}.title`,
    description: `${fullFieldKey}.description`,
    helpText: `${fullFieldKey}.helpText`,
    required: isRequired,
    columnHeaders: yearColumns,
    isYearlyTable: true,
    rows,
    columns,
    readOnly: false,
    ...(schemaField.helpText ? { helpText: schemaField.helpText } : {}),
    ...(schemaField.dependency
      ? {
          dependency: schemaField.dependency.map((dep) =>
            dep.includes(".")
              ? dep
              : `${prefix ? prefix + "." : ""}${fieldKey.split(".").slice(0, -1).join(".")}.${dep}`
          ),
        }
      : {}),
  }
}
