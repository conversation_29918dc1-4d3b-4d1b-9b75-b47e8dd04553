/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

export type FieldType =
  | "text"
  | "number"
  | "date"
  | "select"
  | "table"
  | "checkbox"
  | "file"
  | "textarea"
  | "multiselect"
  | "radio"
  | "boolean-radio"

// Base interface for all form fields, containing common properties
export interface FormFieldBase {
  questionNumber: string
  field: string
  title: string
  description?: string
  placeholder?: string
  required?: boolean
  helpText?: string
  dependency?: string[]
  maxItems?: number
  isYearField?: boolean
}

// Specific field types extending the base interface
export interface TextField extends FormFieldBase {
  type: "text"
}

export interface NumberField extends FormFieldBase {
  type: "number"
  minimum?: number
  maximum?: number
}

export interface DateField extends FormFieldBase {
  type: "date"
  format?: string
}

export interface SelectField extends FormFieldBase {
  type: "select"
  enum: string[]
}

export interface MultiSelectField extends FormFieldBase {
  type: "multiselect"
  enum: string[]
}

export interface RadioSelectField extends FormFieldBase {
  type: "radio"
  enum: string[]
}

export interface BooleanRadioField extends FormFieldBase {
  type: "boolean-radio"
}

export interface CheckboxField extends FormFieldBase {
  type: "checkbox"
}

export interface FileField extends FormFieldBase {
  type: "file"
}

export interface TextareaField extends FormFieldBase {
  type: "textarea"
}

export interface TableField extends FormFieldBase {
  type: "table"
  columns: FormField[]
  predefinedColumns?: FormField[] // For predefined rows in energy-source-table
  readOnly: boolean
  columnHeaders?: string[]
  isYearlyTable?: boolean
  addItemLabel?: string
  rows?: {
    key: string
    title: string
    description?: string
    questionNumber: string
    isHeader?: boolean
  }[]
  isEnergySourceTable?: boolean
  predefinedRows?: {
    key: string
    title: string
    description?: string
    questionNumber: string
  }[]
}

// Union type for all possible form fields
export type FormField =
  | TextField
  | NumberField
  | DateField
  | SelectField
  | TableField
  | CheckboxField
  | FileField
  | TextareaField
  | MultiSelectField
  | RadioSelectField
  | BooleanRadioField

// Interface for a form section, containing a title and a list of fields
export interface FormSection {
  sectionNumber: string
  sectionTitle: string
  sectionDescription?: string
  helpText?: string
  fields: FormField[]
  dependency?: string[]
}

// Type for a field in the JSON schema
export type SchemaField = {
  type: string
  title?: string
  description?: string
  placeholder?: string
  format?: string
  enum?: string[]
  properties?: Record<string, SchemaField>
  items?: SchemaField
  required?: string[]
  minimum?: number
  maximum?: number
  helpText?: string
  label?: string
  dependency?: string[]
  addItemLabel?: string
  maxItems?: number
  "x-display"?: string
  "x-year-columns"?: string[]
  isHeader?: boolean
  isYearField?: boolean
}

// Interface for the input schema
export interface SchemaInput {
  properties: Record<string, SchemaField>
  required?: string[]
  type?: string
  title?: string
}
