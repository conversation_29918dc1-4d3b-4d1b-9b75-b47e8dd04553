/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { FormField, FormSection, SchemaField, SchemaInput, TableField } from "./types"
import { createFormField, handleYearlyTable } from "./utils"

// Handles the creation of an energy-source-table field
function handleEnergySourceTable(
  schemaField: SchemaField,
  questionNumber: string,
  fullFieldKey: string,
  isRequired: boolean,
  prefix: string,
  fieldKey: string
): TableField {
  const predefinedRows: { key: string; title: string; description?: string; questionNumber: string }[] = []
  let subCounter = 0
  let otherSourcesField: SchemaField | undefined

  // Collect predefined rows and identify other_sources
  Object.entries(schemaField.properties!).forEach(([subKey, subValue]) => {
    if (subKey.includes("other_sources")) {
      otherSourcesField = subValue
    } else {
      subCounter++
      const subQuestionNumber = `${questionNumber}.${subCounter}`
      predefinedRows.push({
        key: subKey,
        title: subValue.title ?? subKey,
        description: subValue.description,
        questionNumber: subQuestionNumber,
      })
    }
  })

  // Generate columns for predefined rows (district_heating, gas, etc.)
  const predefinedColumns: FormField[] = []
  predefinedRows.forEach((row) => {
    const rowProps = schemaField.properties![row.key].properties!
    Object.entries(rowProps).forEach(([propKey, propValue]) => {
      const rowSpecificFieldPath = `${fullFieldKey}.${row.key}.${propKey}`
      const formField = createFormField(propValue, questionNumber, rowSpecificFieldPath, false)
      predefinedColumns.push({
        ...formField,
        field: rowSpecificFieldPath,
        title: propValue.title ?? propKey,
        placeholder: propValue.placeholder,
        description: propValue.description,
      })
    })
  })

  // Generate columns for dynamic rows (other_sources)
  let columns: FormField[] = []
  if (
    otherSourcesField &&
    otherSourcesField.type === "array" &&
    otherSourcesField.items?.type === "object" &&
    otherSourcesField.items.properties
  ) {
    const nestedSections = parseSchemaSections(
      {
        properties: otherSourcesField.items.properties,
        required: otherSourcesField.items.required,
      },
      prefix,
      `${fieldKey}`,
      otherSourcesField.items.required,
      questionNumber
    )
    columns = nestedSections
      .flatMap((section) => section.fields)
      .map((f) => ({
        ...f,
        field: `${fullFieldKey}.${f.field.split(".").pop()}`,
        title: f.title || (f.field.split(".").pop() ?? ""),
        placeholder: f.placeholder,
        description: f.description,
      }))
  }

  return {
    questionNumber,
    field: fullFieldKey,
    type: "table",
    title: `${fullFieldKey}.title`,
    description: `${fullFieldKey}.description`,
    required: isRequired,
    addItemLabel: `${fullFieldKey}.addItemLabel`,
    helpText: `${fullFieldKey}.helpText`,
    maxItems: schemaField.maxItems,
    isEnergySourceTable: true,
    predefinedRows,
    predefinedColumns,
    columns,
    readOnly: false,
    ...(schemaField.dependency
      ? {
          dependency: schemaField.dependency.map((dep) =>
            dep.includes(".")
              ? dep
              : `${prefix ? prefix + "." : ""}${fieldKey.split(".").slice(0, -1).join(".")}.${dep}`
          ),
        }
      : {}),
  }
}

// Handle array table (unchanged)
function handleArrayTable(
  schemaField: SchemaField,
  questionNumber: string,
  fullFieldKey: string,
  isRequired: boolean,
  prefix: string,
  fieldKey: string
): TableField {
  const nestedSections = parseSchemaSections(
    {
      properties: schemaField.items!.properties!,
      required: schemaField.items!.required,
    },
    prefix,
    `${fieldKey}.\${index}`,
    schemaField.items!.required,
    questionNumber
  )

  const columns = nestedSections.flatMap((section) => section.fields)

  return {
    questionNumber,
    field: fullFieldKey,
    type: "table",
    title: `${fullFieldKey}.title`,
    description: `${fullFieldKey}.description`,
    required: isRequired,
    addItemLabel: `${fullFieldKey}.addItemLabel`,
    helpText: `${fullFieldKey}.helpText`,
    maxItems: schemaField.maxItems,
    isYearlyTable: false,
    columns: columns.map((f) => {
      const fieldName = f.field.split(".").pop()
      const basePath = `${fullFieldKey}.items.${fieldName}`

      const transformed = {
        ...f,
        field: basePath,
        title: `${basePath}.title`,
        description: `${basePath}.description`,
        helpText: `${basePath}.helpText`,
        placeholder: `${basePath}.placeholder`,
      }

      if (schemaField.items?.dependency) {
        transformed.dependency = schemaField.items.dependency.map((dep) =>
          dep.includes(".") ? dep : `${prefix ? prefix + "." : ""}${fieldKey}.\${index}.${dep}`
        )
      }

      return transformed
    }),
    readOnly: false,
    ...(schemaField.helpText ? { helpText: schemaField.helpText } : {}),
    ...(schemaField.dependency
      ? {
          dependency: schemaField.dependency.map((dep) =>
            dep.includes(".")
              ? dep
              : `${prefix ? prefix + "." : ""}${fieldKey.split(".").slice(0, -1).join(".")}.${dep}`
          ),
        }
      : {}),
  }
}

// Main function to parse a JSON schema into form sections (unchanged)
export const parseSchemaSections = (
  schema: SchemaInput,
  prefix = "",
  parentKey = "",
  requiredFields: string[] = [],
  parentNumber = ""
): FormSection[] => {
  const sections: FormSection[] = []
  let questionCounter = 0

  const schemaRequiredFields = schema.required ?? []
  const allRequiredFields = [...requiredFields, ...schemaRequiredFields]

  Object.entries(schema.properties).forEach(([key, value]) => {
    questionCounter++
    const fieldKey = parentKey ? `${parentKey}.${key}` : key
    const fullFieldKey = prefix ? `${prefix}.${fieldKey}` : fieldKey
    const isRequired = allRequiredFields.includes(key)
    const sectionTitle = `${fullFieldKey}.title`
    const sectionDescription = `${fullFieldKey}.description`
    const helpText = `${fullFieldKey}.helpText`
    const questionNumber = parentNumber ? `${parentNumber}.${questionCounter}` : questionCounter.toString()

    let section = sections.find((s) => s.sectionTitle === sectionTitle)
    if (!section) {
      section = {
        sectionTitle,
        fields: [],
        sectionNumber: questionNumber,
        sectionDescription,
        helpText,
        ...(value.dependency
          ? {
              dependency: value.dependency.map((dep) => (dep.includes(".") ? dep : `${prefix}.${dep}`)),
            }
          : {}),
      }
      sections.push(section)
    }

    if (value["x-display"] === "energy-source-table" && value.type === "object" && value.properties) {
      const tableField = handleEnergySourceTable(value, questionNumber, fullFieldKey, isRequired, prefix, fieldKey)
      section.fields.push(tableField)
    } else if (
      value["x-display"] === "yearly-table" &&
      value.type === "object" &&
      value.properties &&
      value["x-year-columns"]
    ) {
      const tableField = handleYearlyTable(value, questionNumber, fullFieldKey, isRequired, prefix, fieldKey)
      section.fields.push(tableField)
    } else if (value.type === "array" && value.items) {
      const fieldType = value["x-display"]
      if (fieldType === "file-upload" || fieldType === "multiselect") {
        const fileField = createFormField(value, questionNumber, fullFieldKey, isRequired)
        section.fields.push(fileField)
      } else if (value.items.type === "object" && value.items.properties) {
        const tableField = handleArrayTable(value, questionNumber, fullFieldKey, isRequired, prefix, fieldKey)
        section.fields.push(tableField)
      } else {
        const arrayField = createFormField(
          { ...value.items, helpText: value.helpText, dependency: value.dependency },
          questionNumber,
          fullFieldKey,
          isRequired
        )
        section.fields.push(arrayField)
      }
    } else if (value.type === "object" && value.properties) {
      const nestedFields = parseSchemaSections(
        {
          properties: value.properties,
          required: value.required ?? [],
          ...(value.helpText ? { helpText: value.helpText } : {}),
          ...(value.dependency ? { dependency: value.dependency } : {}),
        },
        prefix,
        fieldKey,
        value.required ?? [],
        questionNumber
      )
      nestedFields.forEach((nestedField) => {
        section.fields.push(...nestedField.fields)
      })
    } else {
      const field = createFormField(value, questionNumber, fullFieldKey, isRequired)
      section.fields.push(field)
    }
  })

  return sections
}
