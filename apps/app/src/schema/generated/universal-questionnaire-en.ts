/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { z } from "zod"

export default z.object({
  introduction: z
    .object({
      main_text: z
        .object({})
        .describe(
          "Following regulatory requirements (i.e. the Non-Financial Reporting Directive (NFRD) and upcoming Corporate Sustainability Reporting Directive (CSRD)) as for reporting purposes, this unified questionnaire is designed by Estonian, Latvian and Lithuanian Banking Associations to collect quantitative and qualitative data to assess Client's environmental and climate-related impact, social and governance practices, as well as to evaluate Client's readiness to green transition."
        )
        .optional()
        .nullable(),
    })
    .readonly()
    .optional()
    .nullable(),
  general: z
    .object({
      general_questionnaire_completion_date: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .date()
        .describe(
          "The date on which the questionnaire is completed, establishing the reporting period for the data provided."
        ),
      general_company_name: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .describe("The legal name of the company used for identification and regulatory reporting."),
      general_company_registration_number: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .describe("The unique identifier assigned to the company by the relevant registration authority."),
      general_company_indicators: z
        .object({
          general_company_indicators_financial_year: z
            .number({
              invalid_type_error: "This field must be a valid number",
              required_error: "This field is required",
            })
            .int({ message: "This field must be a whole number" })
            .gte(1900, { message: "This field must be greater than or equal to the minimum value (1900)" })
            .lte(2100, { message: "This field must be less than or equal to the maximum value (2100)" })
            .describe(
              "The fiscal year under review during which the company’s financial and operational performance is measured."
            ),
          general_company_indicators_net_turnover: z
            .number({
              invalid_type_error: "This field must be a valid number",
              required_error: "This field is required",
            })
            .describe("The total revenue generated by the company during the financial year, expressed in euros."),
          general_company_indicators_total_assets: z
            .number({
              invalid_type_error: "This field must be a valid number",
              required_error: "This field is required",
            })
            .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
            .describe(
              "The total value of the company’s assets as of the end of the reporting period, measured in euros."
            ),
          general_company_indicators_number_of_employees: z
            .number({
              invalid_type_error: "This field must be a valid number",
              required_error: "This field is required",
            })
            .int({ message: "This field must be a whole number" })
            .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
            .describe("The number of employees working for the company during the reporting period."),
        })
        .describe("Please provide information on the following Company indicators  for the last full financial year"),
      general_economic_activities: z
        .array(
          z.object({
            general_economic_activities_activity: z
              .enum(
                [
                  "a_agriculture_forestry_and_fishing",
                  "growing_of_cereals_except_rice_leguminous_crops_and_oil_seeds",
                  "growing_of_rice",
                  "growing_of_vegetables_and_melons_roots_and_tubers",
                  "growing_of_sugar_cane",
                  "growing_of_tobacco",
                  "growing_of_fibre_crops",
                  "growing_of_other_non_perennial_crops",
                  "growing_of_perennial_crops",
                  "plant_propagation",
                  "raising_of_dairy_cattle",
                  "raising_of_other_cattle_and_buffaloes",
                  "raising_of_horses_and_other_equines",
                  "raising_of_camels_and_camelids",
                  "raising_of_sheep_and_goats",
                  "raising_of_swine_pigs",
                  "raising_of_poultry",
                  "raising_of_other_animals",
                  "mixed_farming",
                  "support_activities_for_crop_production",
                  "support_activities_for_animal_production",
                  "post_harvest_crop_activities",
                  "seed_process_and_propagation",
                  "hunting_trapping_and_related_service_activities",
                  "silviculture_and_other_forestry_activities",
                  "logging",
                  "gathering_of_wild_growing_non_wood_products",
                  "support_services_to_forestry",
                  "marine_fishing",
                  "freshwater_fishing",
                  "marine_aquaculture",
                  "freshwater_aquaculture",
                  "b_mining_and_quarrying",
                  "mining_of_hard_coal",
                  "mining_of_lignite",
                  "extraction_of_crude_petroleum",
                  "extraction_of_natural_gas",
                  "mining_of_iron_ores",
                  "mining_of_uranium_and_thorium_ores",
                  "mining_of_other_non_ferrous_metal_ores",
                  "quarrying_of_stone_and_sand_and_clay",
                  "quarrying_of_ornamental_and_building_stone_limestone_gypsum_chalk_and_slate",
                  "operation_of_gravel_and_sand_pits_mining_of_clays_and_kaolin",
                  "mining_of_chemical_and_fertiliser_minerals",
                  "extraction_of_peat",
                  "salt_extraction",
                  "other_mining_and_quarrying_nec",
                  "support_activities_for_petroleum_and_natural_gas_extraction",
                  "support_for_other_mining_and_quarrying",
                  "c_manufacturing",
                  "processing_and_preserving_of_meat",
                  "processing_and_preserving_of_poultry_meat",
                  "production_of_meat_and_poultry_meat_products",
                  "processing_and_preserving_of_fish_crustaceans_and_molluscs",
                  "processing_and_preserving_of_potatoes",
                  "manufacture_of_fruit_and_vegetable_juice",
                  "other_processing_and_preserving_of_fruit_and_vegetables",
                  "manufacture_of_oils_and_fats",
                  "manufacture_of_margarine_and_similar_edible_fats",
                  "operation_of_dairies_and_cheese_making",
                  "manufacture_of_ice_cream",
                  "manufacture_of_grain_mill_products_starches_and_starch_products",
                  "manufacture_of_bread_and_fresh_pastry_goods_and_cakes",
                  "manufacture_of_rusks_and_biscuits_preserved_pastry_goods_and_cakes",
                  "manufacture_of_macaroni_noodles_couscous_and_similar_farinaceous_products",
                  "manufacture_of_other_food_products",
                  "manufacture_of_prepared_feeds_for_farm_animals",
                  "manufacture_of_prepared_pet_foods",
                  "distilling_rectifying_and_blending_of_spirits",
                  "manufacture_of_wine_from_grape",
                  "manufacture_of_cider_and_other_fruit_wines",
                  "manufacture_of_tobacco_products",
                  "preparation_of_spinning_of_textile_fibres",
                  "weaving_of_textiles",
                  "finishing_of_textiles",
                  "manufacture_of_other_textiles",
                  "manufacture_of_wearing_apparel_except_fur_apparel",
                  "manufacture_of_articles_of_fur",
                  "manufacture_of_knitted_and_crocheted_apparel",
                  "tanning_and_dressing_of_leather",
                  "manufacture_of_luggage_handbags_saddlery_and_harness",
                  "manufacture_of_footwear",
                  "sawmilling_and_planing_of_wood",
                  "manufacture_of_products_of_wood_cork_straw_and_plaiting_materials",
                  "manufacture_of_pulp",
                  "manufacture_of_paper_and_paperboard",
                  "manufacture_of_articles_of_paperboard",
                  "printing_and_service_activities_related_to_printing",
                  "reproduction_of_recorded_media",
                  "manufacture_of_coke_oven_products",
                  "manufacture_of_refined_petroleum_products",
                  "manufacture_of_basic_chemicals_fertilizers_and_nitrogen_compounds_plastics_and_synthetic_rubber",
                  "manufacture_of_pesticides_and_other_agrochemical_products",
                  "manufacture_of_paints_varnishes_printing_ink_and_mastics",
                  "manufacture_of_soap_detergents_cleaning_polishing_perfumes_and_toilet_preparations",
                  "manufacture_of_other_chemical_products",
                  "manufacture_of_man_made_fibres",
                  "manufacture_of_basic_pharmaceutical_products_and_preparations",
                  "manufacture_of_rubber_tyres_and_tubes_retreading_and_rebuilding",
                  "manufacture_of_other_rubber_products",
                  "manufacture_of_plastic_products",
                  "manufacture_of_glass_and_glass_products",
                  "manufacture_of_refractory_products",
                  "manufacture_of_clay_building_materials",
                  "manufacture_of_other_porcelain_and_ceramic_products",
                  "manufacture_of_cement_lime_and_plaster",
                  "manufacture_of_articles_of_concrete_cement_and_plaster",
                  "cutting_shaping_and_finishing_of_stone",
                  "manufacture_of_abrasive_products_and_non_metallic_mineral_products_nec",
                  "manufacture_of_basic_iron_and_steel_and_ferro_alloys",
                  "manufacture_of_tubes_pipes_hollow_profiles_and_fittings_of_steel",
                  "manufacture_of_other_products_and_first_processing_of_steel",
                  "manufacture_of_basic_precious_and_other_non_ferrous_metals",
                  "processing_of_nuclear_fuel",
                  "casting_of_metals",
                  "manufacture_of_structural_metal_products",
                  "manufacture_of_tanks_reservoirs_and_containers_of_metal",
                  "manufacture_of_steam_generators_except_central_heating_boilers",
                  "manufacture_of_weapons_and_ammunition",
                  "forging_pressing_stamping_and_roll_forming_of_metal_powder_metallurgy",
                  "treatment_and_coating_of_metals",
                  "machining",
                  "manufacture_of_cutlery",
                  "manufacture_of_locks_and_hinges",
                  "manufacture_of_tools",
                  "manufacture_of_other_fabricated_metal_products",
                  "manufacture_of_electronic_components_and_boards",
                  "manufacture_of_computers_and_peripheral_equipment",
                  "manufacture_of_communication_equipment",
                  "manufacture_of_consumer_electronics",
                  "manufacture_of_instruments_and_appliances_for_measuring_testing_and_navigation",
                  "manufacture_of_irradiation_electromedical_and_electrotherapeutic_equipment",
                  "manufacture_of_optical_instruments_and_photographic_equipment",
                  "manufacture_of_magnetic_and_optical_media",
                  "manufacture_of_electric_motors_generators_transformers_and_electricity_distribution",
                  "manufacture_of_batteries_and_accumulators",
                  "manufacture_of_wiring_and_wiring_devices",
                  "manufacture_of_electric_lighting_equipment",
                  "manufacture_of_domestic_appliances",
                  "manufacture_of_other_electrical_equipment",
                  "manufacture_of_general_purpose_machinery",
                  "manufacture_of_ovens_furnaces_and_furnace_burners",
                  "manufacture_of_lifting_and_handling_equipment",
                  "manufacture_of_office_machinery_and_equipment",
                  "manufacture_of_power_driven_hand_tools",
                  "manufacture_of_non_domestic_cooling_and_ventilation_equipment",
                  "manufacture_of_other_general_purpose_machinery_nec",
                  "manufacture_of_agricultural_forestry_machinery",
                  "manufacture_of_metal_forming_machinery_and_machine_tools",
                  "manufacture_of_other_special_purpose_machinery",
                  "manufacture_of_motor_vehicles",
                  "manufacture_of_bodies_for_motor_vehicles_trailers_and_semi_trailers",
                  "manufacture_of_parts_and_accessories_for_motor_vehicles",
                  "building_of_ships_and_floating_structures",
                  "building_of_pleasure_and_sporting_boats",
                  "manufacture_of_railway_locomotives_and_rolling_stock",
                  "manufacture_of_air_and_spacecraft_and_related_machinery",
                  "manufacture_of_military_fighting_vehicles_icbm",
                  "manufacture_of_transport_equipment_nec",
                  "manufacture_of_furniture",
                  "manufacturing_of_jewellery_bijouteries_and_related_articles",
                  "manufacture_of_musical_instruments",
                  "manufacture_of_sports_goods",
                  "manufacture_of_games_and_toys",
                  "manufacture_of_medical_and_dental_instruments_and_supplies",
                  "manufacturing_nec",
                  "repair_of_fabricated_metal_products_machinery_and_equipment",
                  "repair_of_electrical_equipment",
                  "repair_and_maintenance_of_ships_and_boats",
                  "repair_and_maintenance_of_aircraft_and_spacecraft",
                  "repair_and_maintenance_of_other_transport_equipment",
                  "installation_of_industrial_machinery_and_equipment",
                  "d_electricity_gas_steam_and_air_conditioning_supply",
                  "electric_power_generation_transmission_and_distribution",
                  "production_of_electricity",
                  "transmission_of_electricity",
                  "distribution_of_electricity",
                  "trade_of_electricity",
                  "manufacture_of_gas_distribution_of_gaseous_fuels",
                  "distribution_of_gaseous_fuels_through_mains",
                  "trade_of_gas_through_mains",
                  "steam_and_air_conditioning_supply",
                  "e_water_supply_sewerage_waste_management_and_remediation",
                  "water_collection_treatment_and_supply",
                  "sewerage",
                  "collection_of_non_hazardous_waste",
                  "collection_of_hazardous_waste",
                  "treatment_and_disposal_of_non_hazardous_waste",
                  "treatment_and_disposal_of_hazardous_waste",
                  "dismantling_of_wrecks",
                  "recovery_of_sorted_materials",
                  "remediation_activities_and_other_waste_management_services",
                  "f_construction",
                  "development_of_building_projects",
                  "construction_of_residential_and_non_residential_buildings",
                  "construction_of_roads_and_railways",
                  "construction_of_utility_projects",
                  "construction_of_other_civil_engineering_projects",
                  "demolition_and_site_preparation",
                  "electrical_plumbing_and_other_construction_installation",
                  "building_completion_and_finishing",
                  "other_specialised_construction_activities",
                  "g_wholesale_and_retail_trade_repair_of_motor_vehicles",
                  "sale_of_motor_vehicles",
                  "maintenance_and_repair_of_motor_vehicles",
                  "sale_of_motor_vehicle_parts_and_accessories",
                  "sale_maintenance_and_repair_of_motorcycles",
                  "wholesale_on_a_fee_or_contract_basis",
                  "wholesale_of_grain_unmanufactured_tobacco_seeds_and_animal_feeds",
                  "wholesale_of_flowers_and_plants",
                  "wholesale_of_live_animals",
                  "wholesale_of_hides_skins_and_leather",
                  "wholesale_of_food_beverages_and_tobacco",
                  "wholesale_of_tobacco_products",
                  "wholesale_of_household_goods",
                  "wholesale_of_information_and_communication_equipment",
                  "wholesale_of_other_machinery_equipment_and_supplies",
                  "wholesale_of_solid_liquid_and_gaseous_fuels",
                  "wholesale_of_metals_and_metal_ores",
                  "wholesale_of_wood_construction_materials_and_sanitary_equipment",
                  "wholesale_of_hardware_plumbing_and_heating_equipment",
                  "wholesale_of_chemical_products",
                  "wholesale_of_other_intermediate_products",
                  "wholesale_of_waste_and_scrap",
                  "non_specialised_wholesale_trade",
                  "retail_sale_in_non_specialised_stores_food_beverages_tobacco",
                  "other_retail_sale_in_non_specialised_stores",
                  "retail_sale_of_food_beverages_and_tobacco_specialised",
                  "retail_sale_of_tobacco_products_specialised",
                  "retail_sale_of_automotive_fuel_specialised",
                  "retail_sale_of_information_and_communication_equipment_specialised",
                  "retail_sale_of_other_household_equipment_specialised",
                  "retail_sale_of_cultural_and_recreation_goods_specialised",
                  "retail_sale_of_other_goods_specialised",
                  "dispensing_chemist_specialised",
                  "retail_sale_of_medical_and_orthopaedic_goods_specialised",
                  "retail_sale_of_flowers_plants_seeds_fertilisers_pets_specialised",
                  "retail_sale_via_stalls_and_markets",
                  "retail_trade_not_in_stores_stalls_or_markets",
                  "h_transportation_and_storage",
                  "passenger_rail_transport_interurban",
                  "freight_rail_transport",
                  "other_passenger_land_transport",
                  "freight_transport_by_road_and_removal_services",
                  "transport_via_pipeline",
                  "sea_and_coastal_passenger_water_transport",
                  "sea_and_coastal_freight_water_transport",
                  "inland_passenger_water_transport",
                  "inland_freight_water_transport",
                  "passenger_air_transport",
                  "freight_air_transport_and_space_transport",
                  "warehousing_and_storage",
                  "support_activities_for_transportation",
                  "postal_activities_under_universal_service_obligation",
                  "other_postal_and_courier_activities",
                  "i_accommodation_and_food_service_activities",
                  "hotels_and_similar_accommodation",
                  "holiday_and_other_short_stay_accommodation",
                  "camping_grounds_recreational_vehicle_parks_and_trailer_parks",
                  "other_accommodation",
                  "restaurants_and_mobile_food_service_activities",
                  "event_catering_and_other_food_service_activities",
                  "beverage_serving_activities",
                  "j_information_and_communication",
                  "publishing_of_books_periodicals_and_other_publishing",
                  "software_publishing",
                  "motion_picture_video_and_television_programme_activities",
                  "sound_recording_and_music_publishing",
                  "radio_broadcasting",
                  "television_programming_and_broadcasting",
                  "wired_telecommunications_activities",
                  "wireless_telecommunications_activities",
                  "satellite_telecommunications_activities",
                  "other_telecommunications_activities",
                  "computer_programming_consultancy_and_related_activities",
                  "data_processing_hosting_and_related_activities_web_portals",
                  "other_information_service_activities",
                  "k_financial_and_insurance_activities",
                  "monetary_intermediation",
                  "activities_of_holding_companies",
                  "trusts_funds_and_similar_financial_entities",
                  "other_financial_service_activities_except_insurance_and_pension",
                  "insurance",
                  "reinsurance",
                  "pension_funding",
                  "activities_auxiliary_to_financial_services_except_insurance",
                  "activities_auxiliary_to_insurance_and_pension_funding",
                  "fund_management_activities",
                  "l_real_estate_activities",
                  "buying_and_selling_of_own_real_estate",
                  "renting_and_operating_of_own_or_leased_real_estate",
                  "real_estate_agencies",
                  "management_of_real_estate_on_fee_or_contract_basis",
                  "m_professional_scientific_and_technical_activities",
                  "legal_activities",
                  "accounting_bookkeeping_auditing_and_tax_consultancy",
                  "activities_of_head_offices",
                  "management_consultancy_activities",
                  "architectural_and_engineering_activities_and_technical_consultancy",
                  "technical_testing_and_analysis",
                  "research_and_development_on_natural_sciences_and_engineering",
                  "research_and_development_on_social_sciences_and_humanities",
                  "advertising",
                  "market_research_and_public_opinion_polling",
                  "specialised_design_activities",
                  "photographic_activities",
                  "translation_and_interpretation_activities",
                  "other_professional_scientific_and_technical_activities_nec",
                  "veterinary_activities",
                  "n_administrative_and_support_service_activities",
                  "renting_and_leasing_of_motor_vehicles",
                  "renting_and_leasing_of_personal_and_household_goods",
                  "renting_and_leasing_of_recreational_and_sports_goods",
                  "renting_of_video_tapes_and_disks",
                  "renting_and_leasing_of_other_machinery_equipment_and_goods",
                  "activities_of_employment_placement_agencies",
                  "temporary_employment_agencies",
                  "other_human_resources_provision",
                  "travel_agency_and_tour_operator_activities",
                  "other_reservation_service_and_related_activities",
                  "private_security_activities",
                  "security_systems_service_activities",
                  "investigation_activities",
                  "combined_facilities_support_activities",
                  "general_cleaning_of_buildings",
                  "other_buildings_and_industrial_cleaning_activities",
                  "other_cleaning_activities",
                  "landscape_service_activities",
                  "combined_office_administrative_service_activities",
                  "photocopying_document_preparation_and_office_support",
                  "activities_of_call_centres",
                  "organisation_of_conventions_and_trade_shows",
                  "activities_of_collection_agencies_and_credit_bureaus",
                  "packaging_activities",
                  "other_business_support_service_activities_nec",
                  "o_public_administration_and_defence_social_security",
                  "administration_of_the_state_and_economic_social_policy",
                  "provision_of_services_to_the_community",
                  "compulsory_social_security_activities",
                  "p_education",
                  "other_education",
                  "educational_support_activities",
                  "q_human_health_and_social_work_activities",
                  "hospital_activities",
                  "medical_and_dental_practice_activities",
                  "other_human_health_activities",
                  "residential_nursing_care_activities",
                  "residential_care_activities_for_mental_health_and_substance_abuse",
                  "residential_care_activities_for_the_elderly_and_disabled",
                  "other_residential_care_activities",
                  "social_work_activities_without_accommodation_for_elderly",
                  "other_social_work_activities_without_accommodation",
                  "r_arts_entertainment_and_recreation",
                  "creative_arts_and_entertainment_activities",
                  "gambling_and_betting_activities",
                  "amusement_and_recreation_activities",
                  "s_other_service_activities",
                  "activities_of_business_employers_and_professional_organisations",
                  "activities_of_trade_unions",
                  "activities_of_other_membership_organisations",
                  "repair_of_computers_and_communication_equipment",
                  "repair_of_personal_and_household_goods",
                  "washing_and_drycleaning_of_textile_and_fur_products",
                  "hairdressing_and_other_beauty_treatment",
                  "funeral_and_related_activities",
                ],
                { invalid_type_error: "This field must be a valid option", required_error: "This field is required" }
              )
              .describe("Details of a primary economic activity carried out by the company.")
              .optional()
              .nullable(),
            general_economic_activities_industry_category: z
              .enum(["primary", "secondary", "tertiary", "quaternary"], {
                invalid_type_error: "This field must be a valid option",
                required_error: "This field is required",
              })
              .describe("The broad industry classification in which the economic activity is categorized.")
              .optional()
              .nullable(),
            general_economic_activities_industry_sector: z
              .enum(["agriculture", "manufacturing", "services", "technology"], {
                invalid_type_error: "This field must be a valid option",
                required_error: "This field is required",
              })
              .describe("A more detailed industry section classification for the economic activity.")
              .optional()
              .nullable(),
            general_economic_activities_share_in_net_turnover: z
              .number({
                invalid_type_error: "This field must be a valid number",
                required_error: "This field is required",
              })
              .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
              .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
              .describe("The percentage share of the company’s net turnover attributable to this economic activity.")
              .optional()
              .nullable(),
          })
        )
        .max(3, { message: "This field value is too large" })
        .describe(
          "Please indicate up to three main economic activities for the company and their share in the last full financial year"
        )
        .optional()
        .nullable(),
      general_revenue_questions: z.object({
        general_revenue_questions_coal_revenue: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Indicates whether 1% or more of the company’s revenue is derived from hard coal and lignite-related activities, which is relevant for assessing fossil fuel dependency."
          ),
        general_revenue_questions_oil_revenue: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Assesses if 10% or more of the company’s revenue comes from oil fuels, highlighting exposure to fossil fuel activities."
          ),
        general_revenue_questions_gaseous_fuels_revenue: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Evaluates whether 50% or more of the company’s revenue is generated from gaseous fuels, indicating significant reliance on these energy sources."
          ),
        general_revenue_questions_electricity_generation_revenue: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Determines if at least 50% of the company’s revenue comes from electricity generation with high GHG intensity, critical for climate impact assessments."
          ),
      }),
      general_is_pie_company: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Determines if at least 50% of the company’s revenue comes from electricity generation with high GHG intensity, critical for climate impact assessments."
        ),
      general_properties_information: z.object({
        general_properties_information_number_of_properties: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .int({ message: "This field must be a whole number" })
          .describe(
            "Specifies the total number of physical properties or operational locations associated with the company."
          ),
        general_properties_information_can_define_main_properties: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "*Main Real Estate object - real estate object (leased or owned), which is critically important to ensure business activity or represents significant share of the assets of the Company. Please indicate up to 5 main real estate objects.\nIf Company operates more than 5 real estate objects of equal significance, please fill in by providing general information on number, type and location of Group of real estate objects.\n**Group of Real estate objects - objects with identical purpose of use located in the same city/parish/district"
          ),
        general_properties_information_main_properties: z
          .array(
            z.object({
              general_properties_information_main_properties_purpose_of_use: z
                .enum(
                  [
                    "location_of_it_infrastructure",
                    "production",
                    "retail",
                    "warehouse",
                    "agricultural_land",
                    "forest",
                    "other_land_utilized_for_business_purposes",
                    "office",
                  ],
                  { invalid_type_error: "This field must be a valid option", required_error: "This field is required" }
                )
                .describe(
                  "Indicates the intended purpose or use of the first listed address, based on predefined usage categories."
                )
                .optional()
                .nullable(),
              general_properties_information_main_properties_country: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe("Specifies the country where the first property is located.")
                .optional()
                .nullable(),
              general_properties_information_main_properties_street_address: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Provides the street address and building identifier or property name for the first property."
                )
                .optional()
                .nullable(),
              general_properties_information_main_properties_city_county_state: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe("Identifies the city, county, or state for the first property location.")
                .optional()
                .nullable(),
              general_properties_information_main_properties_postal_code: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe("Specifies the postal code corresponding to the first property location.")
                .optional()
                .nullable(),
            })
          )
          .max(5, { message: "This field value is too large" })
          .describe(
            "Please indicate up to 5 main real estate locations, which are critically important for the business activity of the Company."
          )
          .optional()
          .nullable(),
      }),
    })
    .optional()
    .nullable(),
  climate_impact: z
    .object({
      climate_impact_ghg_emissions: z
        .object({
          climate_impact_ghg_emissions_monitors_ghg_emissions: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Indicates whether the company actively monitors and calculates its greenhouse gas (GHG) emissions—a critical element of climate reporting."
            )
            .optional()
            .nullable(),
          climate_impact_ghg_emissions_emission_volumes: z
            .object({
              climate_impact_ghg_emissions_emission_volumes_scope_1: z
                .object({
                  climate_impact_ghg_emissions_emission_volumes_scope_1_year_2023: z
                    .number({
                      invalid_type_error: "This field must be a valid number",
                      required_error: "This field is required",
                    })
                    .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_1_year_2022: z
                    .number({
                      invalid_type_error: "This field must be a valid number",
                      required_error: "This field is required",
                    })
                    .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_1_year_2021: z
                    .number({
                      invalid_type_error: "This field must be a valid number",
                      required_error: "This field is required",
                    })
                    .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                    .optional()
                    .nullable(),
                })
                .describe("Reports direct GHG emissions (Scope 1) measured in tonnes of CO2 equivalent.")
                .optional()
                .nullable(),
              climate_impact_ghg_emissions_emission_volumes_scope_2: z
                .object({
                  climate_impact_ghg_emissions_emission_volumes_scope_2_year_2023: z
                    .number({
                      invalid_type_error: "This field must be a valid number",
                      required_error: "This field is required",
                    })
                    .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_2_year_2022: z
                    .number({
                      invalid_type_error: "This field must be a valid number",
                      required_error: "This field is required",
                    })
                    .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_2_year_2021: z
                    .number({
                      invalid_type_error: "This field must be a valid number",
                      required_error: "This field is required",
                    })
                    .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                    .optional()
                    .nullable(),
                })
                .describe("Reports indirect GHG emissions from purchased energy (Scope 2) in tonnes of CO2 equivalent.")
                .optional()
                .nullable(),
              climate_impact_ghg_emissions_emission_volumes_scope_3: z
                .object({
                  climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe(
                      "Specifies GHG emissions related to purchased goods and services, which are part of Scope 3 emissions."
                    )
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe("Reports GHG emissions resulting from business travel, included under Scope 3.")
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe("Details GHG emissions generated from employee commuting, as part of Scope 3.")
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe("Reports GHG emissions associated with leased assets, included in Scope 3.")
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe(
                      "Specifies GHG emissions from transportation and distribution services, reported under Scope 3."
                    )
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_3_investments: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe("Reports GHG emissions attributed to investments, included as part of Scope 3.")
                    .optional()
                    .nullable(),
                  climate_impact_ghg_emissions_emission_volumes_scope_3_other: z
                    .object({
                      climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2023: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2022: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                      climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2021: z
                        .number({
                          invalid_type_error: "This field must be a valid number",
                          required_error: "This field is required",
                        })
                        .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
                        .optional()
                        .nullable(),
                    })
                    .describe("Covers other categories of Scope 3 GHG emissions not captured in the previous items.")
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Reports other indirect GHG emissions (Scope 3) associated with the company’s value chain, measured in tonnes of CO2 equivalent."
                )
                .optional()
                .nullable(),
            })
            .describe(
              "Requests the disclosure of annual GHG emission volumes for the last three calendar years if monitoring is conducted."
            )
            .optional()
            .nullable(),
          climate_impact_ghg_emissions_calculation_framework: z
            .enum(["ghg_protocol", "iso_14064", "other"], {
              invalid_type_error: "This field must be a valid option",
              required_error: "This field is required",
            })
            .describe(
              "Identifies the framework or methodology (e.g., GHG Protocol, ISO 14064) used for calculating the company’s GHG emissions."
            )
            .optional()
            .nullable(),
          climate_impact_ghg_emissions_external_validation: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Indicates whether the company’s GHG emissions data has undergone external validation or verification."
            )
            .optional()
            .nullable(),
          climate_impact_ghg_emissions_verifier: z
            .enum(["bureau_veritas", "bm_certification", "other"], {
              invalid_type_error: "This field must be a valid option",
              required_error: "This field is required",
            })
            .describe(
              "Provides the name or details of the external verifier if the GHG emissions data has been validated."
            )
            .optional()
            .nullable(),
          climate_impact_ghg_emissions_reduction_plan: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe("Indicates whether the company has a plan in place to reduce its GHG emissions.")
            .optional()
            .nullable(),
          climate_impact_ghg_emissions_reduction_goals: z
            .array(
              z.object({
                climate_impact_ghg_emissions_reduction_goals_reduction_goal: z
                  .string({
                    invalid_type_error: "This field must be a valid text",
                    required_error: "This field is required",
                  })
                  .optional()
                  .nullable(),
                climate_impact_ghg_emissions_reduction_goals_metrics: z
                  .enum(
                    [
                      "tco2eq_absolute_measurement",
                      "tco2eq_as_relation_to_produced_volume_type_in_exact_ratio_eg_02_tco2eq_per_1_t_of_produce",
                      "type_in_other",
                    ],
                    {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    }
                  )
                  .optional()
                  .nullable(),
                climate_impact_ghg_emissions_reduction_goals_scope: z
                  .enum(["scope_1", "scope_1_and_2", "scope_1_2_and_3"], {
                    invalid_type_error: "This field must be a valid option",
                    required_error: "This field is required",
                  })
                  .optional()
                  .nullable(),
                climate_impact_ghg_emissions_reduction_goals_baseline_year: z
                  .string({
                    invalid_type_error: "This field must be a valid text",
                    required_error: "This field is required",
                  })
                  .optional()
                  .nullable(),
                climate_impact_ghg_emissions_reduction_goals_target_year: z
                  .string({
                    invalid_type_error: "This field must be a valid text",
                    required_error: "This field is required",
                  })
                  .optional()
                  .nullable(),
              })
            )
            .describe(
              "Details the GHG emission reduction goals, including short-, medium-, and long-term targets, along with baseline and target years where applicable."
            )
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      climate_impact_energy_consumption: z
        .object({
          climate_impact_energy_consumption_non_renewable_sources: z
            .object({
              climate_impact_energy_consumption_non_renewable_sources_district_heating: z
                .object({
                  climate_impact_energy_consumption_non_renewable_sources_district_heating_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_district_heating_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_district_heating_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Indicates whether district heating is consumed as a non-renewable energy source, including corresponding volume metrics."
                )
                .optional()
                .nullable(),
              climate_impact_energy_consumption_non_renewable_sources_gas: z
                .object({
                  climate_impact_energy_consumption_non_renewable_sources_gas_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_gas_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_gas_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Specifies the consumption of gas as a non-renewable energy source, with volume details provided."
                )
                .optional()
                .nullable(),
              climate_impact_energy_consumption_non_renewable_sources_coal: z
                .object({
                  climate_impact_energy_consumption_non_renewable_sources_coal_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_coal_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_coal_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Reports on the usage of coal as a non-renewable energy source, including consumption volume."
                )
                .optional()
                .nullable(),
              climate_impact_energy_consumption_non_renewable_sources_diesel: z
                .object({
                  climate_impact_energy_consumption_non_renewable_sources_diesel_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_diesel_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_diesel_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe("Details the consumption of diesel as a non-renewable energy source, measured in volume.")
                .optional()
                .nullable(),
              climate_impact_energy_consumption_non_renewable_sources_electricity: z
                .object({
                  climate_impact_energy_consumption_non_renewable_sources_electricity_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_electricity_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_non_renewable_sources_electricity_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Indicates whether electricity is consumed as a non-renewable energy source, with volume metrics provided."
                )
                .optional()
                .nullable(),
              climate_impact_energy_consumption_non_renewable_sources_other_sources: z
                .array(
                  z.object({
                    climate_impact_energy_consumption_non_renewable_sources_other_sources_enabled: z
                      .boolean({
                        invalid_type_error: "This field must be true or false",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                    climate_impact_energy_consumption_non_renewable_sources_other_sources_type: z
                      .string({
                        invalid_type_error: "This field must be a valid text",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                    climate_impact_energy_consumption_non_renewable_sources_other_sources_volume: z
                      .string({
                        invalid_type_error: "This field must be a valid text",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                    climate_impact_energy_consumption_non_renewable_sources_other_sources_metrics: z
                      .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                        invalid_type_error: "This field must be a valid option",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                  })
                )
                .optional()
                .nullable(),
            })
            .describe(
              "Requests details on the consumption of non-renewable energy sources for the last full calendar year, including volume and metric for each type."
            )
            .optional()
            .nullable(),
          climate_impact_energy_consumption_renewable_sources: z
            .object({
              climate_impact_energy_consumption_renewable_sources_district_heating: z
                .object({
                  climate_impact_energy_consumption_renewable_sources_district_heating_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_district_heating_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_district_heating_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe("Specifies consumption details of renewable district heating, measured in volume.")
                .optional()
                .nullable(),
              climate_impact_energy_consumption_renewable_sources_biogas: z
                .object({
                  climate_impact_energy_consumption_renewable_sources_biogas_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_biogas_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_biogas_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Indicates whether biogas is consumed as a renewable energy source, along with volume metrics."
                )
                .optional()
                .nullable(),
              climate_impact_energy_consumption_renewable_sources_electricity: z
                .object({
                  climate_impact_energy_consumption_renewable_sources_electricity_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_electricity_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_electricity_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe("Reports on the consumption of renewable electricity, including volume details.")
                .optional()
                .nullable(),
              climate_impact_energy_consumption_renewable_sources_biomass: z
                .object({
                  climate_impact_energy_consumption_renewable_sources_biomass_enabled: z
                    .boolean({
                      invalid_type_error: "This field must be true or false",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_biomass_volume: z
                    .string({
                      invalid_type_error: "This field must be a valid text",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                  climate_impact_energy_consumption_renewable_sources_biomass_metrics: z
                    .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                      invalid_type_error: "This field must be a valid option",
                      required_error: "This field is required",
                    })
                    .optional()
                    .nullable(),
                })
                .describe(
                  "Details the consumption of biomass as a renewable energy source, with corresponding volume metrics."
                )
                .optional()
                .nullable(),
              climate_impact_energy_consumption_renewable_sources_other_sources: z
                .array(
                  z.object({
                    climate_impact_energy_consumption_renewable_sources_other_sources_enabled: z
                      .boolean({
                        invalid_type_error: "This field must be true or false",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                    climate_impact_energy_consumption_renewable_sources_other_sources_type: z
                      .string({
                        invalid_type_error: "This field must be a valid text",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                    climate_impact_energy_consumption_renewable_sources_other_sources_volume: z
                      .string({
                        invalid_type_error: "This field must be a valid text",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                    climate_impact_energy_consumption_renewable_sources_other_sources_metrics: z
                      .enum(["kwh", "mwh", "gwh", "m³", "tons"], {
                        invalid_type_error: "This field must be a valid option",
                        required_error: "This field is required",
                      })
                      .optional()
                      .nullable(),
                  })
                )
                .optional()
                .nullable(),
            })
            .describe(
              "Requests details on renewable energy consumption for the last full calendar year, including volume and metric for each type."
            )
            .optional()
            .nullable(),
          climate_impact_energy_consumption_efficiency_plan: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe("Indicates whether the company has implemented an energy efficiency improvement plan.")
            .optional()
            .nullable(),
          climate_impact_energy_consumption_efficiency_plan_details: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Requests additional details regarding the energy efficiency improvement plan, if one is in place."
            )
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
    })
    .describe(
      "Climate change and environmental degradation is a threat, which puts preasure on social, economic and business environment. GHG emissions are the primary driver of global climate change, while energy consumption (in particular burning of fossil fuels), is the main source of GHG emissions. To overcome climate change challenges European Union is taking action by setting ambitious goal of GHG reduction by 55% by 2030 and by becomming climate neutral by 2050. In order to achieve it, every action and step towards lower GHG emission economy is important."
    )
    .optional()
    .nullable(),
  green_transition: z
    .object({
      green_transition_sustainability_strategy: z.object({
        green_transition_sustainability_strategy_has_strategy: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Determines if the company has a formal sustainability strategy or if sustainability is integrated within other policies or documents."
          ),
        green_transition_sustainability_strategy_strategy_document: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe("Requests a link to or submission of the document detailing the company’s sustainability strategy.")
          .optional()
          .nullable(),
        green_transition_sustainability_strategy_additional_documents: z
          .array(
            z.object({
              key: z.string({
                invalid_type_error: "This field must be a valid text",
                required_error: "This field is required",
              }),
              mimeType: z.string({
                invalid_type_error: "This field must be a valid text",
                required_error: "This field is required",
              }),
              name: z.string({
                invalid_type_error: "This field must be a valid text",
                required_error: "This field is required",
              }),
              url: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .optional()
                .nullable(),
            })
          )
          .describe("Requests a link to or submission of the document detailing the company’s sustainability strategy.")
          .optional()
          .nullable(),
      }),
      green_transition_sustainability_report: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Indicates whether the company produces a dedicated sustainability report or integrates sustainability disclosures within other reports."
        ),
      green_transition_other_sustainability_reports: z.object({
        green_transition_other_sustainability_reports_has_reports: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Asks if the company prepares additional sustainability-related reports (e.g., environmental reports) or uses alternative communication channels for its sustainability plans."
          ),
        green_transition_other_sustainability_reports_report_document: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe("Requests a link to or submission of any additional sustainability-related report documents.")
          .optional()
          .nullable(),
        green_transition_other_sustainability_reports_additional_documents: z
          .array(
            z.object({
              key: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .optional()
                .nullable(),
              mimeType: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .optional()
                .nullable(),
              name: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .optional()
                .nullable(),
              url: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .optional()
                .nullable(),
            })
          )
          .describe("Requests a link to or submission of any additional sustainability-related report documents.")
          .optional()
          .nullable(),
      }),
      green_transition_sustainability_certification: z.object({
        green_transition_sustainability_certification_has_certification: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Determines if the company holds certifications for sustainability standards or an Environmental Product Declaration (EPD)."
          ),
        green_transition_sustainability_certification_certification_details: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe(
            "Provides space for the company to detail its sustainability certification or EPD information if applicable."
          )
          .optional()
          .nullable(),
      }),
      green_transition_environmentally_friendly_goods: z.object({
        green_transition_environmentally_friendly_goods_has_eco_goods: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Evaluates whether the company supplies environmentally friendly goods or employs circular economic approaches."
          ),
        green_transition_environmentally_friendly_goods_eco_goods_details: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe("Requests further details on the company’s environmentally friendly practices or product lines.")
          .optional()
          .nullable(),
      }),
      green_transition_epd_goods_services: z.object({
        green_transition_epd_goods_services_has_epd: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Indicates whether the company holds an Environmental Product Declaration (EPD) for its produced goods or services."
          ),
        green_transition_epd_goods_services_epd_details: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe("Requests additional details regarding the EPD if one is held by the company.")
          .optional()
          .nullable(),
      }),
      green_transition_carbon_offsetting: z.object({
        green_transition_carbon_offsetting_has_offsetting: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Determines whether the company participates in carbon offsetting initiatives to mitigate its carbon footprint."
          ),
        green_transition_carbon_offsetting_offsetting_details: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe("Provides space for the company to detail its carbon offsetting practices, if applicable.")
          .optional()
          .nullable(),
      }),
      green_transition_international_standards: z.object({
        green_transition_international_standards_follows_standards: z
          .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
          .describe(
            "Assesses whether the company adheres to international or local best practice standards or has committed to external sustainability commitments."
          ),
        green_transition_international_standards_standards_details: z
          .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
          .describe(
            "Requests additional details on the best practice standards or external commitments the company has signed."
          )
          .optional()
          .nullable(),
      }),
      green_transition_eu_taxonomy: z
        .object({
          green_transition_eu_taxonomy_climate_change_mitigation: z
            .object({
              green_transition_eu_taxonomy_climate_change_mitigation_implemented: z
                .boolean({
                  invalid_type_error: "This field must be true or false",
                  required_error: "This field is required",
                })
                .describe(
                  "Indicates if the company has initiatives aimed at mitigating climate change in line with EU Taxonomy objectives."
                ),
              green_transition_eu_taxonomy_climate_change_mitigation_details: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Requests detailed information on the specific activities contributing to climate change mitigation implemented by the company."
                )
                .optional()
                .nullable(),
            })
            .optional()
            .nullable(),
          green_transition_eu_taxonomy_climate_change_adaptation: z
            .object({
              green_transition_eu_taxonomy_climate_change_adaptation_implemented: z
                .boolean({
                  invalid_type_error: "This field must be true or false",
                  required_error: "This field is required",
                })
                .describe(
                  "Evaluates whether the company has measures in place to adapt to the impacts of climate change, as per EU Taxonomy requirements."
                ),
              green_transition_eu_taxonomy_climate_change_adaptation_details: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Requests detailed information on the specific activities contributing to climate change adaptation implemented by the company."
                )
                .optional()
                .nullable(),
            })
            .optional()
            .nullable(),
          green_transition_eu_taxonomy_sustainable_water_use: z
            .object({
              green_transition_eu_taxonomy_sustainable_water_use_implemented: z
                .boolean({
                  invalid_type_error: "This field must be true or false",
                  required_error: "This field is required",
                })
                .describe(
                  "Assesses the company’s activities related to the sustainable use and protection of water and marine resources."
                ),
              green_transition_eu_taxonomy_sustainable_water_use_details: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Requests detailed information on the specific activities contributing to the sustainable use and protection of water and marine resources implemented by the company."
                )
                .optional()
                .nullable(),
            })
            .optional()
            .nullable(),
          green_transition_eu_taxonomy_circular_economy: z
            .object({
              green_transition_eu_taxonomy_circular_economy_implemented: z
                .boolean({
                  invalid_type_error: "This field must be true or false",
                  required_error: "This field is required",
                })
                .describe(
                  "Evaluates if the company is transitioning to a circular economy by implementing relevant activities."
                ),
              green_transition_eu_taxonomy_circular_economy_details: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Requests detailed information on the specific activities contributing to the transition to a circular economy implemented by the company."
                )
                .optional()
                .nullable(),
            })
            .optional()
            .nullable(),
          green_transition_eu_taxonomy_pollution_prevention: z
            .object({
              green_transition_eu_taxonomy_pollution_prevention_implemented: z
                .boolean({
                  invalid_type_error: "This field must be true or false",
                  required_error: "This field is required",
                })
                .describe(
                  "Assesses if the company has measures in place for pollution prevention and control in accordance with environmental standards."
                ),
              green_transition_eu_taxonomy_pollution_prevention_details: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Requests detailed information on the specific activities contributing to pollution prevention and control implemented by the company."
                )
                .optional()
                .nullable(),
            })
            .optional()
            .nullable(),
          green_transition_eu_taxonomy_biodiversity_protection: z
            .object({
              green_transition_eu_taxonomy_biodiversity_protection_implemented: z
                .boolean({
                  invalid_type_error: "This field must be true or false",
                  required_error: "This field is required",
                })
                .describe(
                  "Indicates if the company has initiatives focused on protecting and restoring biodiversity and ecosystems."
                ),
              green_transition_eu_taxonomy_biodiversity_protection_details: z
                .string({
                  invalid_type_error: "This field must be a valid text",
                  required_error: "This field is required",
                })
                .describe(
                  "Requests detailed information on the specific activities contributing to biodiversity protection implemented by the company."
                )
                .optional()
                .nullable(),
            })
            .optional()
            .nullable(),
        })
        .describe(
          "Determines if the company has implemented activities that contribute to the EU Taxonomy environmental objectives, and requests detailed information if so."
        ),
    })
    .describe(
      "To overcome climate change challenges European Union is taking action by increasing regulatory requirements, aiming to help supporting those activities and actions, which positively contribute to limiting negative effects of climate change. For large companies it means increase of regulatory requirements in the forthcoming years according to Corporate Sustainability Reporting Directive regulation (eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32022L2464). At the same time increased awareness towards climate change and threats related to these processes, may lead to change in market behaviour, e.g. consumers giving preference to more sustainable goods and services, business partners willing to cooperate with companies, which practice sustainable approaches, investors - opting to invest in more sustainable businesses. Answers to these questions will help to understand your Company's readiness to green transition."
    )
    .optional()
    .nullable(),
  nature: z
    .object({
      nature_biodiversity_sensitive_areas: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Determines if the company operates in or near biodiversity-sensitive areas, such as Natura 2000 sites or UNESCO World Heritage areas."
        ),
      nature_negative_impact_on_nature: z
        .enum(
          [
            "yes_critically_eg_the_ploughing_of_natural_meadows_construction_works_in_nature_reserves_forest_transformation_deforestation_and_clear-cutting",
            "yes_other_impact",
            "no_impact",
            "i_dont_know",
          ],
          { invalid_type_error: "This field must be a valid option", required_error: "This field is required" }
        )
        .describe(
          "Assesses whether the company’s operations negatively impact natural realms such as oceans, freshwater, land, or the atmosphere."
        ),
      nature_negative_impact_comments: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .describe(
          "Provides an opportunity for the company to comment on any negative impacts its operations may have on nature."
        )
        .optional()
        .nullable(),
      nature_plan_to_reduce_impact: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Determines if the company has developed a plan to prevent or reduce negative environmental impacts identified in previous questions."
        )
        .optional()
        .nullable(),
      nature_measures_details: z
        .object({
          nature_measures_details_avoid: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe("Describes actions taken by the company to avoid causing any negative environmental impacts.")
            .optional()
            .nullable(),
          nature_measures_details_mitigate: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Describes measures implemented to reduce the significance or intensity of unavoidable negative environmental impacts."
            )
            .optional()
            .nullable(),
          nature_measures_details_restore: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Outlines actions taken by the company to restore ecosystems that have been degraded due to unavoidable negative impacts."
            )
            .optional()
            .nullable(),
          nature_measures_details_compensate: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Describes compensation measures taken to address any residual adverse environmental effects that cannot be fully mitigated."
            )
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      nature_natural_capital_dependency: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Assesses the extent to which the company’s activities depend on natural capital, including both renewable and non-renewable resources."
        ),
      nature_natural_capital_plan: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Determines if the company has developed measures to mitigate or reduce its dependency on natural capital."
        )
        .optional()
        .nullable(),
      nature_natural_capital_measures: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .describe("Requests details on specific measures implemented to reduce dependency on natural capital.")
        .optional()
        .nullable(),
      nature_waste_management: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Indicates whether the company has a formal waste management procedure in place to manage and reduce waste."
        ),
      nature_nature_commitments: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Determines if the company has committed to nature-positive objectives or frameworks such as the TNFD."
        ),
      nature_nature_commitments_details: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .describe(
          "Requests additional details on the company’s commitments to nature-positive initiatives or frameworks."
        )
        .optional()
        .nullable(),
    })
    .describe(
      "Biodiversity is a variety of forms of life on earth - such as different species of plants and animals found within specific regions. Biodiversity is essential for functioning of all processes: from ensuring survival of all life forms to climate regulation and economic stability. Biodiversity is also a fundamental component of long-term business survival. Biodiversity underpins the benefits that businesses derive from natural capital and supports the key ecosystem functions to maintain quality of soil, water and air. While the private sector can have a negative effect on Biodiversity, it can also be part of the solution. The resources and impact of the private sector offer important opportunities for innovative and effective contributions to conservation. Answers provided to the questions of this section will allow to get basic information on the impact, which business activity of your Company can have on biodiversity."
    )
    .optional()
    .nullable(),
  social_governance: z
    .object({
      social_governance_workforce_policies: z
        .array(
          z.enum(
            [
              "personnel_management",
              "remuneration",
              "safety_and_health",
              "equality",
              "diversity",
              "harassment",
              "child_labour",
              "other",
            ],
            { invalid_type_error: "This field must be a valid option", required_error: "This field is required" }
          )
        )
        .min(1, { message: "This field value is too small" })
        .describe(
          "Requests disclosure of the policies and procedures related to employee benefits, engagement, and human rights protection."
        ),
      social_governance_end_user_policies: z
        .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
        .describe(
          "Requests details on the policies and procedures in place to ensure product safety and social responsibility for end-users."
        ),
      social_governance_safety_incidents: z
        .object({
          social_governance_safety_incidents_has_incidents: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Inquires about any significant incidents related to the safety or quality of the company’s products or services over the last three years."
            ),
          social_governance_safety_incidents_details: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Provides space for the company to detail any incidents affecting product or service safety and quality."
            )
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      social_governance_stakeholder_engagement: z
        .object({
          social_governance_stakeholder_engagement_has_programs: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Assesses whether the company engages in stakeholder engagement, impact assessment, and development programs to mitigate operational impacts."
            ),
          social_governance_stakeholder_engagement_details: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Requests additional details on stakeholder engagement or impact assessment programs implemented by the company."
            )
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      social_governance_community_benefits: z
        .object({
          social_governance_community_benefits_has_initiatives: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Determines if the company has initiatives aimed at providing benefits to local communities or underserved markets."
            ),
          social_governance_community_benefits_details: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe(
              "Provides space for the company to describe initiatives targeted at supporting local communities."
            )
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      social_governance_supply_chain_esg: z
        .object({
          social_governance_supply_chain_esg_has_policies: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Assesses if the company has established policies or processes to address ESG criteria within its supply chain."
            ),
          social_governance_supply_chain_esg_details: z
            .string({ invalid_type_error: "This field must be a valid text", required_error: "This field is required" })
            .describe("Requests additional details on how the company ensures ESG compliance within its supply chain.")
            .optional()
            .nullable(),
        })
        .describe(
          "(e.g. working conditions, human rights, privacy, labour rights, health and safety protection, social and labour protection, environmental protection)"
        )
        .optional()
        .nullable(),
      social_governance_international_compliance: z
        .object({
          social_governance_international_compliance_complies: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Determines if the company’s policies comply with international guidelines such as the OECD Guidelines and UN Guiding Principles on Business and Human Rights."
            )
            .optional()
            .nullable(),
        })
        .describe(
          "including the principles and rights set out in the eight core conventions and the International Labour Organization Declaration on Fundamental Principles and Rights at Work and the International Bill of Human Rights (as well as local regulations regarding human and employee rights)?"
        ),
      social_governance_governance_policies: z
        .array(
          z.enum(
            [
              "corporate_governance",
              "conflict_of_interest_management",
              "respect_of_human_rights_including_labour_right",
              "prevention_of_money_laundering",
              "whistleblowing",
              "fraud_and_corruption_prevention",
              "tax_transparency",
              "risk_management",
              "data_security_and_cyber_security",
              "code_of_conduct_and_ethics",
              "other",
            ],
            { invalid_type_error: "This field must be a valid option", required_error: "This field is required" }
          )
        )
        .min(1, { message: "This field value is too small" })
        .describe(
          "Determines if the company’s policies comply with international guidelines such as the OECD Guidelines and UN Guiding Principles on Business and Human Rights."
        ),
      social_governance_materiality_assessment: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Determines if the company has performed an ESG materiality assessment to identify key environmental, social, and governance risks."
        ),
      social_governance_supplier_code_of_conduct: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Assesses whether the company has developed a supplier code of conduct outlining the ESG standards required of its suppliers."
        ),
      social_governance_supplier_monitoring: z
        .enum(["high_level_of_monitoring", "medium_level_of_monitoring", "low_level_of_monitoring", "no_monitoring"], {
          invalid_type_error: "This field must be a valid option",
          required_error: "This field is required",
        })
        .describe(
          "Requests details on the extent to which the company monitors and assesses supplier sustainability performance."
        ),
      social_governance_litigation: z
        .object({
          social_governance_litigation_has_litigation: z
            .boolean({
              invalid_type_error: "This field must be true or false",
              required_error: "This field is required",
            })
            .describe(
              "Inquires if the company has been involved in any litigation, controversy, or concerns related to non-compliance with environmental, social, or regulatory standards."
            )
            .optional()
            .nullable(),
        })
        .describe(
          "e.g. water, waste, energy, biodiversity, environment regulations or water, energy, waste, biodiversity, environment disturbances, human and labor rights, health and safety, product/service of the company?"
        ),
      social_governance_non_compliances: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Specifies whether the company has faced non-compliances, warnings, fines, or penalties due to regulatory breaches."
        ),
      social_governance_privacy_breaches: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Inquires if there have been substantiated complaints regarding breaches of customer privacy or data protection issues."
        ),
      social_governance_compliance_officer: z
        .boolean({ invalid_type_error: "This field must be true or false", required_error: "This field is required" })
        .describe(
          "Indicates whether the company has designated an officer responsible for ensuring compliance with internal governance policies."
        ),
    })
    .describe(
      "Social and governance framework is essential in order to implement and maintain sustainability practices. Social area covers impact on surrounding community and workers (such as health and safety, diversity and inclusion). Corporate governance refers to processes, which ensure effective management of the Company (e.g. ethical conduct, risk management, transparency)."
    )
    .optional()
    .nullable(),
  nfrd_reporting: z
    .object({
      nfrd_reporting_taxonomy_eligible_activities: z.object({
        nfrd_reporting_taxonomy_eligible_activities_sustainable_net_turnover: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
          .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
          .describe(
            "Reports the net turnover from environmentally sustainable activities that are aligned with the EU Taxonomy."
          ),
        nfrd_reporting_taxonomy_eligible_activities_eligible_net_turnover: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
          .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
          .describe(
            "Reports the net turnover from activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable."
          ),
        nfrd_reporting_taxonomy_eligible_activities_sustainable_capex: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
          .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
          .describe(
            "Reports the capital expenditure on environmentally sustainable activities that are aligned with the EU Taxonomy."
          ),
        nfrd_reporting_taxonomy_eligible_activities_eligible_capex: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
          .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
          .describe(
            "Reports the capital expenditure on activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable."
          ),
        nfrd_reporting_taxonomy_eligible_activities_sustainable_opex: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
          .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
          .describe(
            "Reports the operational expenditure on environmentally sustainable activities that are aligned with the EU Taxonomy."
          ),
        nfrd_reporting_taxonomy_eligible_activities_eligible_opex: z
          .number({ invalid_type_error: "This field must be a valid number", required_error: "This field is required" })
          .gte(0, { message: "This field must be greater than or equal to the minimum value (0)" })
          .lte(100, { message: "This field must be less than or equal to the maximum value (100)" })
          .describe(
            "Reports the operational expenditure on activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable."
          ),
      }),
    })
    .describe(
      "Requests disclosure of the proportion of economic activities that are eligible under the EU Taxonomy framework."
    )
    .optional()
    .nullable(),
})
