/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "Impactly Client Portal",
  description:
    "ESG Screening and Due Diligence for businesses, banks, and investment companies. Gain insights into the sustainability performance of your business partners, investment targets, or suppliers. We keep you informed by combining a wide range of data sources, predictive analytics and state-of-the-art technology.",
  keywords: ["Impactly", "Client Portal", "ESG", "KYC", "Sustainability"],
  authors: [
    {
      name: "IMPACTLY OÜ",
      url: "https://www.impactly.eu",
    },
  ],
  creator: "IMPACTLY OÜ",
}
