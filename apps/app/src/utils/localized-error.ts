/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { TRPCClientError } from "@trpc/client"
import { useTranslations } from "next-intl"

import { toast } from "@kreios/ui/sonner"

/**
 * Maps tRPC error codes to error message keys in the translation files
 */
const errorCodeToMessageKey: Record<string, string> = {
  UNAUTHORIZED: "errors.common.unauthorized",
  NOT_FOUND: "errors.common.notFound",
  BAD_REQUEST: "errors.common.badRequest",
  INTERNAL_SERVER_ERROR: "errors.common.serverError",
  TIMEOUT: "errors.common.timeout",
  NETWORK_ERROR: "errors.common.networkError",
  // Add more mappings as needed
}

/**
 * Maps specific error messages to translation keys
 * This is useful for handling specific error messages that don't have a specific error code
 */
const errorMessageToKey: Record<string, string> = {
  "Please fill out full survey before submitting": "errors.surveys.notComplete",
  "Survey already submitted": "errors.surveys.alreadySubmitted",
  "Survey deadline has passed": "errors.surveys.pastDeadline",
  "Error submitting order": "errors.orders.submitFailed",
  "Invalid order data": "errors.orders.invalidData",
  "Failed to upload file": "errors.files.uploadFailed",
  // Add more mappings as needed
}

/**
 * Hook to get localized error messages
 * @returns Functions to handle localized error messages
 */
export function useLocalizedError() {
  const t = useTranslations()

  /**
   * Get a localized error message based on the error
   * @param error The error object
   * @returns Localized error message
   */
  const getLocalizedErrorMessage = (error: unknown): string => {
    // Default error message
    let messageKey = "errors.common.default"

    if (error instanceof TRPCClientError) {
      // Try to get the error code from the tRPC error
      const errorCode = error.data?.code
      if (errorCode && errorCodeToMessageKey[errorCode]) {
        messageKey = errorCodeToMessageKey[errorCode]
      } else {
        // If no specific error code mapping, try to match the error message
        const errorMessage = error.message
        for (const [message, key] of Object.entries(errorMessageToKey)) {
          if (errorMessage.includes(message)) {
            messageKey = key
            break
          }
        }
      }
    } else if (error instanceof Error) {
      // Handle regular Error objects
      for (const [message, key] of Object.entries(errorMessageToKey)) {
        if (error.message.includes(message)) {
          messageKey = key
          break
        }
      }
    }

    // Return the localized message
    return t(messageKey)
  }

  /**
   * Show a localized error toast
   * @param error The error object
   */
  const showLocalizedErrorToast = (error: unknown): void => {
    const message = getLocalizedErrorMessage(error)
    toast.error(message)
  }

  return {
    getLocalizedErrorMessage,
    showLocalizedErrorToast,
  }
}

/**
 * Utility function to show a localized error toast without using the hook
 * Useful for places where hooks can't be used
 * @param error The error object
 * @param t The translation function
 */
export function showLocalizedErrorToast(error: unknown, t: (key: string) => string): void {
  // Default error message
  let messageKey = "errors.common.default"

  if (error instanceof TRPCClientError) {
    // Try to get the error code from the tRPC error
    const errorCode = error.data?.code
    if (errorCode && errorCodeToMessageKey[errorCode]) {
      messageKey = errorCodeToMessageKey[errorCode]
    } else {
      // If no specific error code mapping, try to match the error message
      const errorMessage = error.message
      for (const [message, key] of Object.entries(errorMessageToKey)) {
        if (errorMessage.includes(message)) {
          messageKey = key
          break
        }
      }
    }
  } else if (error instanceof Error) {
    // Handle regular Error objects
    for (const [message, key] of Object.entries(errorMessageToKey)) {
      if (error.message.includes(message)) {
        messageKey = key
        break
      }
    }
  }

  // Show the toast with the localized message
  toast.error(t(messageKey))
}
