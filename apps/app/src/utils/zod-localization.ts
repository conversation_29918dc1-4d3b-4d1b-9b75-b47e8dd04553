/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { Language } from "@/utils/constants"
import { z } from "zod"

/**
 * Get localized error messages based on the current language
 * @param language The language to use for error messages
 * @returns An object with localized error messages
 */
export function getLocalizedErrorMessages(language: Language) {
  return language === Language.EN
    ? {
        required: "This field is required",
        string: "This field must be a valid text",
        email: "Please enter a valid email address",
        url: "Please enter a valid URL",
        min: (min: number) => `This field must be at least ${min} characters`,
        max: (max: number) => `This field cannot exceed ${max} characters`,
        number: "This field must be a valid number",
        integer: "This field must be a whole number",
        positive: "This field must be a positive number",
        date: "This field must be a valid date",
        boolean: "This field must be true or false",
        enum: "Please select a valid option",
        array: "This field must be a valid list",
        object: "This field must be a valid object",
      }
    : {
        required: "See väli on kohustuslik",
        string: "See väli peab olema kehtiv tekst",
        email: "Palun sisestage kehtiv e-posti aadress",
        url: "Palun sisestage kehtiv URL",
        min: (min: number) => `See väli peab olema vähemalt ${min} tähemärki`,
        max: (max: number) => `See väli tohib olla maksimaalselt ${max} tähemärki`,
        number: "See väli peab olema kehtiv number",
        integer: "See väli peab olema täisarv",
        positive: "See väli peab olema positiivne number",
        date: "See väli peab olema kehtiv kuupäev",
        boolean: "See väli peab olema tõene või väär",
        enum: "Palun valige kehtiv väärtus",
        array: "See väli peab olema kehtiv loend",
        object: "See väli peab olema kehtiv objekt",
      }
}

/**
 * Get the current language from the URL or cookie
 * @returns The current language
 */
export function getCurrentLanguage(): Language {
  // Check if we're in a browser environment
  if (typeof window !== "undefined") {
    try {
      // Try to get the language from the HTML lang attribute
      const locale = document.documentElement.lang
      return locale === "et" ? Language.ET : Language.EN
    } catch (e) {
      // If there's an error, return English as a default
      console.error(e)
      return Language.EN
    }
  }

  // If we're in a server context, return English as a default
  return Language.EN
}

/**
 * Get localized error messages for the current language
 * @returns An object with localized error messages
 */
export function getErrorMessages() {
  return getLocalizedErrorMessages(getCurrentLanguage())
}

/**
 * Create a localized error message based on the current language
 * @param en English message
 * @param et Estonian message
 * @returns The message in the current language
 */
export function localizedMessage(en: string, et: string): string {
  return getCurrentLanguage() === Language.EN ? en : et
}

/**
 * Create a localized Zod schema with error messages in the current language
 */
export const lz = {
  string: () =>
    z.string({
      invalid_type_error: localizedMessage("This field must be a valid text", "See väli peab olema kehtiv tekst"),
      required_error: localizedMessage("This field is required", "See väli on kohustuslik"),
    }),
  number: () =>
    z.number({
      invalid_type_error: localizedMessage("This field must be a valid number", "See väli peab olema kehtiv number"),
      required_error: localizedMessage("This field is required", "See väli on kohustuslik"),
    }),
  boolean: () =>
    z.boolean({
      invalid_type_error: localizedMessage("This field must be true or false", "See väli peab olema tõene või väär"),
      required_error: localizedMessage("This field is required", "See väli on kohustuslik"),
    }),
  date: () =>
    z.date({
      invalid_type_error: localizedMessage("This field must be a valid date", "See väli peab olema kehtiv kuupäev"),
      required_error: localizedMessage("This field is required", "See väli on kohustuslik"),
    }),
  enum: <T extends [string, ...string[]]>(values: T) => {
    return z.enum(values, {
      errorMap: () => ({
        message: localizedMessage("Please select a valid option", "Palun valige kehtiv väärtus"),
      }),
    })
  },
  object: <T extends z.ZodRawShape>(shape: T, options?: z.RawCreateParams) => z.object(shape, options),
  array: <T extends z.ZodTypeAny>(schema: T) => z.array(schema),
  coerce: z.coerce,
}
