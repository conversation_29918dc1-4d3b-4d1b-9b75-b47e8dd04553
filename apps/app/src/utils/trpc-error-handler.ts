/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { TRPCClientError } from "@trpc/client"

import { toast } from "@kreios/ui/sonner"

/**
 * Handles tRPC errors by displaying a localized toast message
 * The backend sends error messages as translation keys, which are then
 * translated on the frontend using the next-intl translation function
 *
 * @param error The error object from tRPC
 * @param t The translation function from next-intl
 */
export function handleTRPCError(error: unknown, t: (key: string) => string): void {
  // Default error message key
  let messageKey = "errors.common.default"

  if (error instanceof TRPCClientError) {
    // The error message from the backend is already a translation key
    // We just need to use it to get the translated message
    messageKey = error.message
  }

  // Show the toast with the translated message
  toast.error(t(messageKey))
}

/**
 * Hook to handle tRPC errors
 * @param t The translation function from next-intl
 * @returns A function to handle tRPC errors
 */
export function useTRPCErrorHandler(t: (key: string) => string) {
  return (error: unknown) => handleTRPCError(error, t)
}
