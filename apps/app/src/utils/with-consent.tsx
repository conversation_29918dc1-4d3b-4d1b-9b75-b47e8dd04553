/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import type { ComponentType } from "react"
import { headers } from "next/headers"
import { redirect } from "next/navigation"
import { PostHogPageViewTracker } from "@/components/posthog-provider"
import { getPortalUserByEmail } from "@impactly/domain/portal-users/utils/get-portal-user-by-email"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"

import { auth } from "@kreios/auth"

const PATH_CONFIG = {
  BASE_PATHS: ["/"],
  UNAUTHORIZED_PATHS: ["/unauthorized"],
  CONSENT_PATHS: ["/consent"],
  ADMIN_PATHS: ["/admin"],
} as const

const PathValidator = {
  isBasePath: (pathname: string): boolean => PATH_CONFIG.BASE_PATHS.some((path) => pathname === path),
  isUnauthorizedPath: (pathname: string): boolean => PATH_CONFIG.UNAUTHORIZED_PATHS.some((path) => pathname === path),
  isConsentPath: (pathname: string): boolean => PATH_CONFIG.CONSENT_PATHS.some((path) => pathname === path),
  isAdminPath: (pathname: string): boolean => PATH_CONFIG.ADMIN_PATHS.some((path) => pathname.startsWith(path)),
  isDashboardPath: (pathname: string): boolean => pathname === "/admin",
}

type PortalAccess = {
  redirectPath: string
  pages: {
    dashboard: boolean
    portfolios: boolean
    companies: boolean
    surveys: boolean
    evaluationRequests: boolean
    companySurveys: boolean
    selfAssesment: boolean
    assessmentBatches: boolean
    selfAssessments: boolean
  }
  company: {
    canAccessAll: boolean
    companyId?: string
  }
}

const getPortalAccess = (portalUser: PortalUserDocument | null): PortalAccess | null => {
  if (!portalUser) return null

  const { isAdmin, isClientAdmin, isCompanyUser, isDataViewer, isObserver } = getPortalUserRoles(portalUser)

  return {
    redirectPath:
      isClientAdmin || isAdmin
        ? "/admin/companies"
        : isCompanyUser || isObserver
          ? `/admin/companies/${portalUser.companyId}`
          : isDataViewer
            ? "/coming-soon"
            : "/unauthorized",
    pages: {
      dashboard: false,
      portfolios: false,
      companies: isAdmin || isClientAdmin || isCompanyUser || isObserver,
      surveys: isAdmin || isClientAdmin,
      evaluationRequests: false,
      companySurveys: isCompanyUser || isObserver,
      selfAssesment: true,
      assessmentBatches: isAdmin || isClientAdmin,
      selfAssessments: isCompanyUser || isObserver,
    },
    company: {
      canAccessAll: isAdmin || isClientAdmin,
      companyId: isCompanyUser || isObserver ? portalUser.companyId : undefined,
    },
  }
}

/**
 * Get the portal user from the database
 * @returns The portal user or null if the user is not logged in or has no portal user
 */
export const getPortalUser = async () => {
  const session = await auth()

  // If the user is not logged in and is not on the unauthorized path, redirect to the unauthorized page
  if (!session?.user.email) return null

  const portalUserDocument = await getPortalUserByEmail(session.user.email)

  return portalUserDocument ?? null
}

// Logic table on how to handle different users when to redirect
// |Users                             |/   |/consent     |/admin       |/unauthorized|
// |----------------------------------|----|-------------|-------------|-------------|
// |not logged it                     |show|/unauthorized|/unauthorized|show         |
// |Logged in no portal user          |show|/unauthorized|/unauthorized|show         |
// |Portal user but not consented     |show|/unauthorized|/unauthorized|show         |
// |Portal with consent               |show|/admin       |show         |/admin       |
// |Portal with consent (Admin)       |show|/admin       |show         |/admin      |
// |Portal with consent (Client Admin)|show|/admin       |show         |/admin      |
// |Portal with consent (Company User)|show|/admin       |surveys      |/surveys    |
//
// * Client Admin limited pages: dashboard, companies, surveys
// ** Company User limited pages: their company page, surveys

/**
 * Validate the portal user and redirect if necessary
 * @param pathname - The pathname to validate
 */
const validatePortalUser = async (pathname: string) => {
  // Always allow base path
  if (PathValidator.isBasePath(pathname)) return

  const portalUser = await getPortalUser()

  // Handle unauthorized path
  if (PathValidator.isUnauthorizedPath(pathname)) {
    if (!portalUser) return // If the user is not logged in or has no portal user show the page
    if (portalUser.hasGivenConsent)
      redirect("/admin") // if user has given consent redirect to admin
    else redirect("/consent") // if user has not given consent redirect to consent
  }

  // Handle consent path
  if (PathValidator.isConsentPath(pathname)) {
    if (!portalUser) redirect("/unauthorized") // If the user is not logged in or has no portal user redirect to unauthorized
    if (portalUser.hasGivenConsent) redirect("/admin") // if user has given consent redirect to admin
    return // if user has not given consent show the page
  }

  // Handle admin path
  if (PathValidator.isAdminPath(pathname)) {
    if (!portalUser) redirect("/unauthorized")
    if (!portalUser.hasGivenConsent) redirect("/consent") // if user has not given consent redirect to consent

    // Check page-specific permissions
    const access = getPortalAccess(portalUser)
    if (!access) redirect("/")

    const path = pathname.split("/").at(2) // Get the section after /admin/
    switch (path) {
      case "portfolios":
        if (!access.pages.portfolios) redirect(access.redirectPath)
        break
      case "companies":
        if (!access.pages.companies) redirect(access.redirectPath)
        if (!access.company.canAccessAll && !access.company.companyId) redirect(access.redirectPath)
        // Redirect company users to their specific company
        if (!access.company.canAccessAll && access.company.companyId) {
          const companyIdInPath = pathname.split("/")[3]
          if (companyIdInPath && companyIdInPath !== access.company.companyId) {
            redirect(`/admin/companies/${access.company.companyId}`)
          }
          if (!companyIdInPath) redirect(`/admin/companies/${access.company.companyId}`)
        }
        break
      case "surveys":
        if (!access.pages.surveys) redirect(access.redirectPath)
        break
      case "evaluation-requests":
        if (!access.pages.evaluationRequests) redirect(access.redirectPath)
        break
      case "company-surveys":
        if (!access.pages.companySurveys) redirect(access.redirectPath)
        break
      case "self-assesment":
        if (!access.pages.selfAssesment) redirect(access.redirectPath)
        break
      case "assessment-batches":
        if (!access.pages.assessmentBatches) redirect(access.redirectPath)
        break
      case "self-assessments":
        if (!access.pages.selfAssessments) redirect(access.redirectPath)
        break
    }

    if (!access.pages.dashboard && PathValidator.isDashboardPath(pathname)) redirect(access.redirectPath)

    return // if user has given consent show the page
  }

  // for all other paths, show the page
}

export const withConsent = <P extends object>(Component: ComponentType<P>): ComponentType<Omit<P, "portalUser">> => {
  const ServerComponent = async (props: Omit<P, "portalUser">) => {
    const headersList = headers()
    const pathname = headersList.get("x-pathname")

    // This should never happend since the middleware should always set the pathname
    if (!pathname) throw new Error("Pathname is required")

    await validatePortalUser(pathname)

    const portalUser = await getPortalUser()

    return (
      <>
        <PostHogPageViewTracker email={portalUser?.email} />
        {/* @ts-expect-error - We are passing the portalUser from this hoc to the component no need to expose the type to page otherwise it would raise a type error in the nextjs typescript plugin, which only allows specific props to be passed to the page*/}
        <Component {...props} portalUser={portalUser!} />
      </>
    )
  }

  return ServerComponent
}
