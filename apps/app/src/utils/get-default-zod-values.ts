/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { z } from "zod"

// Utility type to make all fields of a type undefined
type DeepUndefined<T> = {
  [K in keyof T]: T[K] extends object ? DeepUndefined<T[K]> : undefined
}

// Function to generate default values with all fields set to undefined
export function getDefaultZodValues<T extends z.ZodRawShape>(
  schema: z.ZodObject<T>
): DeepUndefined<z.infer<z.ZodObject<T>>> {
  // Recursively generate default values
  function createDefaultValues<S extends z.ZodType>(schema: S): unknown {
    // Handle optional and nullable schemas by unwrapping them
    if (schema instanceof z.ZodOptional || schema instanceof z.ZodNullable) {
      return createDefaultValues(schema.unwrap())
    }

    // Handle object schemas
    if (schema instanceof z.ZodObject) {
      const defaults = {} as Record<string, unknown>
      const shape = schema.shape as Record<string, z.ZodType>

      for (const key in shape) {
        defaults[key] = createDefaultValues(shape[key])
      }
      return defaults as DeepUndefined<z.infer<S>>
    }

    // Handle arrays
    if (schema instanceof z.ZodArray) {
      return undefined // Or return [] if empty arrays are preferred
    }

    // Handle all other types (primitives, etc.)
    return undefined
  }

  const result = createDefaultValues(schema)
  return result as DeepUndefined<z.infer<z.ZodObject<T>>>
}
