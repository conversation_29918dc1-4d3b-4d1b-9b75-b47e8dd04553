/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

/* eslint-disable no-restricted-properties */
import { env as apiEnv } from "@impactly/api/env"
import { env as dbEnv } from "@impactly/domain/env"
import { env as jobsEnv } from "@impactly/jobs/env"
import { createEnv } from "@t3-oss/env-nextjs"
import { vercel } from "@t3-oss/env-nextjs/presets"
import { z } from "zod"

import { env as authEnv } from "@kreios/auth/env"
import { env as copilotEnv } from "@kreios/copilot/env"

export const env = createEnv({
  extends: [authEnv, dbEnv, copilotEnv, apiEnv, jobsEnv, vercel()],

  shared: {
    NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
    NEXT_APP_URL: z.string().default("http://localhost:3000"),
  },
  /**
   * Server-side Environment variables, not available on the client.
   *
   * 💡 Will throw if you access these variables on the client.
   */
  server: {
    GRAPHQL_API_KEYS: z.string().transform((val) => val.split(",")),
    REQUEST_ACCESS_CONTACT_LIST: z
      .string()
      .optional()
      .transform((val) =>
        val
          ? val
              .split(",")
              .map((email) => email.trim())
              .filter(Boolean)
          : []
      ),
  },

  /**
   * Environment variables available on the client (and server).
   *
   * 💡 You'll get type errors if these are not prefixed with NEXT_PUBLIC_.
   */
  client: {
    NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),
  },

  /**
   * Due to how Next.js bundles environment variables on Edge and Client,
   * we need to manually destructure them to make sure all are included in bundle.
   *
   * 💡 You'll get type errors if not all variables from `server` & `client` are included here.
   */
  runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    GRAPHQL_API_KEYS: process.env.GRAPHQL_API_KEYS,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_APP_URL: process.env.NEXT_APP_URL,
    REQUEST_ACCESS_CONTACT_LIST: process.env.REQUEST_ACCESS_CONTACT_LIST,
  },

  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation:
    !process.env.VERCEL &&
    (!!process.env.CI ||
      !!process.env.SKIP_ENV_VALIDATION ||
      process.env.npm_lifecycle_event === "lint" ||
      process.env.NODE_ENV === "test"),

  /**
   * Makes it so that empty strings are treated as undefined.
   * `SOME_VAR: z.string()` and `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
})
