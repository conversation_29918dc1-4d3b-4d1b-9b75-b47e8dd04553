/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { getRequestConfig } from "next-intl/server"

import { getLanguage } from "./cookie"

type MessageValue = string | { [key: string]: MessageValue }
type Messages = Record<string, MessageValue>

export default getRequestConfig(async () => {
  const locale = getLanguage()
  const messages = ((await import(`../messages/${locale}.json`)) as { default: Messages }).default

  return {
    locale,
    messages,
  }
})
