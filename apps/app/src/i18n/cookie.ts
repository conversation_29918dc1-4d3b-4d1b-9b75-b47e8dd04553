/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { Language } from "@/utils/constants"
import { cookies } from "next/headers"
import { DEFAULT_LANGUAGE, LANGUAGE_COOKIE_KEY } from "@/utils/constants"

export const getLanguage = (): Language => {
  const cookieValue = cookies().get(LANGUAGE_COOKIE_KEY)?.value
  return (cookieValue ? cookieValue : DEFAULT_LANGUAGE) as Language
}

export const setLanguage = (language: Language) => {
  cookies().set(LANGUAGE_COOKIE_KEY, language)
}
