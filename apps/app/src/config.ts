/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { asFunction } from "awilix"

import { ElasticsearchGateway } from "@kreios/elasticsearch"
import { connect } from "@kreios/eventsourcing/drizzle"

import { env } from "./env"

const { db, client } = await connect({
  type: env.VERCEL ? "vercel" : env.DATABASE_URL.includes("localhost") ? "neon" : "postgres",
  url: env.DATABASE_URL,
})

container.register({
  db: asFunction(() => db)
    .singleton()
    .disposer(() => client.close()),
})

const { config } = await import("@impactly/domain/elastic/index")

container.register({
  gateway: asFunction(() => new ElasticsearchGateway(config))
    .singleton()
    .disposer((gateway) => gateway.close()),
})
