/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

/* eslint-disable no-restricted-properties */
/* eslint-disable turbo/no-undeclared-env-vars */
export async function register() {
  const TURBOPACK_ENABLED = Boolean(process.env.TURBOPACK)
  if (process.env.NEXT_RUNTIME === "nodejs") {
    await Promise.all([
      !TURBOPACK_ENABLED ? import("../sentry.server.config") : Promise.resolve(),
      import("./container").then(() => import("./config")),
    ])
  }

  if (process.env.NEXT_RUNTIME === "edge") {
    if (!TURBOPACK_ENABLED) await import("../sentry.edge.config")
  }
}
