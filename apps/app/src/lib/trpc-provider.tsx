/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { AppRouter } from "@impactly/api/trpc"
import type { TRPCLink } from "@trpc/client"
import type { AnyRouter, inferRouterInputs, inferRouterOutputs } from "@trpc/server"
import type { ReadonlyRequestCookies } from "next/dist/server/web/spec-extension/adapters/request-cookies"
import { useState } from "react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { ReactQueryStreamedHydration } from "@tanstack/react-query-next-experimental"
import { loggerLink, unstable_httpBatchStreamLink } from "@trpc/client"
import { createTRPCReact } from "@trpc/react-query"
import { observable } from "@trpc/server/observable"
import superjson from "superjson"

import { createReplayAsyncGenerator } from "@kreios/utils/create-replay-async-generator"
import { isAsyncIterable } from "@kreios/utils/is-async-iterable"

/** @public */
export type RouterInput = inferRouterInputs<AppRouter>
export type RouterOutput = inferRouterOutputs<AppRouter>

const createQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 1 * 1000,
      },
    },
  })

let clientQueryClientSingleton: QueryClient | undefined = undefined
const getQueryClient = () => {
  if (typeof window === "undefined") {
    // Server: always make a new query client
    return createQueryClient()
  } else {
    // Browser: use singleton pattern to keep the same query client
    return (clientQueryClientSingleton ??= createQueryClient())
  }
}
/**
 * TRPC link that convert async iterators received from a mutation to replayable async iterators
 * Otherwise the could only be consumed once which doesn't play well with tanstack/query
 */
const asyncIteratorMiddlewareLink =
  <TRouter extends AnyRouter>(): TRPCLink<TRouter> =>
  () => {
    return ({ next, op }) => {
      if (op.type !== "mutation") return next(op)

      return observable((observer) => {
        const unsubscribe = next(op).subscribe({
          next(value) {
            const data = value.result.data
            if (isAsyncIterable(data)) {
              // @ts-expect-error we know it's an async iterator
              value.result.data = createReplayAsyncGenerator(data[Symbol.asyncIterator]()) as unknown
            }
            observer.next(value)
          },
          error(err) {
            observer.error(err)
          },
          complete() {
            observer.complete()
          },
        })

        return unsubscribe
      })
    }
  }

export const api = createTRPCReact<AppRouter>()

export function TRPCReactProvider(props: { children: React.ReactNode; cookies: ReadonlyRequestCookies }) {
  const queryClient = getQueryClient()

  const cookieString = props.cookies.toString()

  const [trpcClient] = useState(() =>
    api.createClient({
      links: [
        loggerLink({
          enabled: (op) =>
            // eslint-disable-next-line no-restricted-properties
            process.env.NODE_ENV === "development" || (op.direction === "down" && op.result instanceof Error),
        }),
        asyncIteratorMiddlewareLink(),
        unstable_httpBatchStreamLink({
          transformer: superjson,
          url: getBaseUrl() + "/api/trpc",
          headers() {
            const headers = new Headers()
            headers.set("x-trpc-source", "nextjs-react")
            if (cookieString) {
              headers.set("cookie", cookieString)
            }
            return headers
          },
        }),
      ],
    })
  )

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryStreamedHydration transformer={superjson}>
        <api.Provider client={trpcClient} queryClient={queryClient}>
          {/* eslint-disable-next-line no-restricted-properties */}
          {process.env.NODE_ENV !== "production" && (
            <ReactQueryDevtools initialIsOpen={false} position="right" buttonPosition="bottom-right" />
          )}
          {props.children}
        </api.Provider>
      </ReactQueryStreamedHydration>
    </QueryClientProvider>
  )
}

export const getBaseUrl = () => {
  if (typeof window !== "undefined") return window.location.origin
  // eslint-disable-next-line no-restricted-properties
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`
  // eslint-disable-next-line no-restricted-properties
  return `http://localhost:${process.env.PORT ?? 3000}`
}
