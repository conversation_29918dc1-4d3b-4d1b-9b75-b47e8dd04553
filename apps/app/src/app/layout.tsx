/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { fontSans } from "@/lib/fonts"

import { cn } from "@kreios/ui"

import "./globals.css"

import { PostHogProvider } from "@/components/posthog-provider"
import { VercelToolbar } from "@vercel/toolbar/next"
import { NextIntlClientProvider } from "next-intl"
import { getLocale } from "next-intl/server"
import { NuqsAdapter } from "nuqs/adapters/next/app"

import { TailwindIndicator } from "@kreios/ui/tailwind-indicator"

export { metadata } from "@/config/metadata"

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const locale = await getLocale()

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={cn("min-h-screen bg-muted/40 font-sans text-foreground antialiased", fontSans.variable)}>
        {/* eslint-disable-next-line no-restricted-properties */}
        {process.env.NODE_ENV === "development" && <VercelToolbar />}
        <NuqsAdapter>
          <PostHogProvider>
            <NextIntlClientProvider>{children}</NextIntlClientProvider>
          </PostHogProvider>
        </NuqsAdapter>
        <TailwindIndicator />
      </body>
    </html>
  )
}
