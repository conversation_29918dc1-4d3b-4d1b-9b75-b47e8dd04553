/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import {
  PortfolioAlertsCard,
  PortfolioCompaniesCard,
  PortfolioESGMaturityCard,
  PortfolioGHGReportingCard,
  RecentlyViewedCard,
} from "@/app/admin/dashboard"
import { withConsent } from "@/utils/with-consent"

import { PageHeader } from "@kreios/admin-layout/components/page-header"

export default withConsent(async () => {
  return (
    <>
      <PageHeader title="Dashboard" />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-2 md:gap-8 md:p-8 md:pt-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <PortfolioESGMaturityCard />
          <PortfolioGHGReportingCard />
          <PortfolioCompaniesCard className="md:col-span-2" />
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <RecentlyViewedCard />
          <PortfolioAlertsCard />
        </div>
      </div>
    </>
  )
})
