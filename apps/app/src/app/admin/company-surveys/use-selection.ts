/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { RouterOutput } from "@/lib/trpc-provider"
import { useCallback, useEffect, useState } from "react"

// Define the type for a survey
type Survey = RouterOutput["surveys"]["listSurveys"]["data"][number]

export function useSelection() {
  // Store all selected surveys across all pages
  const [selectedSurveys, setSelectedSurveys] = useState<Survey[]>([])

  // Store selection state by page
  const [selectionByPage, setSelectionByPage] = useState<Record<number, Record<string, boolean>>>({})

  // Track current page
  const [currentPage, setCurrentPage] = useState<number>(0)

  // Handle row selection changes
  const handleRowSelectionChange = useCallback(
    ({
      selectedRows,
      selectedData: pageSelectedData,
    }: {
      selectedRows: Record<string, boolean>
      selectedData: Survey[]
    }) => {
      // Update the selection state for the current page
      setSelectionByPage((prev) => {
        const newState = { ...prev }
        newState[currentPage] = selectedRows
        return newState
      })

      // Update the overall selected surveys
      setSelectedSurveys((prev) => {
        // Get IDs of all selected surveys on the current page
        const currentPageIds = new Set(pageSelectedData.map((survey) => survey.aggregateId))

        // Remove any previously selected surveys from the current page
        const filteredSurveys = prev.filter((survey) => !currentPageIds.has(survey.aggregateId))

        // Add newly selected surveys from the current page
        return [...filteredSurveys, ...pageSelectedData]
      })
    },
    [currentPage]
  )

  // Get selection state for a specific page
  const getPageSelection = useCallback(
    (page: number) => {
      return selectionByPage[page] ?? {}
    },
    [selectionByPage]
  )

  // Clear all selections
  const clearAllSelections = useCallback(() => {
    setSelectedSurveys([])
    setSelectionByPage({})
  }, [])

  // Handle page changes
  const handlePageChange = useCallback((pageNumber: number) => {
    setCurrentPage(pageNumber)
  }, [])

  // Track URL search params for page changes
  useEffect(() => {
    // Function to check URL for page parameter
    const checkUrlForPage = () => {
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href)
        const pageParam = url.searchParams.get("page")

        if (pageParam) {
          const pageNumber = parseInt(pageParam, 10)
          // Convert 1-based page number to 0-based
          const pageIndex = pageNumber - 1

          if (!isNaN(pageIndex) && pageIndex !== currentPage) {
            setCurrentPage(pageIndex)
          }
        }
      }
    }

    // Check on initial load
    checkUrlForPage()

    // Set up event listener for URL changes
    window.addEventListener("popstate", checkUrlForPage)

    // Clean up
    return () => {
      window.removeEventListener("popstate", checkUrlForPage)
    }
  }, [currentPage])

  return {
    selectedSurveys,
    selectionByPage,
    currentPage,
    handleRowSelectionChange,
    getPageSelection,
    clearAllSelections,
    handlePageChange,
  }
}
