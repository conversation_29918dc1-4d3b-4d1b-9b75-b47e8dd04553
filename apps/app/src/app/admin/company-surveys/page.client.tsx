"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import { SurveysList } from "@/components/surveys-list"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"

/**
 * Company Surveys page that displays a list of company surveys
 * Uses the shared SurveysList component with company-specific configuration
 */
export default ({ portalUser }: { params: { id: string }; portalUser: PortalUserDocument }) => {
  const { isCompanyUser } = getPortalUserRoles(portalUser)

  return <SurveysList templateType={["UNIFIED_QUESTIONNAIRE_V1"]} namespace="surveys" isCompanyUser={isCompanyUser} />
}
