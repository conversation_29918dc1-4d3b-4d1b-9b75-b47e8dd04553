/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { cookies } from "next/headers"
import { EmptyStateProvider } from "@/app/admin/@modal/[...catchAll]/empty-node"

import { auth } from "@kreios/auth"
import { AI } from "@kreios/copilot"
import { Chat } from "@kreios/copilot/chat"
import { enableCopilot } from "@kreios/copilot/flags"
import { getUserChat } from "@kreios/copilot/persistance"
import { nanoid } from "@kreios/utils/nanoid"

export default async function Page() {
  if (!(await enableCopilot())) return null
  const session = await auth()

  if (!session?.user) {
    return null
  }

  const chatId = cookies().get("copilotchatid")?.value

  if (!chatId) {
    const id = nanoid()

    return (
      <EmptyStateProvider value={id} key={id}>
        <AI initialAIState={{ chatId: id, messages: [] }}>
          <Chat id={id} session={session} />
        </AI>
      </EmptyStateProvider>
    )
  }

  const chat = await getUserChat(chatId)

  if (!chat || "error" in chat) return null

  return (
    <EmptyStateProvider value={chat.id} key={chat.id}>
      <AI initialAIState={{ chatId: chat.id, messages: chat.messages }}>
        <Chat id={chat.id} session={session} initialMessages={chat.messages} />
      </AI>
    </EmptyStateProvider>
  )
}
