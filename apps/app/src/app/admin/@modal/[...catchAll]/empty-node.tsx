/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC, ReactNode } from "react"
import { createContext } from "react"

/**
 * A context providers which main purpose is to be a empty node that react will use as boundary to reinitialize the component tree.
 */
const EmptyStateContext = createContext<string | null>(null)
export const EmptyStateProvider: FC<{ value: string | null; children: ReactNode }> = ({ value, children }) => (
  <EmptyStateContext.Provider value={value}>{children}</EmptyStateContext.Provider>
)
