/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { SurveyTemplate, surveyZodEnumLabelMapper } from "@/app/admin/surveys/[id]/utils"
import { SendSurveyDialog as SharedSendSurveyDialog } from "@/components/send-survey-dialog"

/**
 * SendSurveyDialog component for creating and sending surveys
 * This is a wrapper around the shared component with survey-specific configuration
 */
export default function SendSurveyDialog({
  open,
  onOpenChange,
  batchId,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  batchId: string
}) {
  return (
    <SharedSendSurveyDialog
      open={open}
      onOpenChange={onOpenChange}
      batchId={batchId}
      templateEnum={SurveyTemplate}
      templateLabelMapper={surveyZodEnumLabelMapper}
      defaultTemplateValue="UNIFIED_QUESTIONNAIRE_V1"
      translationNamespace="surveys.sendSurveyDialog"
      headerBgClass="bg-gray-50 dark:bg-gray-800"
    />
  )
}
