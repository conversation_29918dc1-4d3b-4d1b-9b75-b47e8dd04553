"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { SurveyStatusEnum } from "@/utils/constants"
import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import type { ColumnDef } from "@tanstack/react-table"
import React, { useMemo, useState } from "react"
import Link from "next/link"
import SendSurveyDialog from "@/app/admin/surveys/[id]/components/send-survey-dialog"
import { TranslatedFacetsDataTable } from "@/components/translated-facets-data-table"
// import { useSurveyExport } from "@/hooks/use-survey-export"
import { api } from "@/lib/trpc-provider"
import { SurveyTemplateEnum } from "@/utils/constants"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { Eye } from "lucide-react"
// import { Loader2 } from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

// import type { ButtonProps } from "@kreios/ui/button"
import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { AggregateSearchCombobox } from "@kreios/admin-layout/components/aggregate-search-combobox"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { selectColumn } from "@kreios/datatable/data-table-common-definitions"
import { createEnhancedColumnHelper } from "@kreios/datatable/enhanced-column-helper"
import { Button } from "@kreios/ui/button"
import { useConfirm } from "@kreios/ui/confirm-dialog"
import { Progress } from "@kreios/ui/progress"
import { toast } from "@kreios/ui/sonner"

import { ProgressStats } from "../components"

type Survey = RouterOutput["surveys"]["listSurveys"]["data"][number]

const columnHelper = createEnhancedColumnHelper<Survey>()

// const ExportButton = ({ surveyIds, disabled, ...props }: { surveyIds: string[] } & ButtonProps) => {
//   const { isPending } = useSurveyExport()
//   const t = useTranslations("common")

//   const handleExport = () => {
//     console.log("Surveys to export:", surveyIds)
//   }

//   return (
//     <Button variant="outline" onClick={handleExport} {...props} disabled={isPending || disabled}>
//       {isPending ? (
//         <>
//           <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//           {t("exporting")}
//         </>
//       ) : (
//         t("export")
//       )}
//     </Button>
//   )
// }

export default ({ params: { id }, portalUser }: { params: { id: string }; portalUser: PortalUserDocument }) => {
  const confirm = useConfirm()
  const router = useRouter()
  const [batch] = api.surveys.get.useSuspenseQuery(id)
  const [selectedData, setSelectedData] = useState<Survey[]>([])
  const [isSurveyDialogOpen, setSendSurveyDialog] = useState(false)
  const { isClientAdmin } = getPortalUserRoles(portalUser)
  const t = useTranslations()

  const columns = useMemo(
    () =>
      [
        selectColumn(),
        columnHelper.custom("company", {
          title: t("surveys.columns.company"),
          cell: (context) => {
            const company = context.row.original.company
            return (
              <Link
                href={`/admin/companies/${context.row.original.company?.aggregateId}`}
                className="max-w-[200px] truncate text-primary hover:underline"
              >
                {company?.name ?? t("common.noCompanyName")}
              </Link>
            )
          },
        }),
        columnHelper.custom("status", {
          title: t("surveys.columns.status"),
          cell: (context) => {
            const status = context.row.original.status
            return <div>{t(`surveyStatus.${status.toLowerCase()}`)}</div>
          },
        }),
        columnHelper.custom("progress", {
          title: t("surveys.columns.progress"),
          cell: (context) => (
            <div className="flex w-32 items-center gap-2">
              <Progress
                value={context.row.original.progress}
                className="h-2 [&>div]:translate-x-0 [&>div]:transition-none"
              />
              <span className="min-w-[40px] text-sm text-gray-600">{context.row.original.progress}%</span>
            </div>
          ),
        }),
        columnHelper.date("submittedAt", {
          title: t("surveys.columns.submissionDate"),
          format: "Pp",
          cell: (context) => {
            const submittedAt = context.row.original.submittedAt
            const formattedDate = submittedAt ? new Date(submittedAt).toLocaleDateString() : null
            return formattedDate ? <div>{formattedDate}</div> : <div className="text-muted-foreground">----</div>
          },
        }),
        columnHelper.custom("aggregateId", {
          title: t("surveys.columns.actions"),
          cell: (context) => {
            const survey = context.row.original

            return (
              <Button
                onClick={() => router.push(`/admin/survey/${survey.aggregateId}`)}
                variant={"ghost"}
                className="flex h-auto items-center gap-2 p-0 hover:bg-transparent hover:text-primary"
                disabled={context.row.original.status === "CANCELLED"}
              >
                <Eye className="h-4 w-4" /> {t("common.table.viewColumns")}
              </Button>
            )
          },
        }),
      ] as ColumnDef<Survey, unknown>[],
    [t, router]
  )

  const utils = api.useUtils()
  const updateSurveyStatus = api.surveys.cancelSurvey.useMutation({
    onSuccess: () => utils.surveys.invalidate(),
  })

  return (
    <>
      <SendSurveyDialog open={isSurveyDialogOpen} onOpenChange={(value) => setSendSurveyDialog(value)} batchId={id} />
      <PageHeader
        title={
          <div className="flex items-center gap-2">
            <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight">{batch.name}</h2>
            {/* // TODO: Add edit functionality */}
            {/* <Button variant="ghost" size="icon">
              <Pencil className="h-4 w-4" />
            </Button> */}
          </div>
        }
        includeInBreadcrumb
      >
        <div className="flex flex-wrap gap-2">
          {isClientAdmin && (
            <Button
              variant="outline"
              disabled={selectedData.length === 0 || selectedData.every((survey) => survey.status === "CANCELLED")}
              onClick={async () => {
                try {
                  await confirm({
                    title: t("surveys.cancelSurveyDialog.title"),
                    description: t("surveys.cancelSurveyDialog.description"),
                    confirmLabel: t("surveys.cancelSurveyDialog.confirmLabel"),
                    cancelLabel: t("surveys.cancelSurveyDialog.cancelLabel"),
                  })

                  const surveyIds = selectedData.map((survey) => survey.aggregateId)
                  toast.promise(updateSurveyStatus.mutateAsync({ surveyIds }), {
                    loading: t("surveys.cancelSurveyDialog.loadingText"),
                    success: t("surveys.cancelSurveyDialog.successText"),
                    error: t("surveys.cancelSurveyDialog.errorText"),
                  })
                } catch {
                  toast.info(t("surveys.cancelSurveyDialog.cancellationAborted"), { duration: 2000 })
                }
              }}
            >
              {t("common.cancel")}
            </Button>
          )}
          <Button onClick={() => setSendSurveyDialog(true)}>{t("surveys.sendSurvey")}</Button>
        </div>

        <PathBreadcrumb id={-1}>
          <AggregateSearchCombobox
            label={t("surveys.breadcrumbSearchLabel")}
            procedure={api.surveys.list}
            defaultValue={{ value: id, label: batch.name }}
            onSelect={(value) => value && router.push(`/admin/surveys/${value.value}`)}
            template={[SurveyTemplateEnum.UnifiedQuestionnaireV1]}
          />
        </PathBreadcrumb>
      </PageHeader>
      <ProgressStats stats={batch.stats} />

      <div className="flex w-full flex-1 p-4 md:p-6">
        <TranslatedFacetsDataTable
          columns={columns}
          procedure={api.surveys.listSurveys}
          baseFilters={[
            {
              id: "batchId",
              value: id,
            },
            {
              id: "template",
              value: ["UNIFIED_QUESTIONNAIRE_V1"],
            },
          ]}
          // toolbar={<DataTableDateRangeFilter column="statusChangedAt" />}
          searchPlaceholder={t("surveys.searchPlaceholder")}
          rowIdKey="aggregateId"
          onRowSelectionChange={({ selectedData }) => {
            setSelectedData(selectedData)
          }}
          renderRow={({ original: { overdue, status } }, children) => {
            if (!overdue || status === ("COMPLETE" as SurveyStatusEnum)) return children
            const rowElement = React.cloneElement(children as React.ReactElement, {
              className: "bg-red-100 dark:bg-red-800",
              "data-tooltip-content": t("surveys.overdueSurvey"),
              "data-overdue": "true",
            })

            return rowElement
          }}
        />
      </div>
    </>
  )
}
