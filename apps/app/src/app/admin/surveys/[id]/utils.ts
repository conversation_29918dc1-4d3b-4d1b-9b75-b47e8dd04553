/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

export const SurveyTemplate = z.enum(["UNIFIED_QUESTIONNAIRE_V1"])

export const surveyZodEnumLabelMapper = {
  UNIFIED_QUESTIONNAIRE_V1: "Unified Questionnaire V1",
} as const satisfies Record<z.infer<typeof SurveyTemplate>, string>
