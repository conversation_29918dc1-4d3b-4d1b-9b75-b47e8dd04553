/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { Batch } from "@/components/batches-components"
import { memo } from "react"
import {
  EmptyBatchCard,
  ProgressStats,
  BatchCard as SharedBatchCard,
  SkeletonGrid,
  SurveyPagination,
} from "@/components/batches-components"

/**
 * Re-export shared components with survey-specific configuration
 */
export { EmptyBatchCard, ProgressStats, SkeletonGrid, SurveyPagination }

/**
 * BatchCard component with survey-specific base path
 */
export const BatchCard = memo(({ batch }: { batch: Batch }) => (
  <SharedBatchCard batch={batch} basePath="/admin/surveys" />
))
