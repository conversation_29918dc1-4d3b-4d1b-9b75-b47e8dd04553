"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { ColumnDef } from "@tanstack/react-table"
import { useMemo } from "react"
import { api } from "@/lib/trpc-provider"
import { Language } from "@/utils/constants"
import { useLocale, useTranslations } from "next-intl"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { DataTable } from "@kreios/datatable"
import { selectColumn } from "@kreios/datatable/data-table-common-definitions"
import { createEnhancedColumnHelper } from "@kreios/datatable/enhanced-column-helper"

type Company = RouterOutput["companies"]["list"]["data"][number]

const columnHelper = createEnhancedColumnHelper<Company>()

// const maturityLevelToColor = {
//   high: "green dark:emerald",
//   low: "red dark:rose",
//   medium: "orange dark:amber",
//   very_high: "green dark:emerald",
//   very_low: "red dark:rose",
// }

export default function Page() {
  const t = useTranslations("companies")
  const currentLocale = useLocale() as Language

  const columns = useMemo(
    () =>
      [
        selectColumn(),
        columnHelper.link("name", {
          title: t("columns.company"),
          href: (row) => `/admin/companies/${row.aggregateId}`,
        }),
        columnHelper.custom("country", {
          title: t("columns.country"),
        }),
        columnHelper.custom("businessActivities", {
          title: t("columns.mainActivity"),
          cell: (ctx) => {
            const mainActivity = ctx.getValue().find((activity) => activity.isMainActivity)
            const key = currentLocale === Language.EN ? "label" : "labelEt"
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            return mainActivity?.naceCode?.[key] ?? t("noData")
          },
        }),
        // columnHelper.badge("latestEvaluation.environmentalMaturity", {
        //   title: t("columns.environmental"),
        //   transform: (status) => t(`riskLevel.${status}`),
        //   colorMap: maturityLevelToColor,
        // }),
        // columnHelper.badge("latestEvaluation.socialMaturity", {
        //   title: t("columns.social"),
        //   transform: (status) => t(`riskLevel.${status}`),
        //   colorMap: maturityLevelToColor,
        // }),
        // columnHelper.badge("latestEvaluation.governanceMaturity", {
        //   title: t("columns.governance"),
        //   transform: (status) => t(`riskLevel.${status}`),
        //   colorMap: maturityLevelToColor,
        // }),
        columnHelper.custom("registrationNumber", {
          title: t("columns.registrationNumber"),
        }),
        // columnHelper.date("latestEvaluation.completedAt", {
        //   title: t("columns.lastEvaluation"),
        //   format: "Pp",
        // }),
      ] as ColumnDef<Company, unknown>[],
    [t, currentLocale]
  )

  return (
    <>
      <PageHeader title={t("pageTitle")} />
      <div className="flex w-full flex-1 p-4 md:p-6">
        <DataTable
          procedure={api.companies.list}
          columns={columns}
          searchPlaceholder={t("searchPlaceholder")}
          rowIdKey="aggregateId"
          // toolbar={<DataTableDateRangeFilter column="latestEvaluation_completedAt" />}
        />
      </div>
    </>
  )
}
