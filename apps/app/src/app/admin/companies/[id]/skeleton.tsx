/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useTranslations } from "next-intl"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Alert } from "@kreios/ui/alert"
import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger } from "@kreios/ui/responsive-tabs"
import { Skeleton } from "@kreios/ui/skeleton"
import { TabsContent } from "@kreios/ui/tabs"

const FormFieldSkeleton = () => (
  <div className="space-y-2">
    <Skeleton className="h-4 w-1/3" />
    <Skeleton className="h-10 w-1/2" />
  </div>
)

export const PageSkeleton = () => {
  const t = useTranslations()
  return (
    <>
      <Alert variant="default" className="-mt-4 mb-2 rounded-none">
        <Skeleton className="absolute left-4 top-4 size-4" />
        <div className="pl-7">
          <Skeleton className="mb-1 h-4 w-24" />
          <Skeleton className="h-4 w-full" />
        </div>
      </Alert>

      <ResponsiveTabs defaultValue="company-overview">
        <PageHeader
          title={
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-6 w-24" />
            </div>
          }
        >
          <ResponsiveTabsList>
            <ResponsiveTabsTrigger disabled value="company-overview">
              {t("companyProfile.tabs.companyOverview")}
            </ResponsiveTabsTrigger>
            <ResponsiveTabsTrigger disabled value="esg-assessment">
              {t("companyProfile.tabs.esgAssessment")}
            </ResponsiveTabsTrigger>
            <ResponsiveTabsTrigger disabled value="assessment-history">
              {t("companyProfile.tabs.assessmentHistory")}
            </ResponsiveTabsTrigger>
            <ResponsiveTabsTrigger disabled value="history">
              {t("companyProfile.tabs.history")}
            </ResponsiveTabsTrigger>
          </ResponsiveTabsList>
        </PageHeader>

        <div className="grid flex-1 items-start gap-4 p-4 pt-2 md:p-8 md:pt-4 lg:gap-8">
          <TabsContent className="m-0" value="company-overview">
            <div className="flex flex-col gap-4">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader className="relative">
                    <CardTitle>{t("companyProfile.companyOverview.summary.title")}</CardTitle>
                    <Skeleton className="absolute right-0 top-0 h-[140px] w-[150px] rounded-full" />
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-start justify-between">
                      <div className="flex-grow space-y-4">
                        <FormFieldSkeleton />
                        <FormFieldSkeleton />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>{t("companyProfile.companyOverview.esgRisks.title")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {[1, 2, 3, 4, 5].map((i) => (
                      <div key={i} className="mb-4 space-y-2">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-2 w-full" />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
              <Card>
                <CardHeader>
                  <CardTitle>{t("companyProfile.companyOverview.esgRisks.aboutTheCompany")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                    {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                      <FormFieldSkeleton key={i} />
                    ))}
                  </div>
                  <Skeleton className="mt-4 h-20 w-full" />
                </CardContent>
              </Card>
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>{t("companyProfile.companyOverview.esgRisks.sources")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[1, 2].map((i) => (
                        <div key={i}>
                          <Skeleton className="mb-2 h-4 w-1/3" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="mt-1 h-4 w-full" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>{t("companyProfile.companyOverview.esgRisks.financial")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-[200px] w-full" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </div>
      </ResponsiveTabs>
    </>
  )
}
