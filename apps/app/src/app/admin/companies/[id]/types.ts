/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

export interface ISubCategory {
  transparency_score_status: string
  management_score_status: string
  industry_best_practices: string
  summary_en: string
  summary_et: string
  label_en: string
  label_et: string
  recommendations: string
  recommendations_et: string
  industry_best_practices_et: string
}

export interface ICategory {
  transparency_score_status: string
  management_score_status: string
  sub_categories: Record<string, ISubCategory>
}

export type CompanyScoring = Record<string, ICategory>

export enum ScoringSource {
  USER = "user",
  AI = "ai",
  THIRD_PARTY = "third_party",
}
