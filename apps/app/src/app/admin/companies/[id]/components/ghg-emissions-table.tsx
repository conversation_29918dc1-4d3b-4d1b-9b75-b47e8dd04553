/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { RouterOutput } from "@/lib/trpc-provider"
import type { FC } from "react"
import { useTranslations } from "next-intl"

import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

type Company = RouterOutput["companies"]["get"]

export const ReportedGHGEmissions: FC<{ company: Company }> = ({ company }) => {
  const t = useTranslations("companyProfile.esgAssessment.ghgEmissions")
  const scopeT = useTranslations("selfAssessmentForm.climate_impact_energy_use.ghg_emissions")
  const { years, transformedData } = company.ghgEmissionData

  const hasData = transformedData.length > 0

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          {!hasData && <TableCaption>{t("noData")}</TableCaption>}
          <TableHeader>
            <TableRow>
              <TableHead className="font-medium">{t("scope")}</TableHead>
              {years.map((year) => (
                <TableHead key={year} className="font-medium">
                  {year}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {transformedData.map((row, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">{scopeT(`${row.scope}.tableLabel`)}</TableCell>
                {years.map((year) => (
                  <TableCell key={year}>{row[year.toString()]}</TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
