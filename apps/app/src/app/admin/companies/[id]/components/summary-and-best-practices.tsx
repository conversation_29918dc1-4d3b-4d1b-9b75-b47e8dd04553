/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC } from "react"
import { Language } from "@/utils/constants"
import { useLocale, useTranslations } from "next-intl"

import { cn } from "@kreios/ui"
import { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger } from "@kreios/ui/responsive-tabs"
import { TabsContent } from "@kreios/ui/tabs"

import type { ISubCategory } from "../types"

interface SummaryAndBestPracticesProps {
  selectedSubCategory: ISubCategory | null
  className?: string
}

export const SummaryAndBestPractices: FC<SummaryAndBestPracticesProps> = ({ selectedSubCategory, className }) => {
  const t = useTranslations("companyProfile.esgAssessment.esgCategories")
  const locale = useLocale() as Language

  return (
    <ResponsiveTabs defaultValue="summary" className={cn("", className)}>
      <ResponsiveTabsList className="flex h-auto w-full flex-wrap justify-around">
        <ResponsiveTabsTrigger value="summary" className="flex-1">
          {t("summary")}
        </ResponsiveTabsTrigger>
        <ResponsiveTabsTrigger value="best-practices" className="flex-1">
          {t("industryBestPractices")}
        </ResponsiveTabsTrigger>
        <ResponsiveTabsTrigger value="recommendation" className="flex-1">
          {t("recommendations")}
        </ResponsiveTabsTrigger>
      </ResponsiveTabsList>
      <div className="mt-6">
        <TabsContent value="summary">
          <div className="flex flex-col gap-4">
            {selectedSubCategory ? (
              <p className="break-words text-sm">
                {(locale === Language.EN ? selectedSubCategory.summary_en : selectedSubCategory.summary_et) || "N/A"}
              </p>
            ) : (
              <div className="text-center text-sm text-muted-foreground">{t("selectSubCategory")}</div>
            )}
          </div>
        </TabsContent>
        <TabsContent value="best-practices">
          <div className="flex flex-col gap-4">
            {selectedSubCategory ? (
              <p className="break-words text-sm">
                {(locale === Language.EN
                  ? selectedSubCategory.industry_best_practices
                  : selectedSubCategory.industry_best_practices_et) || "N/A"}
              </p>
            ) : (
              <div className="text-center text-sm text-muted-foreground">{t("selectSubCategory")}</div>
            )}
          </div>
        </TabsContent>
        <TabsContent value="recommendation">
          <div className="flex flex-col gap-4">
            {selectedSubCategory ? (
              <p className="break-words text-sm">
                {(locale === Language.EN
                  ? selectedSubCategory.recommendations
                  : selectedSubCategory.recommendations_et) || "N/A"}
              </p>
            ) : (
              <div className="text-center text-sm text-muted-foreground">{t("selectSubCategory")}</div>
            )}
          </div>
        </TabsContent>
      </div>
    </ResponsiveTabs>
  )
}
