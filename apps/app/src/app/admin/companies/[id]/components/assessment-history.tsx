/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { FC } from "react"

import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

type Company = RouterOutput["companies"]["get"]

export const AssessmentHistory: FC<{ company: Company }> = () => {
  return (
    <div className="flex flex-col gap-4">
      <Card>
        <CardHeader>
          <CardTitle>Assessment History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table className="min-w-[600px]">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[180px]">Timestamp</TableHead>
                <TableHead>Public Data</TableHead>
                <TableHead>Survey Data</TableHead>
                <TableHead>Environmental</TableHead>
                <TableHead>Social</TableHead>
                <TableHead>Governance</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">20/06/2024 11:17</TableCell>
                <TableCell className="text-green-600">Yes</TableCell>
                <TableCell>No</TableCell>
                <TableCell className="text-amber-600">38</TableCell>
                <TableCell className="text-red-600">75</TableCell>
                <TableCell className="text-amber-600">38</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">20/06/2024 11:17</TableCell>
                <TableCell className="text-green-600">Yes</TableCell>
                <TableCell className="text-green-600">Yes</TableCell>
                <TableCell className="text-amber-600">38</TableCell>
                <TableCell className="text-red-600">75</TableCell>
                <TableCell className="text-amber-600">38</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">20/06/2024 11:17</TableCell>
                <TableCell className="text-green-600">Yes</TableCell>
                <TableCell className="text-green-600">Yes</TableCell>
                <TableCell className="text-green-600">62</TableCell>
                <TableCell className="text-amber-600">52</TableCell>
                <TableCell className="text-amber-600">38</TableCell>
              </TableRow>
              {/* Additional rows to demonstrate scrolling */}
              {new Array(10).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">21/06/2024 09:30</TableCell>
                  <TableCell className="text-green-600">Yes</TableCell>
                  <TableCell className="text-green-600">Yes</TableCell>
                  <TableCell className="text-green-600">70</TableCell>
                  <TableCell className="text-amber-600">60</TableCell>
                  <TableCell className="text-amber-600">45</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Uploaded Files</CardTitle>
        </CardHeader>
        <CardContent>
          <Table className="min-w-[600px]">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[180px]">Timestamp</TableHead>
                <TableHead>Filename</TableHead>
                <TableHead>Report Type</TableHead>
                <TableHead>Accounting Year</TableHead>
                <TableHead>Validity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">20/06/2024 11:17</TableCell>
                <TableCell>Annual Report 2023</TableCell>
                <TableCell>Annual Report</TableCell>
                <TableCell>2023</TableCell>
                <TableCell className="text-green-600">Valid</TableCell>
              </TableRow>
              {/* Additional rows to demonstrate scrolling */}
              {new Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">21/06/2024 10:00</TableCell>
                  <TableCell>Q2 Financial Statement 2024</TableCell>
                  <TableCell>Quarterly Report</TableCell>
                  <TableCell>2024</TableCell>
                  <TableCell className="text-green-600">Valid</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
