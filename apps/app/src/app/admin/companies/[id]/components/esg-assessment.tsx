/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { RouterOutput } from "@/lib/trpc-provider"
import type { FC } from "react"
import { useState } from "react"
import { CategoryListingTable } from "@/app/admin/companies/[id]/components/category-listing-table"
import { SummaryAndBestPractices } from "@/app/admin/companies/[id]/components/summary-and-best-practices"

import { Card } from "@kreios/ui/card"

import type { CompanyScoring, ISubCategory, ScoringSource } from "../types"

// const maturityLevelMapper = {
//   very_low: 10,
//   low: 20,
//   medium: 30,
//   high: 40,
//   very_high: 50,
// }

type Company = RouterOutput["companies"]["get"]

// Mock data

// ESG Maturity Component
// const ESGMaturityCard: FC<{ company: Company; className?: string }> = ({ company, className }) => {
//   const t = useTranslations("companyProfile.esgAssessment.maturityCard")
//   const latestEvaluation = company.publishedEvaluations.at(-1)

//   const esgData = latestEvaluation
//     ? [
//         {
//           category: t("categories.environmental"),
//           score: maturityLevelMapper[latestEvaluation.environmentalMaturity],
//           fill: "hsl(var(--chart-1))",
//           trend: "up",
//           trendValue: 3.5,
//         },
//         {
//           category: t("categories.social"),
//           score: maturityLevelMapper[latestEvaluation.socialMaturity],
//           fill: "hsl(var(--chart-2))",
//           trend: "down",
//           trendValue: 1.2,
//         },
//         {
//           category: t("categories.governance"),
//           score: maturityLevelMapper[latestEvaluation.governanceMaturity],
//           fill: "hsl(var(--chart-3))",
//           trend: "up",
//           trendValue: 5.8,
//         },
//       ]
//     : null

//   const chartConfig = {
//     score: {
//       label: t("score"),
//     },
//     Environmental: {
//       label: t("categories.environmental"),
//       color: "hsl(var(--chart-1))",
//     },
//     Social: {
//       label: t("categories.social"),
//       color: "hsl(var(--chart-2))",
//     },
//     Governance: {
//       label: t("categories.governance"),
//       color: "hsl(var(--chart-3))",
//     },
//   }

//   return (
//     <Card className={cn("flex flex-col", className)}>
//       <CardHeader>
//         <CardTitle>{t("title")}</CardTitle>
//         <CardDescription>{t("description")}</CardDescription>
//       </CardHeader>

//       <CardContent className="flex-1 items-center justify-center">
//         {esgData ? (
//           <>
//             <ChartContainer config={chartConfig}>
//               <BarChart
//                 accessibilityLayer
//                 data={esgData}
//                 layout="vertical"
//                 margin={{
//                   left: 0,
//                   right: 0,
//                   top: 0,
//                   bottom: 0,
//                 }}
//               >
//                 <YAxis dataKey="category" type="category" axisLine={false} tickLine={false} tick={false} width={0} />
//                 <XAxis dataKey="score" type="number" hide />
//                 <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
//                 <Bar dataKey="score" layout="vertical" radius={4} />
//               </BarChart>
//             </ChartContainer>
//             <div className="mt-4 flex flex-col space-y-2">
//               {esgData.map((item) => (
//                 <div key={item.category} className="flex items-center justify-between">
//                   <div className="flex items-center">
//                     <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: item.fill }}></div>
//                     <span className="text-sm">{item.category}</span>
//                   </div>
//                 </div>
//               ))}
//             </div>
//           </>
//         ) : (
//           <div className="flex h-full items-center justify-center">
//             <caption className="text-center text-sm text-muted-foreground">{t("noData")}</caption>
//           </div>
//         )}
//       </CardContent>
//     </Card>
//   )
// }

const ESGCategories: FC<{ data: CompanyScoring; source: ScoringSource }> = ({ data, source }) => {
  const [selectedSubCategory, setSelectedSubCategory] = useState<(ISubCategory & { parentCategory: string }) | null>(
    null
  )

  return (
    <Card className="flex flex-col gap-4 p-6 lg:flex-row">
      <CategoryListingTable
        data={data}
        selectedSubCategory={selectedSubCategory}
        setSelectedSubCategory={setSelectedSubCategory}
        className="lg:w-3/5"
        source={source}
      />
      <SummaryAndBestPractices selectedSubCategory={selectedSubCategory} className="lg:w-2/5" />
    </Card>
  )
}

export const ESGAssessment: FC<{ company: Company }> = ({ company }) => {
  return (
    <ESGCategories data={company.companyScoring.data} source={company.companyScoring.source_type as ScoringSource} />
  )
}
