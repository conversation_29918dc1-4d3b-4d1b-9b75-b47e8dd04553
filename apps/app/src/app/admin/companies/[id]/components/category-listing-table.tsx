/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC } from "react"
import { statusStyles } from "@/app/admin/companies/[id]/components/status-styles"
import { DetailDisplay } from "@/components/survey-form/detail-display"
import { Language } from "@/utils/constants"
import { useLocale, useTranslations } from "next-intl"

import { cn } from "@kreios/ui"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@kreios/ui/accordion"
import { Table, TableBody, TableCell, TableRow } from "@kreios/ui/table"

import type { CompanyScoring, ISubCategory } from "../types"
import { ScoringSource } from "../types"

type SubCategory = ISubCategory & { parentCategory: string }

interface CategoryListingTableProps {
  data: CompanyScoring
  selectedSubCategory: SubCategory | null
  setSelectedSubCategory: (subCategory: SubCategory | null) => void
  className?: string
  source: ScoringSource
}

export const CategoryListingTable: FC<CategoryListingTableProps> = ({
  data,
  selectedSubCategory,
  setSelectedSubCategory,
  className,
  source,
}) => {
  const t = useTranslations("companyProfile.esgAssessment.esgCategories")
  const tStatus = useTranslations("common.scoringStatus")
  const tabsLabel = useTranslations("selfAssessmentForm")
  const tBasis = useTranslations("companyProfile.companyOverview.esgRisks")

  const locale = useLocale() as Language

  const handleSubCategoryClick = (subCategory: SubCategory) => {
    setSelectedSubCategory(subCategory)
  }

  return (
    <div className={cn("flex flex-col gap-5", className)}>
      <div className="flex flex-row items-center gap-2">
        <div className="w-1/2">
          <DetailDisplay
            label={
              <div className="flex items-center text-base font-semibold">
                <span>{tBasis("basis")}: </span>
                <span className="ml-1 text-primary">
                  {source === ScoringSource.USER ? tBasis("selfAssessment") : tBasis("publicData")}
                </span>
              </div>
            }
            showInPopup
          />
        </div>
        <div className="flex w-1/2">
          <div className="flex flex-1 flex-row gap-4 overflow-scroll">
            <div className="flex w-1/2 justify-center text-center">
              <DetailDisplay
                label={t("managementScore")}
                // helpInfo={{
                //   title: t("managementScore"),
                //   description: t("managementScoreHelpText"),
                // }}
                className="text-center text-xs font-semibold sm:text-sm"
                showInPopup
              />
            </div>
            <div className="flex w-1/2 justify-center text-center">
              <DetailDisplay
                label={t("transparencyScore")}
                // helpInfo={{
                //   title: t("transparencyScore"),
                //   description: t("transparencyScoreHelpText"),
                // }}
                className="text-center text-xs font-semibold sm:text-sm"
                showInPopup
              />
            </div>
          </div>
        </div>
      </div>
      <Accordion type="multiple" className="space-y-2" defaultValue={Object.keys(data)}>
        {Object.entries(data).map(([categoryName, category], index) => {
          const subCategories = Object.keys(category.sub_categories)

          return (
            <AccordionItem key={index} value={categoryName}>
              <div className="flex flex-row items-center gap-2">
                <div className="w-1/2">
                  <AccordionTrigger
                    onClick={() => {
                      if (selectedSubCategory?.parentCategory === categoryName) setSelectedSubCategory(null)
                    }}
                    className="py-2 pt-0 text-left hover:no-underline"
                  >
                    <span className="font-bold">{tabsLabel(`${categoryName}.title`)}</span>
                  </AccordionTrigger>
                </div>
                <div className="w-1/2">
                  <Table>
                    <TableBody>
                      <TableRow className="text-sm hover:bg-transparent">
                        <TableCell className="w-1/2 px-1 text-center">
                          <div
                            className={cn(
                              "mx-auto max-w-[120px] rounded-lg px-2 py-1 font-semibold",
                              statusStyles[category.management_score_status as keyof typeof statusStyles]
                            )}
                          >
                            {tStatus(category.management_score_status)}
                          </div>
                        </TableCell>
                        <TableCell className="w-1/2 px-1 text-center">
                          <div
                            className={cn(
                              "mx-auto max-w-[120px] rounded-lg px-2 py-1 font-semibold",
                              statusStyles[category.transparency_score_status as keyof typeof statusStyles]
                            )}
                          >
                            {tStatus(category.transparency_score_status)}
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
              {subCategories.length > 0 && (
                <AccordionContent>
                  {subCategories.map((subCategoryName) => {
                    const subCategory = category.sub_categories[subCategoryName]

                    return (
                      <div key={subCategoryName} className="flex flex-row items-center gap-2">
                        <div
                          className={cn(
                            "text-semibold w-1/2 cursor-pointer pl-4 text-sm text-muted-foreground hover:text-primary hover:underline",
                            selectedSubCategory?.label_en === subCategory.label_en && "text-primary"
                          )}
                          onClick={() => handleSubCategoryClick({ ...subCategory, parentCategory: categoryName })}
                        >
                          {locale === Language.EN ? subCategory.label_en : subCategory.label_et}
                        </div>
                        <div className="w-1/2">
                          <Table>
                            <TableBody>
                              <TableRow className="text-sm hover:bg-transparent">
                                <TableCell className="w-1/2 px-1 text-center">
                                  <div
                                    className={cn(
                                      "mx-auto max-w-[120px] rounded-lg px-2 py-1",
                                      statusStyles[subCategory.management_score_status as keyof typeof statusStyles]
                                    )}
                                  >
                                    {tStatus(subCategory.management_score_status)}
                                  </div>
                                </TableCell>
                                <TableCell className="w-1/2 px-1 text-center">
                                  <div
                                    className={cn(
                                      "mx-auto max-w-[120px] rounded-lg px-2 py-1",
                                      statusStyles[subCategory.transparency_score_status as keyof typeof statusStyles]
                                    )}
                                  >
                                    {tStatus(subCategory.transparency_score_status)}
                                  </div>
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )
                  })}
                </AccordionContent>
              )}
            </AccordionItem>
          )
        })}
      </Accordion>
    </div>
  )
}
