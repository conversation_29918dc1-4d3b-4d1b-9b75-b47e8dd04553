/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { localizedMessage, lz } from "@/utils/zod-localization"

// Create a schema with localized error messages
export const companySchema = lz.object({
  name: lz.string().min(1, localizedMessage("Company name is required", "Ettevõtte nimi on kohustuslik")),
  registrationNumber: lz
    .string()
    .min(1, localizedMessage("Registration number is required", "Registreerimisnumber on kohustuslik")),
  status: lz.enum(["bankrupt", "delete", "entered_in_the_register", "in_liquidation"]),
  country: lz.string().min(1, localizedMessage("Country is required", "Riik on kohustuslik")),
  employeeCount: lz
    .number()
    .min(0, localizedMessage("Employee count must be a positive number", "Töötajate arv peab olema positiivne number")),
  website: lz.string().url(localizedMessage("Invalid website URL", "Vigane veebisaidi URL")),
})
