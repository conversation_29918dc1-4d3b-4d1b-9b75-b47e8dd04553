"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import { useRef, useState } from "react"
import { AssessmentHistory } from "@/app/admin/companies/[id]/components/assessment-history"
import { CompanyOverview } from "@/app/admin/companies/[id]/components/company-overview"
import { ESGAssessment } from "@/app/admin/companies/[id]/components/esg-assessment"
import { HelpInformationProvider } from "@/components/survey-form/use-help-information-context"
import { api } from "@/lib/trpc-provider"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { AggregateSearchCombobox } from "@kreios/admin-layout/components/aggregate-search-combobox"
import { HistoryCard } from "@kreios/admin-layout/components/history-card"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { withSkeleton } from "@kreios/admin-layout/utils/with-skeleton"
import { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger } from "@kreios/ui/responsive-tabs"
import { TabsContent } from "@kreios/ui/tabs"

import { PageSkeleton } from "./skeleton"

export default withSkeleton(
  PageSkeleton,
  ({ params: { id }, portalUser }: { params: { id: string }; portalUser: PortalUserDocument }) => {
    const t = useTranslations("companyProfile")
    const router = useRouter()
    const [company] = api.companies.get.useSuspenseQuery(id)
    const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)
    const [tab, setTab] = useState("company-overview")
    const mainRef = useRef<HTMLDivElement>(null)

    const handleTabChange = (tab: string) => {
      setTab(tab)
      mainRef.current?.scrollIntoView({ behavior: "smooth" })
    }

    return (
      <HelpInformationProvider>
        <div ref={mainRef}>
          {/* <div className="px-4 md:px-8">
            <Alert
              variant="default"
              className="mb-6 mt-0 rounded-sm border-0 border-l-4 border-yellow-400 bg-yellow-50 dark:bg-yellow-400/20 md:-mt-4"
            >
              <AlertTriangleIcon className="size-4 !text-yellow-400 dark:text-yellow-300" />
              <AlertTitle className="font-medium text-yellow-700 dark:text-yellow-300">{t("alert.title")}</AlertTitle>
              <AlertDescription className="text-yellow-700 dark:text-yellow-300">
                {t("alert.description")}
              </AlertDescription>
            </Alert>
          </div> */}

          <ResponsiveTabs value={tab} onValueChange={(value) => setTab(value)}>
            {!(isAdmin || isClientAdmin) && (
              <PathBreadcrumb id={-1}>
                <AggregateSearchCombobox
                  label={t("breadcrumbSearchLabel")}
                  procedure={api.companies.list}
                  defaultValue={{ value: company.aggregateId, label: company.name }}
                  onSelect={(value) => value && router.push(`/admin/companies/${value.value}`)}
                />
              </PathBreadcrumb>
            )}
            <PageHeader
              includeInBreadcrumb={isAdmin || isClientAdmin}
              title={
                <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight">
                  {company.name}
                  <span className="ml-2 text-sm font-normal text-muted-foreground">({company.registrationNumber})</span>
                </h2>
              }
            >
              <ResponsiveTabsList>
                <ResponsiveTabsTrigger value="company-overview">{t("tabs.companyOverview")}</ResponsiveTabsTrigger>
                <ResponsiveTabsTrigger value="esg-assessment">{t("tabs.esgAssessment")}</ResponsiveTabsTrigger>
                <ResponsiveTabsTrigger disabled value="assessment-history">
                  {t("tabs.assessmentHistory")}
                </ResponsiveTabsTrigger>
                <ResponsiveTabsTrigger disabled value="history">
                  {t("tabs.history")}
                </ResponsiveTabsTrigger>
              </ResponsiveTabsList>
            </PageHeader>

            <div className="grid w-full max-w-full flex-1 items-start gap-4 p-4 pt-2 md:p-8 md:pt-4 lg:gap-8">
              <TabsContent value="company-overview">
                <CompanyOverview company={company} setTab={handleTabChange} />
              </TabsContent>

              <TabsContent value="esg-assessment">
                <ESGAssessment company={company} />
              </TabsContent>

              <TabsContent value="assessment-history">
                <AssessmentHistory company={company} />
              </TabsContent>

              <TabsContent value="history">
                <HistoryCard procedure={api.companies.history} id={id} />
              </TabsContent>
            </div>
          </ResponsiveTabs>
        </div>
      </HelpInformationProvider>
    )
  }
)
