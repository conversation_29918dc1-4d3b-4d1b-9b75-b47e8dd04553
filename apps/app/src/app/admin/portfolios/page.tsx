/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { WatchlistDocument } from "@impactly/domain/watchlists/elastic"
import { redirect } from "next/navigation"
import { withConsent } from "@/utils/with-consent"
import { getPortalUserByEmail } from "@impactly/domain/portal-users/utils/get-portal-user-by-email"
import { Info } from "lucide-react"

import { auth } from "@kreios/auth"
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@kreios/ui/alert-dialog"
import { ButtonLink } from "@kreios/ui/button-link"

export default withConsent(async () => {
  const gateway = container.resolve("gateway")
  const session = await auth()
  const portalUserEmail = session?.user.email

  if (!portalUserEmail) redirect("/auth/login")

  const start = performance.now()

  // First, look up the portal user by email
  const portalUser = await getPortalUserByEmail(portalUserEmail)

  const end = performance.now()

  console.log(`Time taken: ${end - start} milliseconds`)

  if (!portalUser)
    return (
      <AlertDialog open={true}>
        <AlertDialogContent className="sm:max-w-[425px]">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Info className="h-6 w-6" />
              <span>No portfolios assigned</span>
            </AlertDialogTitle>
            <AlertDialogDescription className="text-left">
              You currently don't have any portfolios assigned to your account. To request access, please submit a
              submissions form.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="mt-4">
            <ButtonLink href="/" className="w-full" variant="default">
              Create a new evaluation request
            </ButtonLink>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    )

  // Now search for watchlists using the found portalUserId
  const { documents } = await gateway.search<WatchlistDocument, never>({
    query: {
      bool: {
        must: [{ term: { portalUserId: portalUser.aggregateId } }],
      },
    },
    index: "watchlists",
    size: 1,
  })

  if (documents.length > 0) redirect(`/admin/portfolios/${documents[0].aggregateId}`)

  return (
    <AlertDialog open={true}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <Info className="h-6 w-6" />
            <span>Currently no portfolios</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-left">
            Currently no portfolios are available. Please use the button below to search for companies & create a new
            portfolio.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="mt-4">
          <ButtonLink href="/admin/companies" className="w-full" variant="default">
            Search Companies
          </ButtonLink>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  )
})
