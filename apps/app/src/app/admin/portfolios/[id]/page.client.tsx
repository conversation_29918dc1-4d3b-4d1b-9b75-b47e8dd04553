"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { ColumnDef } from "@tanstack/react-table"
import { api } from "@/lib/trpc-provider"
import { PlusIcon } from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { AggregateSearchCombobox } from "@kreios/admin-layout/components/aggregate-search-combobox"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { selectColumn } from "@kreios/datatable/data-table-common-definitions"
import { DataTableInline } from "@kreios/datatable/data-table-inline"
import { createEnhancedColumnHelper } from "@kreios/datatable/enhanced-column-helper"
import { Button } from "@kreios/ui/button"
import { toast } from "@kreios/ui/sonner"
import { capitalize } from "@kreios/utils/capitilize"

type Company = RouterOutput["watchlists"]["get"]["entries"][number]["company"]

const columnHelper = createEnhancedColumnHelper<Company>()

const maturityLevelToColor = {
  high: "green",
  low: "red",
  medium: "orange",
  very_high: "green",
  very_low: "red",
}

const columns = [
  selectColumn(),
  columnHelper.link("name", {
    title: "Company",
    href: (row) => `/admin/companies/${row.aggregateId}`,
  }),
  columnHelper.custom("country", {
    title: "Country",
  }),
  columnHelper.custom("businessActivities", {
    title: "Main Activity",
    cell: (ctx) => ctx.getValue().find((activity) => activity.isMainActivity)?.naceCode?.label ?? "N/A",
  }),
  columnHelper.badge("latestEvaluation.environmentalMaturity", {
    title: "Environmental",
    transform: (status) => capitalize(status.replace(/_/g, " ").toLowerCase()),
    colorMap: maturityLevelToColor,
  }),
  columnHelper.badge("latestEvaluation.socialMaturity", {
    title: "Social",
    transform: (status) => capitalize(status.replace(/_/g, " ").toLowerCase()),
    colorMap: maturityLevelToColor,
  }),
  columnHelper.badge("latestEvaluation.governanceMaturity", {
    title: "Governance",
    transform: (status) => capitalize(status.replace(/_/g, " ").toLowerCase()),
    colorMap: maturityLevelToColor,
  }),
  columnHelper.custom("registrationNumber", {
    title: "Registration Number",
  }),
  columnHelper.date("latestEvaluation.completedAt", {
    title: "Last Evaluation",
    format: "Pp",
  }),
] as ColumnDef<Company, unknown>[]

export default function Page({ params: { id } }: { params: { id: string } }) {
  const t = useTranslations("toast")
  const [portfolio] = api.watchlists.get.useSuspenseQuery(id)
  const router = useRouter()

  return (
    <>
      <PathBreadcrumb id={-1}>
        <AggregateSearchCombobox
          label="Portfolio"
          procedure={api.watchlists.list}
          defaultValue={{ value: portfolio.aggregateId, label: portfolio.name }}
          onSelect={(value) => value && router.push(`/admin/portfolios/${value.value}`)}
        />
      </PathBreadcrumb>

      <PageHeader includeInBreadcrumb={false} title={portfolio.name}>
        <Button disabled onClick={() => toast.info(t("info.notImplemented"))} variant="outline" size="sm">
          <PlusIcon className="mr-2 size-4" />
          Create Portfolio
        </Button>
      </PageHeader>
      <div className="flex w-full flex-1 p-4 md:p-6">
        <DataTableInline
          data={portfolio.entries.map(({ company }) => company)}
          columns={columns}
          rowIdKey="aggregateId"
          // toolbar={<DataTableDateRangeFilter column="latestEvaluation_completedAt" />}
        />
      </div>
    </>
  )
}
