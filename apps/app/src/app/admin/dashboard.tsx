"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { BarChartIcon, BellIcon } from "lucide-react"
import { <PERSON>, <PERSON>Chart } from "recharts"

import type { ChartConfig } from "@kreios/ui/chart"
import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { ChartContainer, ChartLegend, ChartLegendContent } from "@kreios/ui/chart"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

const esgMaturityData = [
  { category: "high", value: 30, fill: "var(--color-high)" },
  { category: "medium", value: 40, fill: "var(--color-medium)" },
  { category: "low", value: 30, fill: "var(--color-low)" },
]

const esgMaturityConfig = {
  value: {
    label: "Value",
  },
  high: {
    label: "High",
    color: "hsl(var(--chart-1))",
  },
  medium: {
    label: "Medium",
    color: "hsl(var(--chart-2))",
  },
  low: {
    label: "Low",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig

const ghgReportingData = [
  { category: "reported", value: 60, fill: "var(--color-reported)" },
  { category: "partially-reported", value: 25, fill: "var(--color-partially-reported)" },
  { category: "not-reported", value: 15, fill: "var(--color-not-reported)" },
]

const ghgReportingConfig = {
  value: {
    label: "Value",
  },
  reported: {
    label: "Reported",
    color: "hsl(var(--chart-1))",
  },
  "partially-reported": {
    label: "Partially Reported",
    color: "hsl(var(--chart-2))",
  },
  "not-reported": {
    label: "Not Reported",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig

export function PortfolioESGMaturityCard({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Portfolio ESG Maturity</CardTitle>
      </CardHeader>
      <CardContent className="pl-2">
        <ChartContainer config={esgMaturityConfig} className="aspect-auto h-[250px]">
          <PieChart>
            <Pie data={esgMaturityData} dataKey="value" nameKey="category" />
            <ChartLegend
              content={<ChartLegendContent nameKey="category" />}
              className="-translate-y-2 flex-wrap gap-2 [&>*]:basis-1/4 [&>*]:justify-center"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

export function PortfolioGHGReportingCard({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Portfolio GHG Reporting</CardTitle>
      </CardHeader>
      <CardContent className="pl-2">
        <ChartContainer config={ghgReportingConfig} className="aspect-auto h-[250px]">
          <PieChart data={ghgReportingData}>
            <Pie data={ghgReportingData} dataKey="value" nameKey="category" />
            <ChartLegend
              content={<ChartLegendContent nameKey="category" />}
              className="-translate-y-2 flex-wrap gap-2 [&>*]:basis-1/4 [&>*]:justify-center"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

export function PortfolioCompaniesCard({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Portfolio Companies</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company</TableHead>
              <TableHead>Country</TableHead>
              <TableHead>Main Activity</TableHead>
              <TableHead>Environmental</TableHead>
              <TableHead>Social</TableHead>
              <TableHead>Governance</TableHead>
              <TableHead>Last Evaluation</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>MM Grupp OÜ</TableCell>
              <TableCell>Estonia</TableCell>
              <TableCell>Oil & Gas</TableCell>
              <TableCell className="text-yellow-500 dark:text-yellow-400">Medium</TableCell>
              <TableCell className="text-yellow-500 dark:text-yellow-400">Medium</TableCell>
              <TableCell className="text-green-500 dark:text-green-400">High</TableCell>
              <TableCell>April 30 2024 6:33pm</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export function RecentlyViewedCard({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Recently Viewed</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company</TableHead>
              <TableHead>Country</TableHead>
              <TableHead>Main Activity</TableHead>
              <TableHead>Environmental</TableHead>
              <TableHead>Social</TableHead>
              <TableHead>Governance</TableHead>
              <TableHead>Evaluation Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>MM Grupp OÜ</TableCell>
              <TableCell>Estonia</TableCell>
              <TableCell>Oil & Gas</TableCell>
              <TableCell className="text-yellow-500 dark:text-yellow-400">Medium</TableCell>
              <TableCell className="text-yellow-500 dark:text-yellow-400">Medium</TableCell>
              <TableCell className="text-green-500 dark:text-green-400">High</TableCell>
              <TableCell>April 30 2024 6:33pm</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

export function PortfolioAlertsCard({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Portfolio Alerts</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="flex flex-col gap-2">
          <li className="flex items-center gap-2">
            <BellIcon className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
            <span>New Official Source finding for MM Grupp O</span>
          </li>
          <li className="flex items-center gap-2">
            <BarChartIcon className="h-5 w-5 text-blue-500 dark:text-blue-400" />
            <span>New assessment for Rahva Rabat OÜ</span>
          </li>
          <li className="flex items-center gap-2">
            <BarChartIcon className="h-5 w-5 text-blue-500 dark:text-blue-400" />
            <span>New assessment for Rahva Rabat OÜ</span>
          </li>
          <li className="flex items-center gap-2">
            <BellIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
            <span>New Negative Media finding for MM Grupp O</span>
          </li>
          <li className="flex items-center gap-2">
            <BellIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
            <span>New Official Source finding for MM Grupp O</span>
          </li>
          <li className="flex items-center gap-2">
            <BellIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
            <span>New Official Source finding for MM Grupp O</span>
          </li>
        </ul>
      </CardContent>
    </Card>
  )
}
