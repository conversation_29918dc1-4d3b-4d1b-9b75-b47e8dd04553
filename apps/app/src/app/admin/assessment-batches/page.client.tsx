"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { BatchesPage } from "@/components/batches-page"
import { SurveyTemplateEnum } from "@/utils/constants"

import { withSkeleton } from "@kreios/admin-layout/utils/with-skeleton"

import { BatchCard, EmptyBatchCard, SurveyPagination } from "./components"
import { AddBatchDialog } from "./components/add-batch-dialog"
import { PageSkeleton } from "./skeleton"

/**
 * Assessment Batches page that displays a list of self-assessment survey batches
 * Uses the shared BatchesPage component with assessment-specific configuration
 */
export default withSkeleton(PageSkeleton, () => {
  return (
    <BatchesPage
      templateType={SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1}
      namespace="assessmentBatches"
      BatchCard={BatchCard}
      EmptyBatchCard={EmptyBatchCard}
      SurveyPagination={SurveyPagination}
      AddBatchDialog={AddBatchDialog}
    />
  )
})
