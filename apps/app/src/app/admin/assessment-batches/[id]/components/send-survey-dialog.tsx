/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { AssessmentTemplateEnum, assessmentZodEnumLabelMapper } from "@/app/admin/assessment-batches/[id]/utils"
import { SendSurveyDialog as SharedSendSurveyDialog } from "@/components/send-survey-dialog"

/**
 * SendSurveyDialog component for creating and sending assessment surveys
 * This is a wrapper around the shared component with assessment-specific configuration
 */
export default function SendSurveyDialog({
  open,
  onOpenChange,
  batchId,
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  batchId: string
}) {
  return (
    <SharedSendSurveyDialog
      open={open}
      onOpenChange={onOpenChange}
      batchId={batchId}
      templateEnum={AssessmentTemplateEnum}
      templateLabelMapper={assessmentZodEnumLabelMapper}
      defaultTemplateValue="SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
      translationNamespace="selfAssessments.sendAssessmentDialog"
      headerBgClass="bg-white dark:bg-gray-600"
    />
  )
}
