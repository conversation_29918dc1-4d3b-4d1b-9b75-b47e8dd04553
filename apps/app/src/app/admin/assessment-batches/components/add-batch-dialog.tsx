/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { AddBatchDialog as SharedAddBatchDialog } from "@/components/add-batch-dialog"
import { SurveyTemplateEnum } from "@/utils/constants"

/**
 * AddBatchDialog component with assessment-specific configuration
 */
export const AddBatchDialog = ({ open, onOpenChange }: { open: boolean; onOpenChange: (open: boolean) => void }) => {
  return (
    <SharedAddBatchDialog
      open={open}
      onOpenChange={onOpenChange}
      templateType={SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1}
      namespace="assessmentBatches"
      redirectBasePath="/admin/assessment-batches"
    />
  )
}
