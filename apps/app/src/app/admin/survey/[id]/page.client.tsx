/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type {
  ClimateImpactFormData,
  GeneralFormData,
  GreenTransitionFormData,
  NatureFormData,
  NfrdReportingFormData,
  SocialGovernanceFormData,
} from "@/app/admin/survey/[id]/schema"
import type { Language } from "@/utils/constants"
import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import { useEffect, useMemo, useRef, useState } from "react"
import { HelpPanel } from "@/app/admin/survey/[id]/components/help-pannel"
import { SurveySection } from "@/app/admin/survey/[id]/components/survey-section"
import {
  climateImpactFormSchema,
  generalFormSchema,
  getSurveyFormSchema,
  governanceFormSchema,
  greenTransitionFormSchema,
  natureFormSchema,
  nfrdReportingFormSchema,
} from "@/app/admin/survey/[id]/schema"
import { AutoSaveIndicator } from "@/components/survey-form/AutoSaveIndicator"
import { ClearSurveyFormDialog } from "@/components/survey-form/clear-survey-form-dialog"
import { HelpInfoClearer } from "@/components/survey-form/help-info-clearer"
import { PrefilledHeader } from "@/components/survey-form/prefilled-header"
import { SubmitQuestionnaireDialog } from "@/components/survey-form/submit-questionnaire-dialog"
import { HelpInformationProvider } from "@/components/survey-form/use-help-information-context"
import { FormProvider } from "@/components/survey-form/use-survey-context"
import useTabs from "@/hooks/use-tabs"
import { api } from "@/lib/trpc-provider"
import schema from "@/survey-templates/universal_v1.schema.json"
import { SurveyStatusEnum } from "@/utils/constants"
import { getDefaultZodValues } from "@/utils/get-default-zod-values"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { merge } from "lodash-es"
import { AlertCircle } from "lucide-react"
import { useLocale, useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"
import { useDebouncedCallback } from "use-debounce"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Alert, AlertDescription } from "@kreios/ui/alert"
import { Button } from "@kreios/ui/button"
import { Card, CardContent, CardDescription, CardTitle } from "@kreios/ui/card"
import { Form, useForm } from "@kreios/ui/form"
import { ReactHookFormDevTool } from "@kreios/ui/form/form-dev-tool"
import { useCreateFormSubscribe } from "@kreios/ui/hooks/use-form-subscribe"
import { ResponsiveStepper, ResponsiveStepperList, ResponsiveStepperTrigger } from "@kreios/ui/responsive-stepper"
import { toast } from "@kreios/ui/sonner"
import { StepperContent } from "@kreios/ui/stepper"

type SurveyFormData = {
  general?: GeneralFormData
  climate_impact?: ClimateImpactFormData
  green_transition?: GreenTransitionFormData
  nature?: NatureFormData
  social_governance?: SocialGovernanceFormData
  nfrd_reporting?: NfrdReportingFormData
}

type FormSectionData = SurveyFormData[keyof SurveyFormData]

type SectionId = keyof typeof schema.properties

const sectionKeys = Object.keys(schema.properties) as SectionId[]

const schemaMap = {
  general: generalFormSchema,
  climate_impact: climateImpactFormSchema,
  green_transition: greenTransitionFormSchema,
  nature: natureFormSchema,
  social_governance: governanceFormSchema,
  nfrd_reporting: nfrdReportingFormSchema,
} as const

type StepStatus = {
  color: string
  status: "complete" | "warning" | "default"
}

const getStepStatus = (
  formData: FormSectionData,
  section: keyof typeof schemaMap,
  isSurveyComplete: boolean,
  isFormDisabled: boolean
): StepStatus => {
  // If no data at all, show as default (not started)
  if (!formData || Object.keys(formData).length === 0 || isFormDisabled) {
    return { color: "default", status: "default" }
  }

  if (isSurveyComplete) {
    return { color: "green-500", status: "complete" }
  }

  // Check if all required fields are filled
  const validationResult = schemaMap[section].safeParse(formData)

  // If validation passes AND no empty required fields, show as green
  // Otherwise, show as yellow/warning (in progress)
  // This allows users to navigate between sections even with incomplete fields
  // while maintaining the warning state for incomplete sections
  const result: StepStatus = validationResult.success
    ? { color: "green-500", status: "complete" }
    : { color: "yellow-500", status: "warning" }

  return result
}

// const ExportButton = ({ surveyId, disabled, ...props }: { surveyId: string } & ButtonProps) => {
//   const { mutate, isPending } = useSurveyExport()
//   const t = useTranslations("common")

//   return (
//     <Button variant="outline" onClick={() => mutate(surveyId)} {...props} disabled={isPending || disabled}>
//       {isPending ? (
//         <>
//           <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//           {t("exporting")}
//         </>
//       ) : (
//         t("export")
//       )}
//     </Button>
//   )
// }

export default ({ params: { id }, portalUser }: { params: { id: string }; portalUser: PortalUserDocument }) => {
  const [showDialog, setShowDialog] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showClearDialog, setShowClearDialog] = useState(false)
  const router = useRouter()
  const { isCompanyUser: companyUser, isObserver } = getPortalUserRoles(portalUser)
  const isCompanyUser = companyUser || isObserver
  const locale = useLocale() as Language
  const surveyFormSchema = useMemo(() => getSurveyFormSchema(locale), [locale])

  const { tab, setTab } = useTabs<SectionId>(sectionKeys[0], "tab")
  const t = useTranslations()

  const [survey] = api.surveys.getSurvey.useSuspenseQuery(id)
  const initialValues = useMemo(() => getDefaultZodValues(surveyFormSchema), [surveyFormSchema])
  const initialDefaultValues = merge(initialValues, survey.formData)

  const utils = api.useUtils()
  const updateSurveyStatus = api.surveys.updateSurveyStatus.useMutation({
    onSuccess: () => utils.surveys.invalidate(),
  })
  const isSurveyComplete = survey.status === ("COMPLETE" as SurveyStatusEnum)
  // Allow company users to edit the survey regardless of its status
  const isReadOnly = isSurveyComplete || !companyUser
  // const isExportable = isSurveyComplete && (isClientAdmin || isAdmin)
  // Allow company users to edit prefilled fields regardless of survey status
  const isPrefilledDisabled = isSurveyComplete || !companyUser

  const currentIndex = sectionKeys.findIndex((key) => key === tab)

  // Initialize the form with the survey schema
  const form = useForm({
    schema: surveyFormSchema,
    // Use the survey form data as is, or an empty object if it's null/undefined

    defaultValues: initialDefaultValues,
    mode: "all", // Changed from "onTouched" to "all" for more consistent validation
  })

  const mainRef = useRef<HTMLDivElement>(null)

  const handleNavigation = async (direction: "next" | "prev") => {
    if (direction === "next" && tab === "introduction") {
      if (survey.status === ("NOT_STARTED" as SurveyStatusEnum)) {
        updateSurveyStatus.mutate({ surveyId: id, status: SurveyStatusEnum.InProgress })
      }
    }

    const nextIndex = direction === "next" ? currentIndex + 1 : currentIndex - 1
    if (nextIndex >= 0 && nextIndex < sectionKeys.length) {
      await setTab(sectionKeys[nextIndex])
      mainRef.current?.scrollIntoView({ behavior: "smooth" })
    }
  }
  const openDialog = () => {
    setShowDialog(true)
  }
  const updateSurvey = api.surveys.submitSurveyForm.useMutation()

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      // Validate the form before submitting
      const validationResult = await form.trigger()
      if (!validationResult) {
        // If validation fails, show a toast and keep the dialog open
        toast.error(t("toast.error.surveySubmitError"))
        // Scroll to the first error field
        const firstErrorField = Object.keys(form.formState.errors)[0]
        if (firstErrorField) {
          // Find the section containing the error
          const sectionKey = firstErrorField.split(".")[0] as SectionId
          // Switch to that section if it's a valid section
          if (sectionKeys.includes(sectionKey)) {
            await setTab(sectionKey)
            // Scroll to the top of the form
            mainRef.current?.scrollIntoView({ behavior: "smooth" })
          }
        }
        setIsSubmitting(false)
        return
      }

      // Get form values
      const formValues = form.getValues()

      // Use type assertion with the correct type to fix the type error
      type SurveyFormSubmitData = Parameters<typeof updateSurvey.mutateAsync>[0]["formData"]

      await updateSurvey.mutateAsync({
        id,
        formData: formValues as SurveyFormSubmitData,
      })
      await utils.surveys.getSurvey.invalidate(id)

      toast.success(t("toast.success.surveySubmittedSuccessfully"))
      router.push(isCompanyUser ? `/admin/company-surveys` : `/admin/surveys/${survey.batchId}`)
    } catch (error) {
      console.error("Error submitting survey:", error)
      toast.error(t("toast.error.surveySubmitError"))
    } finally {
      setIsSubmitting(false)
      setShowDialog(false)
    }
  }

  const {
    mutateAsync: saveFormDataAsync,
    isPending: autoSavePending,
    submittedAt: autoSaveSubmittedAt,
  } = api.surveys.autoSaveSurveyForm.useMutation()
  const useFormSubscribe = useCreateFormSubscribe(form.control)

  const saveData = useDebouncedCallback(async (formData: SurveyFormData) => {
    try {
      // Make sure we have valid data to save
      if (Object.keys(formData).length === 0) {
        console.warn("Auto-save skipped: No form data to save")
        return
      }

      // Helper function to check if a value is empty ([], {}, null, undefined)
      const isEmpty = (value: unknown): boolean => {
        if (value === null || value === undefined) return true
        if (Array.isArray(value) && value.length === 0) return true
        if (typeof value === "object" && Object.keys(value).length === 0) return true
        return false
      }

      // Helper function to deeply clean an object by removing empty values
      const deepClean = <T extends Record<string, unknown>>(obj: T): Partial<T> => {
        const result = {} as Partial<T>

        Object.entries(obj).forEach(([key, value]) => {
          // Skip empty values
          if (isEmpty(value)) return

          // Recursively clean nested objects
          if (value !== null && typeof value === "object" && !Array.isArray(value)) {
            const cleaned = deepClean(value as Record<string, unknown>)
            // Only add the cleaned object if it's not empty
            if (Object.keys(cleaned).length > 0) {
              result[key as keyof T] = cleaned as T[keyof T]
            }
          } else if (Array.isArray(value)) {
            // For arrays, filter out empty items and only include if the array is not empty
            const cleanedArray = value
              .filter((item) => !isEmpty(item))
              .map((item) => {
                if (typeof item === "object" && item !== null) {
                  // Use type assertion to ensure the return type is consistent
                  return deepClean(item as Record<string, unknown>) as unknown
                }
                return item as unknown
              })

            if (cleanedArray.length > 0) {
              result[key as keyof T] = cleanedArray as T[keyof T]
            }
          } else {
            // For primitive values, include them directly
            result[key as keyof T] = value as T[keyof T]
          }
        })

        return result
      }

      // Create a more permissive type for the auto-save data
      type AutoSaveFormData = Record<string, Record<string, unknown>>

      // Clean each section of the form data
      const cleanedFormData = Object.entries(formData).reduce((acc, [key, sectionData]) => {
        // TypeScript knows sectionData is an object from Object.entries
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        if (typeof sectionData === "object" && sectionData !== null) {
          // Deep clean the section data
          const cleanedSection = deepClean(sectionData as Record<string, unknown>)

          // Only include sections that have data after cleaning
          if (Object.keys(cleanedSection).length > 0) {
            acc[key] = cleanedSection
          }
        }
        return acc
      }, {} as AutoSaveFormData)

      // Skip auto-save if there's no data after cleaning
      if (Object.keys(cleanedFormData).length === 0) {
        console.warn("Auto-save skipped: No valid data after cleaning")
        return
      }

      // Use the updated backend function with the cleaned form data
      await saveFormDataAsync({
        id,
        formData: cleanedFormData,
      })
      await utils.surveys.getSurvey.invalidate(id)
    } catch (error) {
      // Log the error but don't show a toast to avoid disrupting the user
      console.error("Auto-save error:", error)
    }
  }, 10000)

  const handleClearForm = () => {
    setShowClearDialog(true)
  }

  const handleConfirmClear = async () => {
    form.reset()
    setShowClearDialog(false)
    router.refresh()
    await setTab(sectionKeys[0])
  }

  // Auto-save all filled data, not just the current section
  useFormSubscribe((formData: SurveyFormData) => {
    if (isReadOnly) return

    // Skip auto-save if we're on the introduction tab
    if (tab === "introduction") return

    // Auto-save all the data that the user has filled out
    void saveData(formData)
  })

  // Force re-render when form values change to update the status indicators
  const [, setForceUpdate] = useState({})
  useEffect(() => {
    // Watch all form fields for changes
    const subscription = form.watch(() => {
      // Force a re-render to update the status indicators
      setForceUpdate({})
    })

    // Clean up subscription
    return () => subscription.unsubscribe()
  }, [form])

  return (
    <div ref={mainRef}>
      <Form readOnly={isReadOnly} {...form}>
        <ReactHookFormDevTool control={form.control} />

        <form>
          <HelpInformationProvider>
            <FormProvider
              initialPrefilledData={isPrefilledDisabled ? undefined : survey.prefilledData?.formData}
              isPrefilledDisabled={isPrefilledDisabled}
            >
              <PageHeader
                title={`${t("survey.survey")}: ${survey.company.name}`}
                // actions={<ExportButton surveyId={id} disabled={!isExportable} />}
              />
              <div className="grid flex-1 items-start gap-4 p-4 pt-2 md:p-8 md:pt-4 lg:gap-8">
                <Alert className="flex flex-row items-center gap-3 border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-700 dark:bg-blue-900 dark:text-blue-200">
                  <div>
                    <AlertCircle className="h-4 w-4" />
                  </div>
                  <AlertDescription>{t("survey.helpText")}</AlertDescription>
                </Alert>
                <ResponsiveStepper value={tab} onValueChange={(value) => setTab(value as SectionId)}>
                  <ResponsiveStepperList breakpoint="md">
                    {sectionKeys.map((key) => (
                      <ResponsiveStepperTrigger
                        key={key}
                        value={key}
                        breakpoint="md"
                        color={
                          key !== "introduction"
                            ? getStepStatus(form.watch(key) as FormSectionData, key, isSurveyComplete, isReadOnly).color
                            : isReadOnly
                              ? "default"
                              : "green-500"
                        }
                        status={
                          key !== "introduction"
                            ? getStepStatus(form.watch(key) as FormSectionData, key, isSurveyComplete, isReadOnly)
                                .status
                            : isReadOnly
                              ? "default"
                              : "complete"
                        }
                      >
                        {t(`survey.tabs.${key}.title`)}
                      </ResponsiveStepperTrigger>
                    ))}
                  </ResponsiveStepperList>
                  {tab !== "introduction" && <PrefilledHeader sectionId={tab} />}
                  {sectionKeys.map((key) => (
                    <StepperContent key={key} value={key}>
                      <Card>
                        <CardContent>
                          <div className="flex items-start justify-between px-0 py-6">
                            <div>
                              <CardTitle className="text-2xl font-semibold">{t(`survey.tabs.${key}.title`)}</CardTitle>
                              {t.has(`survey.tabs.${key}.description`) && (
                                <CardDescription>{t(`survey.tabs.${key}.description`)}</CardDescription>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between gap-4">
                            <SurveySection sectionKey={key} schema={schema.properties[key]} />
                            <HelpPanel />
                          </div>
                        </CardContent>
                      </Card>
                    </StepperContent>
                  ))}
                </ResponsiveStepper>

                <div className="mt-6 flex justify-start gap-2">
                  <Button variant="outline" onClick={() => handleNavigation("prev")} disabled={currentIndex === 0}>
                    {t("survey.pagination.previous")}
                  </Button>
                  {tab !== "introduction" && !isReadOnly && (
                    <Button variant="gray" onClick={handleClearForm} disabled={isReadOnly}>
                      {t("survey.clearForm.title")}
                    </Button>
                  )}
                  {currentIndex === sectionKeys.length - 1 ? (
                    !isReadOnly && (
                      <Button onClick={openDialog} disabled={form.formState.disabled || form.formState.isSubmitting}>
                        {t("survey.pagination.submit")}
                      </Button>
                    )
                  ) : (
                    <Button onClick={() => handleNavigation("next")}>{t("survey.pagination.next")}</Button>
                  )}
                </div>
              </div>
              <ClearSurveyFormDialog
                open={showClearDialog}
                onOpenChange={setShowClearDialog}
                onConfirm={handleConfirmClear}
              />
            </FormProvider>
            <HelpInfoClearer tab={tab} />
          </HelpInformationProvider>
        </form>
        <AutoSaveIndicator
          pending={autoSavePending}
          submittedAt={autoSaveSubmittedAt ? new Date(autoSaveSubmittedAt) : null}
        />
        <SubmitQuestionnaireDialog
          open={showDialog}
          onOpenChange={setShowDialog}
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
        />
      </Form>
    </div>
  )
}
