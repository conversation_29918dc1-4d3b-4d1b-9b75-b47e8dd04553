/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import React from "react"

import { FormControl, FormField, FormItem } from "@kreios/ui/form"
import { Input } from "@kreios/ui/input"

interface FormatNumberInputProps {
  name: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  className?: string
}

const numberFormatter = {
  format: (value: number | null | undefined) => {
    if (value === null || value === undefined) return ""

    const formatted = new Intl.NumberFormat("de-DE", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value)

    return formatted.replace(/\./g, " ")
  },
  parse: (value: string) => {
    const numStr = value.replace(/[^\d,]/g, "")
    return numStr ? parseFloat(numStr.replace(",", ".")) : null
  },
}

export function FormatNumberInput({ name, required, disabled, placeholder, className }: FormatNumberInputProps) {
  return (
    <FormField
      name={name}
      render={({ field: { value, onChange, ...field } }) => (
        <FormItem className={className}>
          <FormControl>
            <Input
              {...field}
              value={numberFormatter.format(value as number)}
              onChange={(e) => onChange(numberFormatter.parse(e.target.value))}
              type="text"
              inputMode="decimal"
              placeholder={placeholder}
              required={required}
              disabled={disabled}
            />
          </FormControl>
        </FormItem>
      )}
    />
  )
}
