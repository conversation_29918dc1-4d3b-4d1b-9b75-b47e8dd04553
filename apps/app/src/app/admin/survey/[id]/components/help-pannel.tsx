/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useEffect, useRef } from "react"
import { useHelpInformation } from "@/components/survey-form/use-help-information-context"
import { FileText, X } from "lucide-react"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@kreios/ui/card"

export function HelpPanel() {
  const { helpInfo, setHelpInfo } = useHelpInformation()
  const t = useTranslations("survey.components.detailDisplay")
  const helpPanelRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!helpInfo) return

    const handleClickOutside = (event: MouseEvent) => {
      if (
        helpPanelRef.current &&
        !helpPanelRef.current.contains(event.target as Node) &&
        !(event.target as HTMLElement).closest(".help-trigger")
      ) {
        setHelpInfo(null)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [helpInfo, setHelpInfo])

  if (!helpInfo) return null

  return (
    <div className="sticky top-20 hidden h-fit md:block" ref={helpPanelRef}>
      <Card className="w-[420px]">
        <CardHeader className="flex flex-row items-baseline justify-between gap-2 space-y-0 pb-2">
          <CardTitle className="text-lg font-semibold">{helpInfo.title}</CardTitle>
          <Button variant="ghost" size="icon" onClick={() => setHelpInfo(null)}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="scrollbar-thin scrollbar-track-transparent scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-400 max-h-[250px] overflow-y-auto pr-2">
          <p className="text-muted-foreground">{helpInfo.description}</p>
        </CardContent>
        {helpInfo.exampleDocumentUrl && (
          <CardFooter>
            <span className="flex items-center text-blue-600 transition-colors hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
              <FileText className="mr-2 h-4 w-4" />
              {t("viewExampleDocument")}
            </span>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
