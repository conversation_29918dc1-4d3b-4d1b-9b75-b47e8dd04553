/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { Language } from "@/utils/constants"
import type { z } from "zod"
import { getUniversalQuestionnaireSchema, universalQuestionnaireSchemaEt } from "@/schema/generated"
import { lz } from "@/utils/zod-localization"

export const generalFormSchema = universalQuestionnaireSchemaEt.shape.general._def.innerType

export const climateImpactFormSchema = universalQuestionnaireSchemaEt.shape.climate_impact._def.innerType

export const governanceFormSchema = universalQuestionnaireSchemaEt.shape.social_governance._def.innerType

export const greenTransitionFormSchema = universalQuestionnaireSchemaEt.shape.green_transition._def.innerType

export const natureFormSchema = universalQuestionnaireSchemaEt.shape.nature._def.innerType

export const nfrdReportingFormSchema = universalQuestionnaireSchemaEt.shape.nfrd_reporting._def.innerType

// Function to get schemas based on language
export const getSchemas = (language: Language) => {
  const universalQuestionnaireSchema = getUniversalQuestionnaireSchema(language)

  return {
    surveyFormSchema: lz.object({
      general: universalQuestionnaireSchema.shape.general._def.innerType,
      climate_impact: universalQuestionnaireSchema.shape.climate_impact._def.innerType,
      green_transition: universalQuestionnaireSchema.shape.green_transition._def.innerType,
      nature: universalQuestionnaireSchema.shape.nature._def.innerType,
      social_governance: universalQuestionnaireSchema.shape.social_governance._def.innerType,
      nfrd_reporting: universalQuestionnaireSchema.shape.nfrd_reporting._def.innerType,
    }),
  }
}

// Export types for use in components
export type GeneralFormData = z.infer<typeof generalFormSchema>
export type ClimateImpactFormData = z.infer<typeof climateImpactFormSchema>
export type SocialGovernanceFormData = z.infer<typeof governanceFormSchema>
export type GreenTransitionFormData = z.infer<typeof greenTransitionFormSchema>
export type NatureFormData = z.infer<typeof natureFormSchema>
export type NfrdReportingFormData = z.infer<typeof nfrdReportingFormSchema>

export type SurveyResponse = {
  id: string
  status: "OPEN" | "NOT_STARTED" | "IN_PROGRESS" | "COMPLETE" | "SUBMITTED" | "SENT" | "PENDING" | "CANCELLED"
  company: {
    id: string
    name: string
  }
  batchId: string
  progress: number
  formData: {
    general?: z.infer<typeof generalFormSchema>
    climate_impact?: z.infer<typeof climateImpactFormSchema>
    green_transition?: z.infer<typeof greenTransitionFormSchema>
    nature?: z.infer<typeof natureFormSchema>
    social_governance?: z.infer<typeof governanceFormSchema>
    nfrd_reporting?: z.infer<typeof nfrdReportingFormSchema>
  }
  createdAt: Date
  updatedAt: Date
  statusChangedAt?: Date
  lastReminder?: Date
}

// Function to get the survey form schema based on the language
export const getSurveyFormSchema = (language: Language) => {
  const schemas = getSchemas(language)
  return schemas.surveyFormSchema
}
