"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { ColumnDef } from "@tanstack/react-table"
import { api } from "@/lib/trpc-provider"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { DataTable } from "@kreios/datatable"
import { selectColumn } from "@kreios/datatable/data-table-common-definitions"
import { createEnhancedColumnHelper } from "@kreios/datatable/enhanced-column-helper"
import { capitalize } from "@kreios/utils/capitilize"

type Evaluation = RouterOutput["evaluationRequests"]["list"]["data"][number]

const columnHelper = createEnhancedColumnHelper<Evaluation>()

const maturityLevelToColor = {
  fulfilled: "green dark:emerald",
  pending: "orange dark:amber",
}

const columns = [
  selectColumn(),
  columnHelper.link("companyName", {
    title: "Company",
    href: (row) => `/admin/evaluation-requests/${row.aggregateId}`,
  }),
  columnHelper.custom("portalUserName", {
    title: "Portal User",
  }),
  columnHelper.custom("portalUserEmail", {
    title: "Email",
  }),
  columnHelper.badge("status", {
    title: "status",
    transform: (status) => capitalize(status.replace(/_/g, " ").toLowerCase()),
    colorMap: maturityLevelToColor,
  }),

  columnHelper.date("createdAt", {
    title: "Evaluation Date",
    format: "Pp",
  }),
] as ColumnDef<Evaluation, unknown>[]

export default function Page() {
  return (
    <>
      <PageHeader title="Evaluation" />
      <div className="flex w-full flex-1 p-4 md:p-6">
        <DataTable procedure={api.evaluationRequests.list} columns={columns} rowIdKey="aggregateId" />
      </div>
    </>
  )
}
