"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { PortalUserDocument } from "@impactly/domain/portal-users/elastic"
import type { FC } from "react"
import { api } from "@/lib/trpc-provider"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { useRouter } from "nextjs-toploader/app"

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { AggregateSearchCombobox } from "@kreios/admin-layout/components/aggregate-search-combobox"
import { HistoryCard } from "@kreios/admin-layout/components/history-card"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { withSkeleton } from "@kreios/admin-layout/utils/with-skeleton"
import { cn } from "@kreios/ui"
import { Badge } from "@kreios/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger } from "@kreios/ui/responsive-tabs"
import { TabsContent } from "@kreios/ui/tabs"
import { capitalize } from "@kreios/utils/capitilize"

import { PageSkeleton } from "./skeleton"

type Evaluation = RouterOutput["evaluationRequests"]["get"]

const EvaluationOverview: FC<{ evaluation: Evaluation }> = async ({ evaluation }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>About the Evaluation</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          <div>
            <h3 className="font-semibold">Portal User Name</h3>
            {/* eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing */}
            <p>{evaluation.portalUserName || "Not provided"}</p>
          </div>
          <div>
            <h3 className="font-semibold">Portal User Email</h3>
            <p>{evaluation.portalUserEmail}</p>
          </div>
          <div>
            <h3 className="font-semibold">Company Name</h3>
            <p>{evaluation.companyName}</p>
          </div>
          <div>
            <h3 className="font-semibold">Created date</h3>
            <p>{new Date(evaluation.createdAt).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="font-semibold">Updated date</h3>
            <p>{new Date(evaluation.updatedAt).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="font-semibold">Status</h3>
            <Badge
              className={cn({
                "bg-green-500": evaluation.status === "fulfilled",
                "bg-yellow-500": evaluation.status === "pending",
              })}
            >
              {capitalize(evaluation.status)}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default withSkeleton(
  PageSkeleton,
  ({ params: { id }, portalUser }: { params: { id: string }; portalUser: PortalUserDocument }) => {
    const router = useRouter()
    const [evaluation] = api.evaluationRequests.get.useSuspenseQuery(id)

    const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

    return (
      <ResponsiveTabs defaultValue="company-overview">
        {!(isAdmin || isClientAdmin) && (
          <PathBreadcrumb id={-1}>
            <AggregateSearchCombobox
              label="Evaluation Requests"
              procedure={api.evaluationRequests.list}
              defaultValue={{ value: evaluation.aggregateId, label: evaluation.companyName }}
              onSelect={(value) => value && router.push(`/admin/evaluation/${value.value}`)}
            />
          </PathBreadcrumb>
        )}
        <PageHeader
          includeInBreadcrumb={isAdmin || isClientAdmin}
          title={
            <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight">
              Evaluation request of {evaluation.companyName}
              <span className="ml-2 text-sm font-normal text-muted-foreground">
                from {evaluation.portalUserName ? evaluation.portalUserName : evaluation.portalUserEmail}
              </span>
            </h2>
          }
        >
          <ResponsiveTabsList>
            <ResponsiveTabsTrigger value="company-overview">Evaluation Request Overview</ResponsiveTabsTrigger>

            <ResponsiveTabsTrigger value="history">History</ResponsiveTabsTrigger>
          </ResponsiveTabsList>
        </PageHeader>

        <div className="grid w-full max-w-full flex-1 items-start gap-4 p-4 pt-2 md:p-8 md:pt-4 lg:gap-8">
          <TabsContent
            value="company-overview"
            className="w-[calc(var(--main-content-width)-2rem)] transition-all duration-300 ease-in-out md:w-[calc(var(--main-content-width)-4rem)]"
          >
            <EvaluationOverview evaluation={evaluation} />
          </TabsContent>

          <TabsContent value="history">
            <HistoryCard procedure={api.evaluationRequests.history} id={id} />
          </TabsContent>
        </div>
      </ResponsiveTabs>
    )
  }
)
