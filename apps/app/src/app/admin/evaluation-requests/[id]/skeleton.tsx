/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useTranslations } from "next-intl"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger } from "@kreios/ui/responsive-tabs"
import { Skeleton } from "@kreios/ui/skeleton"
import { TabsContent, TabsTrigger } from "@kreios/ui/tabs"

export const PageSkeleton = () => {
  const t = useTranslations()
  return (
    <ResponsiveTabs defaultValue="company-overview">
      <PageHeader
        title={
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-6 w-24" />
          </div>
        }
      >
        <ResponsiveTabsList>
          <ResponsiveTabsTrigger disabled value="company-overview">
            {t("companyProfile.tabs.evaluationOverview")}
          </ResponsiveTabsTrigger>

          <TabsTrigger disabled value="history">
            {t("companyProfile.tabs.history")}
          </TabsTrigger>
        </ResponsiveTabsList>
      </PageHeader>

      <div className="grid flex-1 items-start gap-4 p-4 pt-2 md:p-8 md:pt-4 lg:gap-8">
        <TabsContent className="m-0" value="company-overview">
          <Card>
            <CardHeader>
              <CardTitle>
                <Skeleton className="h-full w-full" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              {[1, 2].map((i) => (
                <div key={i} className="mb-4 space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-2 w-full" />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </div>
    </ResponsiveTabs>
  )
}
