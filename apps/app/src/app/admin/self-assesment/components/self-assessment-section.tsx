/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { SchemaInput } from "@/schema/generate-schema/types"
import type { Language } from "@/utils/constants"
import type { FC } from "react"
import { useMemo } from "react"
import { getSelfAssessmentFormSchema } from "@/app/admin/self-assesment/components/schema"
import { GenericFormSection } from "@/components/survey-form/generic-form-section"
import { parseSchemaSections } from "@/schema/generate-schema/parse-schema-sections"
import { useLocale, useTranslations } from "next-intl"

import { useReadOnly } from "@kreios/ui/form"

interface SelfAssessmentSectionProps {
  schema: SchemaInput
  sectionKey: string
}

export const SelfAssessmentSection: FC<SelfAssessmentSectionProps> = ({ schema, sectionKey }) => {
  const readOnly = useReadOnly()
  const locale = useLocale() as Language
  const selfAssessmentFormSchema = getSelfAssessmentFormSchema(locale)
  const t = useTranslations("surveyForm.introduction")
  const sections = useMemo(() => {
    return parseSchemaSections(schema, sectionKey)
  }, [schema, sectionKey])

  if (sectionKey === "introduction") {
    return <div className="space-y-4">{t("main_text.description")}</div>
  }

  return (
    <div className="w-full space-y-8">
      {sections.map((section) => (
        <GenericFormSection
          key={section.sectionTitle}
          section={section}
          readOnly={readOnly}
          translationPrefix="selfAssessmentForm"
          schema={selfAssessmentFormSchema}
        />
      ))}
    </div>
  )
}
