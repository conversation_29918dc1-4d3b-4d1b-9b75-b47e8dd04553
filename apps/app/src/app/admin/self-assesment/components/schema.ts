/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { Language } from "@/utils/constants"
import type { z } from "zod"
import { getUniversalQuestionnaireSchema, universalQuestionnaireSchemaEt } from "@/schema/self-assesment-generated"
import { lz } from "@/utils/zod-localization"

export const generalFormSchema = universalQuestionnaireSchemaEt.shape.general._def.innerType

export const climateImpactFormSchema = universalQuestionnaireSchemaEt.shape.esg_management_strategy._def.innerType

export const governanceFormSchema = universalQuestionnaireSchemaEt.shape.climate_impact_energy_use._def.innerType

export const managingResources = universalQuestionnaireSchemaEt.shape.managing_environmental_resources._def.innerType

export const workforceResponsibility =
  universalQuestionnaireSchemaEt.shape.workforce_social_responsibility._def.innerType

export const businessEthicsGovernance = universalQuestionnaireSchemaEt.shape.business_ethics_governance._def.innerType

// Function to get schemas based on language
export const getSchemas = (language: Language) => {
  const universalQuestionnaireSchema = getUniversalQuestionnaireSchema(language)

  return {
    selfAssessmentFormSchema: lz.object({
      general: universalQuestionnaireSchema.shape.general._def.innerType,
      esg_management_strategy: universalQuestionnaireSchema.shape.esg_management_strategy._def.innerType,
      climate_impact_energy_use: universalQuestionnaireSchema.shape.climate_impact_energy_use._def.innerType,
      managing_environmental_resources:
        universalQuestionnaireSchema.shape.managing_environmental_resources._def.innerType,
      workforce_social_responsibility:
        universalQuestionnaireSchema.shape.workforce_social_responsibility._def.innerType,
      business_ethics_governance: universalQuestionnaireSchema.shape.business_ethics_governance._def.innerType,
    }),
  }
}

export type GeneralFormData = z.infer<typeof generalFormSchema>
export type ClimateImpactFormData = z.infer<typeof climateImpactFormSchema>
export type SocialGovernanceFormData = z.infer<typeof governanceFormSchema>
export type ManagingResources = z.infer<typeof managingResources>
export type WorkforceResponsibility = z.infer<typeof workforceResponsibility>
export type BusinessEthicsGovernance = z.infer<typeof businessEthicsGovernance>

export const getSelfAssessmentFormSchema = (language: Language) => {
  const schemas = getSchemas(language)
  return schemas.selfAssessmentFormSchema
}
