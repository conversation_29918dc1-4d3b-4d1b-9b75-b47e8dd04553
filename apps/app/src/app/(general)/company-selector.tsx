/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { ComponentRef } from "react"
import { forwardRef, useState } from "react"
import { api } from "@/lib/trpc-provider"
import { useControllableState } from "@radix-ui/react-use-controllable-state"
import { Check, ChevronsUpDown } from "lucide-react"
import { useDebounce } from "use-debounce"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandLoading,
} from "@kreios/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@kreios/ui/popover"

interface CompanySelectorTexts {
  placeholder: string
  searchPlaceholder: string
  emptyStateWithCreate: string
  emptyStateWithoutCreate: string
  notInDatabase: string
  refineSearch: string
}

type CompanySelectorProps<CompanyType extends { name: string; aggregateId?: string }> = {
  value?: CompanyType
  defaultValue?: CompanyType
  onBlur?: () => void
  disabled?: boolean
  onValueChange?: (state: CompanyType) => void
  texts: CompanySelectorTexts
  className?: string
}

/**
 * CompanySelector component for searching and selecting companies.
 *
 * This component provides an input field for searching companies and displays
 * a list of matching companies as radio options. It also allows for custom
 * company names not found in the database.
 */
export const CompanySelector = forwardRef<
  ComponentRef<typeof Button>,
  | (CompanySelectorProps<{
      name: string
      aggregateId?: string
    }> & {
      allowCreate: true
    })
  | (CompanySelectorProps<{
      name: string
      aggregateId: string
    }> & {
      allowCreate: false
    })
>(({ value, onValueChange, defaultValue, disabled, onBlur, allowCreate, texts, className }, ref) => {
  type CompanyType = NonNullable<typeof value>
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState("")
  const [debouncedSearchValue] = useDebounce(search, 300)
  const [selectedCompany, setSelectedCompany] = useControllableState({
    prop: value,
    onChange: undefined,
    defaultProp: defaultValue,
  })

  // Fetch companies based on the debounced search value
  const { data: companies, isLoading } = api.companies.search.useQuery(
    { search: debouncedSearchValue, limit: 6 },
    {
      enabled: debouncedSearchValue.length > 1,
    }
  )

  const handleSelect = (newValue: CompanyType) => {
    setSelectedCompany(newValue)

    if (onValueChange) {
      // @ts-expect-error - We know the type is correct based on the component props
      onValueChange(newValue)
    }

    setOpen(false)
    onBlur?.()
  }

  return (
    <div className="grid gap-2">
      <Popover
        open={open}
        onOpenChange={(open) => {
          if (!open) {
            onBlur?.()
          }
          setOpen(open)
        }}
      >
        <PopoverTrigger asChild>
          <Button
            id="company-selector"
            disabled={disabled}
            ref={ref}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn("w-full justify-between", className)}
          >
            {selectedCompany?.name ?? texts.placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[25rem] p-0">
          <Command shouldFilter={false}>
            <CommandInput placeholder={texts.searchPlaceholder} value={search} onValueChange={setSearch} />
            <CommandList>
              {isLoading && <CommandLoading />}
              <CommandEmpty>{allowCreate ? texts.emptyStateWithCreate : texts.emptyStateWithoutCreate}</CommandEmpty>
              <CommandGroup>
                {companies?.map((company) => (
                  <CommandItem key={company.aggregateId} value={company.name} onSelect={() => handleSelect(company)}>
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedCompany?.aggregateId === company.aggregateId ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {company.name}
                  </CommandItem>
                ))}
                {allowCreate && (
                  <>
                    {selectedCompany && selectedCompany.aggregateId === undefined && (
                      <CommandItem
                        key="selected"
                        value={selectedCompany.name}
                        onSelect={() => handleSelect(selectedCompany)}
                      >
                        <Check className={cn("mr-2 h-4 w-4", "opacity-100")} />
                        {selectedCompany.name}
                      </CommandItem>
                    )}
                    {search !== selectedCompany?.name && search.length > 1 && companies?.length === 0 && (
                      <CommandItem key="create" value={search} onSelect={() => handleSelect({ name: search })}>
                        <Check className={cn("mr-2 h-4 w-4", "opacity-0")} />
                        {search} <em>({texts.notInDatabase})</em>
                      </CommandItem>
                    )}
                  </>
                )}
              </CommandGroup>
              {companies?.length === 6 && (
                <CommandGroup>
                  <div className="px-2 py-1.5 text-xs text-muted-foreground">{texts.refineSearch}</div>
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
})

CompanySelector.displayName = "CompanySelector"
