"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { z } from "zod"
import { GeneralLayout } from "@/app/(general)/components"
import { LogoLarge } from "@/components/logo"
import { api } from "@/lib/trpc-provider"
import { localizedMessage, lz } from "@/utils/zod-localization"
import { AnimatePresence, motion } from "framer-motion"
import { AlertCircle, BarChart, Cpu, Leaf, Recycle, Wind, Zap } from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

import { Alert, AlertDescription, AlertTitle } from "@kreios/ui/alert"
import { Button } from "@kreios/ui/button"
import { Card, CardContent, CardDescription, CardHeader } from "@kreios/ui/card"
import { Form, useForm } from "@kreios/ui/form"
import { createFormFieldHelper } from "@kreios/ui/form/form-field-helper"
import { Link } from "@kreios/ui/link"

const formSchema = lz.object({
  firstName: lz
    .string()
    .max(50, localizedMessage("First name cannot exceed 50 characters", "Eesnimi ei tohi ületada 50 tähemärki"))
    .optional(),
  lastName: lz
    .string()
    .max(50, localizedMessage("Last name cannot exceed 50 characters", "Perekonnanimi ei tohi ületada 50 tähemärki"))
    .optional(),
  termsAndPrivacy: lz.boolean().refine((val: boolean) => val === true, {
    message: localizedMessage(
      "You must accept the Terms and Privacy Agreement",
      "Te peate nõustuma kasutustingimuste ja privaatsuspoliitikaga"
    ),
  }),
  dataProcessing: lz.boolean().refine((val: boolean) => val === true, {
    message: localizedMessage(
      "You must accept the Data Processing agreement",
      "Te peate nõustuma andmetöötluse lepinguga"
    ),
  }),
  disclaimer: lz.boolean().refine((val: boolean) => val === true, {
    message: localizedMessage("You must accept the Disclaimer", "Te peate nõustuma lahtiütlusega"),
  }),
})

const FormField = createFormFieldHelper(formSchema)

const animationItems = [
  { Icon: Cpu, color: "text-blue-500" },
  { Icon: Leaf, color: "text-green-500" },
  { Icon: Recycle, color: "text-green-600" },
  { Icon: Wind, color: "text-blue-400" },
  { Icon: Zap, color: "text-yellow-500" },
  { Icon: BarChart, color: "text-purple-500" },
]

export default function ConsentForm() {
  const t = useTranslations("consent")
  const router = useRouter()
  const { mutateAsync, isPending, error } = api.user.update.useMutation()
  const [user] = api.user.get.useSuspenseQuery()

  const form = useForm({
    schema: formSchema,
    defaultValues: {
      firstName: user.firstName,
      lastName: user.lastName,
      termsAndPrivacy: false,
      dataProcessing: false,
      disclaimer: false,
    },
  })

  const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await Promise.all([
        delay(3000),
        mutateAsync({
          firstName: values.firstName?.trim() ?? "",
          lastName: values.lastName?.trim() ?? "",
          hasGivenConsent: true,
        }),
      ])
      router.push("/admin")
    } catch (error) {
      console.error("Error updating user consent:", error)
    }
  }

  return (
    <>
      <GeneralLayout.FormContainer>
        <div className="mx-auto grid w-full max-w-[28.5rem] gap-6">
          <Card className="rounded-none border-0 shadow-none">
            <CardHeader>
              <LogoLarge className="h-12 lg:hidden" />

              <CardDescription className="text-base">{t("description")}</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
                  <div className="space-y-4">
                    <h2 className="text-lg font-semibold">{t("usersDetailLabel")}</h2>
                    {user.companyId && "companyName" in user && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium">{t("companyLabel")}</label>
                        <p className="text-sm text-muted-foreground">{user.companyName}</p>
                      </div>
                    )}
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">{t("emailLabel")}</label>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">{t("firstNameLabel")}</label>
                        <FormField name="firstName" label="" />
                      </div>
                      <div>
                        <label className="text-sm font-medium">{t("lastNameLabel")}</label>
                        <FormField name="lastName" label="" />
                      </div>
                    </div>
                  </div>

                  <FormField.Checkbox
                    name="termsAndPrivacy"
                    label={t("termsAndPrivacyAgreementLabel")}
                    required
                    description={t.rich("termsAndPrivacyAgreementDescription", {
                      termsLink: (chunks) => (
                        <Link className="text-blue-600" href="/terms-of-service">
                          {chunks}
                        </Link>
                      ),
                      privacyLink: (chunks) => (
                        <Link className="text-blue-600" href="https://www.impactly.eu/privacy-policy.html">
                          {chunks}
                        </Link>
                      ),
                    })}
                  />

                  <FormField.Checkbox
                    name="dataProcessing"
                    label={t("dataProcessingLabel")}
                    required
                    description={t("dataProcessingDescription")}
                  />

                  <FormField.Checkbox
                    name="disclaimer"
                    label={t("disclaimerLabel")}
                    required
                    description={t("disclaimerDescription")}
                  />

                  {error && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>{t("error")}</AlertTitle>
                      {typeof error.message === "string" && <AlertDescription>{error.message}</AlertDescription>}
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    disabled={!form.formState.isValid || isPending}
                    className="w-full bg-green-600 text-white hover:bg-green-700 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isPending ? (
                      <span className="flex items-center gap-2">
                        <span className="animate-spin">⌛</span> {t("processing")}
                      </span>
                    ) : (
                      t("register")
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </GeneralLayout.FormContainer>

      <AnimatePresence>
        {form.formState.isSubmitting && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden bg-green-50 bg-opacity-80"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {animationItems.map((item, index) => (
              <motion.div
                key={index}
                className={`absolute ${item.color}`}
                initial={{
                  scale: 0,
                  x: Math.random() * 200 - 100,
                  y: Math.random() * 200 - 100,
                  rotate: Math.random() * 360,
                }}
                animate={{
                  scale: [0, 1, 1, 0],
                  x: [null, Math.random() * 200 - 100, Math.random() * 200 - 100, Math.random() * 200 - 100],
                  y: [null, Math.random() * 200 - 100, Math.random() * 200 - 100, Math.random() * 200 - 100],
                  rotate: [null, Math.random() * 360, Math.random() * 360, Math.random() * 360],
                }}
                transition={{
                  repeat: Infinity,
                  duration: 4,
                  delay: index * 0.2,
                  ease: "easeInOut",
                }}
              >
                <item.Icon className="h-8 w-8 md:h-12 md:w-12" />
              </motion.div>
            ))}
            <motion.div
              className="px-4 text-center text-xl font-bold text-green-700 md:text-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.5 }}
            >
              {t("processingMessage")}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
