/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { redirect } from "next/navigation"
import { GeneralLayout } from "@/app/(general)/components"
import { LanguageSelector } from "@/components/language-selector"
import { HeroImage, LogoLarge } from "@/components/logo"
import { getPortalUser, withConsent } from "@/utils/with-consent"
import { getTranslations } from "next-intl/server"

import { auth } from "@kreios/auth"
import { AuthSwitch } from "@kreios/auth/ui/auth-switch"
import { LogoutButton } from "@kreios/auth/ui/logout-button"
import { ButtonLink } from "@kreios/ui/button-link"

import ConsentForm from "./consent-form"

const LayoutButton = async () => {
  const portalUser = await getPortalUser()
  const t = await getTranslations("common")
  return (
    <AuthSwitch>
      {portalUser?.hasGivenConsent ? (
        <ButtonLink
          href="/admin"
          variant="ghost"
          className="absolute right-4 top-4 z-10 lg:right-[calc(50%+2rem)] lg:top-8"
        >
          {t("toDashboard")}
        </ButtonLink>
      ) : (
        <LogoutButton variant="ghost" className="absolute right-4 top-4 z-10 lg:right-[calc(50%+2rem)] lg:top-8" />
      )}
    </AuthSwitch>
  )
}

export default withConsent(async () => {
  const t = await getTranslations("common")
  const session = await auth()
  if (!session?.user) redirect("/auth/login?callbackUrl=/admin")

  return (
    <div className="min-h-screen w-full bg-background lg:overflow-hidden">
      <LayoutButton />
      <GeneralLayout>
        <GeneralLayout.FormContainer>
          <div className="x mx-auto grid w-full max-w-[28.5rem] gap-6">
            <ConsentForm />
          </div>
        </GeneralLayout.FormContainer>{" "}
        <GeneralLayout.ImageContainer
          branding={
            <div className="flex w-full flex-col items-center px-6 text-center">
              <LogoLarge className="h-6 text-primary sm:h-8" />
              <h1 className="mt-2 font-mono text-2xl font-bold text-primary sm:text-3xl">{t("clientPortal")}</h1>
              <HeroImage className="mt-6 w-full max-w-md lg:w-1/2" />
            </div>
          }
        >
          <div className="absolute right-4 top-4 md:right-8 md:top-8">
            <LanguageSelector />
          </div>
        </GeneralLayout.ImageContainer>
      </GeneralLayout>
    </div>
  )
})
