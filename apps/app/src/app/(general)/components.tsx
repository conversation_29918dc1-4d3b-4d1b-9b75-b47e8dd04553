import type { ComponentProps, FC, ReactNode } from "react"

import { cn } from "@kreios/ui"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
const FormContainer: FC<ComponentProps<"div">> = ({ className, children, ...props }) => (
  <div
    className={cn(
      "flex min-h-screen w-full flex-col items-center justify-center overflow-y-auto px-4 py-8 lg:h-full lg:px-8",
      className
    )}
    {...props}
  >
    {children}
  </div>
)

const ImageContainer: FC<
  Omit<ComponentProps<"div">, "children"> & {
    branding: ReactNode
    children?: ReactNode
  }
> = ({ branding, children, className, ...props }) => (
  <div
    className={cn(
      "relative hidden h-full items-center justify-center overflow-hidden bg-muted/10 text-primary-foreground lg:flex",
      className
    )}
    {...props}
  >
    {branding}
    {children}
  </div>
)

export function GeneralLayout({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div
      className={cn(
        "flex min-h-screen w-full flex-col-reverse lg:fixed lg:inset-0 lg:grid lg:h-screen lg:grid-cols-2",
        className
      )}
    >
      {children}
    </div>
  )
}

GeneralLayout.FormContainer = FormContainer
GeneralLayout.ImageContainer = ImageContainer
