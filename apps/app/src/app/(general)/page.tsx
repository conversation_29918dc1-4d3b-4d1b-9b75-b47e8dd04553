/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { redirect } from "next/navigation"
import { CustomAuthenticationForm } from "@/components/auth/custom-authentication-form"
import { LanguageSelector } from "@/components/language-selector"
import { HeroImage, LogoLarge } from "@/components/logo"
import { getPortalUserByEmail } from "@impactly/domain/portal-users/utils/get-portal-user-by-email"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { getTranslations } from "next-intl/server"

import { auth } from "@kreios/auth"
import { LoginLayout } from "@kreios/auth/ui/pages/login"

export default async function RootPage({ searchParams }: { searchParams: { callbackUrl?: string } }) {
  const session = await auth()

  // If user is logged in, redirect to the appropriate admin page based on role
  if (session?.user.email) {
    const portalUser = await getPortalUserByEmail(session.user.email)

    if (portalUser) {
      const { isAdmin, isClientAdmin, isCompanyUser, isObserver } = getPortalUserRoles(portalUser)

      // Redirect based on user role - same logic as in withConsent utility
      if (isAdmin || isClientAdmin) {
        redirect("/admin/companies")
      } else if (isCompanyUser || isObserver) {
        redirect(`/admin/companies/${portalUser.companyId}`)
      } else {
        // Default admin path for other roles
        redirect("/admin")
      }
    }
  }

  // If not logged in, show login page
  const t = await getTranslations("auth.login")

  const logo = (
    <div className="flex w-fit flex-col items-center">
      <LogoLarge className="h-8 text-primary" />
      <h1 className="font-mono text-3xl font-bold text-primary">{t("clientPortal")}</h1>
      <HeroImage className="w-1/2" />
    </div>
  )

  return (
    <div className="h-screen">
      <LoginLayout className="h-full">
        <LoginLayout.FormContainer>
          <CustomAuthenticationForm
            callbackUrl={searchParams.callbackUrl ?? "/admin"}
            branding={logo}
            content={{
              title: t("title"),
              email: t("email"),
              oauth: t("oauth"),
              oauthMultiple: t("oauthMultiple"),
              emailLabel: t("emailLabel"),
              emailPlaceholder: t("emailPlaceholder"),
              signInWithEmail: t("title"),
              sendingEmail: t("sendingEmail"),
              userNotFound: t("userNotFound"),
              requestAccess: t("requestAccess"),
            }}
          />
        </LoginLayout.FormContainer>
        <LoginLayout.ImageContainer className="relative bg-inherit" branding={logo}>
          <div className="absolute right-4 top-4 md:right-8 md:top-8">
            <LanguageSelector />
          </div>
        </LoginLayout.ImageContainer>
      </LoginLayout>
    </div>
  )
}

/** Company Evaluation Page */

// import { useState } from "react"
// import { GeneralLayout } from "@/app/(general)/components"
// import { LogoLarge } from "@/components/logo"
// import { api } from "@/lib/trpc-provider"
// import { localizedMessage, lz } from "@/utils/zod-localization"
// import { AnimatePresence, motion } from "framer-motion"
// import { BarChart, Cpu, Leaf, Recycle, Wind, Zap } from "lucide-react"
// import { useTranslations } from "next-intl"
// import { z } from "zod"

// import { Button } from "@kreios/ui/button"
// import { Card, CardContent, CardDescription, CardHeader } from "@kreios/ui/card"
// import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@kreios/ui/dialog"
// import { Form, useForm } from "@kreios/ui/form"
// import { ReactHookFormDevTool } from "@kreios/ui/form/form-dev-tool"
// import { createFormFieldHelper } from "@kreios/ui/form/form-field-helper"
// import { Link } from "@kreios/ui/link"
// import { toast } from "@kreios/ui/sonner"

// import { CompanySelector } from "./company-selector"

// const formSchema = lz.object({
//   company: lz.object({
//     aggregateId: lz.string().optional(),
//     name: lz.string().min(1, localizedMessage("Company name is required", "Ettevõtte nimi on kohustuslik")),
//   }),
//   email: lz.string().email(localizedMessage("Invalid email address", "Vigane e-posti aadress")),
//   name: lz.string(),
//   infoSustainability: lz.boolean(),
//   infoSupplyChain: lz.boolean(),
//   infoAI: lz.boolean(),
//   termsOfServiceAndPrivacyPolicy: z.literal(true, {
//     errorMap: () => ({
//       message: localizedMessage(
//         "You must agree to the Terms of Service and Privacy Policy",
//         "Te peate nõustuma kasutustingimuste ja privaatsuspoliitikaga"
//       ),
//     }),
//   }),
// })

// const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// const FormField = createFormFieldHelper(formSchema)

// const animationItems = [
//   { Icon: Cpu, color: "text-blue-500" },
//   { Icon: Leaf, color: "text-green-500" },
//   { Icon: Recycle, color: "text-green-600" },
//   { Icon: Wind, color: "text-blue-400" },
//   { Icon: Zap, color: "text-yellow-500" },
//   { Icon: BarChart, color: "text-purple-500" },
// ]

// export default function SubmissionForm() {
//   const t = useTranslations()

//   const form = useForm({
//     schema: formSchema,
//     mode: "onChange",
//     criteriaMode: "all",
//     defaultValues: {
//       companyId: "",
//       companyName: "",
//       name: "",
//       email: "",
//       infoSustainability: false,
//       infoSupplyChain: false,
//       infoAI: false,
//       // @ts-expect-error false is fine here since it show throw an error if the user doesn't check the checkbox
//       termsOfServiceAndPrivacyPolicy: false,
//     },
//   })

//   const [showNotification, setShowNotification] = useState(false)

//   const submitOrderMutation = api.order.submitOrder.useMutation()

//   const onSubmit = async ({ company, ...values }: z.infer<typeof formSchema>) => {
//     try {
//       await Promise.all([
//         delay(5000),
//         submitOrderMutation.mutateAsync({
//           ...values,
//           companyId: company.aggregateId,
//           companyName: company.name,
//         }),
//       ])
//       form.reset()
//       setShowNotification(true)
//     } catch (error) {
//       console.error("Error submitting order:", error)
//       toast.error(t("toast.error.submitOrderError"))
//     }
//   }

//   return (
//     <>
//       <GeneralLayout.FormContainer>
//         <div className="mx-auto grid w-full max-w-[28.5rem] gap-6">
//           <Card className="rounded-none border-0 shadow-none">
//             <CardHeader>
//               <LogoLarge className="h-12 lg:hidden" />
//               <CardDescription className="text-base">{t("submission.description")}</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <Form {...form}>
//                 <ReactHookFormDevTool control={form.control} />
//                 <form onSubmit={form.handleSubmit(onSubmit)} className="flex-start flex flex-col gap-4">
//                   <FormField
//                     name="company"
//                     label={t("submission.form.companyLabel")}
//                     render={({ field }) => (
//                       <CompanySelector
//                         allowCreate
//                         value={field.value}
//                         onValueChange={field.onChange}
//                         texts={{
//                           placeholder: t("submission.form.companyNamePlaceholder"),
//                           searchPlaceholder: t("submission.form.searchPlaceholder"),
//                           emptyStateWithCreate: t("submission.form.emptyStateWithCreate"),
//                           emptyStateWithoutCreate: t("submission.form.emptyStateWithoutCreate"),
//                           notInDatabase: t("submission.form.notInDatabase"),
//                           refineSearch: t("submission.form.refineSearch"),
//                         }}
//                       />
//                     )}
//                   />
//                   <FormField
//                     key="email"
//                     name="email"
//                     description={t("submission.form.emailDescription")}
//                     label={t("submission.form.emailLabel")}
//                     placeholder={t("submission.form.emailPlaceholder")}
//                   />
//                   <FormField
//                     required={false}
//                     key="name"
//                     name="name"
//                     optionalText={t("submission.form.optionalText")}
//                     label={t("submission.form.nameLabel")}
//                     placeholder={t("submission.form.namePlaceholder")}
//                   />

//                   <FormField.Checkbox
//                     name="infoSustainability"
//                     required={false}
//                     optionalText={t("submission.form.optionalText")}
//                     label={t("submission.form.sustainabilityLabel")}
//                     description={t("submission.form.sustainabilityDescription")}
//                   />
//                   <FormField.Checkbox
//                     name="infoSupplyChain"
//                     required={false}
//                     optionalText={t("submission.form.optionalText")}
//                     label={t("submission.form.supplyChainLabel")}
//                     description={t("submission.form.supplyChainDescription")}
//                   />
//                   <FormField.Checkbox
//                     name="infoAI"
//                     required={false}
//                     optionalText={t("submission.form.optionalText")}
//                     label={t("submission.form.aiLabel")}
//                     description={t("submission.form.aiDescription")}
//                   />

//                   <FormField.Checkbox
//                     required
//                     name="termsOfServiceAndPrivacyPolicy"
//                     label={t("submission.form.termsLabel")}
//                     description={
//                       <>
//                         {t.rich("submission.form.termsDescription", {
//                           termsLink: (chunks) => (
//                             <Link className="text-blue-600" href="/terms-of-service">
//                               {chunks}
//                             </Link>
//                           ),
//                           privacyLink: (chunks) => (
//                             <Link className="text-blue-600" href="https://www.impactly.eu/privacy-policy.html">
//                               {chunks}
//                             </Link>
//                           ),
//                         })}
//                       </>
//                     }
//                   />

//                   <Button
//                     disabled={form.formState.isSubmitting || !form.formState.isValid}
//                     type="submit"
//                     className="w-full bg-green-600 text-white hover:bg-green-700"
//                   >
//                     {form.formState.isSubmitting ? t("submission.form.processing") : t("submission.form.submit")}
//                   </Button>
//                 </form>
//               </Form>
//             </CardContent>
//           </Card>
//         </div>
//       </GeneralLayout.FormContainer>

//       <AnimatePresence>
//         {form.formState.isSubmitting && (
//           <motion.div
//             className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden bg-green-50 bg-opacity-80"
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             exit={{ opacity: 0 }}
//           >
//             {animationItems.map((item, index) => (
//               <motion.div
//                 key={index}
//                 className={`absolute ${item.color}`}
//                 initial={{
//                   scale: 0,
//                   x: Math.random() * 200 - 100,
//                   y: Math.random() * 200 - 100,
//                   rotate: Math.random() * 360,
//                 }}
//                 animate={{
//                   scale: [0, 1, 1, 0],
//                   x: [null, Math.random() * 200 - 100, Math.random() * 200 - 100, Math.random() * 200 - 100],
//                   y: [null, Math.random() * 200 - 100, Math.random() * 200 - 100, Math.random() * 200 - 100],
//                   rotate: [null, Math.random() * 360, Math.random() * 360, Math.random() * 360],
//                 }}
//                 transition={{
//                   repeat: Infinity,
//                   duration: 4,
//                   delay: index * 0.2,
//                   ease: "easeInOut",
//                 }}
//               >
//                 <item.Icon className="h-8 w-8 md:h-12 md:w-12" />
//               </motion.div>
//             ))}
//             <motion.div
//               className="px-4 text-center text-xl font-bold text-green-700 md:text-2xl"
//               initial={{ opacity: 0, y: 20 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ delay: 1, duration: 0.5 }}
//             >
//               {t("submission.processing.message")}
//             </motion.div>
//           </motion.div>
//         )}
//       </AnimatePresence>

//       <Dialog open={showNotification} onOpenChange={setShowNotification}>
//         <DialogContent>
//           <DialogHeader>
//             <DialogTitle>{t("dialog.title")}</DialogTitle>
//             <DialogDescription>
//               {t.rich("submission.dialog.description", {
//                 br: () => <br />,
//               })}
//             </DialogDescription>
//           </DialogHeader>
//           <DialogFooter className="sm:justify-center">
//             <Button onClick={() => setShowNotification(false)} className="w-full sm:w-auto">
//               {t("submission.dialog.close")}
//             </Button>
//           </DialogFooter>
//         </DialogContent>
//       </Dialog>
//     </>
//   )
// }
