/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { RequestAccessForm } from "@/components/auth/request-access-form"
import { LanguageSelector } from "@/components/language-selector"
import { HeroImage, LogoLarge } from "@/components/logo"
import { useTranslations } from "next-intl"

import { LoginLayout } from "@kreios/auth/ui/pages/login"

export default function RequestAccess({ searchParams }: { searchParams: { email?: string } }) {
  const t = useTranslations("auth.requestAccess")

  const logo = (
    <div className="flex w-fit flex-col items-center">
      <LogoLarge className="h-8 text-primary" />
      <h1 className="font-mono text-3xl font-bold text-primary">{t("clientPortal")}</h1>
      <HeroImage className="w-1/2" />
    </div>
  )

  return (
    <div className="h-screen">
      <LoginLayout className="h-full">
        <LoginLayout.FormContainer>
          <RequestAccessForm
            userEmail={searchParams.email}
            branding={logo}
            content={{
              title: t("title"),
              description: t("description"),
              emailLabel: t("emailLabel"),
              companyName: t("companyName"),
              cancel: t("cancel"),
              submit: t("submit"),
              successTitle: t("successTitle"),
              successMessage: t("successMessage"),
              backToLogin: t("backToLogin"),
            }}
          />
        </LoginLayout.FormContainer>
        <LoginLayout.ImageContainer className="relative bg-inherit" branding={logo}>
          <div className="absolute right-4 top-4 md:right-8 md:top-8">
            <LanguageSelector />
          </div>
        </LoginLayout.ImageContainer>
      </LoginLayout>
    </div>
  )
}
