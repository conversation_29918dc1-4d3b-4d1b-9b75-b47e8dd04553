"use server"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { ReactNode } from "react"
import { redirect } from "next/navigation"
import { LanguageSelector } from "@/components/language-selector"
import { HeroImage, LogoLarge } from "@/components/logo"
import { LogoutButton, RestrictedLayoutContainer, RestrictedLayoutImageContainer } from "@/components/restricted-access"
import { withConsent } from "@/utils/with-consent"
import { getTranslations } from "next-intl/server"

import { auth, signOut } from "@kreios/auth"
import { cn } from "@kreios/ui"

async function handleLogout() {
  "use server"
  await signOut({ redirectTo: "/auth/login?callbackUrl=/admin" })
}

function UnauthorizedLayout({ children, className }: { children: ReactNode; className?: string }) {
  return <div className={cn("h-full w-full lg:grid lg:grid-cols-2", className)}>{children}</div>
}

export default withConsent(async () => {
  const t = await getTranslations("unauthorized")
  const session = await auth()
  if (!session?.user) redirect("/auth/login?callbackUrl=/admin")
  const logo = (
    <div className="flex w-fit flex-col items-center">
      <LogoLarge className="h-8 text-primary" />
      <h1 className="font-mono text-3xl font-bold text-primary">{t("clientPortal")}</h1>
      <HeroImage className="w-1/2" />
    </div>
  )

  return (
    <div className="fixed inset-0 h-screen">
      <UnauthorizedLayout>
        <RestrictedLayoutContainer>
          <div className="mx-auto flex w-[22.5rem] flex-col items-center gap-8">
            <div className="flex items-center justify-center text-primary lg:hidden">
              <HeroImage className="w-32" />
            </div>
            <div className="grid gap-4 text-center">
              <h1 className="text-3xl font-bold text-gray-900">{t("title")}</h1>
              <p className="text-balance text-muted-foreground">{t("description")}</p>
            </div>
            <LogoutButton onLogout={handleLogout} buttonText={t("logout")} />
          </div>
        </RestrictedLayoutContainer>
        <RestrictedLayoutImageContainer branding={logo}>
          <div className="absolute right-4 top-4 md:right-8 md:top-8">
            <LanguageSelector />
          </div>
        </RestrictedLayoutImageContainer>
      </UnauthorizedLayout>
    </div>
  )
})

UnauthorizedLayout.FormContainer = RestrictedLayoutContainer
UnauthorizedLayout.ImageContainer = RestrictedLayoutImageContainer
