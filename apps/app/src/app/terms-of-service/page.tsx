/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import { LanguageSelector } from "@/components/language-selector"
import { LogoLarge } from "@/components/logo"
import { useTranslations } from "next-intl"

export default function Page() {
  const t = useTranslations("terms")

  return (
    <div className="min-h-screen w-full bg-background">
      <div className="container mx-auto w-full max-w-4xl px-4 py-8">
        <div className="mb-8 flex items-center justify-between">
          <LogoLarge className="h-8 text-primary sm:h-10" />
          <LanguageSelector />
        </div>

        <h1 className="mb-6 text-3xl font-bold text-primary sm:text-4xl">{t("title")}</h1>

        <div className="space-y-6 rounded-lg bg-card/50 p-6 shadow-sm backdrop-blur-sm">
          <section>
            <p className="font-semibold">{t("effectiveDate")}</p>
            <p className="mt-4">{t("welcome")}</p>
          </section>

          <Section title={t("sections.acceptance.title")}>
            <p>{t("sections.acceptance.content")}</p>
          </Section>

          <Section title={t("sections.demo.title")}>
            <p>{t("sections.demo.content")}</p>
          </Section>

          <Section title={t("sections.disclaimer.title")}>
            <p>{t("sections.disclaimer.content1")}</p>
            <p className="mt-2">{t("sections.disclaimer.content2")}</p>
          </Section>

          <Section title={t("sections.commercial.title")}>
            <p>{t("sections.commercial.content")}</p>
          </Section>

          <Section title={t("sections.intellectual.title")}>
            <p>{t("sections.intellectual.content")}</p>
          </Section>

          <Section title={t("sections.privacy.title")}>
            <p>{t("sections.privacy.content")}</p>
          </Section>

          <Section title={t("sections.conduct.title")}>
            <p>{t("sections.conduct.intro")}</p>
            <ul className="mt-2 list-disc space-y-2 pl-6">
              {(t.raw("sections.conduct.rules") as string[]).map((rule, index) => (
                <li key={index}>{rule}</li>
              ))}
            </ul>
          </Section>

          <Section title={t("sections.changes.title")}>
            <p>{t("sections.changes.content")}</p>
          </Section>

          <Section title={t("sections.law.title")}>
            <p>{t("sections.law.content")}</p>
          </Section>

          <Section title={t("sections.contact.title")}>
            <p>{t("sections.contact.email")}</p>
            <p>{t("sections.contact.address")}</p>
          </Section>

          <p className="mt-8 text-center font-medium text-primary">{t("thankYou")}</p>
        </div>
      </div>
    </div>
  )
}

function Section({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <section className="space-y-2">
      <h2 className="text-xl font-semibold text-primary">{title}</h2>
      <div className="text-muted-foreground">{children}</div>
    </section>
  )
}
