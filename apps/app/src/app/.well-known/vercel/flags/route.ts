/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { ApiData } from "@vercel/flags"
import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import * as flags from "@impactly/flags"
import { verifyAccess } from "@vercel/flags"
import { unstable_getProviderData as getProviderData } from "@vercel/flags/next"

export const runtime = "edge"
export const dynamic = "force-dynamic" // defaults to auto

export async function GET(request: NextRequest) {
  const access = await verifyAccess(request.headers.get("Authorization"))
  if (!access) return NextResponse.json(null, { status: 401 })

  return NextResponse.json<ApiData>(getProviderData(flags))
}
