/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { LanguageSelector } from "@/components/language-selector"
import { useTranslations } from "next-intl"

import { ErrorPage } from "@kreios/auth/ui/pages/error"

interface ErrorPageProps {
  params: { error?: string }
}

export default function AuthError({ params }: ErrorPageProps) {
  const t = useTranslations("auth.error")

  return (
    <ErrorPage
      params={params}
      headerActions={<LanguageSelector />}
      content={{
        title: t("title"),
        description: t("description"),
        returnToLogin: t("returnToLogin"),
      }}
    />
  )
}
