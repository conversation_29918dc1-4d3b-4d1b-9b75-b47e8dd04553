/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useTranslations } from "next-intl"

import { VerifyRequestPage } from "@kreios/auth/ui/pages/verify-request"

export default function VerifyRequest() {
  const t = useTranslations("auth.verifyRequest")

  return (
    <VerifyRequestPage
      content={{
        title: t("title"),
        description: t("description"),
        returnToLogin: t("returnToLogin"),
      }}
    />
  )
}
