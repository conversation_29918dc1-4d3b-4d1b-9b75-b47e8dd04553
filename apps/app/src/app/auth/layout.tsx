/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { ReactNode } from "react"
import NextTopLoader from "nextjs-toploader"

import { ThemeProvider } from "@kreios/admin-layout/components/theme-provider"
import { Toaster } from "@kreios/ui/sonner"
import { TooltipProvider } from "@kreios/ui/tooltip"

export default async function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
      <Toaster />
      <TooltipProvider>
        <NextTopLoader />
        <main className="grid h-[100dvh] grid-rows-[1fr] overflow-hidden">{children}</main>
      </TooltipProvider>
    </ThemeProvider>
  )
}
