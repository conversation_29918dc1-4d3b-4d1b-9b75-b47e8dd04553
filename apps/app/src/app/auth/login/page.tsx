/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { CustomAuthenticationForm } from "@/components/auth/custom-authentication-form"
import { LanguageSelector } from "@/components/language-selector"
import { HeroImage, LogoLarge } from "@/components/logo"
import { useTranslations } from "next-intl"

import { LoginLayout } from "@kreios/auth/ui/pages/login"

export default function Login({ searchParams }: { searchParams: { callbackUrl?: string } }) {
  const t = useTranslations("auth.login")

  const logo = (
    <div className="flex w-fit flex-col items-center">
      <LogoLarge className="h-8 text-primary" />
      <h1 className="font-mono text-3xl font-bold text-primary">{t("clientPortal")}</h1>
      <HeroImage className="w-1/2" />
    </div>
  )

  return (
    <LoginLayout className="h-full">
      <LoginLayout.FormContainer>
        <CustomAuthenticationForm
          callbackUrl={searchParams.callbackUrl}
          branding={logo}
          content={{
            title: t("title"),
            email: t("email"),
            oauth: t("oauth"),
            oauthMultiple: t("oauthMultiple"),
            emailLabel: t("emailLabel"),
            emailPlaceholder: t("emailPlaceholder"),
            signInWithEmail: t("signInWithEmail"),
            sendingEmail: t("sendingEmail"),
            userNotFound: t("userNotFound"),
            requestAccess: t("requestAccess"),
          }}
        />
      </LoginLayout.FormContainer>
      <LoginLayout.ImageContainer className="relative bg-inherit" branding={logo}>
        <div className="absolute right-4 top-4 md:right-8 md:top-8">
          <LanguageSelector />
        </div>
      </LoginLayout.ImageContainer>
    </LoginLayout>
  )
}
