/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { NextRequest } from "next/server"

export const runtime = "nodejs"

export const dynamic = "force-dynamic"
export const maxDuration = 300

const handler = async (request: NextRequest | Request, params?: unknown) => {
  const { requestHandler } = await import("@impactly/jobs")
  return requestHandler(request, params)
}

export const GET = handler
export const POST = handler
