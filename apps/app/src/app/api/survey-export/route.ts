/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { NextResponse } from "next/server"
import { service } from "@impactly/integration/backend"
import contentDisposition from "content-disposition"
import JSZip from "jszip"
import { isArray, isObject } from "lodash-es"
import unidecode from "unidecode"
import * as XLSX from "xlsx"
import { z } from "zod"

import { createIndexDataLoaderFactory } from "@kreios/api/utils"
import { auth } from "@kreios/auth"
import { capitalize } from "@kreios/utils/capitilize"
import { isTruthy } from "@kreios/utils/isTruthy"

type Row = [string, string, string, string]
type DataValue = string | number | boolean | object | null | undefined
type DataObject = Record<string, DataValue | DataValue[]>
type AttachmentDocument = {
  key: string
  name: string
}

type SurveyFormSections = {
  green_transition?: {
    sustainability_strategy?: {
      additional_documents?: AttachmentDocument[]
    }
    other_sustainability_reports?: {
      additional_documents?: AttachmentDocument[]
    }
  }
}
type AttachmentLocation = {
  section: keyof SurveyFormSections
  path: string[]
  accessor: (data: SurveyFormSections) => AttachmentDocument[] | undefined
}

const formatCategory = (s: string) => s.split("_").map(capitalize).join(" ")
const formatQuestionName = (s: string) => s.split("_").map(capitalize).join(" ")

export const maxDuration = 300

// Helper function to process data recursively
function processData(data: DataObject | null = null): Row[] {
  const header: Row = ["Category", "ID", "Question", "Answer"]
  if (!data) return [header]

  const rows: Row[] = [header]

  const addRow = (category: string, path: string, key: string, value: DataValue): void => {
    rows.push([category, path, formatQuestionName(key), String(value ?? "")])
  }

  const processObject = (obj: DataObject, category: string, parentPath = "") => {
    Object.entries(obj).forEach(([key, value]) => {
      const path = parentPath ? `${parentPath}.${key}` : key

      if (isArray(value)) {
        value.forEach((item, i) =>
          isObject(item)
            ? processObject(item as DataObject, category, `${path}.${i + 1}`)
            : addRow(category, `${path}.${i + 1}`, key, item as DataValue)
        )
        return
      }

      if (isObject(value)) {
        processObject(value as DataObject, category, path)
      } else {
        addRow(category, path, key, value)
      }
    })
  }

  Object.entries(data).forEach(([section, value]) => {
    if (isObject(value)) {
      processObject(value as DataObject, formatCategory(section))
    }
  })

  return rows
}

/*
 * Helper function to add attachments to ZIP
 */
async function addAttachments(formData: SurveyFormSections = {}) {
  const attachmentLocations: AttachmentLocation[] = [
    {
      section: "green_transition",
      path: ["sustainability_strategy"],
      accessor: (data) => data.green_transition?.sustainability_strategy?.additional_documents,
    },
    {
      section: "green_transition",
      path: ["other_sustainability_reports"],
      accessor: (data) => data.green_transition?.other_sustainability_reports?.additional_documents,
    },
  ]

  const attachments = await Promise.all(
    attachmentLocations.map(async (location) => {
      const documents = location.accessor(formData)
      if (!documents?.length) return []

      return await Promise.all(
        documents.map(async (doc) => {
          try {
            const fileUrl = await service.getFileUrl({ filePath: doc.key })
            const response = await fetch(fileUrl)
            const buffer = await response.arrayBuffer()
            return {
              name: `attachments/${location.path.join("/")}/${doc.name}`,
              buffer,
            }
          } catch (error) {
            console.error(`Failed to fetch file ${doc.name}:`, error)
            throw error
          }
        })
      )
    })
  )

  return attachments.flat()
}

/*
 * Function that returns a list of files to be added to the ZIP
 * 1. Excel file with the survey responses
 * 2. Attachments
 */
const getSurveyFiles = async (formData: SurveyFormSections) => {
  // Create Excel file
  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.aoa_to_sheet(processData(formData))
  worksheet["!cols"] = [{ wch: 25 }, { wch: 25 }, { wch: 50 }, { wch: 100 }]
  XLSX.utils.book_append_sheet(workbook, worksheet, "Survey Responses")

  // Add Excel to ZIP
  const excelBuffer = XLSX.write(workbook, { type: "array", bookType: "xlsx" }) as Uint8Array

  // Add attachments
  const attachments = await addAttachments(formData)

  return [
    {
      name: "survey_responses.xlsx",
      buffer: excelBuffer,
    },
    ...attachments,
  ]
}

/*
 * Helper function to convert a Node.js stream to a Web stream
 */
const nodeStreamToWebStream = (stream: NodeJS.ReadableStream) => {
  return new ReadableStream({
    start(controller) {
      stream.on("data", (chunk) => controller.enqueue(chunk))
      stream.on("end", () => controller.close())
      stream.on("error", (err) => controller.error(err))
    },
  })
}

export const POST = auth(async (req) => {
  try {
    // importing here to make the dependency injection via instrumentation.ts work
    const [{ surveyEventStore }, { getPortalUserByEmail }, { getPortalUserRoles }, { surveyBatchEventStore }] =
      await Promise.all([
        import("@impactly/domain/surveys/store"),
        import("@impactly/domain/portal-users/utils/get-portal-user-by-email"),
        import("@impactly/domain/portal-users/utils/get-portal-user-roles"),
        import("@impactly/domain/survey-batches/store"),
      ])

    if (!req.auth?.user.email) return NextResponse.json({ error: "Unauthorized" }, { status: 401 })

    const portalUser = await getPortalUserByEmail(req.auth.user.email)

    if (!portalUser) return NextResponse.json({ error: "Unauthorized" }, { status: 401 })

    const { isClientAdmin, isAdmin } = getPortalUserRoles(portalUser)

    if (!(isClientAdmin || isAdmin))
      return NextResponse.json({ error: "You are not allowed to access this resource" }, { status: 403 })

    const safeParams = z
      .object({ id: z.union([z.string().transform((id) => [id]), z.string().array()]) })
      .safeParse(await req.json())

    if (!safeParams.success) return NextResponse.json({ error: "Invalid survey ID" }, { status: 400 })

    const createIndexDataLoader = createIndexDataLoaderFactory(container.resolve("gateway"))
    const companyLoader = createIndexDataLoader("companies")

    const surveysResult = await Promise.allSettled(
      safeParams.data.id.map(async (id) => {
        const { aggregate } = await surveyEventStore.getExistingAggregate(id)

        if (!aggregate.companyId) throw new Error("Company ID is required")

        return {
          ...aggregate,
          company: await companyLoader.load(aggregate.companyId),
        }
      })
    )

    if (surveysResult.some((r) => r.status === "rejected"))
      return NextResponse.json({ error: "Survey not found" }, { status: 404 })

    const surveys = surveysResult.map((r) => (r.status === "fulfilled" ? r.value : null)).filter(isTruthy)

    if (!surveys.length) return NextResponse.json({ error: "Survey not found" }, { status: 404 })

    let filename = ""

    // if there is only one survey, use the company name as the zip filename
    if (surveys.length === 1) {
      filename = surveys[0].company.name.toLowerCase()
    } else {
      // if there are multiple surveys, check if they are part of the same batch
      const batchId = surveys[0].batchId

      if (surveys.some(({ batchId }) => batchId !== batchId))
        return NextResponse.json({ error: "Surveys are not part of the same batch" }, { status: 400 })

      const { aggregate: batch } = await surveyBatchEventStore.getExistingAggregate(batchId)

      filename = batch.name.toLowerCase()
    }

    const surveyAttachments = await Promise.all(
      surveys.map(({ formData }) => getSurveyFiles(formData as SurveyFormSections))
    )

    const zip = new JSZip()

    // if only one survey, add all files to the root of the zip
    if (surveys.length === 1)
      surveyAttachments[0].forEach(({ name, buffer }) => {
        zip.file(name, buffer)
      })
    else
      // if there are multiple surveys, add all files to subfolder named after the company
      surveyAttachments.forEach((surveyAttachments, index) => {
        const survey = surveys[index]
        surveyAttachments.forEach(({ name, buffer }) => {
          zip.file(`${survey.company.name.toLowerCase()}/${name}`, buffer)
        })
      })

    const zipStream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true })

    const webStream = nodeStreamToWebStream(zipStream)

    return new Response(webStream, {
      headers: {
        "Content-Type": "application/zip",
        "Content-Disposition": contentDisposition(`${unidecode(filename)}.zip`, {
          type: "attachment",
        }),
      },
    })
  } catch (error) {
    console.error("Export error:", error)
    return NextResponse.json({ error: "Failed to upload file" }, { status: 500 })
  }
})
