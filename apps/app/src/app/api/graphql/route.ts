/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { NextResponse } from "next/server"
import { env } from "@/env"
import { createYoga } from "graphql-yoga"

import { auth } from "@kreios/auth"

const getHandler = async () => {
  const { schema } = await import("@impactly/graphql/schema/index")

  const { handleRequest } = createYoga({
    schema,
    context: ({ request }) => {
      // Securing all requests to the GraphQL API using an API key
      const apiKey = request.headers.get("authorization")
      if (!apiKey || !env.GRAPHQL_API_KEYS.includes(apiKey)) throw new Error("Unauthorized")

      return {}
    },
    plugins: [
      {
        onResponse: ({ response }) => {
          response.headers.set("Cache-Control", "no-store, max-age=0, must-revalidate")
          response.headers.set("Pragma", "no-cache")
          response.headers.set("Expires", "0")
        },
      },
    ],
    graphiql: {
      defaultQuery: `
      query {
        // Add a default query for Impactly here
      }
    `,
      additionalHeaders: {
        authorization: env.GRAPHQL_API_KEYS.at(0)!,
      },
    },
    graphqlEndpoint: "/api/graphql",
    fetchAPI: { Response },
  })

  return handleRequest
}

// Securing the GraphiQL interface using the auth middleware
export const GET = auth(async (req) => {
  if (!req.auth) return NextResponse.redirect(new URL("/auth/login?callbackUrl=/api/graphql", req.url))

  const handleRequest = await getHandler()

  return handleRequest(req, {})
})

const handleRequest = async (req: Request) => {
  const handleRequest = await getHandler()

  return handleRequest(req, {})
}

export { handleRequest as POST, handleRequest as OPTIONS }
