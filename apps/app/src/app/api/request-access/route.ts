/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { NextResponse } from "next/server"
import { env } from "@/env"
import { generateRequestAccessNotificationEmail } from "@impactly/email"
import { z } from "zod"

// Import types from mail-sender package
import type { MailOptions } from "@kreios/mail-sender"
import { MailSender } from "@kreios/mail-sender"
import { ResendProvider } from "@kreios/mail-sender/providers"

// Define the input schema for the API
const requestAccessSchema = z.object({
  email: z.string().email("Invalid email address"),
  companyName: z.string().min(1, "Company name is required"),
})

// Export the schema type for use in other files
export type RequestAccessInput = z.infer<typeof requestAccessSchema>

/**
 * Validates the request body against the schema
 * @param body The request body to validate
 * @returns A validation result object
 */
const validateRequestBody = (body: unknown) => {
  return requestAccessSchema.safeParse(body)
}

/**
 * Formats validation errors into a user-friendly format
 * @param error The Zod validation error
 * @returns A formatted error object
 */
const formatValidationErrors = (error: z.ZodError) => {
  const errorMap = error.flatten().fieldErrors
  const formattedErrors: Record<string, string> = {}

  // Check for email errors
  if (errorMap.email?.length) {
    formattedErrors.email = errorMap.email[0]
  }

  // Check for company name errors
  if (errorMap.companyName?.length) {
    formattedErrors.companyName = errorMap.companyName[0]
  }

  return {
    message: "Validation failed",
    fieldErrors: formattedErrors,
  }
}

/**
 * Validates email addresses and filters out invalid ones
 * @param emails Array of email addresses to validate
 * @returns Array of valid email addresses
 */
const validateEmails = (emails: string[]): string[] => {
  const emailSchema = z.string().email()
  return emails.filter((email) => {
    const result = emailSchema.safeParse(email)
    if (!result.success) {
      console.warn(`Invalid email address ignored: ${email}`)
      return false
    }
    return true
  })
}

/**
 * Gets the list of recipient emails from environment or fallback
 * @returns Array of valid recipient email addresses
 */
const getRecipientEmails = (): string[] => {
  // Get contact list from environment variable
  const contactList = env.REQUEST_ACCESS_CONTACT_LIST

  if (contactList.length > 0) {
    console.log(`Using contact list from environment: ${contactList.length} emails`)
    return validateEmails(contactList)
  }

  // Fallback to RESEND_FROM_EMAIL if no contact list is configured
  if (env.RESEND_FROM_EMAIL) {
    console.log("No contact list configured, falling back to RESEND_FROM_EMAIL")
    return validateEmails([env.RESEND_FROM_EMAIL])
  }

  throw new Error("No recipient emails configured. Please set REQUEST_ACCESS_CONTACT_LIST or RESEND_FROM_EMAIL.")
}

/**
 * Processes the request access request
 * @param req The incoming request
 * @returns The response
 */
const processRequestAccessRequest = async (req: Request) => {
  try {
    // Parse request body
    const rawBody: unknown = await req.json()

    console.log("---------------------------------------------------------------------")
    console.log("Request Access - Raw Body: ", { rawBody: JSON.stringify(rawBody) })
    console.log("---------------------------------------------------------------------")

    // Validate the request body
    const validationResult = validateRequestBody(rawBody)

    if (!validationResult.success) {
      console.log("Validation failed:", validationResult.error.flatten())
      return NextResponse.json(
        {
          error: "Validation failed",
          details: formatValidationErrors(validationResult.error),
        },
        { status: 400 }
      )
    }

    // ===== ACTUAL IMPLEMENTATION =====

    // 1. Extract the validated data
    const { email, companyName } = validationResult.data

    // 2. Get recipient emails
    const recipientEmails = getRecipientEmails()

    if (recipientEmails.length === 0) {
      throw new Error("No valid recipient emails found")
    }

    console.log(`Sending request access notification to ${recipientEmails.length} recipients`)

    // 3. Initialize the mail sender
    if (!env.RESEND_API_KEY || !env.RESEND_FROM_EMAIL) {
      throw new Error("Resend API key or from email is not configured. Please set the necessary environment variables.")
    }

    // Initialize the mail sender with Resend provider
    const resendProvider: ResendProvider = new ResendProvider(env.RESEND_API_KEY)

    // Create the mail sender
    const mailSender: MailSender = new MailSender(resendProvider, {
      name: env.AUTH_EMAIL_NAME ?? "Impactly",
      email: env.RESEND_FROM_EMAIL,
    })

    // 4. Generate email content
    const emailContent = await generateRequestAccessNotificationEmail(email, companyName)

    // 5. Send individual emails to each recipient
    const emailResults = []
    const emailErrors = []

    for (const recipientEmail of recipientEmails) {
      try {
        // Create individual email options for each recipient
        const emailOptions: MailOptions = {
          to: [{ name: recipientEmail, email: recipientEmail }],
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
        }

        console.log(`Sending individual email to: ${recipientEmail}`)

        const result = await mailSender.send(emailOptions)
        emailResults.push({ recipient: recipientEmail, success: true, result })

        console.log(`Email sent successfully to: ${recipientEmail}`)
      } catch (error) {
        console.error(`Error sending email to ${recipientEmail}:`, error)
        emailErrors.push({
          recipient: recipientEmail,
          error: error instanceof Error ? error.message : "Unknown error",
        })
      }
    }

    console.log("---------------------------------------------------------------------")
    console.log("Email sending completed:", {
      successful: emailResults.length,
      failed: emailErrors.length,
      results: emailResults,
      errors: emailErrors,
    })
    console.log("---------------------------------------------------------------------")

    // 6. Return response based on results
    if (emailResults.length > 0) {
      return NextResponse.json({
        message: "Request access notifications sent",
        totalRecipients: recipientEmails.length,
        successfulSends: emailResults.length,
        failedSends: emailErrors.length,
        status: emailErrors.length === 0 ? "sent" : "partial",
        errors: emailErrors.length > 0 ? emailErrors : undefined,
      })
    } else {
      return NextResponse.json(
        {
          error: "Failed to send notifications to any recipients",
          message: "All email sends failed",
          errors: emailErrors,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error processing request access request:", error)
    return NextResponse.json(
      {
        error: "Failed to process request",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * Handles POST requests to send request access notifications
 * This endpoint is public and doesn't require authentication
 */
export const POST = async (req: Request) => {
  return processRequestAccessRequest(req)
}
