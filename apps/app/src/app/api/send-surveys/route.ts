/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { NextResponse } from "next/server"
import { env } from "@/env"
import { generateSurveyInvitationEmail } from "@impactly/email"
import { z } from "zod"

// Import types from mail-sender package
import type { MailOptions } from "@kreios/mail-sender"
import { MailSender } from "@kreios/mail-sender"
import { ResendProvider } from "@kreios/mail-sender/providers"

// Define the survey template enum
const SurveyTemplateEnum = z.enum(["UNIFIED_QUESTIONNAIRE_V1", "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"])

// Define the schema for individual survey items
const surveyItemSchema = z.object({
  id: z.number(),
  company_name: z.string(),
  contact_emails: z.array(z.string().email()).min(1, "At least one email address is required"),
})

// Define the input schema for the API
const sendSurveySchema = z.object({
  template: SurveyTemplateEnum,
  surveys: z.array(surveyItemSchema).min(1, "At least one survey is required"),
})

// Export the schema types for use in other files
export type SurveyItem = z.infer<typeof surveyItemSchema>
export type SendSurveyInput = z.infer<typeof sendSurveySchema>

/**
 * Validates the request body against the schema
 * @param body The request body to validate
 * @returns A validation result object
 */
const validateRequestBody = (body: unknown) => {
  return sendSurveySchema.safeParse(body)
}

/**
 * Formats Zod validation errors into a user-friendly format
 * @param error The Zod validation error
 * @returns An object with formatted error messages
 */
const formatValidationErrors = (error: z.ZodError): Record<string, unknown> => {
  const formattedErrors: Record<string, unknown> = {}

  // Extract errors from the ZodError
  const errorMap = error.flatten().fieldErrors
  const formErrors = error.flatten().formErrors

  // Check for template errors
  if (errorMap.template?.length) {
    formattedErrors.template = "Invalid Template"
  }

  // Check for surveys array errors
  if (errorMap.surveys?.length) {
    const errorMessage = errorMap.surveys[0]
    if (errorMessage.includes("at least")) {
      formattedErrors.surveys = "At least one survey is required"
    } else {
      formattedErrors.surveys = "Invalid surveys format"
    }
  }

  // Check for errors in individual survey items
  if (typeof errorMap.surveys === "object" && !Array.isArray(errorMap.surveys)) {
    const surveyErrors: Record<string, Record<string, string>> = {}

    // Type for survey error object
    type SurveyErrorMap = {
      id?: string[]
      company_name?: string[]
      contact_emails?: string[]
      [key: string]: unknown
    }

    // Process each survey item's errors
    const surveysErrorMap = errorMap.surveys as Record<string, unknown>
    // Use Object.entries to avoid the unnecessary conditional warning
    Object.entries(surveysErrorMap).forEach(([index, surveyErrorObj]) => {
      if (!isNaN(Number(index))) {
        const surveyIndex = Number(index)

        if (surveyErrorObj && typeof surveyErrorObj === "object") {
          const surveyError = surveyErrorObj as SurveyErrorMap
          const itemErrors: Record<string, string> = {}

          // Process ID errors
          if (surveyError.id && surveyError.id.length > 0) {
            itemErrors.id = "Invalid ID format"
          }

          // Process company_name errors
          if (surveyError.company_name && surveyError.company_name.length > 0) {
            itemErrors.company_name = "Company name is required"
          }

          // Process contact_emails errors
          if (surveyError.contact_emails && surveyError.contact_emails.length > 0) {
            const emailError = surveyError.contact_emails[0]
            if (typeof emailError === "string" && emailError.includes("at least")) {
              itemErrors.contact_emails = "At least one email is required"
            } else {
              itemErrors.contact_emails = "Invalid email format"
            }
          }

          if (Object.keys(itemErrors).length > 0) {
            surveyErrors[`survey_${surveyIndex}`] = itemErrors
          }
        }
      }
    })

    if (Object.keys(surveyErrors).length > 0) {
      formattedErrors.survey_items = surveyErrors as unknown as Record<string, string>
    }
  }

  // If we have no specific errors but validation failed, add a generic error
  if (Object.keys(formattedErrors).length === 0 && formErrors.length > 0) {
    formattedErrors.form = formErrors[0]
  } else if (Object.keys(formattedErrors).length === 0) {
    formattedErrors.form = "Invalid input data"
  }

  return formattedErrors
}

/**
 * Creates an error response with formatted validation errors
 * @param error The Zod validation error
 * @returns A NextResponse with formatted errors
 */
const createValidationErrorResponse = (error: z.ZodError) => {
  const formattedErrors = formatValidationErrors(error)
  return NextResponse.json({ errors: formattedErrors }, { status: 400 })
}

/**
 * Verifies API key authentication using the CRON_SECRET environment variable
 * @param request The incoming request
 * @returns True if the Authorization header contains a valid API key, false otherwise
 */
const verifyApiKeyAuth = (request: Request): boolean => {
  const authHeader = request.headers.get("authorization")
  // Use CRON_SECRET for API key authentication
  return authHeader === `Bearer ${env.CRON_SECRET}`
}

// The generateSurveyInvitationEmail function is imported from @impactly/email

// Define types for the new request format
interface NewSurveyItem {
  company_name: string
  company_contacts: string[]
  survey_id: number
}

interface NewRequestBody {
  template: string
  surveys: Record<string, NewSurveyItem>
}

/**
 * Transforms the new request format to match the expected schema format
 * @param body The request body in the new format
 * @returns The transformed body in the expected format
 */
const transformRequestBody = (body: unknown): unknown => {
  // Check if the body is in the new format (has surveys as an object with numeric keys)
  if (
    body &&
    typeof body === "object" &&
    "surveys" in body &&
    body.surveys &&
    typeof body.surveys === "object" &&
    !Array.isArray(body.surveys) &&
    "template" in body
  ) {
    const newFormatBody = body as NewRequestBody

    // Create a new body object with the same template
    const transformedBody: {
      template: string
      surveys: {
        id: number
        company_name: string
        contact_emails: string[]
      }[]
    } = {
      template: newFormatBody.template,
      surveys: [],
    }

    // Convert the surveys object to an array
    Object.entries(newFormatBody.surveys).forEach(([_, surveyData]) => {
      // Map the new format to the expected format
      transformedBody.surveys.push({
        id: surveyData.survey_id,
        company_name: surveyData.company_name,
        contact_emails: Array.isArray(surveyData.company_contacts) ? surveyData.company_contacts : [],
      })
    })

    return transformedBody
  }

  // If it's already in the expected format, return as is
  return body
}

/**
 * Processes the survey request
 * @param req The incoming request
 * @returns The response
 */
const processSurveyRequest = async (req: Request) => {
  try {
    // Parse request body
    const rawBody: unknown = await req.json()

    console.log("---------------------------------------------------------------------")
    console.log("Raw Body: ", { rawBody: JSON.stringify(rawBody) })
    console.log("---------------------------------------------------------------------")

    // Transform the body to match the expected schema format
    const transformedBody = transformRequestBody(rawBody)

    console.log("---------------------------------------------------------------------")
    console.log("Backend Payload TRansformed: ", { body: JSON.stringify(transformedBody) })
    console.log("---------------------------------------------------------------------")

    // Validate the transformed body
    const validationResult = validateRequestBody(transformedBody)

    // Handle validation errors
    if (!validationResult.success) {
      return createValidationErrorResponse(validationResult.error)
    }

    // ===== ACTUAL IMPLEMENTATION =====

    // 1. Extract the validated data (template and surveys array)
    const { template, surveys } = validationResult.data

    // 2. Count total recipients for reporting
    const totalRecipients = surveys.reduce((sum, survey) => sum + survey.contact_emails.length, 0)

    // 3. Initialize the mail sender
    if (!env.RESEND_API_KEY || !env.RESEND_FROM_EMAIL) {
      throw new Error("Resend API key or from email is not configured. Please set the necessary environment variables.")
    }

    //eslint-disable @typescript-eslint/no-unnecessary-condition
    // Initialize the mail sender with Resend provider
    const resendProvider: ResendProvider = new ResendProvider(env.RESEND_API_KEY)

    // Create the mail sender
    const mailSender: MailSender = new MailSender(resendProvider, {
      name: env.AUTH_EMAIL_NAME ?? "Impactly",
      email: env.RESEND_FROM_EMAIL,
    })

    // 4. Create an array to store email options
    const emailOptions: MailOptions[] = []

    // 5. Process each survey
    for (const survey of surveys) {
      try {
        // Generate survey link
        const baseUrl = env.NEXT_APP_URL

        // TODO: Create Link Based on the Survey Template
        // TODO: Generate Link with NexAuth Sign In and add callback to redirect to survey

        const surveyLink = `${baseUrl}/admin/${template === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1" ? "self-assesment" : "survey"}/${survey.id}`

        // Generate email content using the enhanced React-based template
        const emailContent = await generateSurveyInvitationEmail(survey.company_name, template, surveyLink)

        // Create recipient addresses in the format expected by MailSender
        const recipients = survey.contact_emails.map((email) => ({
          name: email,
          email: email,
        }))

        // Add to email options
        emailOptions.push({
          to: recipients,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
        })
      } catch (error) {
        console.error(`Error processing survey ${survey.id}:`, error)
      }
    }

    // 6. Start processing emails asynchronously (don't await)
    // This allows us to return a response immediately while emails are sent in the background
    // Using void to explicitly mark the promise as ignored
    console.log("---------------------------------------------------------------------")
    console.log("Email Options: ", { emailOptions: JSON.stringify(emailOptions) })
    console.log("---------------------------------------------------------------------")

    try {
      const result = await mailSender.sendBatch(emailOptions)
      console.log("---------------------------------------------------------------------")
      console.log("Emails sent successfully: ", { result: JSON.stringify(result) })
      console.log("---------------------------------------------------------------------")

      return NextResponse.json({
        message: "Survey invitations are being processed",
        totalSurveys: surveys.length,
        totalRecipients,
        status: "processing",
      })
    } catch (error) {
      console.error("Error sending emails:", error)

      return NextResponse.json(
        { error: "Failed to send emails", message: error instanceof Error ? error.message : "Unknown error" },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error processing request:", error)
    return NextResponse.json(
      { error: "Failed to process request", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    )
  }
}

/**
 * Handles POST requests to send surveys
 * This endpoint requires API key authentication via Authorization header
 */
export const POST = async (req: Request) => {
  // Check for API key authentication
  if (!verifyApiKeyAuth(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Process the request if API key is valid
  return processSurveyRequest(req)
}
