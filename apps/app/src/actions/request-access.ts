/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use server"

import { env } from "@/env"
import { getTranslations } from "next-intl/server"
import { z } from "zod"

// Create localized schema function
const createRequestAccessSchema = async () => {
  const t = await getTranslations("auth.requestAccess.errors")

  return z.object({
    email: z.string().email(t("emailInvalid")).min(1, t("emailRequired")),
    companyName: z.string().min(1, t("companyNameRequired")),
  })
}

type RequestAccessResult = {
  success?: boolean
  error?: {
    type: "VALIDATION_ERROR" | "UNKNOWN_ERROR"
    message: string
    fieldErrors?: Record<string, string[]>
  }
}

export async function requestAccess(_prevState: RequestAccessResult, formData: FormData): Promise<RequestAccessResult> {
  try {
    // Get translations for error messages
    const t = await getTranslations("auth.requestAccess.errors")

    // Create localized schema
    const requestAccessSchema = await createRequestAccessSchema()

    // Validate the form data
    const validatedFields = requestAccessSchema.safeParse({
      email: formData.get("email"),
      companyName: formData.get("companyName"),
    })

    if (!validatedFields.success) {
      return {
        error: {
          type: "VALIDATION_ERROR",
          message: t("validationError"),
          fieldErrors: validatedFields.error.flatten().fieldErrors,
        },
      }
    }

    const { email, companyName } = validatedFields.data

    // Call the request access API endpoint
    try {
      const response = await fetch(`${env.NEXT_APP_URL}/api/request-access`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          companyName,
        }),
      })

      if (!response.ok) {
        const errorData = (await response.json()) as { error: string; message: string }
        console.error("API request failed:", errorData)

        return {
          error: {
            type: "UNKNOWN_ERROR",
            message: t("submitFailed"),
          },
        }
      }

      const result = (await response.json()) as RequestAccessResult
      console.log("Request access notification sent:", result)

      return { success: true }
    } catch (apiError) {
      console.error("Error calling request access API:", apiError)

      return {
        error: {
          type: "UNKNOWN_ERROR",
          message: t("networkError"),
        },
      }
    }
  } catch (error) {
    console.error("Error in requestAccess:", error)

    // Get translations for unexpected error (outside try block)
    const t = await getTranslations("auth.requestAccess.errors")

    return {
      error: {
        type: "UNKNOWN_ERROR",
        message: t("unexpectedError"),
      },
    }
  }
}
