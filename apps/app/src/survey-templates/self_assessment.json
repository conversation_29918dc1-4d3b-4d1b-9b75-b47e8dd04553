{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"introduction": {"type": "object", "title": "Introduction", "readOnly": true, "properties": {"main_text": {"type": "object", "title": "", "description": "Following regulatory requirements (i.e. the Non-Financial Reporting Directive (NFRD) and upcoming Corporate Sustainability Reporting Directive (CSRD)) as for reporting purposes, this unified questionnaire is designed by Estonian, Latvian and Lithuanian Banking Associations to collect quantitative and qualitative data to assess Client's environmental and climate-related impact, social and governance practices, as well as to evaluate Client's readiness to green transition.", "properties": {}}}}, "general": {"type": "object", "properties": {"survey_fin_year": {"type": "number", "minimum": 2001, "maximum": 2024, "isYearField": true}, "survey_revenue_for_year": {"type": "number"}}, "required": ["survey_fin_year", "survey_revenue_for_year"]}, "esg_management_strategy": {"type": "object", "properties": {"practices_for_transitioning_towards_a_more_sustainable_economy": {"type": "object", "properties": {"if_sust_vision": {"x-display": "boolean-radio", "type": "boolean"}, "sust_vision": {"type": "string", "x-display": "textarea", "dependency": ["if_sust_vision"]}, "if_sust_strategy": {"x-display": "boolean-radio", "type": "boolean"}, "sust_strategy": {"type": "string", "x-display": "textarea", "dependency": ["if_sust_strategy"]}, "sust_strategy_ref": {"type": "array", "x-display": "file-upload", "title": "Upload attachment", "description": "Requests a link to or submission of the document detailing the company’s sustainability strategy.", "dependency": ["if_sust_strategy"], "items": {"type": "object", "properties": {"key": {"type": "string"}, "mimeType": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}}, "required": ["key", "mimeType", "name"]}}, "if_sustainable_practices": {"x-display": "boolean-radio", "type": "boolean"}, "sustainable_practices": {"type": "string", "x-display": "textarea", "dependency": ["if_sustainable_practices"]}, "if_sust_commitments": {"x-display": "boolean-radio", "type": "boolean"}, "sust_commitments": {"type": "string", "x-display": "textarea", "dependency": ["if_sust_commitments"]}, "if_sust_certification": {"x-display": "boolean-radio", "type": "boolean"}, "sust_certification": {"type": "string", "x-display": "textarea", "dependency": ["if_sust_certification"]}, "esg_initiatives": {"type": "string", "x-display": "textarea"}}, "required": ["if_sust_vision", "if_sust_strategy", "if_sustainable_practices", "if_sust_commitments", "if_sust_certification"]}, "human_rights_policies": {"type": "object", "properties": {"if_child_labour_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_forced_labour_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_human_trafficking": {"x-display": "boolean-radio", "type": "boolean"}, "if_discrimination_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_accident_prevention": {"x-display": "boolean-radio", "type": "boolean"}, "other_hr_policy": {"type": "string", "x-display": "textarea"}}}, "business_conduct_policies": {"type": "object", "properties": {"if_bribery_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_corruption_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_fraud_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_whistleb_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_supplier_policy": {"x-display": "boolean-radio", "type": "boolean"}, "if_aml_policy": {"x-display": "boolean-radio", "type": "boolean"}}}}}, "climate_impact_energy_use": {"type": "object", "title": "Climate Impact & Energy Use", "properties": {"energy_consumption": {"type": "object", "properties": {"total_energy_cons": {"type": "number", "title": "Total Energy consumption"}, "renewable_electricity_cons": {"type": "number", "title": "Renewable electricity consumption"}, "nonrenewable_electricity_cons": {"type": "number", "title": "Non-Renewable electricity consumption"}, "renewable_fuels_cons": {"type": "number", "title": "Renewable fuels consumption"}, "nonrenewable_fuels_cons": {"type": "number", "title": "Non-Renewable fuels consumption"}}}, "ghg_emissions": {"type": "object", "properties": {"ghg_scope1": {"type": "number", "title": "Scope 1 GHG emissions"}, "ghg_scope1_biogenic": {"type": "number", "title": "Scope 1 Biogenic GHG emissions"}, "ghg_scope2_locationbased": {"type": "number", "title": "Scope 2 GHG emissions (location based)"}, "ghg_scope2_marketbased": {"type": "number", "title": "Scope 2 GHG emissions (market based)"}, "ghg_scope1_2_total": {"type": "number", "title": "Scope 1 and 2 emissions total"}, "ghg_scope3_total": {"type": "number", "title": "Scope 3 GHG emissions"}, "ghg_scope3_cat_1": {"type": "number", "title": "Scope 3 cat 1: Purchased Goods and Services", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_2": {"type": "number", "title": "Scope 3 cat 2: Capital Goods", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_3": {"type": "number", "title": "Scope 3 cat 3: Fuel- and Energy-Related Activities", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_4": {"type": "number", "title": "Scope 3 cat 4: Upstream Transportation and Distribution", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_5": {"type": "number", "title": "Scope 3 cat 5: Waste Generated in Operations", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_6": {"type": "number", "title": "Scope 3 cat 6: Business Travel", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_7": {"type": "number", "title": "Scope 3 cat 7: Employee Commuting", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_8": {"type": "number", "title": "Scope 3 cat 8: Upstream Leased Assets", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_9": {"type": "number", "title": "Scope 3 cat 9: Downstream Transportation and Distribution", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_10": {"type": "number", "title": "Scope 3 cat 10: Processing of Sold Products", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_11": {"type": "number", "title": "Scope 3 cat 11: Use of Sold Products", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_12": {"type": "number", "title": "Scope 3 cat 12: End-of-Life Treatment of Sold Products", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_13": {"type": "number", "title": "Scope 3 cat 13: Downstream Leased Assets", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_14": {"type": "number", "title": "Scope 3 cat 14: <PERSON><PERSON><PERSON><PERSON>", "dependency": ["ghg_scope3_total"]}, "ghg_scope3_cat_15": {"type": "number", "title": "Scope 3 cat 15: Investments", "dependency": ["ghg_scope3_total"]}, "ghg_total_emissions": {"type": "number", "title": "Scope 1 and 2 emissions total"}}}, "ghg_reduction": {"type": "object", "properties": {"if_ghg_reduction_goals": {"x-display": "boolean-radio", "type": "boolean"}, "ghggoal": {"type": "array", "title": "GHG reduction goals", "dependency": ["if_ghg_reduction_goals"], "items": {"type": "object", "properties": {"ghggoal_element": {"type": "string", "title": "Target scope", "enum": ["total_ghg_emissions", "total_scope_1_&_scope_2", "scope_1", "scope_2_(location_based)", "scope_2_(market_based)", "scope_3"]}, "ghggoal_base_year": {"type": "number", "title": "Base year", "minimum": 0, "isYearField": true}, "ghggoal_target_year_st": {"type": "number", "title": "Target year Short Term", "minimum": 0, "isYearField": true}, "ghggoal_goal_numeric_st": {"type": "number", "title": "Short Term Value", "minimum": 0}, "ghggoal_target_year_lt": {"type": "number", "title": "Target year Long Term", "minimum": 0, "isYearField": true}, "ghggoal_goal_numeric_lt": {"type": "number", "title": "Long Term Value", "minimum": 0}}}}, "ghggoal_narrative": {"type": "string", "x-display": "textarea", "dependency": ["if_ghg_reduction_goals"]}}}, "transition_plan": {"type": "object", "properties": {"if_transition_plan_existence": {"x-display": "boolean-radio", "type": "boolean"}, "transition_plan_description": {"type": "string", "x-display": "textarea", "dependency": ["if_transition_plan_existence"]}}}, "physical_risks_from_climate_change": {"type": "object", "properties": {"if_physical_risk_identification": {"x-display": "boolean-radio", "type": "boolean"}, "physical_risk_identification": {"type": "string", "x-display": "textarea", "dependency": ["if_physical_risk_identification"]}, "climate_change_adaptation_actions": {"type": "string", "x-display": "textarea", "dependency": ["if_physical_risk_identification"]}, "insurance_coverage": {"type": "number", "minimum": 0, "dependency": ["if_physical_risk_identification"]}, "physical_risk_effect": {"type": "string", "x-display": "textarea"}}}}}, "managing_environmental_resources": {"type": "object", "title": "Managing Environmental Resources", "properties": {"pollution": {"type": "object", "properties": {"if_pollution_reporting": {"x-display": "boolean-radio", "type": "boolean"}, "if_pollution_to_air": {"x-display": "boolean-radio", "type": "boolean", "dependency": ["if_pollution_reporting"]}, "if_pollution_to_water": {"x-display": "boolean-radio", "type": "boolean", "dependency": ["if_pollution_reporting"]}, "if_pollution_to_soil": {"x-display": "boolean-radio", "type": "boolean", "dependency": ["if_pollution_reporting"]}}}, "waste_management": {"type": "object", "properties": {"generation_of_waste": {"type": "number", "title": "Generation of waste"}, "recycling_or_reuse": {"type": "number", "title": "Recycling or reuse", "minimum": 0}, "use_of_materials": {"type": "string", "title": "Use of materials", "x-display": "textarea"}}}, "company_water_use": {"type": "object", "properties": {"water_withdrawal": {"type": "number", "title": "Water withdrawal"}, "water_withdrawal_in_area_of_high_water_stress": {"type": "number", "title": "Water withdrawal in area of high water stress"}, "water_consumption": {"type": "number", "title": "Water consumption"}}}, "biodiversity": {"type": "object", "properties": {"if_land_near_sensitive_areas": {"x-display": "boolean-radio", "type": "boolean"}, "land_area_near_biodiversity": {"type": "number", "title": "Total land area", "dependency": ["if_land_near_sensitive_areas"]}, "total_use_of_land": {"type": "number", "title": "Total use of land", "dependency": ["if_land_near_sensitive_areas"]}, "total_sealed_area": {"type": "number", "title": "Total sealed area", "dependency": ["if_land_near_sensitive_areas"]}, "total_nature_oriented_area_on_site": {"type": "number", "title": "Total nature-oriented area on-site", "minimum": 0, "dependency": ["if_land_near_sensitive_areas"]}, "total_nature_oriented_area_off_site": {"type": "number", "minimum": 0, "title": "Total nature-oriented area off-site", "dependency": ["if_land_near_sensitive_areas"]}}}}}, "workforce_social_responsibility": {"type": "object", "title": "Workforce & Social Responsibility", "properties": {"workforce_general_characteristics": {"type": "object", "properties": {"nr_employees": {"type": "number", "title": "Number of employees"}, "nr_employees_gender_m": {"type": "number", "title": "Number of male employees"}, "nr_employees_gender_f": {"type": "number", "title": "Number of female employees"}, "nr_employees_gender_other": {"type": "number", "title": "Number or employees of other gender"}, "nr_temporary_employees": {"type": "number", "title": "Temporary employees"}, "nr_permanent_employees": {"type": "number", "title": "Permanent employees"}, "employee_turnover_rate": {"type": "number", "title": "Employee turnover rate", "minimum": 0, "maximum": 100}, "nr_non_employees": {"type": "number", "title": "Number of non-employees"}}}, "workforce_remuneration_collective_bargaining_and_training": {"type": "object", "properties": {"gender_paygap": {"type": "number", "title": "Gender paygap", "minimum": 0, "maximum": 100}, "employees_covered_cb": {"type": "number", "title": "Employees covered by CB", "minimum": 0, "maximum": 100}, "trainings_gender_m": {"type": "number", "title": "Training hours (M)"}, "trainings_gender_f": {"type": "number", "title": "Training hours (F)"}, "if_minimum_wage_pay": {"x-display": "boolean-radio", "type": "boolean"}}}, "workforce_health_safety": {"type": "object", "properties": {"work_accidents_employees": {"type": "number", "title": "Work accidents (employees)", "minimum": 0}, "work_fatalities_employees": {"type": "number", "title": "Fatalities (employees)", "minimum": 0}}}}}, "business_ethics_governance": {"type": "object", "properties": {"incidents_related_severe_human_rights": {"type": "object", "properties": {"nr_human_rights_violations": {"type": "number", "title": "Human rights violations", "minimum": 0}, "nr_human_rights_stakeholders_cases": {"type": "number", "title": "Incidents communities", "minimum": 0}}}, "corruption_and_bribery": {"type": "object", "properties": {"if_abc_convictions": {"x-display": "boolean-radio", "type": "boolean"}, "nr_abc_convictions": {"type": "number", "dependency": ["if_abc_convictions"]}, "abc_fines": {"type": "number", "dependency": ["if_abc_convictions"]}}}, "workforce_characteristics": {"type": "object", "properties": {"management_board_f": {"type": "number"}, "management_board_m": {"type": "number"}}}}}}}