{"$schema": "http://json-schema.org/draft-07/schema#", "title": "UNIFIED CLIENT QUESTIONNAIRE ON ENVIRONMENTAL AND CLIMATE-RELATED DISCLOSURES", "type": "object", "properties": {"introduction": {"type": "object", "title": "Introduction", "readOnly": true, "properties": {"main_text": {"type": "object", "title": "", "description": "Following regulatory requirements (i.e. the Non-Financial Reporting Directive (NFRD) and upcoming Corporate Sustainability Reporting Directive (CSRD)) as for reporting purposes, this unified questionnaire is designed by Estonian, Latvian and Lithuanian Banking Associations to collect quantitative and qualitative data to assess Client's environmental and climate-related impact, social and governance practices, as well as to evaluate Client's readiness to green transition.", "properties": {}}}}, "general": {"type": "object", "title": "General", "properties": {"general_questionnaire_completion_date": {"type": "string", "format": "date", "title": "Questionnaire Completion Date", "description": "The date on which the questionnaire is completed, establishing the reporting period for the data provided."}, "general_company_name": {"type": "string", "title": "Company Name", "description": "The legal name of the company used for identification and regulatory reporting.", "placeholder": "Enter company name"}, "general_company_registration_number": {"type": "string", "title": "Company Registration Number", "description": "The unique identifier assigned to the company by the relevant registration authority.", "placeholder": "Enter company registration number"}, "general_company_indicators": {"type": "object", "title": "Company Indicators", "description": "Please provide information on the following Company indicators  for the last full financial year", "helpText": "This section header introduces key company indicators for the last full financial year.", "properties": {"general_company_indicators_financial_year": {"type": "integer", "title": "Financial Year", "description": "The fiscal year under review during which the company’s financial and operational performance is measured.", "minimum": 1900, "maximum": 2100, "placeholder": "Enter financial year", "isYearField": true}, "general_company_indicators_net_turnover": {"type": "number", "title": "Net Turnover (EUR)", "description": "The total revenue generated by the company during the financial year, expressed in euros.", "placeholder": "Enter net turnover"}, "general_company_indicators_total_assets": {"type": "number", "title": "Total Assets (EUR)", "description": "The total value of the company’s assets as of the end of the reporting period, measured in euros.", "minimum": 0, "placeholder": "Enter total assets"}, "general_company_indicators_number_of_employees": {"type": "integer", "title": "Number of Employees", "description": "The number of employees working for the company during the reporting period.", "minimum": 0, "placeholder": "Enter number of employees"}}, "required": ["general_company_indicators_financial_year", "general_company_indicators_net_turnover", "general_company_indicators_total_assets", "general_company_indicators_number_of_employees"]}, "general_economic_activities": {"type": "array", "title": "Main Economic Activities", "description": "Please indicate up to three main economic activities for the company and their share in the last full financial year", "helpText": "This section requests the disclosure of up to three main economic activities and their respective contributions to net turnover.", "addItemLabel": "Economic Activity", "maxItems": 3, "items": {"type": "object", "properties": {"general_economic_activities_activity": {"type": "string", "title": "Add Economic Activity", "label": "Activity", "description": "Details of a primary economic activity carried out by the company.", "enum": ["a_agriculture_forestry_and_fishing", "growing_of_cereals_except_rice_leguminous_crops_and_oil_seeds", "growing_of_rice", "growing_of_vegetables_and_melons_roots_and_tubers", "growing_of_sugar_cane", "growing_of_tobacco", "growing_of_fibre_crops", "growing_of_other_non_perennial_crops", "growing_of_perennial_crops", "plant_propagation", "raising_of_dairy_cattle", "raising_of_other_cattle_and_buffaloes", "raising_of_horses_and_other_equines", "raising_of_camels_and_camelids", "raising_of_sheep_and_goats", "raising_of_swine_pigs", "raising_of_poultry", "raising_of_other_animals", "mixed_farming", "support_activities_for_crop_production", "support_activities_for_animal_production", "post_harvest_crop_activities", "seed_process_and_propagation", "hunting_trapping_and_related_service_activities", "silviculture_and_other_forestry_activities", "logging", "gathering_of_wild_growing_non_wood_products", "support_services_to_forestry", "marine_fishing", "freshwater_fishing", "marine_aquaculture", "freshwater_aquaculture", "b_mining_and_quarrying", "mining_of_hard_coal", "mining_of_lignite", "extraction_of_crude_petroleum", "extraction_of_natural_gas", "mining_of_iron_ores", "mining_of_uranium_and_thorium_ores", "mining_of_other_non_ferrous_metal_ores", "quarrying_of_stone_and_sand_and_clay", "quarrying_of_ornamental_and_building_stone_limestone_gypsum_chalk_and_slate", "operation_of_gravel_and_sand_pits_mining_of_clays_and_kaolin", "mining_of_chemical_and_fertiliser_minerals", "extraction_of_peat", "salt_extraction", "other_mining_and_quarrying_nec", "support_activities_for_petroleum_and_natural_gas_extraction", "support_for_other_mining_and_quarrying", "c_manufacturing", "processing_and_preserving_of_meat", "processing_and_preserving_of_poultry_meat", "production_of_meat_and_poultry_meat_products", "processing_and_preserving_of_fish_crustaceans_and_molluscs", "processing_and_preserving_of_potatoes", "manufacture_of_fruit_and_vegetable_juice", "other_processing_and_preserving_of_fruit_and_vegetables", "manufacture_of_oils_and_fats", "manufacture_of_margarine_and_similar_edible_fats", "operation_of_dairies_and_cheese_making", "manufacture_of_ice_cream", "manufacture_of_grain_mill_products_starches_and_starch_products", "manufacture_of_bread_and_fresh_pastry_goods_and_cakes", "manufacture_of_rusks_and_biscuits_preserved_pastry_goods_and_cakes", "manufacture_of_macaroni_noodles_couscous_and_similar_farinaceous_products", "manufacture_of_other_food_products", "manufacture_of_prepared_feeds_for_farm_animals", "manufacture_of_prepared_pet_foods", "distilling_rectifying_and_blending_of_spirits", "manufacture_of_wine_from_grape", "manufacture_of_cider_and_other_fruit_wines", "manufacture_of_tobacco_products", "preparation_of_spinning_of_textile_fibres", "weaving_of_textiles", "finishing_of_textiles", "manufacture_of_other_textiles", "manufacture_of_wearing_apparel_except_fur_apparel", "manufacture_of_articles_of_fur", "manufacture_of_knitted_and_crocheted_apparel", "tanning_and_dressing_of_leather", "manufacture_of_luggage_handbags_saddlery_and_harness", "manufacture_of_footwear", "sawmilling_and_planing_of_wood", "manufacture_of_products_of_wood_cork_straw_and_plaiting_materials", "manufacture_of_pulp", "manufacture_of_paper_and_paperboard", "manufacture_of_articles_of_paperboard", "printing_and_service_activities_related_to_printing", "reproduction_of_recorded_media", "manufacture_of_coke_oven_products", "manufacture_of_refined_petroleum_products", "manufacture_of_basic_chemicals_fertilizers_and_nitrogen_compounds_plastics_and_synthetic_rubber", "manufacture_of_pesticides_and_other_agrochemical_products", "manufacture_of_paints_varnishes_printing_ink_and_mastics", "manufacture_of_soap_detergents_cleaning_polishing_perfumes_and_toilet_preparations", "manufacture_of_other_chemical_products", "manufacture_of_man_made_fibres", "manufacture_of_basic_pharmaceutical_products_and_preparations", "manufacture_of_rubber_tyres_and_tubes_retreading_and_rebuilding", "manufacture_of_other_rubber_products", "manufacture_of_plastic_products", "manufacture_of_glass_and_glass_products", "manufacture_of_refractory_products", "manufacture_of_clay_building_materials", "manufacture_of_other_porcelain_and_ceramic_products", "manufacture_of_cement_lime_and_plaster", "manufacture_of_articles_of_concrete_cement_and_plaster", "cutting_shaping_and_finishing_of_stone", "manufacture_of_abrasive_products_and_non_metallic_mineral_products_nec", "manufacture_of_basic_iron_and_steel_and_ferro_alloys", "manufacture_of_tubes_pipes_hollow_profiles_and_fittings_of_steel", "manufacture_of_other_products_and_first_processing_of_steel", "manufacture_of_basic_precious_and_other_non_ferrous_metals", "processing_of_nuclear_fuel", "casting_of_metals", "manufacture_of_structural_metal_products", "manufacture_of_tanks_reservoirs_and_containers_of_metal", "manufacture_of_steam_generators_except_central_heating_boilers", "manufacture_of_weapons_and_ammunition", "forging_pressing_stamping_and_roll_forming_of_metal_powder_metallurgy", "treatment_and_coating_of_metals", "machining", "manufacture_of_cutlery", "manufacture_of_locks_and_hinges", "manufacture_of_tools", "manufacture_of_other_fabricated_metal_products", "manufacture_of_electronic_components_and_boards", "manufacture_of_computers_and_peripheral_equipment", "manufacture_of_communication_equipment", "manufacture_of_consumer_electronics", "manufacture_of_instruments_and_appliances_for_measuring_testing_and_navigation", "manufacture_of_irradiation_electromedical_and_electrotherapeutic_equipment", "manufacture_of_optical_instruments_and_photographic_equipment", "manufacture_of_magnetic_and_optical_media", "manufacture_of_electric_motors_generators_transformers_and_electricity_distribution", "manufacture_of_batteries_and_accumulators", "manufacture_of_wiring_and_wiring_devices", "manufacture_of_electric_lighting_equipment", "manufacture_of_domestic_appliances", "manufacture_of_other_electrical_equipment", "manufacture_of_general_purpose_machinery", "manufacture_of_ovens_furnaces_and_furnace_burners", "manufacture_of_lifting_and_handling_equipment", "manufacture_of_office_machinery_and_equipment", "manufacture_of_power_driven_hand_tools", "manufacture_of_non_domestic_cooling_and_ventilation_equipment", "manufacture_of_other_general_purpose_machinery_nec", "manufacture_of_agricultural_forestry_machinery", "manufacture_of_metal_forming_machinery_and_machine_tools", "manufacture_of_other_special_purpose_machinery", "manufacture_of_motor_vehicles", "manufacture_of_bodies_for_motor_vehicles_trailers_and_semi_trailers", "manufacture_of_parts_and_accessories_for_motor_vehicles", "building_of_ships_and_floating_structures", "building_of_pleasure_and_sporting_boats", "manufacture_of_railway_locomotives_and_rolling_stock", "manufacture_of_air_and_spacecraft_and_related_machinery", "manufacture_of_military_fighting_vehicles_icbm", "manufacture_of_transport_equipment_nec", "manufacture_of_furniture", "manufacturing_of_jewellery_bijouteries_and_related_articles", "manufacture_of_musical_instruments", "manufacture_of_sports_goods", "manufacture_of_games_and_toys", "manufacture_of_medical_and_dental_instruments_and_supplies", "manufacturing_nec", "repair_of_fabricated_metal_products_machinery_and_equipment", "repair_of_electrical_equipment", "repair_and_maintenance_of_ships_and_boats", "repair_and_maintenance_of_aircraft_and_spacecraft", "repair_and_maintenance_of_other_transport_equipment", "installation_of_industrial_machinery_and_equipment", "d_electricity_gas_steam_and_air_conditioning_supply", "electric_power_generation_transmission_and_distribution", "production_of_electricity", "transmission_of_electricity", "distribution_of_electricity", "trade_of_electricity", "manufacture_of_gas_distribution_of_gaseous_fuels", "distribution_of_gaseous_fuels_through_mains", "trade_of_gas_through_mains", "steam_and_air_conditioning_supply", "e_water_supply_sewerage_waste_management_and_remediation", "water_collection_treatment_and_supply", "sewerage", "collection_of_non_hazardous_waste", "collection_of_hazardous_waste", "treatment_and_disposal_of_non_hazardous_waste", "treatment_and_disposal_of_hazardous_waste", "dismantling_of_wrecks", "recovery_of_sorted_materials", "remediation_activities_and_other_waste_management_services", "f_construction", "development_of_building_projects", "construction_of_residential_and_non_residential_buildings", "construction_of_roads_and_railways", "construction_of_utility_projects", "construction_of_other_civil_engineering_projects", "demolition_and_site_preparation", "electrical_plumbing_and_other_construction_installation", "building_completion_and_finishing", "other_specialised_construction_activities", "g_wholesale_and_retail_trade_repair_of_motor_vehicles", "sale_of_motor_vehicles", "maintenance_and_repair_of_motor_vehicles", "sale_of_motor_vehicle_parts_and_accessories", "sale_maintenance_and_repair_of_motorcycles", "wholesale_on_a_fee_or_contract_basis", "wholesale_of_grain_unmanufactured_tobacco_seeds_and_animal_feeds", "wholesale_of_flowers_and_plants", "wholesale_of_live_animals", "wholesale_of_hides_skins_and_leather", "wholesale_of_food_beverages_and_tobacco", "wholesale_of_tobacco_products", "wholesale_of_household_goods", "wholesale_of_information_and_communication_equipment", "wholesale_of_other_machinery_equipment_and_supplies", "wholesale_of_solid_liquid_and_gaseous_fuels", "wholesale_of_metals_and_metal_ores", "wholesale_of_wood_construction_materials_and_sanitary_equipment", "wholesale_of_hardware_plumbing_and_heating_equipment", "wholesale_of_chemical_products", "wholesale_of_other_intermediate_products", "wholesale_of_waste_and_scrap", "non_specialised_wholesale_trade", "retail_sale_in_non_specialised_stores_food_beverages_tobacco", "other_retail_sale_in_non_specialised_stores", "retail_sale_of_food_beverages_and_tobacco_specialised", "retail_sale_of_tobacco_products_specialised", "retail_sale_of_automotive_fuel_specialised", "retail_sale_of_information_and_communication_equipment_specialised", "retail_sale_of_other_household_equipment_specialised", "retail_sale_of_cultural_and_recreation_goods_specialised", "retail_sale_of_other_goods_specialised", "dispensing_chemist_specialised", "retail_sale_of_medical_and_orthopaedic_goods_specialised", "retail_sale_of_flowers_plants_seeds_fertilisers_pets_specialised", "retail_sale_via_stalls_and_markets", "retail_trade_not_in_stores_stalls_or_markets", "h_transportation_and_storage", "passenger_rail_transport_interurban", "freight_rail_transport", "other_passenger_land_transport", "freight_transport_by_road_and_removal_services", "transport_via_pipeline", "sea_and_coastal_passenger_water_transport", "sea_and_coastal_freight_water_transport", "inland_passenger_water_transport", "inland_freight_water_transport", "passenger_air_transport", "freight_air_transport_and_space_transport", "warehousing_and_storage", "support_activities_for_transportation", "postal_activities_under_universal_service_obligation", "other_postal_and_courier_activities", "i_accommodation_and_food_service_activities", "hotels_and_similar_accommodation", "holiday_and_other_short_stay_accommodation", "camping_grounds_recreational_vehicle_parks_and_trailer_parks", "other_accommodation", "restaurants_and_mobile_food_service_activities", "event_catering_and_other_food_service_activities", "beverage_serving_activities", "j_information_and_communication", "publishing_of_books_periodicals_and_other_publishing", "software_publishing", "motion_picture_video_and_television_programme_activities", "sound_recording_and_music_publishing", "radio_broadcasting", "television_programming_and_broadcasting", "wired_telecommunications_activities", "wireless_telecommunications_activities", "satellite_telecommunications_activities", "other_telecommunications_activities", "computer_programming_consultancy_and_related_activities", "data_processing_hosting_and_related_activities_web_portals", "other_information_service_activities", "k_financial_and_insurance_activities", "monetary_intermediation", "activities_of_holding_companies", "trusts_funds_and_similar_financial_entities", "other_financial_service_activities_except_insurance_and_pension", "insurance", "reinsurance", "pension_funding", "activities_auxiliary_to_financial_services_except_insurance", "activities_auxiliary_to_insurance_and_pension_funding", "fund_management_activities", "l_real_estate_activities", "buying_and_selling_of_own_real_estate", "renting_and_operating_of_own_or_leased_real_estate", "real_estate_agencies", "management_of_real_estate_on_fee_or_contract_basis", "m_professional_scientific_and_technical_activities", "legal_activities", "accounting_bookkeeping_auditing_and_tax_consultancy", "activities_of_head_offices", "management_consultancy_activities", "architectural_and_engineering_activities_and_technical_consultancy", "technical_testing_and_analysis", "research_and_development_on_natural_sciences_and_engineering", "research_and_development_on_social_sciences_and_humanities", "advertising", "market_research_and_public_opinion_polling", "specialised_design_activities", "photographic_activities", "translation_and_interpretation_activities", "other_professional_scientific_and_technical_activities_nec", "veterinary_activities", "n_administrative_and_support_service_activities", "renting_and_leasing_of_motor_vehicles", "renting_and_leasing_of_personal_and_household_goods", "renting_and_leasing_of_recreational_and_sports_goods", "renting_of_video_tapes_and_disks", "renting_and_leasing_of_other_machinery_equipment_and_goods", "activities_of_employment_placement_agencies", "temporary_employment_agencies", "other_human_resources_provision", "travel_agency_and_tour_operator_activities", "other_reservation_service_and_related_activities", "private_security_activities", "security_systems_service_activities", "investigation_activities", "combined_facilities_support_activities", "general_cleaning_of_buildings", "other_buildings_and_industrial_cleaning_activities", "other_cleaning_activities", "landscape_service_activities", "combined_office_administrative_service_activities", "photocopying_document_preparation_and_office_support", "activities_of_call_centres", "organisation_of_conventions_and_trade_shows", "activities_of_collection_agencies_and_credit_bureaus", "packaging_activities", "other_business_support_service_activities_nec", "o_public_administration_and_defence_social_security", "administration_of_the_state_and_economic_social_policy", "provision_of_services_to_the_community", "compulsory_social_security_activities", "p_education", "other_education", "educational_support_activities", "q_human_health_and_social_work_activities", "hospital_activities", "medical_and_dental_practice_activities", "other_human_health_activities", "residential_nursing_care_activities", "residential_care_activities_for_mental_health_and_substance_abuse", "residential_care_activities_for_the_elderly_and_disabled", "other_residential_care_activities", "social_work_activities_without_accommodation_for_elderly", "other_social_work_activities_without_accommodation", "r_arts_entertainment_and_recreation", "creative_arts_and_entertainment_activities", "gambling_and_betting_activities", "amusement_and_recreation_activities", "s_other_service_activities", "activities_of_business_employers_and_professional_organisations", "activities_of_trade_unions", "activities_of_other_membership_organisations", "repair_of_computers_and_communication_equipment", "repair_of_personal_and_household_goods", "washing_and_drycleaning_of_textile_and_fur_products", "hairdressing_and_other_beauty_treatment", "funeral_and_related_activities"]}, "general_economic_activities_industry_category": {"type": "string", "title": "Industry (Category)", "description": "The broad industry classification in which the economic activity is categorized.", "placeholder": "Category", "enum": ["primary", "secondary", "tertiary", "quaternary"]}, "general_economic_activities_industry_sector": {"type": "string", "title": "Industry (Sector)", "description": "A more detailed industry section classification for the economic activity.", "placeholder": "Sector", "enum": ["agriculture", "manufacturing", "services", "technology"]}, "general_economic_activities_share_in_net_turnover": {"type": "number", "title": "Share in net turnover (%)", "description": "The percentage share of the company’s net turnover attributable to this economic activity.", "placeholder": "Enter %", "minimum": 0, "maximum": 100}}}}, "general_revenue_questions": {"type": "object", "title": "Revenue Information", "properties": {"general_revenue_questions_coal_revenue": {"title": "Does the Company derive 1 % or more of their revenues from the exploration, mining, extraction, distribution or refining of hard coal and lignite?", "description": "Indicates whether 1% or more of the company’s revenue is derived from hard coal and lignite-related activities, which is relevant for assessing fossil fuel dependency.", "x-display": "boolean-radio", "type": "boolean"}, "general_revenue_questions_oil_revenue": {"title": "Does the Company derive 10 % or more of their revenues from the exploration, extraction, distribution or refining of oil fuels?", "description": "Assesses if 10% or more of the company’s revenue comes from oil fuels, highlighting exposure to fossil fuel activities.", "x-display": "boolean-radio", "type": "boolean"}, "general_revenue_questions_gaseous_fuels_revenue": {"title": "Does the Company derive 50 % or more of their revenues from the exploration, extraction, manufacturing or distribution of gaseous fuels?", "description": "Evaluates whether 50% or more of the company’s revenue is generated from gaseous fuels, indicating significant reliance on these energy sources.", "x-display": "boolean-radio", "type": "boolean"}, "general_revenue_questions_electricity_generation_revenue": {"title": "Does the Company derive 50 % or more of their revenues from electricity generation with a GHG intensity of more than 100 g CO2 e/kWh?", "description": "Determines if at least 50% of the company’s revenue comes from electricity generation with high GHG intensity, critical for climate impact assessments.", "x-display": "boolean-radio", "type": "boolean"}}, "required": ["general_revenue_questions_coal_revenue", "general_revenue_questions_oil_revenue", "general_revenue_questions_gaseous_fuels_revenue", "general_revenue_questions_electricity_generation_revenue"]}, "general_is_pie_company": {"title": "Is the company a PIE?", "description": "Determines if at least 50% of the company’s revenue comes from electricity generation with high GHG intensity, critical for climate impact assessments.", "x-display": "boolean-radio", "type": "boolean"}, "general_properties_information": {"type": "object", "title": "Company Properties", "properties": {"general_properties_information_number_of_properties": {"type": "integer", "title": "How many properties and/or locations are associated with the Company's operations?", "placeholder": "Enter number of locations", "description": "Specifies the total number of physical properties or operational locations associated with the company."}, "general_properties_information_can_define_main_properties": {"title": "Is it possible to define up to 5 main properties/locations?", "description": "*Main Real Estate object - real estate object (leased or owned), which is critically important to ensure business activity or represents significant share of the assets of the Company. Please indicate up to 5 main real estate objects.\nIf Company operates more than 5 real estate objects of equal significance, please fill in by providing general information on number, type and location of Group of real estate objects.\n**Group of Real estate objects - objects with identical purpose of use located in the same city/parish/district", "x-display": "boolean-radio", "type": "boolean"}, "general_properties_information_main_properties": {"title": "Main Real Estate Locations", "type": "array", "helpText": "Requests detailed location information (country, address, city, state, postal code) for each property associated with the company.", "description": "Please indicate up to 5 main real estate locations, which are critically important for the business activity of the Company.", "maxItems": 5, "addItemLabel": "Location", "items": {"type": "object", "properties": {"general_properties_information_main_properties_purpose_of_use": {"type": "string", "title": "Purpose of use", "description": "Indicates the intended purpose or use of the first listed address, based on predefined usage categories.", "enum": ["location_of_it_infrastructure", "production", "retail", "warehouse", "agricultural_land", "forest", "other_land_utilized_for_business_purposes", "office"], "placeholder": "purpose"}, "general_properties_information_main_properties_country": {"type": "string", "title": "Country", "description": "Specifies the country where the first property is located.", "placeholder": "Enter Country"}, "general_properties_information_main_properties_street_address": {"type": "string", "title": "Street Address", "description": "Provides the street address and building identifier or property name for the first property.", "placeholder": "Enter Street Address"}, "general_properties_information_main_properties_city_county_state": {"type": "string", "title": "City/County/State", "description": "Identifies the city, county, or state for the first property location.", "placeholder": "Enter City/County/State"}, "general_properties_information_main_properties_postal_code": {"type": "string", "title": "Postal code", "description": "Specifies the postal code corresponding to the first property location.", "placeholder": "Enter Postal code"}}}}}, "required": ["general_properties_information_number_of_properties", "general_properties_information_can_define_main_properties"]}}, "required": ["general_questionnaire_completion_date", "general_company_name", "general_company_registration_number", "general_company_indicators", "general_revenue_questions", "general_is_pie_company", "general_properties_information"]}, "climate_impact": {"type": "object", "title": "Climate Impact", "description": "Climate change and environmental degradation is a threat, which puts preasure on social, economic and business environment. GHG emissions are the primary driver of global climate change, while energy consumption (in particular burning of fossil fuels), is the main source of GHG emissions. To overcome climate change challenges European Union is taking action by setting ambitious goal of GHG reduction by 55% by 2030 and by becomming climate neutral by 2050. In order to achieve it, every action and step towards lower GHG emission economy is important.", "properties": {"climate_impact_ghg_emissions": {"type": "object", "title": "GHG Emissions", "properties": {"climate_impact_ghg_emissions_monitors_ghg_emissions": {"title": "Does the Company monitor and/or calculate GHG emissions?", "description": "Indicates whether the company actively monitors and calculates its greenhouse gas (GHG) emissions—a critical element of climate reporting.", "x-display": "boolean-radio", "type": "boolean"}, "climate_impact_ghg_emissions_emission_volumes": {"type": "object", "x-display": "yearly-table", "x-year-columns": ["2023", "2022", "2021"], "dependency": ["climate_impact_ghg_emissions_monitors_ghg_emissions"], "title": "Please provide information, (where available), on the Company's GHG emission volume for the last 3 calendar years:", "description": "Requests the disclosure of annual GHG emission volumes for the last three calendar years if monitoring is conducted.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_1": {"type": "object", "title": "GHG emissions scope 1, (tCO2eq)", "description": "Reports direct GHG emissions (Scope 1) measured in tonnes of CO2 equivalent.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_1_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_1_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_1_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_2": {"type": "object", "title": "GHG emissions scope 2, (tCO2eq)", "description": "Reports indirect GHG emissions from purchased energy (Scope 2) in tonnes of CO2 equivalent.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_2_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_2_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_2_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3": {"type": "object", "title": "GHG emissions scope 3, (tCO2eq), whereof:", "isHeader": true, "description": "Reports other indirect GHG emissions (Scope 3) associated with the company’s value chain, measured in tonnes of CO2 equivalent.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods": {"type": "object", "title": "Purchased goods and services", "placeholder": "Enter volume", "description": "Specifies GHG emissions related to purchased goods and services, which are part of Scope 3 emissions.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_purchased_goods_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel": {"type": "object", "title": "Business travel", "description": "Reports GHG emissions resulting from business travel, included under Scope 3.", "placeholder": "Enter volume", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_business_travel_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting": {"description": "Details GHG emissions generated from employee commuting, as part of Scope 3.", "placeholder": "Enter volume", "type": "object", "title": "Employee commuting", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_employee_commuting_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets": {"type": "object", "title": "Leased assets", "placeholder": "Enter volume", "description": "Reports GHG emissions associated with leased assets, included in Scope 3.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_leased_assets_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services": {"type": "object", "title": "Transport Services", "placeholder": "Enter volume", "description": "Specifies GHG emissions from transportation and distribution services, reported under Scope 3.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_transportation_services_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_investments": {"type": "object", "title": "Investments", "placeholder": "Enter volume", "description": "Reports GHG emissions attributed to investments, included as part of Scope 3.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_investments_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}, "climate_impact_ghg_emissions_emission_volumes_scope_3_other": {"type": "object", "title": "Other", "placeholder": "Enter volume", "description": "Covers other categories of Scope 3 GHG emissions not captured in the previous items.", "properties": {"climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2023": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2022": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}, "climate_impact_ghg_emissions_emission_volumes_scope_3_other_year_2021": {"type": "number", "minimum": 0, "placeholder": "Enter volume"}}}}}}}, "climate_impact_ghg_emissions_calculation_framework": {"type": "string", "title": "What framework is used for the calculation of GHG emissions?", "placeholder": "framework", "description": "Identifies the framework or methodology (e.g., GHG Protocol, ISO 14064) used for calculating the company’s GHG emissions.", "enum": ["ghg_protocol", "iso_14064", "other"]}, "climate_impact_ghg_emissions_external_validation": {"title": "Is external validation/verification available for the GHG emissions calculation?", "description": "Indicates whether the company’s GHG emissions data has undergone external validation or verification.", "x-display": "boolean-radio", "type": "boolean"}, "climate_impact_ghg_emissions_verifier": {"type": "string", "title": "If yes, please disclose the verifier:", "description": "Provides the name or details of the external verifier if the GHG emissions data has been validated.", "placeholder": "verifier", "enum": ["bureau_veritas", "bm_certification", "other"], "dependency": ["climate_impact_ghg_emissions_external_validation"]}, "climate_impact_ghg_emissions_reduction_plan": {"title": "Does the Company have a GHG emission reduction plan?", "description": "Indicates whether the company has a plan in place to reduce its GHG emissions.", "x-display": "boolean-radio", "type": "boolean"}, "climate_impact_ghg_emissions_reduction_goals": {"type": "array", "title": "Please indicate your GHG emissions reduction goals:", "description": "Details the GHG emission reduction goals, including short-, medium-, and long-term targets, along with baseline and target years where applicable.", "dependency": ["climate_impact_ghg_emissions_reduction_plan"], "addItemLabel": "Reduction Goal", "items": {"type": "object", "properties": {"climate_impact_ghg_emissions_reduction_goals_reduction_goal": {"type": "string", "title": "Reduction Goal", "placeholder": "Enter reduction goal"}, "climate_impact_ghg_emissions_reduction_goals_metrics": {"type": "string", "title": "Metrics", "placeholder": "Select metrics", "enum": ["tco2eq_absolute_measurement", "tco2eq_as_relation_to_produced_volume_type_in_exact_ratio_eg_02_tco2eq_per_1_t_of_produce", "type_in_other"]}, "climate_impact_ghg_emissions_reduction_goals_scope": {"type": "string", "title": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "enum": ["scope_1", "scope_1_and_2", "scope_1_2_and_3"]}, "climate_impact_ghg_emissions_reduction_goals_baseline_year": {"type": "string", "title": "Baseline Year", "placeholder": "Enter baseline year"}, "climate_impact_ghg_emissions_reduction_goals_target_year": {"type": "string", "title": "Target Year", "placeholder": "Enter target year"}}}}}}, "climate_impact_energy_consumption": {"type": "object", "title": "Energy consumption", "properties": {"climate_impact_energy_consumption_non_renewable_sources": {"type": "object", "x-display": "energy-source-table", "addItemLabel": "Add Other Source", "title": "Check all types of NON-RENEWABLE sources of energy consumed by the Company for the last full calendar year", "description": "Requests details on the consumption of non-renewable energy sources for the last full calendar year, including volume and metric for each type.", "properties": {"climate_impact_energy_consumption_non_renewable_sources_district_heating": {"type": "object", "title": "District Heating", "description": "Indicates whether district heating is consumed as a non-renewable energy source, including corresponding volume metrics.", "properties": {"climate_impact_energy_consumption_non_renewable_sources_district_heating_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_non_renewable_sources_district_heating_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_district_heating_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_non_renewable_sources_gas": {"type": "object", "title": "Gas", "description": "Specifies the consumption of gas as a non-renewable energy source, with volume details provided.", "properties": {"climate_impact_energy_consumption_non_renewable_sources_gas_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_non_renewable_sources_gas_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_gas_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_non_renewable_sources_coal": {"type": "object", "title": "Coal", "description": "Reports on the usage of coal as a non-renewable energy source, including consumption volume.", "properties": {"climate_impact_energy_consumption_non_renewable_sources_coal_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_non_renewable_sources_coal_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_coal_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_non_renewable_sources_diesel": {"type": "object", "title": "Diesel", "description": "Details the consumption of diesel as a non-renewable energy source, measured in volume.", "properties": {"climate_impact_energy_consumption_non_renewable_sources_diesel_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_non_renewable_sources_diesel_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_diesel_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_non_renewable_sources_electricity": {"type": "object", "title": "Electricity", "description": "Indicates whether electricity is consumed as a non-renewable energy source, with volume metrics provided.", "properties": {"climate_impact_energy_consumption_non_renewable_sources_electricity_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_non_renewable_sources_electricity_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_electricity_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_non_renewable_sources_other_sources": {"type": "array", "items": {"type": "object", "properties": {"climate_impact_energy_consumption_non_renewable_sources_other_sources_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_non_renewable_sources_other_sources_type": {"type": "string", "title": "Energy source", "placeholder": "Enter energy source"}, "climate_impact_energy_consumption_non_renewable_sources_other_sources_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_non_renewable_sources_other_sources_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}}}}, "climate_impact_energy_consumption_renewable_sources": {"type": "object", "x-display": "energy-source-table", "addItemLabel": "Add Other Source", "title": "Check all types of RENEWABLE sources of energy consumed by the Company for the last full calendar year", "description": "Requests details on renewable energy consumption for the last full calendar year, including volume and metric for each type.", "properties": {"climate_impact_energy_consumption_renewable_sources_district_heating": {"type": "object", "title": "District heating", "description": "Specifies consumption details of renewable district heating, measured in volume.", "properties": {"climate_impact_energy_consumption_renewable_sources_district_heating_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_renewable_sources_district_heating_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_district_heating_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_renewable_sources_biogas": {"type": "object", "title": "Biogas", "description": "Indicates whether biogas is consumed as a renewable energy source, along with volume metrics.", "properties": {"climate_impact_energy_consumption_renewable_sources_biogas_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_renewable_sources_biogas_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_biogas_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_renewable_sources_electricity": {"type": "object", "title": "Electricity", "description": "Reports on the consumption of renewable electricity, including volume details.", "properties": {"climate_impact_energy_consumption_renewable_sources_electricity_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_renewable_sources_electricity_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_electricity_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_renewable_sources_biomass": {"type": "object", "title": "Biomass", "description": "Details the consumption of biomass as a renewable energy source, with corresponding volume metrics.", "properties": {"climate_impact_energy_consumption_renewable_sources_biomass_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_renewable_sources_biomass_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_biomass_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}, "climate_impact_energy_consumption_renewable_sources_other_sources": {"type": "array", "items": {"type": "object", "properties": {"climate_impact_energy_consumption_renewable_sources_other_sources_enabled": {"type": "boolean"}, "climate_impact_energy_consumption_renewable_sources_other_sources_type": {"type": "string", "title": "Energy source", "placeholder": "Enter energy source"}, "climate_impact_energy_consumption_renewable_sources_other_sources_volume": {"type": "string", "title": "Volume", "placeholder": "Enter volume"}, "climate_impact_energy_consumption_renewable_sources_other_sources_metrics": {"type": "string", "title": "Indicate metrics", "enum": ["kwh", "mwh", "gwh", "m³", "tons"], "placeholder": "metric"}}}}}}, "climate_impact_energy_consumption_efficiency_plan": {"title": "Does the Company have an energy efficiency improvement plan?", "description": "Indicates whether the company has implemented an energy efficiency improvement plan.", "x-display": "boolean-radio", "type": "boolean"}, "climate_impact_energy_consumption_efficiency_plan_details": {"type": "string", "x-display": "textarea", "title": "Please provide details", "description": "Requests additional details regarding the energy efficiency improvement plan, if one is in place.", "placeholder": "Enter efficiency plan details", "dependency": ["climate_impact_energy_consumption_efficiency_plan"]}}}}}, "green_transition": {"type": "object", "title": "Green transition readiness assessment", "description": "To overcome climate change challenges European Union is taking action by increasing regulatory requirements, aiming to help supporting those activities and actions, which positively contribute to limiting negative effects of climate change. For large companies it means increase of regulatory requirements in the forthcoming years according to Corporate Sustainability Reporting Directive regulation (eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32022L2464). At the same time increased awareness towards climate change and threats related to these processes, may lead to change in market behaviour, e.g. consumers giving preference to more sustainable goods and services, business partners willing to cooperate with companies, which practice sustainable approaches, investors - opting to invest in more sustainable businesses. Answers to these questions will help to understand your Company's readiness to green transition.", "properties": {"green_transition_sustainability_strategy": {"type": "object", "title": "Sustainability Strategy", "properties": {"green_transition_sustainability_strategy_has_strategy": {"title": "Does the Company have a sustainability strategy in place or does it exist as an integral part of other documents or policies?", "description": "Determines if the company has a formal sustainability strategy or if sustainability is integrated within other policies or documents.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_sustainability_strategy_strategy_document": {"type": "string", "title": "Link to sustainability strategy", "description": "Requests a link to or submission of the document detailing the company’s sustainability strategy.", "dependency": ["green_transition_sustainability_strategy_has_strategy"], "placeholder": "Please add link"}, "green_transition_sustainability_strategy_additional_documents": {"type": "array", "x-display": "file-upload", "title": "Upload attachment", "description": "Requests a link to or submission of the document detailing the company’s sustainability strategy.", "dependency": ["green_transition_sustainability_strategy_has_strategy"], "items": {"type": "object", "properties": {"key": {"type": "string"}, "mimeType": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}}, "required": ["key", "mimeType", "name"]}}}, "required": ["green_transition_sustainability_strategy_has_strategy"]}, "green_transition_sustainability_report": {"title": "Does the Company prepare sustainability report or has it as an integral part of any other reports?", "description": "Indicates whether the company produces a dedicated sustainability report or integrates sustainability disclosures within other reports.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_other_sustainability_reports": {"type": "object", "title": "Other Sustainability Reports", "properties": {"green_transition_other_sustainability_reports_has_reports": {"title": "Does the Company prepare other reports related to sustainability (e.g., environmental reports) or does it use other forms of communication for its sustainability plans?", "description": "Asks if the company prepares additional sustainability-related reports (e.g., environmental reports) or uses alternative communication channels for its sustainability plans.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_other_sustainability_reports_report_document": {"type": "string", "title": "Link to other sustainability reports", "description": "Requests a link to or submission of any additional sustainability-related report documents.", "placeholder": "Please add link", "dependency": ["green_transition_other_sustainability_reports_has_reports"]}, "green_transition_other_sustainability_reports_additional_documents": {"type": "array", "x-display": "file-upload", "dependency": ["green_transition_other_sustainability_reports_has_reports"], "title": "Upload attachment", "description": "Requests a link to or submission of any additional sustainability-related report documents.", "items": {"type": "object", "properties": {"key": {"type": "string"}, "mimeType": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}}, "required": ["green_transition_other_sustainability_reports_additional_documents_key", "green_transition_other_sustainability_reports_additional_documents_mimeType", "green_transition_other_sustainability_reports_additional_documents_name"]}}}, "required": ["green_transition_other_sustainability_reports_has_reports"]}, "green_transition_sustainability_certification": {"type": "object", "title": "Sustainability Certification", "properties": {"green_transition_sustainability_certification_has_certification": {"title": "Does the Company hold certification of Sustainability standards or Environmental Product Declaration (EPD)?", "description": "Determines if the company holds certifications for sustainability standards or an Environmental Product Declaration (EPD).", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_sustainability_certification_certification_details": {"type": "string", "x-display": "textarea", "title": "Please provide details", "description": "Provides space for the company to detail its sustainability certification or EPD information if applicable.", "dependency": ["green_transition_sustainability_certification_has_certification"], "placeholder": "Please provide details"}}, "required": ["green_transition_sustainability_certification_has_certification"]}, "green_transition_environmentally_friendly_goods": {"type": "object", "title": "Environmentally Friendly Goods", "properties": {"green_transition_environmentally_friendly_goods_has_eco_goods": {"title": "Does the Company supply environmentally friendly goods or use environmentally friendly approaches?", "description": "Evaluates whether the company supplies environmentally friendly goods or employs circular economic approaches.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_environmentally_friendly_goods_eco_goods_details": {"type": "string", "x-display": "textarea", "title": "Please provide details", "description": "Requests further details on the company’s environmentally friendly practices or product lines.", "dependency": ["green_transition_environmentally_friendly_goods_has_eco_goods"], "placeholder": "Please provide details"}}, "required": ["green_transition_environmentally_friendly_goods_has_eco_goods"]}, "green_transition_epd_goods_services": {"type": "object", "title": "Environmental Product Declaration (EPD)", "properties": {"green_transition_epd_goods_services_has_epd": {"title": "Does the Company hold EPD on produced goods/services?", "description": "Indicates whether the company holds an Environmental Product Declaration (EPD) for its produced goods or services.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_epd_goods_services_epd_details": {"type": "string", "title": "Please provide details", "description": "Requests additional details regarding the EPD if one is held by the company.", "x-display": "textarea", "dependency": ["green_transition_epd_goods_services_has_epd"], "placeholder": "Please provide details"}}, "required": ["green_transition_epd_goods_services_has_epd"]}, "green_transition_carbon_offsetting": {"type": "object", "title": "Carbon Offsetting", "properties": {"green_transition_carbon_offsetting_has_offsetting": {"title": "Does the Company have carbon offsetting in place?", "description": "Determines whether the company participates in carbon offsetting initiatives to mitigate its carbon footprint.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_carbon_offsetting_offsetting_details": {"type": "string", "title": "Please provide details", "description": "Provides space for the company to detail its carbon offsetting practices, if applicable.", "x-display": "textarea", "dependency": ["green_transition_carbon_offsetting_has_offsetting"], "placeholder": "Please provide details"}}, "required": ["green_transition_carbon_offsetting_has_offsetting"]}, "green_transition_international_standards": {"type": "object", "title": "International Standards", "properties": {"green_transition_international_standards_follows_standards": {"title": "Does the Company follow any international or local best practice standards?", "description": "Assesses whether the company adheres to international or local best practice standards or has committed to external sustainability commitments.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_international_standards_standards_details": {"type": "string", "title": "Please provide details", "description": "Requests additional details on the best practice standards or external commitments the company has signed.", "x-display": "textarea", "dependency": ["green_transition_international_standards_follows_standards"], "placeholder": "Please provide details"}}, "required": ["green_transition_international_standards_follows_standards"]}, "green_transition_eu_taxonomy": {"type": "object", "title": "Has the Company implemented any activities contributing to the following EU Taxonomy of environmental objectives?", "description": "Determines if the company has implemented activities that contribute to the EU Taxonomy environmental objectives, and requests detailed information if so.", "properties": {"green_transition_eu_taxonomy_climate_change_mitigation": {"type": "object", "properties": {"green_transition_eu_taxonomy_climate_change_mitigation_implemented": {"title": "Climate change mitigation", "description": "Indicates if the company has initiatives aimed at mitigating climate change in line with EU Taxonomy objectives.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_eu_taxonomy_climate_change_mitigation_details": {"type": "string", "title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to climate change mitigation implemented by the company.", "x-display": "textarea", "dependency": ["green_transition_eu_taxonomy_climate_change_mitigation_implemented"], "placeholder": "Please provide details"}}, "required": ["green_transition_eu_taxonomy_climate_change_mitigation_implemented"]}, "green_transition_eu_taxonomy_climate_change_adaptation": {"type": "object", "properties": {"green_transition_eu_taxonomy_climate_change_adaptation_implemented": {"title": "Climate change adaptation", "description": "Evaluates whether the company has measures in place to adapt to the impacts of climate change, as per EU Taxonomy requirements.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_eu_taxonomy_climate_change_adaptation_details": {"type": "string", "title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to climate change adaptation implemented by the company.", "x-display": "textarea", "dependency": ["green_transition_eu_taxonomy_climate_change_adaptation_implemented"], "placeholder": "Please provide details"}}, "required": ["green_transition_eu_taxonomy_climate_change_adaptation_implemented"]}, "green_transition_eu_taxonomy_sustainable_water_use": {"type": "object", "properties": {"green_transition_eu_taxonomy_sustainable_water_use_implemented": {"title": "The sustainable use and protection of water and marine resources", "description": "Assesses the company’s activities related to the sustainable use and protection of water and marine resources.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_eu_taxonomy_sustainable_water_use_details": {"type": "string", "title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to the sustainable use and protection of water and marine resources implemented by the company.", "x-display": "textarea", "dependency": ["green_transition_eu_taxonomy_sustainable_water_use_implemented"], "placeholder": "Please provide details"}}, "required": ["green_transition_eu_taxonomy_sustainable_water_use_implemented"]}, "green_transition_eu_taxonomy_circular_economy": {"type": "object", "properties": {"green_transition_eu_taxonomy_circular_economy_implemented": {"title": "The transition to a circular economy", "description": "Evaluates if the company is transitioning to a circular economy by implementing relevant activities.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_eu_taxonomy_circular_economy_details": {"type": "string", "title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to the transition to a circular economy implemented by the company.", "x-display": "textarea", "dependency": ["green_transition_eu_taxonomy_circular_economy_implemented"], "placeholder": "Please provide details"}}, "required": ["green_transition_eu_taxonomy_circular_economy_implemented"]}, "green_transition_eu_taxonomy_pollution_prevention": {"type": "object", "properties": {"green_transition_eu_taxonomy_pollution_prevention_implemented": {"title": "Pollution prevention and control", "description": "Assesses if the company has measures in place for pollution prevention and control in accordance with environmental standards.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_eu_taxonomy_pollution_prevention_details": {"type": "string", "title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to pollution prevention and control implemented by the company.", "x-display": "textarea", "dependency": ["green_transition_eu_taxonomy_pollution_prevention_implemented"], "placeholder": "Please provide details"}}, "required": ["green_transition_eu_taxonomy_pollution_prevention_implemented"]}, "green_transition_eu_taxonomy_biodiversity_protection": {"type": "object", "properties": {"green_transition_eu_taxonomy_biodiversity_protection_implemented": {"title": "The protection and restoration of biodiversity and ecosystems", "description": "Indicates if the company has initiatives focused on protecting and restoring biodiversity and ecosystems.", "x-display": "boolean-radio", "type": "boolean"}, "green_transition_eu_taxonomy_biodiversity_protection_details": {"type": "string", "title": "Please provide details", "description": "Requests detailed information on the specific activities contributing to biodiversity protection implemented by the company.", "x-display": "textarea", "dependency": ["green_transition_eu_taxonomy_biodiversity_protection_implemented"], "placeholder": "Please provide details"}}, "required": ["green_transition_eu_taxonomy_biodiversity_protection_implemented"]}}}}, "required": ["green_transition_sustainability_strategy", "green_transition_sustainability_report", "green_transition_other_sustainability_reports", "green_transition_sustainability_certification", "green_transition_environmentally_friendly_goods", "green_transition_epd_goods_services", "green_transition_carbon_offsetting", "green_transition_international_standards", "green_transition_eu_taxonomy"]}, "nature": {"type": "object", "title": "Nature", "description": "Biodiversity is a variety of forms of life on earth - such as different species of plants and animals found within specific regions. Biodiversity is essential for functioning of all processes: from ensuring survival of all life forms to climate regulation and economic stability. Biodiversity is also a fundamental component of long-term business survival. Biodiversity underpins the benefits that businesses derive from natural capital and supports the key ecosystem functions to maintain quality of soil, water and air. While the private sector can have a negative effect on Biodiversity, it can also be part of the solution. The resources and impact of the private sector offer important opportunities for innovative and effective contributions to conservation. Answers provided to the questions of this section will allow to get basic information on the impact, which business activity of your Company can have on biodiversity.", "properties": {"nature_biodiversity_sensitive_areas": {"title": "Does the Company operate in or near biodiversity-sensitive areas (including the Natura 2000 network of protected areas, UNESCO Natural World Heritage sites and Key Biodiversity Areas)?", "description": "Determines if the company operates in or near biodiversity-sensitive areas, such as Natura 2000 sites or UNESCO World Heritage areas.", "x-display": "boolean-radio", "type": "boolean"}, "nature_negative_impact_on_nature": {"type": "string", "title": "Can your Company's operations negatively affect nature's realms in any way: the oceans, freshwater, land, atmosphere?", "description": "Assesses whether the company’s operations negatively impact natural realms such as oceans, freshwater, land, or the atmosphere.", "enum": ["yes_critically_eg_the_ploughing_of_natural_meadows_construction_works_in_nature_reserves_forest_transformation_deforestation_and_clear-cutting", "yes_other_impact", "no_impact", "i_dont_know"], "placeholder": "Answer"}, "nature_negative_impact_comments": {"type": "string", "title": "If yes, please comment:", "description": "Provides an opportunity for the company to comment on any negative impacts its operations may have on nature.", "placeholder": "Please provide details", "dependency": ["nature_negative_impact_on_nature"], "x-display": "textarea"}, "nature_plan_to_reduce_impact": {"title": "If you answered Yes to question 1. or 2., have you developed a plan of measures to prevent or reduce possible negative impacts?", "description": "Determines if the company has developed a plan to prevent or reduce negative environmental impacts identified in previous questions.", "x-display": "boolean-radio", "type": "boolean", "dependency": ["nature_biodiversity_sensitive_areas", "nature_negative_impact_on_nature"]}, "nature_measures_details": {"type": "object", "title": "Please indicate the measure(-s) and comment(-s) for selected measures", "helpText": "Requests detailed information on the specific measures and comments for each mitigation action undertaken to reduce negative environmental impacts.", "dependency": ["nature_plan_to_reduce_impact"], "properties": {"nature_measures_details_avoid": {"type": "string", "title": "Avoid: Actions taken to avoid causing a negative impact.", "description": "Describes actions taken by the company to avoid causing any negative environmental impacts.", "placeholder": "Please provide details", "x-display": "textarea", "dependency": ["nature.nature_plan_to_reduce_impact"]}, "nature_measures_details_mitigate": {"type": "string", "title": "Mitigate: measures taken to reduce the significance/intensity of any negative impacts that cannot be completely avoided.", "description": "Describes measures implemented to reduce the significance or intensity of unavoidable negative environmental impacts.", "placeholder": "Please provide details", "x-display": "textarea", "dependency": ["nature.nature_plan_to_reduce_impact"]}, "nature_measures_details_restore": {"type": "string", "title": "Restore: Actions taken to restore ecosystems degraded from negative impacts that cannot be fully avoided and/or minimized.", "description": "Outlines actions taken by the company to restore ecosystems that have been degraded due to unavoidable negative impacts.", "placeholder": "Please provide details", "x-display": "textarea", "dependency": ["nature.nature_plan_to_reduce_impact"]}, "nature_measures_details_compensate": {"type": "string", "title": "Compensate: Actions taken to compensate for residual adverse effects that cannot be prevented, reduced and/or restored.", "description": "Describes compensation measures taken to address any residual adverse environmental effects that cannot be fully mitigated.", "placeholder": "Please provide details", "x-display": "textarea", "dependency": ["nature.nature_plan_to_reduce_impact"]}}}, "nature_natural_capital_dependency": {"title": "Is your Company's activity critically dependant on natural capital: renewable and non-renewable natural resources such as plants, forest, animals, air, water, soil and minerals.", "description": "Assesses the extent to which the company’s activities depend on natural capital, including both renewable and non-renewable resources.", "x-display": "boolean-radio", "type": "boolean"}, "nature_natural_capital_plan": {"title": "If Yes, have you developed a plan of measures to mitigate or reduce the dependency on natural capital?", "description": "Determines if the company has developed measures to mitigate or reduce its dependency on natural capital.", "x-display": "boolean-radio", "type": "boolean", "dependency": ["nature_natural_capital_dependency"]}, "nature_natural_capital_measures": {"type": "string", "title": "If yes, please indicate the measures", "description": "Requests details on specific measures implemented to reduce dependency on natural capital.", "placeholder": "Please provide details", "x-display": "textarea", "dependency": ["nature_natural_capital_plan"]}, "nature_waste_management": {"title": "Does the Company have a waste management procedure in place?", "description": "Indicates whether the company has a formal waste management procedure in place to manage and reduce waste.", "x-display": "boolean-radio", "type": "boolean"}, "nature_nature_commitments": {"title": "Has your Company committed to Nature positive objectives, the Taskforce on Nature-related Financial Disclosures(TNFD) framework, Science-based Targets for Nature or similar initiatives?", "description": "Determines if the company has committed to nature-positive objectives or frameworks such as the TNFD.", "x-display": "boolean-radio", "type": "boolean"}, "nature_nature_commitments_details": {"type": "string", "title": "If yes, please provide details:", "description": "Requests additional details on the company’s commitments to nature-positive initiatives or frameworks.", "placeholder": "Please provide details", "x-display": "textarea", "dependency": ["nature_nature_commitments"]}}, "required": ["nature_biodiversity_sensitive_areas", "nature_negative_impact_on_nature", "nature_natural_capital_dependency", "nature_waste_management", "nature_nature_commitments"]}, "social_governance": {"type": "object", "title": "Social and governance practices", "description": "Social and governance framework is essential in order to implement and maintain sustainability practices. Social area covers impact on surrounding community and workers (such as health and safety, diversity and inclusion). Corporate governance refers to processes, which ensure effective management of the Company (e.g. ethical conduct, risk management, transparency).", "properties": {"social_governance_workforce_policies": {"type": "array", "minItems": 1, "title": "What policies and procedures related to benefits, engagement and human rights protection of own workforce the Company has in place?", "description": "Requests disclosure of the policies and procedures related to employee benefits, engagement, and human rights protection.", "x-display": "multiselect", "placeholder": "Select Workforce Policies", "items": {"type": "string", "enum": ["personnel_management", "remuneration", "safety_and_health", "equality", "diversity", "harassment", "child_labour", "other"]}}, "social_governance_end_user_policies": {"type": "string", "title": "What policies and procedures related to end-users the Company has in place?", "description": "Requests details on the policies and procedures in place to ensure product safety and social responsibility for end-users.", "x-display": "textarea", "placeholder": "Please provide details"}, "social_governance_safety_incidents": {"type": "object", "title": "Safety Incidents", "properties": {"social_governance_safety_incidents_has_incidents": {"x-display": "boolean-radio", "type": "boolean", "title": "Have there been any significant incidents related to the safety or quality of the Company's products or services in the last 3 years?", "description": "Inquires about any significant incidents related to the safety or quality of the company’s products or services over the last three years."}, "social_governance_safety_incidents_details": {"type": "string", "title": "If yes, please provide details", "description": "Provides space for the company to detail any incidents affecting product or service safety and quality.", "dependency": ["social_governance_safety_incidents_has_incidents"], "x-display": "textarea", "placeholder": "Please provide details"}}, "required": ["social_governance_safety_incidents_has_incidents"]}, "social_governance_stakeholder_engagement": {"type": "object", "title": "Stakeholder Engagement", "properties": {"social_governance_stakeholder_engagement_has_programs": {"title": "Does the Company implement stakeholder engagement, impact assessment, and/or development programs to avoid, minimize or mitigate impacts of operation?", "description": "Assesses whether the company engages in stakeholder engagement, impact assessment, and development programs to mitigate operational impacts.", "x-display": "boolean-radio", "type": "boolean"}, "social_governance_stakeholder_engagement_details": {"type": "string", "title": "If yes, please provide details", "description": "Requests additional details on stakeholder engagement or impact assessment programs implemented by the company.", "dependency": ["social_governance_stakeholder_engagement_has_programs"], "x-display": "textarea", "placeholder": "Please provide details"}}, "required": ["social_governance_stakeholder_engagement_has_programs"]}, "social_governance_community_benefits": {"type": "object", "title": "Community Benefits", "properties": {"social_governance_community_benefits_has_initiatives": {"title": "Does the Company have any initiatives to provide benefits to local communities or to underserved markets?", "description": "Determines if the company has initiatives aimed at providing benefits to local communities or underserved markets.", "x-display": "boolean-radio", "type": "boolean"}, "social_governance_community_benefits_details": {"type": "string", "title": "If yes, please provide details", "description": "Provides space for the company to describe initiatives targeted at supporting local communities.", "dependency": ["social_governance_community_benefits_has_initiatives"], "x-display": "textarea", "placeholder": "Please provide details"}}, "required": ["social_governance_community_benefits_has_initiatives"]}, "social_governance_supply_chain_esg": {"type": "object", "title": "ESG Criteria in Supply Chain", "description": "(e.g. working conditions, human rights, privacy, labour rights, health and safety protection, social and labour protection, environmental protection)", "properties": {"social_governance_supply_chain_esg_has_policies": {"x-display": "boolean-radio", "type": "boolean", "title": "Does the Company have policies and/or processes in place that cover and/or address ESG criteria within its supply chain?", "description": "Assesses if the company has established policies or processes to address ESG criteria within its supply chain."}, "social_governance_supply_chain_esg_details": {"type": "string", "title": "If yes, please provide details", "description": "Requests additional details on how the company ensures ESG compliance within its supply chain.", "dependency": ["social_governance_supply_chain_esg_has_policies"], "x-display": "textarea", "placeholder": "Please provide details"}}, "required": ["social_governance_supply_chain_esg_has_policies"]}, "social_governance_international_compliance": {"type": "object", "title": "International Compliance", "description": "including the principles and rights set out in the eight core conventions and the International Labour Organization Declaration on Fundamental Principles and Rights at Work and the International Bill of Human Rights (as well as local regulations regarding human and employee rights)?", "properties": {"social_governance_international_compliance_complies": {"x-display": "boolean-radio", "type": "boolean", "title": "Do the company's policies and procedures comply with the OECD Guidelines for Multinational Enterprises and the UN Guiding Principles on Business and Human Rights?", "description": "Determines if the company’s policies comply with international guidelines such as the OECD Guidelines and UN Guiding Principles on Business and Human Rights."}}}, "social_governance_governance_policies": {"type": "array", "minItems": 1, "title": "Please indicate which areas are covered in the Company's internal policies and procedures:", "x-display": "multiselect", "placeholder": "Select Governance Policies", "description": "Determines if the company’s policies comply with international guidelines such as the OECD Guidelines and UN Guiding Principles on Business and Human Rights.", "items": {"type": "string", "enum": ["corporate_governance", "conflict_of_interest_management", "respect_of_human_rights_including_labour_right", "prevention_of_money_laundering", "whistleblowing", "fraud_and_corruption_prevention", "tax_transparency", "risk_management", "data_security_and_cyber_security", "code_of_conduct_and_ethics", "other"]}}, "social_governance_materiality_assessment": {"title": "Has the Company performed a materiality assessment in the area ESG and identified material ESG related risks?", "description": "Determines if the company has performed an ESG materiality assessment to identify key environmental, social, and governance risks.", "x-display": "boolean-radio", "type": "boolean"}, "social_governance_supplier_code_of_conduct": {"title": "Has the Company developed a supplier code of conduct that specifies environmental, social, and governance requirements that suppliers must adhere to?", "description": "Assesses whether the company has developed a supplier code of conduct outlining the ESG standards required of its suppliers.", "x-display": "boolean-radio", "type": "boolean"}, "social_governance_supplier_monitoring": {"type": "string", "title": "To what extent does the Company monitor and assess the performance of suppliers in the field of sustainability?", "description": "Requests details on the extent to which the company monitors and assesses supplier sustainability performance.", "enum": ["high_level_of_monitoring", "medium_level_of_monitoring", "low_level_of_monitoring", "no_monitoring"], "placeholder": "Answer"}, "social_governance_litigation": {"type": "object", "title": "Litigation and Controversies", "description": "e.g. water, waste, energy, biodiversity, environment regulations or water, energy, waste, biodiversity, environment disturbances, human and labor rights, health and safety, product/service of the company?", "properties": {"social_governance_litigation_has_litigation": {"title": "Has the company been involved in any litigation, controversy or concerns in relation to non-compliance?", "description": "Inquires if the company has been involved in any litigation, controversy, or concerns related to non-compliance with environmental, social, or regulatory standards.", "x-display": "boolean-radio", "type": "boolean"}}}, "social_governance_non_compliances": {"title": "Have there been non-compliances or controversies including a warning, fine, penalty, closure of the company?", "x-display": "boolean-radio", "type": "boolean", "description": "Specifies whether the company has faced non-compliances, warnings, fines, or penalties due to regulatory breaches."}, "social_governance_privacy_breaches": {"description": "Inquires if there have been substantiated complaints regarding breaches of customer privacy or data protection issues.", "title": "Have there been any records of substantiated complaints concerning breaches of customer privacy, incidents related to data protection and losses of customer data?", "x-display": "boolean-radio", "type": "boolean"}, "social_governance_compliance_officer": {"description": "Indicates whether the company has designated an officer responsible for ensuring compliance with internal governance policies.", "x-display": "boolean-radio", "type": "boolean"}}, "required": ["social_governance_workforce_policies", "social_governance_end_user_policies", "social_governance_international_compliance", "social_governance_governance_policies", "social_governance_materiality_assessment", "social_governance_supplier_code_of_conduct", "social_governance_supplier_monitoring", "social_governance_litigation", "social_governance_non_compliances", "social_governance_privacy_breaches", "social_governance_compliance_officer"]}, "nfrd_reporting": {"type": "object", "title": "Please indicate the proportion of Taxonomy-eligible economic activities in the net turnover, capital expenditure (Capex) and operational expenditure (Opex):", "description": "Requests disclosure of the proportion of economic activities that are eligible under the EU Taxonomy framework.", "properties": {"nfrd_reporting_taxonomy_eligible_activities": {"type": "object", "title": "Please indicate the proportion of Taxonomy-eligible economic activities in the net turnover, capital expenditure (Capex)", "helpText": "Requests disclosure of the proportion of economic activities that are eligible under the EU Taxonomy framework.", "properties": {"nfrd_reporting_taxonomy_eligible_activities_sustainable_net_turnover": {"type": "number", "title": "Net turnover of environmentally sustainable activities (Taxonomy aligned)", "description": "Reports the net turnover from environmentally sustainable activities that are aligned with the EU Taxonomy.", "minimum": 0, "maximum": 100, "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_eligible_net_turnover": {"type": "number", "title": "Net turnover of Taxonomy eligible, but not environmentally sustainable activities (not Taxonomy aligned)", "description": "Reports the net turnover from activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable.", "minimum": 0, "maximum": 100, "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_sustainable_capex": {"type": "number", "title": "Capex of environmentally sustainable activities (Taxonomy aligned)", "description": "Reports the capital expenditure on environmentally sustainable activities that are aligned with the EU Taxonomy.", "minimum": 0, "maximum": 100, "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_eligible_capex": {"type": "number", "title": "Capex of Taxonomy eligible, but unsustainable activities (not Taxonomy aligned)", "description": "Reports the capital expenditure on activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable.", "minimum": 0, "maximum": 100, "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_sustainable_opex": {"type": "number", "title": "Opex of environmentally sustainable activities (Taxonomy aligned)", "description": "Reports the operational expenditure on environmentally sustainable activities that are aligned with the EU Taxonomy.", "minimum": 0, "maximum": 100, "placeholder": "Enter percentage"}, "nfrd_reporting_taxonomy_eligible_activities_eligible_opex": {"type": "number", "title": "Opex of Taxonomy eligible, but unsustainable activities (not Taxonomy aligned)", "description": "Reports the operational expenditure on activities that are eligible under the EU Taxonomy but are not considered environmentally sustainable.", "minimum": 0, "maximum": 100, "placeholder": "Enter percentage"}}, "required": ["nfrd_reporting_taxonomy_eligible_activities_sustainable_net_turnover", "nfrd_reporting_taxonomy_eligible_activities_eligible_net_turnover", "nfrd_reporting_taxonomy_eligible_activities_sustainable_capex", "nfrd_reporting_taxonomy_eligible_activities_eligible_capex", "nfrd_reporting_taxonomy_eligible_activities_sustainable_opex", "nfrd_reporting_taxonomy_eligible_activities_eligible_opex"]}}, "required": ["nfrd_reporting_taxonomy_eligible_activities"]}}}