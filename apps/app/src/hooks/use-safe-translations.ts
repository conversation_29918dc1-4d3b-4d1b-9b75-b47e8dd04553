/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useTranslations } from "next-intl"

/**
 * Safely checks if a translation key exists and returns the translation or undefined
 * @param key The translation key to check
 * @returns The translated string if key exists, undefined otherwise
 */
export function useSafeTranslation() {
  const t = useTranslations()

  return (key: string): string | undefined => {
    // Check if the translation key exists
    if (t.has(key)) {
      return t(key)
    }
    // Return undefined for missing keys without triggering a warning
    return undefined
  }
}
