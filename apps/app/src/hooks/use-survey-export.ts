/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { useMutation } from "@tanstack/react-query"
import contentDisposition from "content-disposition"
import { saveAs } from "file-saver"

/**
 * Hook to export a survey as a zip file
 * @returns A mutation hook to export a survey as a zip file
 */
export function useSurveyExport() {
  return useMutation({
    mutationKey: ["survey-export"],
    mutationFn: async (surveyIdOrIds: string | string[]) => {
      try {
        const response = await fetch(`/api/survey-export`, {
          method: "POST",
          body: JSON.stringify({ id: surveyIdOrIds }),
        })

        if (!response.ok) throw new Error(`Export failed: ${response.status} ${response.statusText}`)

        const contentDispositionHeader = response.headers.get("content-disposition")

        const filename = contentDispositionHeader
          ? contentDisposition.parse(contentDispositionHeader).parameters.filename
          : `surveys_${new Date().toISOString().split("T")[0]}.zip`

        const blob = await response.blob()
        saveAs(blob, filename)

        return { success: true, filename }
      } catch (error) {
        console.error("[Export] Error during export:", error)
        throw error
      }
    },
  })
}
