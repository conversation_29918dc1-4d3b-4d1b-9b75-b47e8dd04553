/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { useMutation } from "@tanstack/react-query"

interface UploadResponse {
  success: boolean
  message: string
  file_path: string
  file_url: string
}

export function useFileUpload() {
  return useMutation<UploadResponse, Error, File>({
    mutationFn: async (file) => {
      const formData = new FormData()
      formData.append("file", file)

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Upload failed")
      }

      return response.json() as Promise<UploadResponse>
    },
  })
}
