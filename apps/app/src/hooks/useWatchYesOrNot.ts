/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { Control, FieldValues, Path, PathValue } from "react-hook-form"
import { useWatch } from "react-hook-form"

interface UseWatchYesOrNotProps<TFieldValues extends FieldValues> {
  control: Control<TFieldValues>
  dependencyArray: readonly Path<TFieldValues>[]
}

export const useWatchYesOrNot = <TFieldValues extends FieldValues>({
  control,
  dependencyArray,
}: UseWatchYesOrNotProps<TFieldValues>): boolean => {
  const values = useWatch<TFieldValues, Path<TFieldValues>[]>({
    control,
    name: dependencyArray,
  }) as PathValue<TFieldValues, Path<TFieldValues>>[]

  return dependencyArray.length
    ? values.some((value) => {
        if (typeof value === "string") {
          return (value as string).startsWith("yes")
        }
        return !!value
      })
    : true
}
