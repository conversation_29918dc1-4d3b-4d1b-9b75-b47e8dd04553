/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { Options } from "nuqs"
import { useQueryState } from "nuqs"

const useTabs = <T extends string>(initialValue: T, searchParam: string) => {
  const [tab, setTab] = useQueryState(searchParam, { defaultValue: initialValue })
  return {
    tab: tab as T,
    setTab: setTab as (value: T | ((old: T) => T | null) | null, options?: Options) => Promise<URLSearchParams>,
  }
}

export default useTabs
