/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { RouterOutput } from "@/lib/trpc-provider"
import { memo, useMemo } from "react"
import Link from "next/link"
import { ChevronLeft, ChevronRight, Info } from "lucide-react"
import { useTranslations } from "next-intl"
import { Cell, Pie, PieChart, ResponsiveContainer } from "recharts"

import { Button } from "@kreios/ui/button"
import { Card } from "@kreios/ui/card"

const COLORS = {
  completed: "#2E7D32",
  inProgress: "#F59F00",
  notStarted: "#E9ECEF",
} as const

type Survey = RouterOutput["surveys"]["listSurveys"]["data"][number]

export interface SurveyPaginationProps {
  currentPage: number
  totalPages: number
  onPrevious: () => void
  onNext: () => void
}

export interface Batch {
  id: string
  title: string
  companies: string
  complete: number
  inProgress: number
  notStarted: number
  open: number
  submitted: number
  sent: number
  pending: number
}

export interface BatchCardProps {
  batch: Batch
  /**
   * The base path for the batch detail page
   * e.g. "/admin/surveys" or "/admin/assessment-batches"
   */
  basePath: string
}

export const EmptyBatchCard = memo(({ message }: { message: string }) => (
  <Card className="flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white p-8 dark:border-gray-700 dark:bg-gray-800">
    <Info className="mr-4 h-6 w-6 flex-shrink-0 text-gray-500 dark:text-gray-400" />
    <p className="text-center text-gray-600 dark:text-gray-300">{message}</p>
  </Card>
))

const BatchPieChart = ({ pieData }: { pieData: ReadonlyArray<{ name: string; value: number }> }) => {
  const t = useTranslations("surveyStatus")
  const total = pieData.reduce((sum, d) => sum + d.value, 0)
  const isEmpty = total === 0
  return (
    <div className="relative mx-auto aspect-square w-full max-w-[120px]">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={isEmpty ? [{ name: "empty", value: 100 }] : [...pieData]}
            cx="50%"
            cy="50%"
            innerRadius={30}
            outerRadius={50}
            paddingAngle={2}
            dataKey="value"
            isAnimationActive
          >
            {isEmpty ? (
              <Cell fill="#E9ECEF" />
            ) : (
              <>
                <Cell fill={COLORS.completed} />
                <Cell fill={COLORS.inProgress} />
                <Cell fill={COLORS.notStarted} />
              </>
            )}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="text-[10px] font-medium text-gray-400 dark:text-gray-300">{t("complete")}</div>
          <div className="text-base font-semibold text-gray-600 dark:text-gray-300">{pieData[0].value}%</div>
        </div>
      </div>
    </div>
  )
}

export const BatchCard = memo(({ batch, basePath }: BatchCardProps) => {
  const t = useTranslations("surveyStatus")

  const pieData = useMemo(
    () => [
      { name: "Completed", value: batch.complete },
      { name: "In Progress", value: batch.inProgress },
      { name: "Not Started", value: batch.notStarted },
    ],
    [batch]
  )

  return (
    <Link href={`${basePath}/${batch.id}`} className="block">
      <Card className="flex h-full flex-col border-gray-200 bg-white p-3 shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-2 flex items-start justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">{batch.title}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">{batch.companies}</p>
          </div>
          {/* <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-500">
            <MoreVertical className="h-4 w-4" />
          </Button> */}
        </div>
        <BatchPieChart pieData={pieData} />
        <div className="mt-auto grid grid-cols-3 gap-1 text-xs">
          {Object.entries({
            [t("complete")]: COLORS.completed,
            [t("in_progress")]: COLORS.inProgress,
            [t("not_started")]: "#4b5563",
          }).map(([status, color], index) => (
            <div key={index} className="text-center">
              <div className="whitespace-nowrap font-medium" style={{ color }}>
                {status}
              </div>
              <div className="text-gray-600 dark:text-gray-300">{pieData[index].value}%</div>
            </div>
          ))}
        </div>
      </Card>
    </Link>
  )
})

export const SurveyPagination = memo(({ currentPage, totalPages, onPrevious, onNext }: SurveyPaginationProps) => {
  const t = useTranslations("pagination")

  return (
    <div className="mt-8 flex flex-row items-center justify-end gap-4">
      <Button variant="outline" size="sm" className="text-sm" onClick={onPrevious} disabled={currentPage === 1}>
        <ChevronLeft className="mr-2 h-4 w-4" />
        {t("previous")}
      </Button>
      <div className="text-sm text-gray-600 dark:text-gray-300">{t("pageOf", { currentPage, totalPages })}</div>
      <Button variant="outline" size="sm" className="text-sm" onClick={onNext} disabled={currentPage === totalPages}>
        {t("next")}
        <ChevronRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  )
})

export const SkeletonGrid = memo(({ itemsCount }: { itemsCount: number }) => (
  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
    {Array.from({ length: itemsCount }).map((_, index) => (
      <Card key={index} className="h-[300px] animate-pulse bg-gray-100 dark:bg-gray-700" />
    ))}
  </div>
))

export const ProgressStats = memo(({ stats }: { stats: Record<Lowercase<Survey["status"]>, number> }) => {
  const t = useTranslations("surveyStatus")
  return (
    <div className="p-4 md:p-6">
      <div className="flex h-10 rounded-full">
        {["not_started", "in_progress", "complete", "cancelled"].map((status, index) => {
          const colors = ["bg-gray-700", "bg-blue-800", "bg-green-800", "bg-yellow-700"]
          const percentage = stats[status as keyof typeof stats]
          return (
            <div
              key={status}
              className={`relative h-10 ${colors[index]} ${index === 0 ? "rounded-l-full" : index === 3 ? "rounded-r-full" : ""}`}
              style={{ width: `${percentage}%`, minWidth: "15%" }}
            >
              <span className="absolute inset-0 left-1 top-1/2 flex -translate-y-1/2 items-center justify-center text-sm font-semibold text-white">
                {t(status)}: {percentage}%
              </span>
            </div>
          )
        })}
      </div>
    </div>
  )
})
