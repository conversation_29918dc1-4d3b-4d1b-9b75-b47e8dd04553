"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { useTranslations } from "next-intl"

import type { DataTableProps } from "@kreios/datatable/data-table-client"
import type { FilterConfig } from "@kreios/datatable/data-table-toolbar"
import { DataTable } from "@kreios/datatable"

/**
 * A wrapper around DataTable that translates facet labels on the client side.
 * This ensures that facet labels are updated when the language changes without requiring a refetch.
 */
export function TranslatedFacetsDataTable<TData, TValue, TFacetMode extends "toolbar" | "sidebar" = "toolbar">(
  props: DataTableProps<TData, TValue, TFacetMode>
) {
  const surveyStatusT = useTranslations("surveyStatus")

  // Return the DataTable with the wrapped procedure
  return (
    <DataTable
      {...props}
      procedure={{
        ...props.procedure,
        useSuspenseQuery: (input: unknown) => {
          const { useSuspenseQuery } = props.procedure
          // eslint-disable-next-line react-compiler/react-compiler
          const [data, utils] = useSuspenseQuery(
            input as {
              page: number
              limit: number
              filters?: Record<string, unknown>
              search?: string
              sort?: Record<string, "asc" | "desc">
            }
          )

          return [
            {
              ...data,
              facets: data.facets.map((facet: FilterConfig) => {
                if (facet.column === "status") {
                  return {
                    ...facet,
                    title: surveyStatusT("status"),
                    options: facet.options.map((option) => ({
                      ...option,
                      label: surveyStatusT(option.label),
                    })),
                  }
                }
                return facet
              }),
            },
            utils,
          ] as const
        },
      }}
    />
  )
}
