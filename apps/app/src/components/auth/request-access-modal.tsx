/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC } from "react"
import { requestAccess } from "@/actions/request-access"
import { useTranslations } from "next-intl"
import { useFormState } from "react-dom"

import { Button } from "@kreios/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@kreios/ui/dialog"
import { Input } from "@kreios/ui/input"
import { Label } from "@kreios/ui/label"
import { Textarea } from "@kreios/ui/textarea"

interface RequestAccessModalProps {
  isOpen: boolean
  onClose: () => void
  userEmail?: string
}

export const RequestAccessModal: FC<RequestAccessModalProps> = ({ isOpen, onClose, userEmail }) => {
  const t = useTranslations("auth.requestAccess")
  const [state, formAction] = useFormState(requestAccess, {})

  // Close modal on success
  if (state.success) {
    setTimeout(() => {
      onClose()
    }, 2000)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>

        {state.success ? (
          <div className="py-4">
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">{t("successTitle")}</h3>
              <p className="text-sm text-gray-500">{t("successMessage")}</p>
            </div>
          </div>
        ) : (
          <form action={formAction} className="space-y-4">
            <input type="hidden" name="email" value={userEmail} />

            {state.error && <div className="text-sm text-destructive">{state.error.message}</div>}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t("firstName")}</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  required
                  className={state.error?.fieldErrors?.firstName ? "border-destructive" : ""}
                />
                {state.error?.fieldErrors?.firstName && (
                  <p className="text-xs text-destructive">{state.error.fieldErrors.firstName[0]}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">{t("lastName")}</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  required
                  className={state.error?.fieldErrors?.lastName ? "border-destructive" : ""}
                />
                {state.error?.fieldErrors?.lastName && (
                  <p className="text-xs text-destructive">{state.error.fieldErrors.lastName[0]}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyName">{t("companyName")}</Label>
              <Input
                id="companyName"
                name="companyName"
                required
                className={state.error?.fieldErrors?.companyName ? "border-destructive" : ""}
              />
              {state.error?.fieldErrors?.companyName && (
                <p className="text-xs text-destructive">{state.error.fieldErrors.companyName[0]}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">{t("message")}</Label>
              <Textarea id="message" name="message" placeholder={t("messagePlaceholder")} rows={3} />
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                {t("cancel")}
              </Button>
              <Button type="submit">{t("submit")}</Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
