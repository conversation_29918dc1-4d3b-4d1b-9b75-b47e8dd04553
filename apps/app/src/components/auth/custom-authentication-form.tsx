/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { FC, ReactNode } from "react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { checkUserAndSendLogin } from "@/actions/check-user-and-send-login"
import { Loader2Icon, MailIcon } from "lucide-react"
import { useFormState, useFormStatus } from "react-dom"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { Input } from "@kreios/ui/input"
import { Label } from "@kreios/ui/label"

interface CustomAuthenticationFormProps {
  className?: string
  callbackUrl?: string
  branding?: ReactNode
  content: {
    title: string
    email: string
    oauth: string
    oauthMultiple: string
    emailLabel: string
    emailPlaceholder: string
    signInWithEmail: string
    sendingEmail: string
    userNotFound: string
    requestAccess: string
  }
}

const SubmitButton: FC<{ children: ReactNode; sendingText: string }> = ({ children, sendingText }) => {
  const { pending } = useFormStatus()

  return (
    <Button type="submit" variant="outline" disabled={pending}>
      {pending ? <Loader2Icon className="mr-2 h-4 w-4 animate-spin" /> : <MailIcon className="mr-2 h-4 w-4" />}
      {pending ? sendingText : children}
    </Button>
  )
}

const RequestAccessButton: FC<{ onClick: () => void; children: ReactNode }> = ({ onClick, children }) => (
  <Button type="button" variant="outline" onClick={onClick} className="w-full">
    {children}
  </Button>
)

const EmailInput: FC<{
  content: { emailLabel: string; emailPlaceholder: string }
  onEmailChange: (email: string) => void
}> = ({ content, onEmailChange }) => {
  const { pending } = useFormStatus()

  return (
    <div className="grid gap-1">
      <Label htmlFor="email">{content.emailLabel}</Label>
      <Input
        id="email"
        name="email"
        placeholder={content.emailPlaceholder}
        type="email"
        autoCapitalize="none"
        autoComplete="email"
        autoCorrect="off"
        required
        disabled={pending}
        onChange={(e) => onEmailChange(e.target.value)}
      />
    </div>
  )
}

export const CustomAuthenticationForm: FC<CustomAuthenticationFormProps> = ({
  callbackUrl,
  className,
  branding,
  content,
}) => {
  const router = useRouter()
  const [state, formAction] = useFormState(checkUserAndSendLogin, {})
  const [userEmail, setUserEmail] = useState("")

  const handleRequestAccess = () => {
    const params = new URLSearchParams()
    if (userEmail) {
      params.set("email", userEmail)
    }
    router.push(`/request-access?${params.toString()}`)
  }

  return (
    <div className={cn("mx-auto grid w-[22.5rem] gap-6", className)}>
      <div className="mb-10 flex items-center justify-center text-primary lg:hidden">{branding}</div>
      <div className="grid gap-2 text-center">
        <h1 className="text-3xl font-bold">{content.title}</h1>
        <p className="text-balance text-muted-foreground">{content.email}</p>
      </div>

      <form className="grid gap-4" action={formAction}>
        <input type="hidden" name="callbackUrl" value={callbackUrl} />

        <div className="grid gap-2">
          <EmailInput
            content={{
              emailLabel: content.emailLabel,
              emailPlaceholder: content.emailPlaceholder,
            }}
            onEmailChange={setUserEmail}
          />

          <SubmitButton sendingText={content.sendingEmail}>{content.signInWithEmail}</SubmitButton>
        </div>

        {state.error?.type === "USER_NOT_FOUND" && (
          <div className="grid gap-2">
            <p className="text-center text-sm text-destructive">{content.userNotFound}</p>
            <RequestAccessButton onClick={handleRequestAccess}>{content.requestAccess}</RequestAccessButton>
          </div>
        )}
      </form>
    </div>
  )
}
