"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { Batch } from "@/components/batches-components"
import type { SurveyTemplateEnum } from "@/utils/constants"
import { useDeferredValue, useMemo, useState } from "react"
import { api } from "@/lib/trpc-provider"
import { useTranslations } from "next-intl"
import { useSpinDelay } from "spin-delay"
import { useDebounce } from "use-debounce"
import { useDeepCompareMemo } from "use-deep-compare"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"
import { Input } from "@kreios/ui/input"

const ITEMS_PER_PAGE = 12

export interface BatchesPageProps {
  /**
   * The template type to filter batches by
   */
  templateType: SurveyTemplateEnum
  /**
   * The translation namespace to use for this page
   */
  namespace: string
  /**
   * The BatchCard component to use for rendering batch cards
   */
  BatchCard: React.ComponentType<{ batch: Batch }>
  /**
   * The EmptyBatchCard component to use when no batches are found
   */
  EmptyBatchCard: React.ComponentType<{ message: string }>
  /**
   * The SurveyPagination component for pagination
   */
  SurveyPagination: React.ComponentType<{
    currentPage: number
    totalPages: number
    onPrevious: () => void
    onNext: () => void
  }>
  /**
   * The AddBatchDialog component for adding new batches
   */
  AddBatchDialog: React.ComponentType<{
    open: boolean
    onOpenChange: (open: boolean) => void
  }>
}

/**
 * A reusable component for displaying a list of batches filtered by template type
 * Used by both surveys and assessment-batches pages
 */
export const BatchesPage: React.FC<BatchesPageProps> = ({
  templateType,
  namespace,
  BatchCard,
  EmptyBatchCard,
  SurveyPagination,
  AddBatchDialog,
}) => {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const t = useTranslations(namespace)

  const [debouncedSearch] = useDebounce(search, 300)

  const handleAddBatchClick = () => {
    setIsDialogOpen(true)
  }

  const input = useDeepCompareMemo(
    () => ({
      limit: ITEMS_PER_PAGE,
      page,
      search: debouncedSearch,
      template: [templateType],
    }),
    [ITEMS_PER_PAGE, page, debouncedSearch, templateType]
  )

  const deferredInput = useDeferredValue(input)

  const [data] = api.surveys.list.useSuspenseQuery(deferredInput)

  const isSuspending = useSpinDelay(input !== deferredInput)

  const paginationProps = useMemo(() => {
    const totalPages = Math.ceil(data.count / ITEMS_PER_PAGE)

    return {
      currentPage: page,
      totalPages,
      onPrevious: () => setPage((prev) => Math.max(1, prev - 1)),
      onNext: () => setPage((prev) => (prev < totalPages ? prev + 1 : prev)),
    }
  }, [data, page])

  const batches = useMemo(() => {
    return data.data.map((batch) => ({
      id: batch.aggregateId,
      title: batch.label,
      companies: `${batch.companyCount} ${t("batchCard.companies")}`,
      complete: batch.stats.complete,
      inProgress: batch.stats.inProgress,
      notStarted: batch.stats.notStarted,
      open: batch.stats.open,
      submitted: batch.stats.submitted,
      sent: batch.stats.sent,
      pending: batch.stats.pending,
    }))
  }, [data.data, t])

  return (
    <>
      <PageHeader title={t("title")}>
        <div className="flex w-full flex-wrap items-center gap-4 md:w-auto">
          {batches.length > 0 && (
            <Input
              placeholder={t("searchPlaceholder")}
              value={search}
              onChange={(e) => {
                setSearch(e.target.value)
                setPage(1)
              }}
              className="h-8 w-[150px] bg-background lg:w-[250px]"
            />
          )}
          <Button size="sm" onClick={handleAddBatchClick}>
            {t("addNewBatch")}
          </Button>
        </div>
      </PageHeader>

      <div className="relative p-4 md:p-6">
        {isSuspending && (
          <div className="absolute h-1 w-[calc(100%-2rem)] overflow-hidden rounded-full md:w-[calc(100%-3rem)]">
            <div className="h-1 w-[12.5%] animate-slide bg-primary" />
          </div>
        )}
        {batches.length ? (
          <>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {batches.map((batch, index) => (
                <BatchCard key={index} batch={batch} />
              ))}
            </div>

            <div className="mt-8">
              <SurveyPagination {...paginationProps} />
            </div>
          </>
        ) : (
          <EmptyBatchCard message={search ? t("emptyBatch.noResults") : t("emptyBatch.noBatchCreated")} />
        )}
      </div>
      <AddBatchDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </>
  )
}
