"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { RouterOutput } from "@/lib/trpc-provider"
import type { SurveyStatusEnum } from "@/utils/constants"
import type { ColumnDef } from "@tanstack/react-table"
import React, { useMemo } from "react"
import { ProgressStats } from "@/app/admin/surveys/components"
import { TranslatedFacetsDataTable } from "@/components/translated-facets-data-table"
import { api } from "@/lib/trpc-provider"
import { useTranslations } from "next-intl"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { selectColumn } from "@kreios/datatable/data-table-common-definitions"
import { createEnhancedColumnHelper } from "@kreios/datatable/enhanced-column-helper"
// import { Button } from "@kreios/ui/button"
import { Progress } from "@kreios/ui/progress"

type Survey = RouterOutput["surveys"]["listSurveys"]["data"][number]

export interface SurveysListProps {
  /**
   * The template type to filter surveys by
   */
  templateType: string[]
  /**
   * The main translation namespace to use for this page
   * All other keys will be accessed within this namespace
   */
  namespace: string

  /**
   * Whether the current user is a company user
   * If true, the surveys will be filtered by company
   */
  isCompanyUser?: boolean
}

const columnHelper = createEnhancedColumnHelper<Survey>()

/**
 * A reusable component for displaying a list of surveys filtered by template type
 * Used by both company-surveys and self-assessments pages
 */
export const SurveysList: React.FC<SurveysListProps> = ({ templateType, namespace, isCompanyUser = false }) => {
  const [surveyList] = api.surveys.getCompanySurveys.useSuspenseQuery({ filters: { template: templateType } })
  const t = useTranslations()

  const columns = useMemo(
    () =>
      [
        selectColumn(),
        columnHelper.link("issuer", {
          title: t("surveys.columns.issuer"),
          isDisabled: (row) => row.status === "CANCELLED",
          href: (row) =>
            `/admin/${templateType[0] === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1" ? "self-assesment" : "survey"}/${row.aggregateId}`,
        }),
        columnHelper.custom("status", {
          title: t("surveys.columns.status"),
          cell: (context) => {
            const status = context.row.original.status
            return <div>{t(`surveyStatus.${status.toLowerCase()}`)}</div>
          },
        }),
        columnHelper.date("deadline", {
          title: t("surveys.columns.deadline"),
          format: "Pp",
          cell: (context) => {
            const deadline = context.row.original.deadline
            const formattedDate = deadline ? new Date(deadline).toLocaleDateString() : null
            return formattedDate ? <div>{formattedDate}</div> : <div className="text-muted-foreground">----</div>
          },
        }),
        columnHelper.date("submittedAt", {
          title: t("surveys.columns.submissionDate"),
          format: "Pp",
          cell: (context) => {
            const submittedAt = context.row.original.submittedAt
            const formattedDate = submittedAt ? new Date(submittedAt).toLocaleDateString() : null
            return formattedDate ? <div>{formattedDate}</div> : <div className="text-muted-foreground">----</div>
          },
        }),
        columnHelper.custom("progress", {
          title: t("surveys.columns.progress"),
          cell: (context) => (
            <div className="flex w-32 items-center gap-2">
              <Progress
                value={context.row.original.progress}
                className="h-2 [&>div]:translate-x-0 [&>div]:transition-none"
              />
              <span className="min-w-[40px] text-sm text-gray-600 dark:text-gray-300">
                {context.row.original.progress}%
              </span>
            </div>
          ),
        }),
      ] as ColumnDef<Survey, unknown>[],
    [t, templateType]
  )

  return (
    <>
      <PageHeader
        title={
          <div className="flex items-center gap-2">
            <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight">
              {t(`${namespace}.${namespace === "surveys" ? "surveys" : "selfAssessments"}`)}
            </h2>
          </div>
        }
        includeInBreadcrumb
      />
      {isCompanyUser && templateType[0] === "UNIFIED_QUESTIONNAIRE_V1" && <ProgressStats stats={surveyList.stats} />}

      <div className="flex w-full flex-1 p-4 md:p-6">
        <TranslatedFacetsDataTable
          columns={columns}
          procedure={api.surveys.listSurveys}
          initialSorting={[{ id: "progress", desc: false }]}
          searchPlaceholder={t(`${namespace}.searchPlaceholder`)}
          baseFilters={[
            {
              id: "template",
              value: templateType,
            },
          ]}
          rowIdKey="aggregateId"
          // onRowSelectionChange={({ selectedData }) => {
          //   setSelectedData(selectedData)
          // }}
          renderRow={({ original: { overdue, status } }, children) => {
            if (!overdue || status === ("COMPLETE" as SurveyStatusEnum)) return children
            const rowElement = React.cloneElement(children as React.ReactElement, {
              className: "bg-red-100 dark:bg-red-800",
              "data-tooltip-content": t("surveys.overdueSurvey"),
              "data-overdue": "true",
            })

            return rowElement
          }}
        />
      </div>
    </>
  )
}
