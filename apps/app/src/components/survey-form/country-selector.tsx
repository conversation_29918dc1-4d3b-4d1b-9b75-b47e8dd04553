/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { ComponentProps } from "react"
import { countries } from "countries-list"

import { Combobox } from "@kreios/ui/combobox"

const countryOptions = Object.entries(countries)
  .map(([code, country]) => ({
    value: code,
    label: country.name,
  }))
  .sort((a, b) => a.label.localeCompare(b.label))

export function CountrySelector({
  value,
  onChange,
  ...props
}: {
  value: string
  onChange: (value: string) => void
} & Omit<ComponentProps<typeof Combobox>, "options" | "value" | "onChange" | "label">) {
  return (
    <Combobox
      label="Country"
      options={countryOptions}
      value={value ? (countryOptions.find((c) => c.value === value) ?? null) : null}
      onChange={(option) => onChange(option?.value ?? "")}
      {...props}
    />
  )
}
