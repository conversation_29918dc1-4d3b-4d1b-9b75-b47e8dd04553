/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import React, { useEffect, useState } from "react"
import { useTranslations } from "next-intl"
import { useDebounce } from "use-debounce"

import { FormControl, FormField, FormItem, FormLabel } from "@kreios/ui/form"
import { Input } from "@kreios/ui/input"
import { RadioGroup, RadioGroupItem } from "@kreios/ui/radio-group"

interface ActivitySelectorProps {
  name: string
  activities: string[]
  label?: string
  required?: boolean
  disabled?: boolean
}

export function ActivitySelector({ name, activities, label, required = true, disabled }: ActivitySelectorProps) {
  const t = useTranslations("survey.components.activitySelector")
  const acticityValue = useTranslations("surveyEnums")

  const [searchValue, setSearchValue] = useState<string>("")
  const [initialized, setIsInitialized] = useState(false)

  useEffect(() => {
    setIsInitialized(false)
  }, [acticityValue])

  const [debouncedSearchValue] = useDebounce(searchValue, 300)
  const filteredActivities = activities.filter((key) =>
    acticityValue(key).toLowerCase().includes(debouncedSearchValue.toLowerCase())
  )

  const hasMoreActivities = filteredActivities.length > 5

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchValue(value)
  }

  return (
    <FormField
      name={name}
      render={({ field }: { field: { value: string; onChange: (value: string) => void } }) => {
        // Update searchValue when language or field.value changes
        if (field.value && !initialized) {
          setSearchValue(acticityValue(field.value))
          setIsInitialized(true)
        }

        const handleActivitySelect = (activity: string) => {
          setSearchValue(acticityValue(activity))
          field.onChange(activity)
        }

        return (
          <FormItem className="mt-2 space-y-2">
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              <Input
                value={searchValue}
                onChange={handleInputChange}
                placeholder={disabled ? "" : t("placeholder")}
                required={required}
                disabled={disabled}
              />
            </FormControl>
            {debouncedSearchValue.length > 2 && (
              <>
                <RadioGroup value={field.value} onValueChange={handleActivitySelect}>
                  {filteredActivities.length > 0 ? (
                    filteredActivities.slice(0, 5).map((activity) => (
                      <div
                        key={activity}
                        className="flex items-center space-x-2 rounded-md border border-input p-2 text-sm"
                      >
                        <RadioGroupItem value={activity} id={activity} />
                        <label
                          htmlFor={activity}
                          className="flex-grow cursor-pointer"
                          onClick={() => handleActivitySelect(activity)}
                        >
                          {acticityValue(activity)}
                        </label>
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground">{t("notFound")}</div>
                  )}
                </RadioGroup>
                {hasMoreActivities && <div className="mt-2 text-xs text-muted-foreground">{t("moreActivities")}</div>}
              </>
            )}
            {searchValue.length > 0 && searchValue.length <= 2 && (
              <div className="text-sm text-muted-foreground">{t("searchPlaceholder")}</div>
            )}
          </FormItem>
        )
      }}
    />
  )
}
