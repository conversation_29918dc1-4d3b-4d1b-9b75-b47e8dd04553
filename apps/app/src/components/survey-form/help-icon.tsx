/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { MouseEvent } from "react"
import { HelpCircle } from "lucide-react"

import { Button } from "@kreios/ui/button"

type HelpIconProps = {
  handleHelpClick: () => void
}

export function HelpIcon({ handleHelpClick }: HelpIconProps) {
  const handleClickOutside = (e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>) => {
    e.stopPropagation()
    handleHelpClick()
  }

  return (
    <Button
      variant="ghost"
      size="icon"
      className="help-trigger hidden h-5 w-5 flex-none self-start text-gray-400 hover:text-gray-600 md:block"
      onClick={handleClickOutside}
    >
      <HelpCircle className="h-4 w-4" />
    </Button>
  )
}
