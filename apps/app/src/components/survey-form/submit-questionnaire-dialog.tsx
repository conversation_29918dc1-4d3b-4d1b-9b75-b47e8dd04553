/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useMemo } from "react"
import { AnimatePresence, motion } from "framer-motion"
import { BarChart, Cpu, FileIcon, Leaf, Recycle, Wind, Zap } from "lucide-react"
import { useTranslations } from "next-intl"
import { useFormContext, useWatch } from "react-hook-form"

import { Button } from "@kreios/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@kreios/ui/dialog"

const animationItems = [
  { Icon: Cpu, color: "text-blue-500" },
  { Icon: Leaf, color: "text-green-500" },
  { Icon: Recycle, color: "text-green-600" },
  { Icon: Wind, color: "text-blue-400" },
  { Icon: Zap, color: "text-yellow-500" },
  { Icon: BarChart, color: "text-purple-500" },
]

interface SubmitQuestionnaireDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: () => void
  isSubmitting?: boolean
  isSelfAssessment?: boolean
}

export function SubmitQuestionnaireDialog({
  open,
  onOpenChange,
  onSubmit,
  isSubmitting: externalIsSubmitting,
  isSelfAssessment = false,
}: SubmitQuestionnaireDialogProps) {
  const t = useTranslations("survey.components.submitQuestionnaireDialog")
  const { control } = useFormContext()
  const formValues = useWatch({ control })

  const isSubmitting = useMemo(() => {
    // Use external isSubmitting if provided, otherwise calculate based on form values
    return externalIsSubmitting ?? (open && !formValues)
  }, [open, formValues, externalIsSubmitting])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t(`${isSelfAssessment ? "selfAssessmentTitle" : "title"}`)}</DialogTitle>
          <DialogDescription className="mt-10">
            {t(`${isSelfAssessment ? "selfAssessmentThankyouMessage" : "thankyouMessage"}`)}
          </DialogDescription>
        </DialogHeader>
        <AnimatePresence>
          {!isSubmitting ? (
            <div className="flex flex-col gap-4">
              {/* <div className="flex flex-col gap-2">
                <p className="text-sm text-muted-foreground">{t("summaryMessage")}</p>
              </div> */}
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  {t("cancel")}
                </Button>
                <Button onClick={onSubmit}>{t("submit")}</Button>
              </div>
            </div>
          ) : (
            <motion.div
              className="flex flex-col items-center justify-center gap-4 py-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="flex items-center justify-center"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="relative h-24 w-24">
                  {animationItems.map(({ Icon, color }, index) => (
                    <motion.div
                      key={index}
                      className={`absolute ${color}`}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{
                        opacity: [0, 1, 0],
                        scale: [0.8, 1.2, 0.8],
                        rotate: [0, 10, 0],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: index * 0.2,
                      }}
                    >
                      <Icon size={48} />
                    </motion.div>
                  ))}
                  <motion.div
                    className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-primary"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{
                      opacity: [0.3, 1, 0.3],
                      scale: [0.8, 1, 0.8],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                    }}
                  >
                    <FileIcon size={32} />
                  </motion.div>
                </div>
              </motion.div>
              <motion.div
                className="px-4 text-center text-xl font-bold text-green-700 md:text-2xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1, duration: 0.5 }}
              >
                {t(`${isSelfAssessment ? "selfAssessmentSubmitting" : "submitting"}`)}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  )
}
