/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { FormField, FormSection as SectionType } from "@/schema/generate-schema/types"
import type { FieldValues, Path } from "react-hook-form"
import type { z } from "zod"
import { useEffect, useRef } from "react"
import { DetailDisplay } from "@/components/survey-form/detail-display"
import { GenericFieldArray } from "@/components/survey-form/generic-field-array"
import { GenericFieldRenderer } from "@/components/survey-form/generic-field-renderer"
import { useSafeTranslation } from "@/hooks/use-safe-translations"
import { useWatchYesOrNot } from "@/hooks/useWatchYesOrNot"
import { useFormContext } from "react-hook-form"

interface GenericFormSectionProps<TFieldValues extends FieldValues> {
  section: SectionType
  readOnly?: boolean
  className?: string
  translationPrefix: string
  schema: z.ZodType<TFieldValues>
}

export const GenericFormSection = <TFieldValues extends FieldValues>({
  section,
  readOnly = false,
  className = "space-y-6",
  translationPrefix,
  schema,
}: GenericFormSectionProps<TFieldValues>) => {
  const { control } = useFormContext()

  const getTranslation = useSafeTranslation()

  const dependencyArray = section.dependency ?? []

  const isVisible = useWatchYesOrNot({
    control,
    dependencyArray: dependencyArray as unknown as readonly Path<TFieldValues>[],
  })

  const sectionTitle = getTranslation(`${translationPrefix}.${section.sectionTitle}`) ?? ""
  const sectionDescription = getTranslation(`${translationPrefix}.${section.sectionDescription}`) ?? ""
  const helpText = getTranslation(`${translationPrefix}.${section.helpText}`)
  return (
    <div className={className}>
      {section.fields.length > 1 && isVisible && (
        <div className="space-y-4">
          <DetailDisplay
            label={`${sectionTitle}`}
            helpInfo={
              helpText
                ? {
                    title: sectionTitle,
                    description: helpText,
                  }
                : undefined
            }
            className="text-xl font-semibold"
          />
          {sectionDescription && <p className="text-sm text-gray-500 dark:text-gray-400">{sectionDescription}</p>}
        </div>
      )}
      <div className="space-y-4">
        {section.fields.map((field) => (
          <FieldWithDependency
            key={field.field}
            field={field}
            readOnly={readOnly}
            translationPrefix={translationPrefix}
            schema={schema}
            addMargin={section.fields.length > 1}
          />
        ))}
      </div>
    </div>
  )
}

const FieldWithDependency = <TFieldValues extends FieldValues>({
  field,
  readOnly = false,
  translationPrefix,
  schema,
  hideQuestionNumber = true,
  addMargin = false,
}: {
  field: FormField
  schema: z.ZodType<TFieldValues>
  readOnly?: boolean
  translationPrefix: string
  hideQuestionNumber?: boolean
  addMargin?: boolean
}) => {
  const { control, setValue } = useFormContext()
  const prevVisibleRef = useRef<boolean>(true)
  const dependencyArray = field.dependency ?? []

  const isVisible = useWatchYesOrNot({
    control,
    dependencyArray: dependencyArray as unknown as Path<TFieldValues>[],
  })

  useEffect(() => {
    if (prevVisibleRef.current && !isVisible) {
      setValue(field.field as Path<TFieldValues>, null as unknown as TFieldValues[Path<TFieldValues>], {
        shouldValidate: true,
      })
    }
    prevVisibleRef.current = isVisible
  }, [isVisible, field.field, setValue])

  if (!isVisible) {
    return null
  }

  return field.type === "table" ? (
    <GenericFieldArray
      field={field}
      readOnly={readOnly}
      translationPrefix={translationPrefix}
      schema={schema}
      hideQuestionNumber={hideQuestionNumber}
      addMargin={addMargin}
    />
  ) : (
    <GenericFieldRenderer
      field={field}
      name={field.field}
      readOnly={readOnly}
      translationPrefix={translationPrefix}
      schema={schema}
      hideQuestionNumber={hideQuestionNumber}
      addMargin={addMargin}
    />
  )
}
