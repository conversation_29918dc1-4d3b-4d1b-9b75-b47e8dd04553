/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import type { FileField } from "@/schema/generate-schema/types"
import type { Control, FieldValues, Path, PathValue } from "react-hook-form"
import type { z } from "zod"
import { useRef } from "react"
import { useFileUpload } from "@/hooks/use-file-upload"
import { useFormContext } from "react-hook-form"

import { FileUploader } from "@kreios/storage/file-uploader"
import { FormControl } from "@kreios/ui/form"
import { createFormFieldHelper } from "@kreios/ui/form/form-field-helper"

// Define the exact type for a file object based on the schema

interface FileUploadFieldProps<TFieldValues extends FieldValues> {
  field: FileField
  schema: z.ZodType<TFieldValues>
  control: Control<TFieldValues>
  isReadOnly?: boolean
}

interface FileObject {
  key: string
  mimeType: string
  name: string
  url?: string
}

export const FileUploadField = <TFieldValues extends FieldValues>({
  field,
  schema,
  control,
  isReadOnly = false,
}: FileUploadFieldProps<TFieldValues>) => {
  const FormField = createFormFieldHelper(schema)
  const { mutateAsync: uploadFile } = useFileUpload()
  const { setValue, getValues } = useFormContext<TFieldValues>()
  const fieldName = field.field as Path<TFieldValues>

  // Get the current files directly from the field path
  const currentFilesRaw = getValues(fieldName) as PathValue<TFieldValues, Path<TFieldValues>>

  // Ensure currentFiles is an array of FileObject; default to empty array if not
  const currentFiles: FileObject[] = Array.isArray(currentFilesRaw) ? currentFilesRaw : []

  // Initialize a map of file keys to URLs
  const initialUrls = currentFiles.reduce(
    (acc: Record<string, string>, file: FileObject) => ({
      ...acc,
      [file.key]: file.url ?? "",
    }),
    {} as Record<string, string>
  )

  const fileUrlMap = useRef<Record<string, string>>(initialUrls)

  // Handle file upload and store the URL in the map
  const handleFileUpload = async (file: File) => {
    const response = await uploadFile(file)
    if (response.success) {
      fileUrlMap.current[response.file_path] = response.file_url
      return response.file_path
    }
    throw new Error("Upload failed")
  }

  return (
    <div className="mt-4 rounded-lg bg-gray-50 p-6 dark:bg-gray-900">
      {/* File upload section */}
      <div className="">
        <div className="flex items-center gap-3">
          <FormField
            control={control}
            name={fieldName}
            label={false}
            disabled={isReadOnly}
            className=""
            render={({ field: formField }) => (
              <FormControl>
                <FileUploader
                  value={(formField.value ?? []) as FileObject[]}
                  onValueChange={(arg) => {
                    const previousValue = (getValues(fieldName) ?? []) as FileObject[]
                    const valueRaw = typeof arg === "function" ? arg(previousValue) : arg
                    const value: FileObject[] = Array.isArray(valueRaw) ? valueRaw : []
                    const filesWithUrls = value.map((file: FileObject) => ({
                      ...file,
                      url: fileUrlMap.current[file.key],
                    }))
                    setValue(fieldName, filesWithUrls as PathValue<TFieldValues, Path<TFieldValues>>, {
                      shouldDirty: true,
                      shouldTouch: true,
                    })
                  }}
                  fileListComponent={
                    <FileUploader.List>
                      {(item) => (
                        <FileUploader.ListItem
                          item={item}
                          key={`${field.field}-${item.key}`} // Use the dynamic field path
                        />
                      )}
                    </FileUploader.List>
                  }
                  uploadProcedure={handleFileUpload}
                  maxSize={200 * 1024 * 1024} // 200 MB
                  multiple={true}
                  accept={{
                    "application/pdf": [".pdf"],
                    "application/step": [".stp", ".step"],
                    "image/jpeg": [".jpg", ".jpeg"],
                    "image/png": [".png"],
                    "application/msword": [".doc"],
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
                  }}
                  disabled={isReadOnly}
                />
              </FormControl>
            )}
          />
        </div>
      </div>
    </div>
  )
}
