/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { memo } from "react"
import { Check, Loader2 } from "lucide-react"
import { useTranslations } from "next-intl"

import { cn } from "@kreios/ui"

export const AutoSaveIndicator = memo(function AutoSaveIndicator({
  pending,
  submittedAt,
  className,
}: {
  pending: boolean
  submittedAt: Date | null
  className?: string
}) {
  const t = useTranslations("survey.components.autoSaveIndicator")
  if (!pending && !submittedAt) return null

  return (
    <div
      className={cn(
        "fixed bottom-4 left-4 z-50 flex items-center gap-2 rounded-md bg-gray-800/90 px-3 py-2 text-sm text-white backdrop-blur-sm transition-all duration-200 dark:bg-gray-900/90",
        className
      )}
    >
      {pending ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>{t("pending")}</span>
        </>
      ) : (
        <>
          <Check className="h-4 w-4 text-green-400" />
          <span>{t("success")}</span>
        </>
      )}
    </div>
  )
})
