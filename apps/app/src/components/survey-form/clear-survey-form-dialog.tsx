/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useUpdatePrefilledState } from "@/components/survey-form/use-survey-context"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@kreios/ui/dialog"

interface ClearSurveyFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void
}

export function ClearSurveyFormDialog({ open, onOpenChange, onConfirm }: ClearSurveyFormDialogProps) {
  const setPrefilledAcceptanceState = useUpdatePrefilledState()

  const t = useTranslations("survey.clearForm")
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t("title")}</DialogTitle>
          <DialogDescription>{t("description")}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("cancelLabel")}
          </Button>
          <Button
            variant="default"
            onClick={() => {
              setPrefilledAcceptanceState({})
              onConfirm()
            }}
          >
            {t("confirmLabel")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
