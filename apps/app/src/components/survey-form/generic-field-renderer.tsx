/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { FormField as FieldType } from "@/schema/generate-schema/types"
import type { FieldValues, Path } from "react-hook-form"
import type { z } from "zod"
import React from "react"
import { ActivitySelector } from "@/components/survey-form/activity-selector"
import { DetailDisplay } from "@/components/survey-form/detail-display"
import { FileUploadField } from "@/components/survey-form/file-upload-field"
import { PrefilledField } from "@/components/survey-form/prefilled-field"
import { useSafeTranslation } from "@/hooks/use-safe-translations"
import { useTranslations } from "next-intl"
import { useFormContext } from "react-hook-form"

import { cn } from "@kreios/ui"
import { createFormFieldHelper } from "@kreios/ui/form/form-field-helper"
import { MultiSelectorContent, MultiSelectorItem } from "@kreios/ui/multi-select"
import { SelectItem } from "@kreios/ui/select"

interface GenericFieldRendererProps<TFieldValues extends FieldValues> {
  field: FieldType
  name: string
  readOnly?: boolean
  hideLabel?: boolean
  fieldClassName?: string
  hidePrefilledField?: boolean
  hideQuestionNumber?: boolean
  translationPrefix: string
  schema: z.ZodType<TFieldValues>
  addMargin?: boolean
  isArrayTable?: boolean
}

export const GenericFieldRenderer = <TFieldValues extends FieldValues>({
  field,
  name,
  readOnly = false,
  hideLabel = false,
  fieldClassName = "",
  hideQuestionNumber = false,
  hidePrefilledField = false,
  translationPrefix,
  schema,
  addMargin = false,
  isArrayTable = false,
}: GenericFieldRendererProps<TFieldValues>) => {
  const { control } = useFormContext()
  const getTranslation = useSafeTranslation()

  const t = useTranslations("surveyEnums")
  const FormField = React.useMemo(() => createFormFieldHelper(schema), [])

  // Hide placeholders when in readonly mode to avoid showing example values like "(e.g. 2024)"
  const shouldShowPlaceholder = !readOnly
  const placeholder = shouldShowPlaceholder ? (getTranslation(`${translationPrefix}.${field.placeholder}`) ?? "") : ""
  const title = getTranslation(`${translationPrefix}.${field.title}`) ?? ""
  const description = getTranslation(`${translationPrefix}.${field.description}`)
  const helpText = getTranslation(`${translationPrefix}.${field.helpText}`)

  const fieldName = name as Path<TFieldValues>

  const renderField = React.useCallback(() => {
    switch (field.type) {
      case "date":
        return <FormField.DatePicker name={fieldName} label="" disabled={readOnly} />

      case "checkbox":
        return <FormField.Checkbox name={fieldName} label={title} disabled={readOnly} />

      case "file":
        return <FileUploadField field={field} schema={schema} control={control} isReadOnly={readOnly} />

      case "textarea":
        return <FormField.Textarea name={fieldName} label="" placeholder={placeholder} disabled={readOnly} autoGrow />

      case "select":
        if (name.split(".").pop() === "activity") {
          return <ActivitySelector name={name} activities={field.enum} required={field.required} disabled={readOnly} />
        }
        return (
          <FormField.Select
            name={fieldName}
            label=""
            placeholder={placeholder}
            disabled={readOnly}
            className={cn(isArrayTable ? "w-full" : "max-w-[250px]", fieldClassName)}
          >
            {field.enum.map((option) => (
              <SelectItem key={option} value={option}>
                {t(option)}
              </SelectItem>
            ))}
          </FormField.Select>
        )

      case "multiselect":
        return (
          <FormField.MultiSelect
            name={fieldName}
            label=""
            className={isArrayTable ? "w-full" : "max-w-[450px]"}
            placeholder={placeholder}
            disabled={readOnly}
            getLabel={(key) => t(key)}
          >
            <MultiSelectorContent>
              {field.enum.map((option) => (
                <MultiSelectorItem key={option} value={option}>
                  {t(option)}
                </MultiSelectorItem>
              ))}
            </MultiSelectorContent>
          </FormField.MultiSelect>
        )
      case "boolean-radio":
        return (
          <FormField.RadioCheckbox
            name={fieldName}
            label=""
            options={[
              { label: t("yes"), value: true },
              { label: t("no"), value: false },
            ]}
            allowUnselect
          />
        )
      case "radio":
        return (
          <FormField.RadioCheckbox
            name={fieldName}
            label=""
            options={field.enum.map((option) => ({
              label: t(option),
              value: option,
            }))}
            allowUnselect
          />
        )
      default:
        return (
          <FormField
            name={fieldName}
            label=""
            placeholder={placeholder}
            disabled={readOnly}
            className={cn(isArrayTable ? "w-full" : "max-w-[400px]", fieldClassName)}
            isYearlyField={field.isYearField}
            isFormInput
          />
        )
    }
  }, [
    field,
    readOnly,
    fieldClassName,
    t,
    placeholder,
    title,
    FormField,
    control,
    fieldName,
    name,
    schema,
    isArrayTable,
  ])

  return (
    <div className={cn("", "flex-1", addMargin && "ml-6")}>
      {!hideLabel && (
        <DetailDisplay
          label={hideQuestionNumber || field.dependency ? title : `${field.questionNumber}. ${title}`}
          helpInfo={
            helpText || description
              ? {
                  title: title,
                  description: helpText ?? description ?? "",
                }
              : undefined
          }
          required={field.required}
        />
      )}
      {hidePrefilledField ? (
        renderField()
      ) : (
        <PrefilledField name={name} isArrayTable={isArrayTable}>
          {renderField()}
        </PrefilledField>
      )}
    </div>
  )
}
