/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { Dispatch, SetStateAction } from "react"
import type { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react"
import { useForm, useFormContext, useWatch } from "react-hook-form"

type PrefilledFormData<TFieldValues extends FieldValues = FieldValues> = Partial<TFieldValues>

interface SurveyContextType {
  // allow accepting or rejecting prefilled data for a given field
  handlePrefilledAction: (fieldName: FieldPath<PrefilledFormData>, action: "accept" | "reject") => void

  // track which fields have been accepted or rejected
  prefilledAcceptanceState: Record<string, "accepted" | "rejected">

  // track which fields have been mounted
  mountedPrefilledFields: Set<string>
  setMountedPrefilledFields: Dispatch<SetStateAction<Set<string>>>

  // form which holds the prefilled data
  form: UseFormReturn<PrefilledFormData>

  // whether the prefilled data is disabled
  isPrefilledDisabled: boolean

  setPrefilledAcceptanceState: Dispatch<SetStateAction<Record<string, "accepted" | "rejected">>>
}

const SurveyContext = createContext<SurveyContextType | undefined>(undefined)

export const FormProvider = ({
  children,
  initialPrefilledData,
  isPrefilledDisabled = false,
}: {
  children: React.ReactNode
  initialPrefilledData?: PrefilledFormData
  isPrefilledDisabled?: boolean
}) => {
  const { setValue } = useFormContext()

  const form = useForm({
    values: initialPrefilledData ?? {},
  })

  // track which fields have been accepted or rejected
  // it's not necessary to save this to backend since after accepting a field the state will be saved to the backend
  // if the field is rejected and filled manually its state will also be saved
  // if the field is rejected and not filled manually we will show the prefilled ui again
  const [prefilledAcceptanceState, setPrefilledAcceptanceState] = useState<Record<string, "accepted" | "rejected">>({})

  const [mountedPrefilledFields, setMountedPrefilledFields] = useState(new Set<string>())

  const getPrefillValues = form.getValues

  /**
   * Allow accepting or rejecting prefilled data for a given field
   */
  const handlePrefilledAction = useCallback(
    (fieldName: FieldPath<PrefilledFormData>, action: "accept" | "reject") => {
      // if the action is accept, merge the prefill values with the current values
      if (action === "accept") {
        const prefill = getPrefillValues(fieldName)

        setValue(fieldName, prefill)
      }

      // update the acceptance state
      setPrefilledAcceptanceState((prev) => ({
        ...prev,
        [fieldName]: action === "accept" ? "accepted" : "rejected",
      }))
    },
    [setValue, getPrefillValues]
  )

  return (
    <SurveyContext.Provider
      value={{
        handlePrefilledAction,
        prefilledAcceptanceState,
        form,
        mountedPrefilledFields,
        setMountedPrefilledFields,
        isPrefilledDisabled,
        setPrefilledAcceptanceState,
      }}
    >
      {children}
    </SurveyContext.Provider>
  )
}

/**
 * Return the isPrefilledDisabled state
 */
export const useIsPrefilledDisabled = () => {
  const { isPrefilledDisabled } = useSurvey()
  return isPrefilledDisabled
}

/**
 * Checks if at least one prefilled field is mounted
 * @returns true if at least one prefilled field is mounted
 */
export const useIsAtLeastOnePrefilledFieldMounted = () => {
  const { mountedPrefilledFields } = useSurvey()
  return useMemo(() => mountedPrefilledFields.size > 0, [mountedPrefilledFields])
}

/**
 * Track the mounted state of all prefilled fields
 * @param name - The name of the field to track
 * @param mounted - Whether the field is mounted
 */
export const useMountPrefilledField = (name: FieldPath<PrefilledFormData>, mounted = true) => {
  const { setMountedPrefilledFields } = useSurvey()

  useEffect(() => {
    // on mount check if the field is mounted add it to the state of mounted fields
    setMountedPrefilledFields((prev) => {
      const newSet = new Set(prev)
      if (mounted) newSet.add(name)
      else newSet.delete(name)
      return newSet
    })

    // on unmount remove the field from the state of mounted fields
    return () => {
      setMountedPrefilledFields((prev) => {
        const newSet = new Set(prev)
        newSet.delete(name)
        return newSet
      })
    }
  }, [name, mounted, setMountedPrefilledFields])
}

/**
 * Helper function to check if the prefilled ui should be shown for a given field
 * Checks the following:
 * 1. If the field has already been filled in, don't show the prefilled ui
 * 2. If the field doesn't have prefilled data, don't show the prefilled ui
 * 3. If the field has already been accepted or rejected, don't show the prefilled ui
 * 4. If the field is part of a section that has already been accepted or rejected, don't show the prefilled ui
 * Otherwise, show the prefilled ui
 *
 * @param name - The name of the field to check
 * @returns true if the field should be shown
 */
export const useShowPrefilledField = (name: FieldPath<PrefilledFormData>) => {
  const { form, prefilledAcceptanceState } = useSurvey()

  // Get the actual values instead of using !! which can be problematic for boolean false values
  const formValue = useWatch({ control: useFormContext().control, name })
  const prefilledValue = useWatch({ control: form.control, name })

  // Check if the form has a value (including boolean false)
  const hasData = formValue !== undefined && formValue !== null && formValue !== ""

  // Check if there's prefilled data (including boolean false)
  const hasPrefilledData = prefilledValue !== undefined && prefilledValue !== null && prefilledValue !== ""

  // has the user already filled in the data?
  if (hasData) return false

  // Do we have prefilled data?
  if (!hasPrefilledData) return false

  // Was this specific field already accepted or rejected?
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (prefilledAcceptanceState[name]) return false

  // Get the field path
  const fieldPath = name.toString()

  // Check if any accepted/rejected field affects this field
  for (const key of Object.keys(prefilledAcceptanceState)) {
    const keyPath = key.toString()

    // If this is an exact match
    if (fieldPath === keyPath) {
      return false
    }

    // If this field is a direct child of an accepted/rejected field
    if (fieldPath.startsWith(keyPath + ".")) {
      return false
    }

    const fieldParts = fieldPath.split(".")
    const keyParts = keyPath.split(".")

    if (fieldParts[0] === keyParts[0]) {
      const fieldName = fieldParts[fieldParts.length - 1]
      const keyName = keyParts[keyParts.length - 1]

      if (fieldName.startsWith(keyName + "_")) {
        continue
      }
    }

    // Check if this field starts with the accepted/rejected field
    // This is the original check for other fields
    if (fieldPath.startsWith(keyPath)) {
      return false
    }
  }

  // Show the field
  return true
}

/**
 * Return the react-hook-form form for the prefilled form
 */
export const usePrefilledForm = () => {
  const { form } = useSurvey()
  return form
}

/**
 * Return the handlePrefilledAction function
 */
export const useHandlePrefilledAction = () => {
  const { handlePrefilledAction } = useSurvey()
  return handlePrefilledAction
}

export const useUpdatePrefilledState = () => {
  const { setPrefilledAcceptanceState } = useSurvey()
  return setPrefilledAcceptanceState
}

const useSurvey = () => {
  const context = useContext(SurveyContext)
  if (!context) throw new Error("useSurvey must be used within SurveyProvider")
  return context
}
