/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { ComponentProps, FC } from "react"
import Image from "next/image"

import { cn } from "@kreios/ui"

export const HeroImage = ({
  className,
  ...props
}: Omit<ComponentProps<typeof Image>, "width" | "height" | "src" | "alt">) => (
  <Image src={"/hero.svg"} width={532} height={570} alt="Hero" className={className} {...props} />
)

export const LogoLarge: FC<ComponentProps<"svg">> = ({ className, ...props }) => {
  return (
    <svg viewBox="0 0 127 25" fill="none" className={className} {...props}>
      <title>Impactly logo</title>
      <path
        d="M4.407 19.712H0V.48h4.407v19.23ZM26.628 5.048c1.937 0 3.439.561 4.504 1.683 1.098 1.122 1.646 2.66 1.646 4.615v8.366h-4.116v-7.597c0-2.211-.872-3.317-2.615-3.317-.84 0-1.55.337-2.13 1.01-.582.64-.872 1.41-.872 2.307v7.597h-4.068v-7.597c0-2.211-.888-3.317-2.664-3.317-.807 0-1.5.337-2.082 1.01-.581.64-.872 1.41-.872 2.307v7.597H9.292V5.288h3.874v1.49h.29a5.593 5.593 0 0 1 1.792-1.25c.71-.32 1.437-.48 2.18-.48 2 0 3.502.641 4.503 1.923h.194c.484-.545 1.146-.993 1.985-1.346.84-.385 1.679-.577 2.518-.577ZM36.986 25V5.288h3.874v1.49h.29a5.113 5.113 0 0 1 1.889-1.25 6.155 6.155 0 0 1 2.373-.48c2.034 0 3.696.705 4.988 2.115 1.291 1.41 1.937 3.19 1.937 5.337s-.646 3.926-1.937 5.337c-1.292 1.41-2.954 2.115-4.988 2.115a6.112 6.112 0 0 1-2.228-.433c-.742-.32-1.356-.737-1.84-1.25h-.29V25h-4.068Zm7.554-8.75c1.033 0 1.905-.353 2.615-1.058.71-.737 1.066-1.634 1.066-2.692 0-1.058-.356-1.94-1.066-2.644-.71-.737-1.582-1.106-2.615-1.106-1.033 0-1.905.353-2.615 1.058-.678.705-1.017 1.602-1.017 2.692s.34 1.987 1.017 2.692c.71.705 1.582 1.058 2.615 1.058ZM64.724 5.288c1.097 0 1.969.321 2.615.962.645.641.968 1.506.968 2.596v10.866h-3.826v-1.49h-.29C62.9 19.406 61.285 20 59.348 20c-1.582 0-2.857-.417-3.825-1.25-.937-.833-1.405-1.907-1.405-3.221 0-1.443.484-2.532 1.453-3.27 1-.769 2.421-1.153 4.262-1.153h4.503V9.712c0-.545-.274-.818-.823-.818h-8.039V5.288h9.25Zm-4.31 11.731c1.162 0 2.098-.32 2.808-.961.743-.641 1.114-1.394 1.114-2.26v-.144h-3.825c-1.55 0-2.325.545-2.325 1.635 0 .48.194.897.581 1.25.42.32.969.48 1.647.48ZM79.287 20c-2.26 0-4.116-.705-5.569-2.115-1.42-1.41-2.13-3.206-2.13-5.385 0-2.18.71-3.974 2.13-5.385C75.171 5.705 77.028 5 79.288 5c2 0 3.647.513 4.939 1.538 1.291 1.026 2.082 2.372 2.373 4.039h-4.213a2.941 2.941 0 0 0-1.211-1.298c-.517-.353-1.146-.529-1.889-.529-1.065 0-1.937.353-2.615 1.058-.645.705-.968 1.602-.968 2.692s.323 1.987.968 2.692c.678.705 1.55 1.058 2.615 1.058.743 0 1.372-.16 1.889-.48a3.233 3.233 0 0 0 1.21-1.347H86.6c-.29 1.667-1.082 3.013-2.373 4.039C82.935 19.486 81.289 20 79.287 20Zm20.967-11.106h-5.327v6.298c0 .545.274.818.823.818h4.02v3.701h-5.327c-1.098 0-1.97-.32-2.615-.961-.646-.641-.969-1.506-.969-2.596v-7.26h-3.196V5.288h3.196V0h4.068v5.288h5.327v3.606Zm7.27 10.818h-4.068V0h4.068v19.712Zm15.263-14.424H127l-6.392 16.635c-.452 1.122-.969 1.907-1.55 2.356-.549.48-1.291.721-2.228.721h-5.326v-3.702h4.6a.759.759 0 0 0 .678-.384l-6.78-15.626h4.456l4.213 11.154h.339l3.777-11.154Z"
        fill="currentColor"
      />
    </svg>
  )
}

export const LogoSmall: FC<ComponentProps<"svg">> = ({ className, ...props }) => (
  <svg viewBox="0 0 512 512" className={cn("size-5", className)} {...props}>
    <path
      fill="none"
      d="
M258.000000,0.999999
	C342.957153,1.000000 427.914307,1.000000 512.935730,1.000000
	C512.935730,171.666656 512.935730,342.333344 512.935730,513.000000
	C479.570404,513.000000 446.253876,513.000000 412.136688,512.705444
	C414.071716,509.137512 414.957611,505.646301 413.144836,501.525391
	C411.170349,497.036743 407.279236,498.031006 403.813416,498.027863
	C357.652649,497.986053 311.491821,497.999939 265.331024,497.999939
	C263.585999,497.999939 261.841003,497.999939 260.000214,497.999939
	C260.000214,472.759827 259.915070,448.265198 260.132538,423.773285
	C260.154175,421.337250 261.408417,418.312225 263.121704,416.581177
	C288.793060,390.644165 314.597839,364.838379 340.531281,339.163422
	C342.266388,337.445587 345.263245,336.196136 347.710754,336.130585
	C368.624084,335.570526 389.566376,335.792328 410.459503,334.866394
	C429.478851,334.023560 445.968292,325.827148 459.922699,313.369141
	C480.184906,295.279877 490.668488,272.550781 490.078705,244.932816
	C489.669617,225.777313 490.000763,206.606018 489.999542,187.441788
	C489.999115,180.621948 487.403625,178.008286 480.555023,178.003723
	C462.390656,177.991638 444.218323,177.681198 426.065308,178.135330
	C417.137665,178.358688 408.203918,179.741943 399.352142,181.112061
	C393.237701,182.058502 391.003143,185.397903 391.001923,191.773331
	C390.997498,215.103699 391.070465,238.434631 390.883209,261.763489
	C390.865936,263.914459 389.725311,266.548218 388.225342,268.113678
	C380.042114,276.653870 371.569763,284.916534 363.224091,293.301788
	C358.724030,297.823212 354.278503,302.398926 348.989410,307.783966
	C348.989410,296.780792 348.983765,287.159851 348.991394,277.538879
	C348.996521,271.039917 348.130707,264.389221 349.208374,258.071442
	C352.054047,241.388580 358.822083,226.422653 371.715210,214.888885
	C375.444794,211.552551 376.628174,205.672134 372.687805,201.368866
	C370.226532,198.680969 363.678680,199.706787 360.366760,203.176132
	C356.958405,206.746490 353.336884,210.197205 350.450256,214.166840
	C339.694427,228.958176 333.109772,245.330460 333.019653,263.879333
	C332.929779,282.376556 333.096588,300.875671 332.877655,319.370758
	C332.850311,321.679474 331.745667,324.553864 330.140503,326.178314
	C308.004059,348.580627 285.694611,370.812012 263.415680,393.073425
	C262.479126,394.009216 261.427582,394.829956 260.000305,396.080353
	C260.000305,352.257507 259.967743,309.263855 260.113251,266.270844
	C260.119385,264.455475 261.383759,262.253265 262.730347,260.892853
	C280.313873,243.128372 298.089661,225.553848 315.631989,207.749176
	C319.156555,204.171890 322.287750,200.070206 324.949127,195.804062
	C334.039734,181.231857 340.368408,165.705414 342.161713,148.328537
	C343.662201,133.789047 343.158691,119.629585 339.208130,105.511986
	C333.022064,83.405525 320.153870,65.414917 304.590881,49.209961
	C290.165527,34.189575 275.167969,19.719238 260.480591,4.948803
	C259.411377,3.873521 258.815796,2.327274 258.000000,0.999999
M432.547089,368.000305
	C420.964233,367.891388 409.987305,370.027649 400.066742,376.423523
	C383.474060,387.121002 374.151215,402.269592 373.119720,421.952789
	C372.475952,434.237122 372.982849,446.582703 373.007385,458.900757
	C373.020142,465.313995 375.798920,467.986176 382.407867,467.996368
	C392.229004,468.011505 402.054535,467.814819 411.870422,468.042114
	C428.350403,468.423645 442.868561,464.108521 455.177399,452.491119
	C466.119049,442.164215 471.987213,429.599579 472.846405,414.951263
	C473.585907,402.343628 473.018646,389.658234 472.992035,377.007294
	C472.979034,370.830811 470.173615,368.026672 463.996429,368.008423
	C453.842438,367.978455 443.688263,368.000305 432.547089,368.000305
z"
    />

    <path
      fill="currentColor"
      opacity="1.000000"
      stroke="none"
      d="
M92.468658,513.000000
	C91.859283,512.032288 91.984451,510.914398 91.536034,510.120483
	C87.741005,503.401489 90.686264,498.008148 98.279594,498.006165
	C145.433563,497.993805 192.587524,497.999969 239.741486,497.999908
	C240.906631,497.999908 242.071777,497.999878 243.618500,497.999878
	C243.618500,421.384369 243.618500,344.859650 243.623138,268.334595
	C232.372147,267.266571 224.371933,259.665863 223.917755,247.646393
	C223.747070,243.129288 222.650970,238.647079 221.974716,234.149292
	C221.950150,233.985825 221.923630,233.817886 221.864288,233.665131
	C220.248840,229.507233 205.758240,224.890152 202.112991,227.388779
	C196.763763,231.055450 191.635849,235.099869 186.040085,238.337814
	C179.273621,242.253128 169.095276,240.425171 163.402985,234.876877
	C159.823959,231.388382 156.533279,227.573517 152.743256,224.338501
	C145.284622,217.972061 143.421066,205.407852 150.431229,196.072205
	C152.581024,193.209244 154.467026,190.054825 157.026505,187.613266
	C161.632492,183.219528 161.609894,178.745544 158.350571,173.791748
	C156.380112,170.796875 154.996002,166.837860 152.226578,165.105835
	C149.319519,163.287750 145.092834,163.284775 141.407761,163.057571
	C126.741905,162.153351 118.183014,155.225403 118.058258,138.996124
	C118.016724,133.592529 118.331436,128.140976 119.044762,122.786148
	C120.570076,111.335854 129.998886,106.941635 139.211624,105.960876
	C143.062561,105.550919 146.854172,104.297089 150.696762,104.173431
	C154.746964,104.043091 155.380310,101.206535 157.113312,98.572144
	C163.683960,88.583923 158.200821,81.777115 152.077026,74.362968
	C145.170013,66.000610 143.036469,58.718388 149.038177,49.903130
	C153.971664,42.656868 160.684738,36.115479 167.863831,31.034624
	C176.601791,24.850506 185.407730,28.666090 193.177383,34.694977
	C196.265427,37.091164 199.259781,39.620052 202.464249,41.847645
	C203.557037,42.607304 205.377869,43.261021 206.495377,42.878948
	C211.057983,41.318996 216.021072,40.057529 219.740997,37.239704
	C221.902161,35.602619 222.208359,31.270256 222.940750,28.057585
	C223.801865,24.280180 224.319672,20.414492 224.808792,16.564528
	C225.651627,9.930397 229.470779,5.678349 235.293396,2.889245
	C235.837753,2.628496 236.202408,1.992585 236.325256,1.265431
	C243.020950,1.000000 250.041885,1.000000 257.531433,0.999999
	C258.815796,2.327274 259.411377,3.873521 260.480591,4.948803
	C275.167969,19.719238 290.165527,34.189575 304.590881,49.209961
	C320.153870,65.414917 333.022064,83.405525 339.208130,105.511986
	C343.158691,119.629585 343.662201,133.789047 342.161713,148.328537
	C340.368408,165.705414 334.039734,181.231857 324.949127,195.804062
	C322.287750,200.070206 319.156555,204.171890 315.631989,207.749176
	C298.089661,225.553848 280.313873,243.128372 262.730347,260.892853
	C261.383759,262.253265 260.119385,264.455475 260.113251,266.270844
	C259.967743,309.263855 260.000305,352.257507 260.000305,396.080353
	C261.427582,394.829956 262.479126,394.009216 263.415680,393.073425
	C285.694611,370.812012 308.004059,348.580627 330.140503,326.178314
	C331.745667,324.553864 332.850311,321.679474 332.877655,319.370758
	C333.096588,300.875671 332.929779,282.376556 333.019653,263.879333
	C333.109772,245.330460 339.694427,228.958176 350.450256,214.166840
	C353.336884,210.197205 356.958405,206.746490 360.366760,203.176132
	C363.678680,199.706787 370.226532,198.680969 372.687805,201.368866
	C376.628174,205.672134 375.444794,211.552551 371.715210,214.888885
	C358.822083,226.422653 352.054047,241.388580 349.208374,258.071442
	C348.130707,264.389221 348.996521,271.039917 348.991394,277.538879
	C348.983765,287.159851 348.989410,296.780792 348.989410,307.783966
	C354.278503,302.398926 358.724030,297.823212 363.224091,293.301788
	C371.569763,284.916534 380.042114,276.653870 388.225342,268.113678
	C389.725311,266.548218 390.865936,263.914459 390.883209,261.763489
	C391.070465,238.434631 390.997498,215.103699 391.001923,191.773331
	C391.003143,185.397903 393.237701,182.058502 399.352142,181.112061
	C408.203918,179.741943 417.137665,178.358688 426.065308,178.135330
	C444.218323,177.681198 462.390656,177.991638 480.555023,178.003723
	C487.403625,178.008286 489.999115,180.621948 489.999542,187.441788
	C490.000763,206.606018 489.669617,225.777313 490.078705,244.932816
	C490.668488,272.550781 480.184906,295.279877 459.922699,313.369141
	C445.968292,325.827148 429.478851,334.023560 410.459503,334.866394
	C389.566376,335.792328 368.624084,335.570526 347.710754,336.130585
	C345.263245,336.196136 342.266388,337.445587 340.531281,339.163422
	C314.597839,364.838379 288.793060,390.644165 263.121704,416.581177
	C261.408417,418.312225 260.154175,421.337250 260.132538,423.773285
	C259.915070,448.265198 260.000214,472.759827 260.000214,497.999939
	C261.841003,497.999939 263.585999,497.999939 265.331024,497.999939
	C311.491821,497.999939 357.652649,497.986053 403.813416,498.027863
	C407.279236,498.031006 411.170349,497.036743 413.144836,501.525391
	C414.957611,505.646301 414.071716,509.137512 411.668030,512.705444
	C305.645782,513.000000 199.291550,513.000000 92.468658,513.000000
M244.000046,113.500160
	C244.000046,106.405968 244.000046,99.311768 244.000046,91.784134
	C233.912582,95.078316 225.273895,99.153732 218.756195,107.353188
	C217.155029,109.367531 212.420074,109.987999 209.425919,109.480110
	C206.601471,109.001022 204.849716,106.119026 204.872925,102.373474
	C204.908707,96.598061 208.822800,93.385345 212.389801,90.338509
	C221.462677,82.588638 232.033981,77.570145 243.666321,75.729988
	C243.666321,55.932697 243.666321,36.565201 243.666321,16.617496
	C238.126968,19.547253 239.796432,24.708887 239.173676,28.573593
	C237.704697,37.689457 237.083054,47.039700 227.532471,51.859554
	C222.238312,54.531345 216.816437,57.486137 211.111145,58.669582
	C204.188202,60.105602 197.072052,59.312195 191.358246,53.928642
	C189.724091,52.388920 187.674881,51.285397 185.798035,50.008259
	C176.722687,43.832767 176.720520,43.835953 168.922485,51.633148
	C160.971283,59.583492 161.115982,59.474182 168.025528,68.619820
	C172.894073,75.063950 179.039902,81.434692 176.846481,90.457870
	C175.341553,96.648849 172.367615,102.478500 170.108505,108.491531
	C167.613190,115.133247 162.185852,118.327332 155.850098,119.861443
	C150.811066,121.081566 145.545853,121.423370 140.361435,121.968719
	C136.546646,122.369995 134.236786,124.262054 134.050888,128.081787
	C133.840576,132.403000 133.581711,136.803940 134.200546,141.045227
	C134.468597,142.882339 136.582367,145.268036 138.381973,145.891541
	C141.997437,147.144180 145.954269,147.517334 149.803680,147.966873
	C157.640228,148.882065 165.047775,150.604752 169.003220,158.405396
	C171.256912,162.850006 173.015244,167.543991 175.049072,172.102295
	C178.648285,180.169022 177.525162,187.577682 172.142715,194.490433
	C169.151596,198.331955 166.054535,202.127396 163.494278,206.247498
	C162.643661,207.616348 162.520660,210.592545 163.436310,211.695969
	C166.717636,215.650223 170.372742,219.379929 174.355927,222.616913
	C175.644943,223.664413 178.791000,223.704849 180.363129,222.859161
	C183.610382,221.112427 186.403732,218.501907 189.324539,216.172424
	C195.607574,211.161362 202.549744,207.597412 210.655106,210.141129
	C217.207291,212.197418 223.683395,214.945984 229.616302,218.383270
	C234.351013,221.126312 237.023026,226.164566 237.951248,231.702682
	C238.811737,236.836685 239.177200,242.055252 240.088348,247.178299
	C240.406555,248.967529 241.701874,250.582993 242.815689,252.806213
	C243.613708,251.528519 243.906021,251.278854 243.906631,251.028488
	C243.952057,232.538406 244.032471,214.047760 243.862946,195.559402
	C243.852081,194.374420 242.041595,192.374435 240.818069,192.156143
	C220.742157,188.574570 207.156998,176.764420 198.466553,158.872604
	C194.740616,151.201706 192.974792,142.913116 193.054718,134.359161
	C193.094971,130.051346 197.049500,126.163826 200.895966,126.064461
	C204.843536,125.962502 208.920486,129.577179 208.966278,133.587143
	C209.130188,147.938095 215.098373,159.498260 226.039581,168.428238
	C231.085358,172.546478 236.850571,175.653198 244.000046,175.953873
	C244.000046,155.121552 244.000046,134.810806 244.000046,113.500160
M317.952148,93.640434
	C313.952454,85.697876 309.512756,78.064934 303.041840,71.610359
	C289.019501,85.591576 275.314636,99.167206 261.779144,112.909691
	C260.612030,114.094681 260.088654,116.380844 260.074799,118.161766
	C259.947998,134.473694 259.993256,150.787048 260.014618,167.099945
	C260.016144,168.287750 260.222870,169.475296 260.380890,171.161316
	C278.986816,152.541443 297.104248,134.408417 315.224091,116.277847
	C324.671906,106.824455 324.674103,106.826271 318.349579,94.804710
	C318.273163,94.659439 318.150848,94.538300 317.952148,93.640434
M292.507629,208.007660
	C303.133636,198.123688 312.541962,187.320633 318.584106,173.917130
	C325.112854,159.434036 328.114594,144.364700 326.680328,127.526039
	C304.478638,149.813110 282.873749,171.484268 261.322479,193.208603
	C260.604919,193.931900 260.062286,195.190979 260.056610,196.203766
	C259.975555,210.624191 260.001373,225.045242 260.001373,240.551498
	C271.175629,229.364136 281.591644,218.935898 292.507629,208.007660
M410.543793,281.000000
	C427.362091,281.000000 444.184174,280.805878 460.995972,281.136993
	C465.178833,281.219391 466.873230,279.389862 467.934509,276.107086
	C470.144836,269.269836 472.234833,262.393707 474.540375,255.001053
	C457.869843,255.001053 442.067169,254.992828 426.264587,255.026077
	C425.500000,255.027679 424.490753,255.147293 424.008636,255.627121
	C415.733582,263.863495 407.523621,272.165283 398.756256,281.000000
	C402.958130,281.000000 406.261047,281.000000 410.543793,281.000000
M425.507629,231.007660
	C437.627991,218.886612 449.748322,206.765579 461.513458,194.999756
	C444.559937,194.999756 427.831177,195.069809 411.103973,194.942078
	C407.658508,194.915771 406.925720,196.386200 406.952057,199.457550
	C407.064789,212.608093 407.000000,225.760132 407.000000,238.911636
	C406.999969,242.138412 407.000000,245.365204 407.000000,249.654190
	C413.574371,243.029037 419.290955,237.268311 425.507629,231.007660
M414.500000,296.999725
	C405.022980,296.999756 395.545502,296.953552 386.069397,297.046143
	C384.527069,297.061188 382.509552,297.183990 381.528442,298.116394
	C374.266235,305.017944 367.210846,312.137085 359.270325,319.999939
	C371.530121,319.999939 382.459320,319.807800 393.378876,320.051117
	C405.588440,320.323212 417.403107,318.710724 428.716614,313.983551
	C437.854675,310.165375 445.791260,304.649658 453.426788,296.999725
	C440.060394,296.999725 427.780212,296.999725 414.500000,296.999725
M260.000214,83.322159
	C260.000214,85.728729 260.000214,88.135300 260.000214,90.829536
	C270.793457,79.967896 281.153534,69.542152 290.756287,59.878559
	C280.668488,49.750576 270.309174,39.350006 260.000214,29.000004
	C260.000214,46.532551 260.000214,64.450836 260.000214,83.322159
M442.385071,237.083847
	C442.559509,237.721344 442.729095,238.915192 442.909088,238.916763
	C453.170044,239.006134 463.431885,238.996353 473.684265,238.996353
	C473.684265,227.788773 473.684265,217.116592 473.684265,205.881012
	C463.140106,216.411758 453.038727,226.500275 442.385071,237.083847
z"
    />
    <path
      fill="currentColor"
      opacity="1.000000"
      stroke="none"
      d="
M433.040649,368.000305
	C443.688263,368.000305 453.842438,367.978455 463.996429,368.008423
	C470.173615,368.026672 472.979034,370.830811 472.992035,377.007294
	C473.018646,389.658234 473.585907,402.343628 472.846405,414.951263
	C471.987213,429.599579 466.119049,442.164215 455.177399,452.491119
	C442.868561,464.108521 428.350403,468.423645 411.870422,468.042114
	C402.054535,467.814819 392.229004,468.011505 382.407867,467.996368
	C375.798920,467.986176 373.020142,465.313995 373.007385,458.900757
	C372.982849,446.582703 372.475952,434.237122 373.119720,421.952789
	C374.151215,402.269592 383.474060,387.121002 400.066742,376.423523
	C409.987305,370.027649 420.964233,367.891388 433.040649,368.000305
M449.000336,403.499420
	C433.077667,419.417725 417.155029,435.335999 400.997833,451.488770
	C443.168579,457.209198 462.005615,428.794250 456.196136,396.100067
	C453.885376,398.481201 451.693207,400.740112 449.000336,403.499420
M399.984558,397.524384
	C388.371704,409.504211 388.202576,424.321198 389.282776,439.692902
	C408.100616,421.360565 426.683777,403.256836 446.077148,384.363800
	C422.649567,382.805420 412.289246,385.202301 399.984558,397.524384
z"
    />
    <path
      fill="currentColor"
      opacity="1.000000"
      stroke="none"
      d="
M207.998596,354.440247
	C207.966675,361.089111 208.404297,367.210388 205.119537,373.070404
	C202.356934,377.998962 198.440063,380.284546 193.196289,380.990387
	C190.286774,381.382050 187.336838,381.527313 184.454498,382.053955
	C180.455658,382.784607 178.889252,390.542816 181.944962,394.389526
	C185.274658,398.581146 189.188171,402.531891 188.968903,408.405304
	C188.566452,419.185608 182.336472,426.080719 173.326187,430.875763
	C164.297699,435.680450 156.907410,431.635803 150.446289,425.690796
	C147.929871,423.375366 146.046448,423.227844 143.270584,424.272186
	C139.909607,425.536682 138.337341,427.787476 137.926529,431.375153
	C137.487610,435.208374 137.133591,439.290527 135.535660,442.698059
	C132.869446,448.383759 127.190056,450.714142 121.412682,451.695831
	C114.720528,452.832947 108.028343,452.166351 101.989883,448.488708
	C96.107498,444.906128 94.332077,439.152649 94.059662,432.803436
	C93.914413,429.418304 93.893578,426.517487 89.997948,424.898376
	C86.493904,423.442017 84.126877,423.570618 81.045753,426.265900
	C74.397530,432.081512 66.884499,435.513672 58.013275,430.540680
	C49.235332,425.619965 42.803043,418.687378 43.307003,408.225220
	C43.517937,403.846222 46.780651,399.423553 49.282959,395.411926
	C50.979027,392.692871 53.320305,390.726440 51.587437,386.992493
	C49.995129,383.561432 48.252331,381.942017 43.982330,381.796295
	C35.132938,381.494354 27.237179,378.558319 25.055853,368.514038
	C22.948242,358.809204 22.178185,348.930054 30.939081,341.895081
	C33.928787,339.494324 38.565331,338.707520 42.584007,338.145294
	C46.784092,337.557648 50.413136,336.695099 51.287655,332.102081
	C51.658306,330.155426 51.324352,327.577484 50.278473,325.949585
	C46.875889,320.653503 42.037537,316.034515 43.020046,308.945007
	C44.390137,299.058807 50.366032,292.535126 59.062084,288.528046
	C66.907310,284.913025 74.003845,287.017578 80.202927,292.703735
	C82.506866,294.817017 84.547775,296.913666 88.335320,295.350769
	C92.366135,293.687531 93.944077,291.256439 94.100586,286.859283
	C94.591850,273.057404 105.915291,265.175354 119.699738,267.778137
	C127.049324,269.165863 133.383865,271.164459 136.126465,278.725403
	C137.209671,281.711609 137.660141,284.997833 137.945038,288.187988
	C138.488907,294.278137 144.471283,297.355835 149.615112,294.063782
	C154.875122,290.697357 159.433502,286.201111 166.691879,286.889313
	C179.540085,288.107513 193.198196,303.717194 187.916992,316.430420
	C186.642761,319.497772 184.706833,322.495667 182.403824,324.875916
	C179.792999,327.574219 179.553741,329.941711 180.960052,333.225586
	C182.586594,337.023743 185.529724,337.987579 189.143967,337.987793
	C194.952667,337.988129 200.356140,339.238831 203.709427,344.364288
	C205.607620,347.265625 206.598099,350.760864 207.998596,354.440247
M113.742912,284.000519
	C109.672760,283.891357 110.074020,286.928955 110.020798,289.469269
	C109.639038,307.691498 91.337799,315.580200 78.035416,310.793396
	C73.625336,309.206451 69.960693,305.547974 66.353943,303.106476
	C63.808224,305.670898 61.510876,307.985107 59.086338,310.427460
	C64.776382,316.817596 69.960663,323.591095 68.061020,333.361572
	C66.432999,341.734985 60.038647,352.727325 49.014664,353.149231
	C39.813606,353.501373 39.830967,354.065704 40.024410,363.302521
	C40.034389,363.778900 40.220657,364.251617 40.393715,365.039612
	C42.920902,365.357056 45.475006,365.752563 48.043846,365.985413
	C53.795311,366.506744 59.111610,368.458496 62.462883,373.138885
	C69.549019,383.035492 71.644081,394.457642 62.219090,405.327301
	C59.038551,408.995300 59.614792,412.161621 63.555717,414.932678
	C64.481941,415.583984 65.518059,416.078979 66.220123,416.482788
	C70.533798,413.666473 74.530418,409.978149 79.247551,408.180664
	C93.156044,402.880768 109.331993,414.482819 109.901619,429.938843
	C110.066971,434.425385 111.545944,436.096222 116.057152,436.103546
	C120.666107,436.111023 122.347015,434.260406 122.068748,429.827423
	C121.971176,428.273041 122.736328,426.680298 122.984383,425.085938
	C124.727051,413.885010 139.263824,404.143951 150.031281,407.713684
	C155.668121,409.582489 160.733002,413.176483 166.355560,416.149628
	C166.420090,416.106750 167.242325,415.567902 168.057114,415.018036
	C172.633789,411.929535 173.242584,409.349487 169.675858,405.253082
	C164.550674,399.366821 162.758270,392.682648 164.028625,385.136200
	C165.150467,378.471802 171.028595,367.639862 179.907288,366.905426
	C183.884689,366.576447 187.803513,365.539337 191.999680,364.778076
	C191.999680,362.399689 191.878448,360.244507 192.029037,358.108459
	C192.237625,355.149567 191.156723,353.812164 188.043915,353.931702
	C186.475113,353.991913 184.868042,353.006561 183.296753,353.058014
	C170.537476,353.475891 163.133926,338.325439 163.683060,327.558258
	C164.066360,320.042969 169.278656,315.453461 172.891632,310.241943
	C170.430115,307.770447 168.130280,305.461273 165.642151,302.963043
	C162.101944,305.483429 158.753113,308.972290 154.664917,310.574005
	C141.626541,315.682281 123.300537,308.389984 121.991379,290.714844
	C121.493835,283.997467 121.437637,284.001617 113.742912,284.000519
z"
    />

    <path
      fill="currentColor"
      opacity="1.000000"
      stroke="none"
      d="
M151.999832,359.992249
	C150.386383,383.535583 134.504929,395.856476 115.872810,395.652100
	C94.721718,395.420135 80.254425,380.468628 80.152809,359.704010
	C80.053940,339.502350 95.502312,324.665894 114.426498,324.002106
	C136.412643,323.230865 151.102280,339.570557 151.999832,359.992249
M135.996124,361.235626
	C135.893326,348.457062 127.287781,340.885498 117.036751,340.111816
	C103.196854,339.067291 92.544235,353.861511 97.321152,366.831055
	C100.427254,375.264252 110.815018,380.810364 119.530167,379.051849
	C129.010635,377.138947 133.612595,370.772034 135.996124,361.235626
z"
    />
  </svg>
)

export const Logo = () => (
  <>
    <LogoLarge className="w-full text-primary group-[[data-collapsed=true]]:hidden" />
    <LogoSmall className="hidden size-5 md:group-[[data-collapsed=true]]:block md:group-[[data-collapsed=false]]:hidden" />
  </>
)
