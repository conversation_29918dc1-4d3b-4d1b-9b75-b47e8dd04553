"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyTemplateEnum } from "@/utils/constants"
import React, { Fragment, memo, useCallback, useState } from "react"
import { CompanySelector } from "@/app/(general)/company-selector"
import { api } from "@/lib/trpc-provider"
import { Language, SurveyStatusEnum } from "@/utils/constants"
import { useControllableState } from "@radix-ui/react-use-controllable-state"
import { PlusCircle, X } from "lucide-react"
import { useLocale, useTranslations } from "next-intl"
import { z } from "zod"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@kreios/ui/dialog"
import { Form, FormItem, useFieldArray, useForm } from "@kreios/ui/form"
// We're using the schema directly instead of zodResolver
import { createFormFieldHelper } from "@kreios/ui/form/form-field-helper"
import { Input } from "@kreios/ui/input"
import { Separator } from "@kreios/ui/separator"
import { toast } from "@kreios/ui/sonner"

// Constants for date calculations
const currentYear = new Date().getFullYear()
const currentDate = new Date()
const previousYear = currentYear - 1

const defaultDeadline = new Date()
defaultDeadline.setMonth(defaultDeadline.getMonth() + 1)

// Email validation utility function
const isValidEmail = (email: string) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())
}

// Memoized EmailInput component
const EmailInput = memo(({ value, onChange }: { value: string[]; onChange: (emails: string[]) => void }) => {
  const [currentEmail, setCurrentEmail] = useState("")
  const t = useTranslations("common")

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" || e.key === ",") {
        e.preventDefault()
        if (currentEmail && isValidEmail(currentEmail)) {
          onChange([...value, currentEmail.trim()])
          setCurrentEmail("")
        }
      }
    },
    [currentEmail, onChange, value]
  )

  const removeEmail = useCallback(
    (emailToRemove: string) => {
      onChange(value.filter((email) => email !== emailToRemove))
    },
    [onChange, value]
  )

  const addEmail = useCallback(() => {
    if (currentEmail && isValidEmail(currentEmail)) {
      onChange([...value, currentEmail.trim()])
      setCurrentEmail("")
    }
  }, [currentEmail, onChange, value])

  return (
    <div className="space-y-2">
      {value.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {value.map((email, i) => (
            <div key={i} className="flex items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm dark:bg-gray-700">
              <span>{email}</span>
              <button
                type="button"
                onClick={() => removeEmail(email)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
      <div className="flex gap-2">
        <Input
          type="email"
          value={currentEmail}
          onChange={(e) => setCurrentEmail(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={t("emailPlaceholder")}
        />
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={addEmail}
          disabled={!currentEmail || !isValidEmail(currentEmail)}
        >
          <PlusCircle className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
})

// Props for the shared SendSurveyDialog component
export interface SendSurveyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  batchId: string
  // The template enum to use (passed from the specific implementation)
  templateEnum: z.ZodEnum<[string, ...string[]]>
  // The template enum label mapper (maps enum values to display labels)
  templateLabelMapper: Record<string, string>
  // Default template value to use
  defaultTemplateValue: string
  // Translation namespace to use
  translationNamespace: string
  // Header background style (optional)
  headerBgClass?: string
}

/**
 * Internal SendSurveyDialog component that handles the actual dialog functionality
 * This is a client component that can accept non-serializable props
 */
function SendSurveyDialogContent({
  open: openValue,
  onOpenChange: onOpenChangeValue,
  batchId,
  templateEnum: _templateEnum,
  templateLabelMapper: _templateLabelMapper,
  defaultTemplateValue,
  translationNamespace,
  headerBgClass = "bg-gray-50",
}: SendSurveyDialogProps) {
  const utils = api.useUtils()
  const t = useTranslations(translationNamespace)
  const CompanySelectorTexts = useTranslations("submission.form")
  const currentLocale = useLocale() as Language

  // Function to create schema based on the current locale
  const createSchemas = (locale: Language) => {
    // Schema for a single survey row
    const rowSchema = z.object({
      company: z.object(
        {
          aggregateId: z.string(),
          name: z.string().min(1),
        },
        {
          message: locale === Language.EN ? "Company is required" : "Ettevõte on kohustuslik",
        }
      ),
      companyContacts: z
        .array(z.string().email(locale === Language.EN ? "Invalid email address" : "Vigane e-posti aadress"))
        .min(
          1,
          locale === Language.EN
            ? "At least one email address is required"
            : "Vähemalt üks e-posti aadress on kohustuslik"
        ),
      deadline: z.coerce
        .date()
        .min(currentDate, locale === Language.EN ? "Deadline must be in the future" : "Tähtaeg peab olema tulevikus")
        .optional()
        .default(defaultDeadline),
    })

    // Schema for the entire form
    const formSchema = z.object({
      surveys: z
        .array(rowSchema)
        .min(1, locale === Language.EN ? "At least one survey is required" : "Vähemalt üks küsitlus on kohustuslik"),
    })

    return { surveyRowSchema: rowSchema, formSchema }
  }

  // Create schema based on current locale
  const { formSchema } = createSchemas(currentLocale)

  // Create form field helper
  const FormField = createFormFieldHelper(formSchema)

  // Define form value types based on the schema
  type FormValues = z.infer<ReturnType<typeof createSchemas>["formSchema"]>
  // Define a type for a new survey row with optional company field
  type NewSurveyRow = Omit<z.infer<ReturnType<typeof createSchemas>["formSchema"]>["surveys"][0], "company"> & {
    company?: { name: string; aggregateId: string }
  }

  const sendSurvey = api.surveys.sendSurvey.useMutation({
    onSuccess: async () => {
      await Promise.all([
        utils.surveys.get.invalidate(),
        utils.surveys.listSurveys.invalidate(),
        utils.surveys.getCompanySurveys.invalidate(),
      ])
    },
  })

  const [open, setOpen] = useControllableState({
    prop: openValue,
    onChange: onOpenChangeValue,
  })

  const form = useForm({
    schema: formSchema,
    defaultValues: {
      surveys: [
        {
          companyContacts: [],
          deadline: defaultDeadline,
        },
      ],
    },
    mode: "onChange",
    // This ensures the form validation updates when the language changes
    context: { locale: currentLocale },
  })
  const { fields, append, remove } = useFieldArray({
    name: "surveys",
    control: form.control,
  })

  const onSubmit = async (data: FormValues) => {
    try {
      // Transform the form data to match the new backend requirements
      const companySurveys = data.surveys.map((survey) => ({
        readyForEdit: true,
        progress: 0,
        status: SurveyStatusEnum.NotStarted,
        companyId: survey.company.aggregateId,
        contactEmails: survey.companyContacts,
        deadline: survey.deadline.toISOString(),
        financialYear: previousYear, // Use the default previous year
      }))

      // Create the payload according to the new schema
      const payload = {
        batchId,
        template: defaultTemplateValue as SurveyTemplateEnum, // Use the default template value
        companySurveys,
        // Note: The requestedBy field is automatically set to the current user's email in the TRPC router
      }

      // Send a single request with all the data
      await sendSurvey.mutateAsync(payload)

      toast.success(t("successMessage"))
      setOpen(false)
      form.reset({
        surveys: [
          {
            companyContacts: [],
            deadline: defaultDeadline,
          },
        ],
      })
      // Update the form context with the current locale
      form.clearErrors()
      // Re-create the form with the new locale
      form.reset(form.getValues())
    } catch (error) {
      console.error(error)
      toast.error(t("errorMessage"))
    }
  }

  const watchCompanyContacts = form.watch("surveys")

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen)
        // Reset form when dialog is closed
        if (!newOpen) {
          form.reset({
            surveys: [
              {
                companyContacts: [],
                deadline: defaultDeadline,
              },
            ],
          })
          // Update the form context with the current locale
          form.clearErrors()
          // Re-create the form with the new locale
          form.reset(form.getValues())
        }
      }}
    >
      <DialogContent className="max-h-[calc(100vh-10px)] w-[95vw] max-w-[1200px] overflow-y-auto p-4 lg:max-h-[calc(100vh-100px)] lg:p-6">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold lg:text-2xl">{t("title")}</DialogTitle>
          <p className="text-sm text-gray-600 dark:text-gray-300 lg:text-base">{t("description")}</p>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 lg:space-y-8">
            <div className="rounded-lg border">
              {/* Header - Hide on mobile, show labels within fields */}
              <div
                className={`hidden grid-cols-[minmax(200px,1fr)_minmax(250px,1.5fr)_minmax(150px,0.5fr)] gap-4 ${headerBgClass} p-4 font-medium lg:grid`}
              >
                {[
                  { key: "company", required: true },
                  { key: "companyContacts", required: true },
                  { key: "deadline", required: true },
                ].map(({ key, required }) => (
                  <div key={key}>
                    {t(`columns.${key}`)}
                    {required && <span className="ml-1 text-destructive">*</span>}
                  </div>
                ))}
              </div>

              <div className="space-y-4 p-4">
                {fields.length > 0 &&
                  fields.map((field, index, array) => (
                    <Fragment key={field.id}>
                      <div
                        className={cn(
                          "relative box-border rounded-lg border p-4 lg:rounded-none lg:border-0 lg:px-0",
                          index === 0 && "lg:pt-0",
                          !!watchCompanyContacts.at(index)?.companyContacts.length && index > 0 && "lg:pt-0"
                        )}
                      >
                        <div className="box-border grid grid-cols-1 gap-4 lg:grid-cols-[minmax(200px,1fr)_minmax(250px,1.5fr)_minmax(150px,0.5fr)] lg:items-end">
                          {/* Add labels for mobile view */}
                          <div className="space-y-2">
                            <label className="text-sm font-medium lg:hidden">{t("columns.company")}</label>
                            <FormField
                              name={`surveys.${index}.company`}
                              label=""
                              render={({ field }) => (
                                <CompanySelector
                                  allowCreate={false}
                                  ref={field.ref}
                                  onBlur={field.onBlur}
                                  disabled={field.disabled}
                                  value={field.value}
                                  onValueChange={(newValue) => {
                                    field.onChange(newValue)
                                    void form.trigger(`surveys.${index}.company`)
                                  }}
                                  texts={{
                                    placeholder: CompanySelectorTexts("companyNamePlaceholder"),
                                    searchPlaceholder: CompanySelectorTexts("searchPlaceholder"),
                                    emptyStateWithCreate: CompanySelectorTexts("emptyStateWithCreate"),
                                    emptyStateWithoutCreate: CompanySelectorTexts("emptyStateWithoutCreate"),
                                    notInDatabase: CompanySelectorTexts("notInDatabase"),
                                    refineSearch: CompanySelectorTexts("refineSearch"),
                                  }}
                                />
                              )}
                            />
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium lg:hidden">{t("columns.companyContacts")}</label>
                            <FormField
                              name={`surveys.${index}.companyContacts`}
                              label=""
                              render={({ field }) => (
                                <FormItem>
                                  <EmailInput
                                    value={Array.isArray(field.value) ? field.value : []}
                                    onChange={(emails) => {
                                      field.onChange(emails)
                                    }}
                                  />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium lg:hidden">{t("columns.deadline")}</label>
                            <FormField.DatePicker
                              name={`surveys.${index}.deadline`}
                              label=""
                              className="lg:max-w-[150px]"
                              minDate={new Date()}
                            />
                          </div>
                        </div>
                        {index > 0 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() => remove(index)}
                            className={cn(
                              "absolute -right-2 -top-2 h-6 w-6 rounded-full bg-white shadow-sm dark:bg-gray-800",
                              watchCompanyContacts[index].companyContacts.length > 0 && "lg:top-5"
                            )}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      {index !== array.length - 1 && <Separator className="" />}
                    </Fragment>
                  ))}
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  // We intentionally omit the company field as it will be selected by the user
                  // Use a partial type to allow missing required fields during initialization
                  // Add a new survey row without the company field
                  // The user will select a company later
                  const newSurvey: NewSurveyRow = {
                    companyContacts: [],
                    deadline: defaultDeadline,
                    // company field is intentionally omitted
                  }
                  // Cast to the expected type for append
                  append(newSurvey as z.infer<ReturnType<typeof createSchemas>["formSchema"]>["surveys"][0])
                  // Update the form context with the current locale
                  form.clearErrors()
                  // Re-create the form with the new locale
                  form.reset(form.getValues())
                }}
                className="w-full max-w-xs"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("addMore")}
              </Button>
            </div>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setOpen(false)
                  form.reset({
                    surveys: [
                      {
                        companyContacts: [],
                        deadline: defaultDeadline,
                      },
                    ],
                  })
                  // Update the form context with the current locale
                  form.clearErrors()
                  // Re-create the form with the new locale
                  form.reset(form.getValues())
                }}
                className="w-full lg:w-auto"
              >
                {t("cancelLabel")}
              </Button>
              <Button type="submit" className="w-full lg:w-auto" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? t("loadingText") : t("confirmLabel")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

/**
 * SendSurveyDialog component for creating and sending surveys
 * This is the exported component that accepts only serializable props
 * and passes them to the internal client component
 */
export function SendSurveyDialog(props: SendSurveyDialogProps) {
  return <SendSurveyDialogContent {...props} />
}
