/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyTemplateEnum } from "@/utils/constants"
import type { z } from "zod"
import { api } from "@/lib/trpc-provider"
import { localizedMessage, lz } from "@/utils/zod-localization"
import { useControllableState } from "@radix-ui/react-use-controllable-state"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"

import { Button } from "@kreios/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@kreios/ui/dialog"
import { Form, useForm } from "@kreios/ui/form"
import { createFormFieldHelper } from "@kreios/ui/form/form-field-helper"
import { toast } from "@kreios/ui/sonner"

const batchFormSchema = lz.object({
  name: lz.string().min(1, localizedMessage("Batch name is required", "Paki nimi on kohustuslik")),
})

const FormField = createFormFieldHelper(batchFormSchema)

export interface AddBatchDialogProps {
  /**
   * Whether the dialog is open
   */
  open: boolean
  /**
   * Callback when the open state changes
   */
  onOpenChange: (open: boolean) => void
  /**
   * The template type to use when creating a batch
   */
  templateType: SurveyTemplateEnum
  /**
   * The translation namespace to use for this dialog
   */
  namespace: string
  /**
   * The base path to redirect to after creating a batch
   */
  redirectBasePath: string
}

/**
 * A reusable dialog component for adding new batches
 * Used by both surveys and assessment-batches pages
 */
export const AddBatchDialog: React.FC<AddBatchDialogProps> = ({
  open: openValue,
  onOpenChange: onOpenChangeValue,
  templateType,
  namespace,
  redirectBasePath,
}) => {
  const createBatch = api.surveys.createBatch.useMutation()
  const t = useTranslations(`${namespace}.addNewBatchDialog`)

  const [open, onOpenChange] = useControllableState({
    prop: openValue,
    onChange: onOpenChangeValue,
  })

  const router = useRouter()

  const form = useForm({
    schema: batchFormSchema,
    defaultValues: { name: "" },
    mode: "onSubmit",
  })

  const onSubmit = async (data: z.infer<typeof batchFormSchema>) => {
    try {
      const { aggregateId } = await createBatch.mutateAsync({
        name: data.name,
        template: templateType,
      })
      form.reset()
      onOpenChange(false)
      toast.success(t("successMessage"))
      router.push(`${redirectBasePath}/${aggregateId}`)
    } catch (error) {
      console.error("Error creating batch:", error)
      toast.error(t("errorMessage"))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <DialogHeader>
              <DialogTitle>{t("title")}</DialogTitle>
              <DialogDescription>{t("description")}</DialogDescription>
            </DialogHeader>
            <FormField name="name" placeholder={t("inputPlaceholder")} className="w-full" label={t("inputLabel")} />
            <div className="mt-4 flex justify-end gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                {t("cancel")}
              </Button>
              <Button type="submit" disabled={!form.formState.isValid || createBatch.isPending} className="ml-2">
                {t("save")}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
