/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { useTranslations } from "next-intl"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"
import { Input } from "@kreios/ui/input"

const ITEMS_PER_PAGE = 12

export interface BatchesPageSkeletonProps {
  /**
   * The translation namespace to use for this page
   */
  namespace: string
  /**
   * The SkeletonGrid component to use for rendering skeleton grid
   */
  SkeletonGrid: React.ComponentType<{ itemsCount: number }>
}

/**
 * A reusable skeleton component for the batches page
 * Used by both surveys and assessment-batches pages
 */
export const BatchesPageSkeleton: React.FC<BatchesPageSkeletonProps> = ({ namespace, SkeletonGrid }) => {
  const t = useTranslations(namespace)
  return (
    <>
      <PageHeader title={t("title")}>
        <div className="flex w-full flex-wrap items-center gap-4 md:w-auto">
          <Input placeholder="Search batches..." disabled className="h-8 w-[150px] bg-background lg:w-[250px]" />
          {/* <Button variant="outline" size="sm" disabled>
            {t("export")}
          </Button> */}
          <Button size="sm" disabled>
            {t("addNewBatch")}
          </Button>
        </div>
      </PageHeader>

      <div className="p-4 md:p-6">
        <SkeletonGrid itemsCount={ITEMS_PER_PAGE} />
      </div>
    </>
  )
}
