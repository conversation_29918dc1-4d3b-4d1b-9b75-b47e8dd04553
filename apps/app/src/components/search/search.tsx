/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { RouterOutput } from "@/lib/trpc-provider"
import { api } from "@/lib/trpc-provider"

import type { ComponentDictionary } from "@kreios/command-menu/commands/search-commands"
import { SearchCommands } from "@kreios/command-menu/commands/search-commands"

type ImpactlyGlobalSearchOutput = RouterOutput["globalSearch"]["searchAll"]

const componentDictionary: ComponentDictionary<ImpactlyGlobalSearchOutput[number]> = {}

export const ImpactlySearchCommands: React.FC = () => {
  return <SearchCommands componentDict={componentDictionary} procedure={api.globalSearch.searchAll} />
}
