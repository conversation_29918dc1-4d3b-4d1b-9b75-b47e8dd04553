/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import { useCallback, useEffect, useRef, useState } from "react"
import { useRouter } from "next/navigation"
import setLanguageAction from "@/actions/set-language-action"
import { Language } from "@/utils/constants"
import { Globe } from "lucide-react"
import { useLocale } from "next-intl"
import { createPortal } from "react-dom"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@kreios/ui/dropdown-menu"

interface LanguageSelectorProps {
  className?: string
}

export const LanguageSelector = ({ className }: LanguageSelectorProps) => {
  const router = useRouter()
  const currentLocale = useLocale() as Language
  const [isLoading, setIsLoading] = useState(false)
  const lastSwitchTimeRef = useRef<number | null>(null)
  const [timeoutDuration, setTimeoutDuration] = useState(3000) // Start with 3 seconds

  // Safety timeout to ensure loading state is reset if page refresh takes too long
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        setIsLoading(false)
      }, timeoutDuration)

      return () => clearTimeout(timeout)
    }
  }, [isLoading, timeoutDuration])

  const onLanguageSelect = useCallback(
    async (language: Language) => {
      if (language === currentLocale) return

      try {
        const startTime = Date.now()
        setIsLoading(true)
        await setLanguageAction(language)
        router.refresh()

        // Store the time it took to switch languages
        const switchTime = Date.now() - startTime

        // If we have a previous switch time, adjust the timeout duration
        // based on the average of the previous and current switch times
        if (lastSwitchTimeRef.current) {
          const avgTime = Math.ceil((lastSwitchTimeRef.current + switchTime) / 2)
          // Add a buffer of 1 seconds to the average time
          const newTimeout = avgTime + 1000
          // Ensure timeout is at least 3 seconds and at most 15 seconds
          setTimeoutDuration(Math.max(3000, Math.min(7000, newTimeout)))
          console.log(`Adjusted timeout to ${newTimeout}ms based on avg switch time of ${avgTime}ms`)
        }

        lastSwitchTimeRef.current = switchTime

        // The loading state will persist until the page refreshes completely
        // We don't need to manually set it to false as the component will remount
      } catch (error) {
        console.error("Error changing language:", error)
        setIsLoading(false)
      }
    },
    [router, currentLocale]
  )

  return (
    <>
      {isLoading &&
        typeof document !== "undefined" &&
        createPortal(
          <div
            className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/30 backdrop-blur-sm dark:bg-black/50"
            style={{ width: "100vw", height: "100vh", position: "fixed", top: 0, left: 0 }}
          >
            <div className="rounded-lg bg-white/10 p-6 backdrop-blur-md dark:bg-black/20">
              <div className="flex space-x-3">
                <div className="h-4 w-4 animate-bounce rounded-full bg-white [animation-delay:-0.3s] dark:bg-gray-200"></div>
                <div className="h-4 w-4 animate-bounce rounded-full bg-white [animation-delay:-0.15s] dark:bg-gray-200"></div>
                <div className="h-4 w-4 animate-bounce rounded-full bg-white dark:bg-gray-200"></div>
              </div>
            </div>
          </div>,
          document.body
        )}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className={cn("text-gray-600 dark:text-white", className)}>
            <Globe className="h-6 w-6" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => onLanguageSelect(Language.ET)} className="gap-2">
            <span>🇪🇪</span> Eesti
            {currentLocale === Language.ET && <span className="ml-auto h-2 w-2 rounded-full bg-primary" />}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onLanguageSelect(Language.EN)} className="gap-2">
            <span>🇬🇧</span> English
            {currentLocale === Language.EN && <span className="ml-auto h-2 w-2 rounded-full bg-primary" />}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
