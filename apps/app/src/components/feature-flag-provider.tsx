"use client"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { enableCopilot, roles } from "@impactly/flags"

import { createFeatureFlagsProvider } from "@kreios/flags/feature-flag-provider-factory"

// A Scoped Feature Flag Provider for the Impactly Solutioning Platform
// Using the useFeatureFlags hook, you can access the feature flags for the Impactly Solutioning Platform inside a client component
export const [FeatureFlagsProvider, useFeatureFlags] = createFeatureFlagsProvider<{
  enableCopilot: typeof enableCopilot
  roles: typeof roles
}>()
