/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { ComponentProps, FC, ReactNode } from "react"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"

interface LogoutButtonProps {
  onLogout: () => Promise<void>
  buttonText: string
}

export const RestrictedLayoutContainer: FC<ComponentProps<"div">> = ({ children, className, ...props }) => (
  <div className={cn("relative flex h-full items-center justify-center bg-background", className)} {...props}>
    {children}
  </div>
)

export const RestrictedLayoutImageContainer: FC<
  ComponentProps<"div"> & {
    branding: ReactNode
  }
> = ({ branding, children, className, ...props }) => (
  <div
    className={cn(
      "relative hidden h-full items-center justify-center bg-muted/10 text-primary-foreground lg:flex",
      className
    )}
    {...props}
  >
    {branding}
    {children}
  </div>
)

export function LogoutButton({ onLogout, buttonText }: LogoutButtonProps) {
  return (
    <Button
      onClick={() => onLogout()}
      className="w-full bg-primary-600 p-2 hover:bg-primary-700 focus:ring-primary-500"
    >
      {buttonText}
    </Button>
  )
}
