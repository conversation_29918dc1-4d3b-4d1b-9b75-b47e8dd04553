{"extends": "@kreios/tsconfig/base.json", "compilerOptions": {"lib": ["es2022", "dom", "dom.iterable"], "jsx": "preserve", "baseUrl": "src", "paths": {"@/*": ["*"], "@/lib/*": ["lib/*"], "@/components/*": ["components/*"], "@/app/*": ["app/*"], "@/app/(general)/components": ["app/(general)/components.tsx"]}, "plugins": [{"name": "next"}], "tsBuildInfoFile": ".tsbuildinfo", "module": "esnext", "moduleResolution": "bundler"}, "include": ["src/**/*", "next.config.mjs", "next-env.d.ts", ".next/types/**/*.ts", "src/app/.well-known/**/*.ts"], "exclude": ["node_modules", ".next"]}