{"name": "@impactly/app", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "scripts": {"build": "next build $([ \"$NEXT_OUTPUT\" = \"standalone\" ] && echo \"--experimental-build-mode compile\")", "clean": "git clean -xdf .next .turbo node_modules", "codegen": "json-schema-to-zod -i src/survey-templates/universal_v1.schema.json -o src/schema/generated/universal-questionnaire-en.ts && json-schema-to-zod -i src/survey-templates/universal_v1.schema.json -o src/schema/generated/universal-questionnaire-et.ts", "self-codegen": "json-schema-to-zod -i src/survey-templates/self_assessment.json -o src/schema/self-assesment-generated/self-assesment-questionnaire-en.ts && json-schema-to-zod -i src/survey-templates/self_assessment.json -o src/schema/self-assesment-generated/self-assesment-questionnaire-et.ts", "postcodegen": "node ../../scripts/add-error-messages.js universal", "postself-codegen": "node ../../scripts/add-error-messages.js selfAssessment", "dev": "pnpm with-env next dev --turbo", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "validate-schemas": "node ../../scripts/validate-schemas.js", "validate-translations": "node ../../scripts/check-translations.js", "with-env": "dotenvx run -q -f ../../.env.local -f ../../.env --overload --"}, "prettier": "@kreios/prettier-config", "dependencies": {"@dotenvx/dotenvx": "1.14.0", "@impactly/api": "workspace:*", "@impactly/domain": "workspace:*", "@impactly/email": "workspace:*", "@impactly/flags": "workspace:*", "@impactly/graphql": "workspace:*", "@impactly/integration": "workspace:*", "@impactly/jobs": "workspace:*", "@kreios/admin-layout": "workspace:*", "@kreios/api": "workspace:*", "@kreios/auth": "workspace:*", "@kreios/command-menu": "workspace:*", "@kreios/copilot": "workspace:*", "@kreios/datatable": "workspace:*", "@kreios/elasticsearch": "workspace:*", "@kreios/eventsourcing": "workspace:*", "@kreios/flags": "workspace:*", "@kreios/mail-sender": "workspace:*", "@kreios/storage": "workspace:*", "@kreios/ui": "workspace:*", "@kreios/utils": "workspace:*", "@radix-ui/react-use-controllable-state": "1.1.0", "@sentry/nextjs": "8.49.0", "@t3-oss/env-nextjs": "0.11.1", "@tanstack/react-query": "5.55.4", "@tanstack/react-query-devtools": "5.55.4", "@tanstack/react-query-next-experimental": "5.55.4", "@tanstack/react-table": "8.20.5", "@trpc/client": "11.0.0-rc.401", "@trpc/react-query": "11.0.0-rc.401", "@trpc/server": "11.0.0-rc.401", "@vercel/flags": "2.6.1", "@vercel/toolbar": "0.1.18", "awilix": "12.0.3", "content-disposition": "0.5.4", "countries-list": "3.1.1", "cross-env": "7.0.3", "file-saver": "2.0.5", "framer-motion": "11.9.0", "glob": "11.0.0", "graphql-yoga": "5.7.0", "jiti": "2.0.0-rc.1", "js-cookie": "3.0.5", "json-schema-to-zod": "2.5.0", "jszip": "3.10.1", "lodash-es": "4.17.21", "lucide-react": "0.457.0", "next": "14.2.13", "next-intl": "4.0.2", "nextjs-toploader": "3.7.15", "nuqs": "2.3.0", "parquet-wasm": "0.6.1", "posthog-js": "1.219.6", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "7.53.0", "recharts": "2.12.7", "spin-delay": "2.0.1", "ssr-only-secrets": "0.1.2", "superjson": "2.2.1", "unidecode": "1.1.0", "use-debounce": "10.0.3", "use-deep-compare": "1.3.0", "xlsx": "0.18.5", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/content-disposition": "0.5.8", "@types/file-saver": "2.0.7", "@types/js-cookie": "3.0.6", "@types/node": "20.14.10", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "@types/unidecode": "0.1.3", "autoprefixer": "10.4.20", "eslint": "9.10.0", "postcss": "8.4.45", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}}