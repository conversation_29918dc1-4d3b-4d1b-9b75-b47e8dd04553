# Impactly Client Portal

This repository contains Impactly Client Portal, built on top of the Kreios Liquiddata Core platform.

## Repository Structure

This repository uses a hybrid structure:

- Project-specific Impactly Client Portal code and configuration in the root directory
- Platform code mounted at `/platform` using git subtree (see [Platform Documentation](platform/README.md))

## License

This repository contains two distinct codebases with different licenses:

- **Impactly Client Portal Codebase**: All code outside the `/platform` directory is proprietary software owned by IMPACTLY OÜ. and is not licensed for public use, distribution, or modification. See [LICENSE](LICENSE) for details.
- **Platform Codebase**: Code within the `/platform` directory is part of the open-source Kreios Liquiddata Core and is licensed under the MIT License. See [platform/LICENSE](platform/LICENSE) for details.

## Setup

### 1. Configure Environment Variables

```bash
# Install vercel CLI and authenticate
pnpm i -g vercel
vercel login
vercel link

# Pull environment variables
vercel env pull

# Optionally generate a custom auth secret
echo NEXTAUTH_SECRET="$(openssl rand -base64 32)" >> .env
```

### 2. Install Dependencies

```bash
pnpm install

# Rebuild SQLite bindings for the eventsourcing module
cd platform/core/eventsourcing
pnpm rebuild better-sqlite3
cd ../../..
```

This rebuild step is necessary because SQLite bindings are platform-specific native code that needs to be compiled for your specific environment.

### 3. Start Development Infrastructure

```bash
docker compose up
```

Development tools available:

- Database UI: pgAdmin
- Elasticsearch UI: [Kibana](http://localhost:5601)

Additional commands:

- `pnpm db:push` - Push database schema
- `pnpm es:reindex` - Reindex Elasticsearch documents

### 4. Start Development Server

```bash
pnpm dev
# Or with turbo: pnpm dev --turbo
```

## Platform Management

Impactly Client Portal uses the Kreios Liquiddata Core platform, mounted at `/platform`. To manage the platform integration:

```bash
# Check current platform version
pnpm platform status

# Update platform
pnpm platform upgrade <ref>

# Push local platform changes upstream
pnpm platform push <branch>
```

See the [Platform README](platform/README.md) for detailed documentation of the core platform features and components.

## Docker Build

```bash
APP_PATH=<path to the app you would like to build>
TURBO_SCOPE=<npm name of the app package.json>
docker build -f $APP_PATH/Dockerfile \
  --build-arg TURBO_SCOPE=$TURBO_SCOPE \
  --build-arg APP_PATH=$APP_PATH \
  . -t <image-tag>

docker run --env-file .env -p 3000:3000 <image-tag>
```
