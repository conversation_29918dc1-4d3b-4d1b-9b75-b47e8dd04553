{"$schema": "https://unpkg.com/knip@5/schema.json", "ignoreDependencies": ["vitest", "tsx", "prettier", "sharp", "@vitest/coverage-v8"], "ignore": ["**/eslint.config.js", "platform/core/utils/src/types/drop-first.ts", "platform/core/command-menu/src/commands/routes-commands.tsx"], "workspaces": {".": {"entry": ["turbo/generators/config.ts"]}, "apps/app": {"entry": ["src/env.ts"]}, "packages/*": {"entry": ["**/cli.ts"]}}}