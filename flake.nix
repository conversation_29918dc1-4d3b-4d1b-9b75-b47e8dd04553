{
  description = "A <PERSON> Flake to capture the requirements of the Liquiddata development environment";

  inputs = {
    nixpkgs.url = "github:nixos/nixpkgs?ref=nixos-unstable";
  };

  outputs = { self, nixpkgs }: let
    # Define supported systems
    systems = [ "x86_64-linux" "aarch64-darwin" "x86_64-darwin" ];

    # Helper function to create system-specific outputs
    forAllSystems = nixpkgs.lib.genAttrs systems;
  in {
    # This is how you define what the user will get when they run `nix develop`
    devShells = forAllSystems (system: {
      default = let
        pkgs = import nixpkgs { 
          inherit system; 
          config.allowUnfree = true;
        };
      in pkgs.mkShell {
        packages = with pkgs; [
          # Core stuff you should have installed, anyway
          bash
          git

          # Container management
          docker
          docker-compose 
          kubectl
          kubernetes-helm

          # We can use rclone to copy files between different cloud providers and this host
          rclone

          # We need to look at PostgreSQL databases from time to time
          postgresql
          pgadmin4-desktopmode

          # Node.js and Node.js package manager
          nodejs_20
          pnpm_9

          # Some CLI tools used in our e.g. package.json scripts
          turbo
          azure-cli
          terraform
          nodePackages.vercel
        ];
      };
    });
  };
}
