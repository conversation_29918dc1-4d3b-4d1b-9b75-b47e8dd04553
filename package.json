{"name": "@kreios/monorepo", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo run clean", "dev": "turbo run dev", "docker:build": "pnpm --filter @kreios/docker docker:build", "format": "turbo run format --continue -- --cache --cache-location node_modules/.cache/.prettiercache", "format:fix": "turbo run format --continue -- --write --cache --cache-location node_modules/.cache/.prettiercache", "format:fix:no-cache": "turbo run format --no-cache --continue -- --write", "generate": "turbo generate --config platform/tools/turbo/generators/config.ts", "generate-secret": "pnpm --filter=@kreios/scripts generate-secret", "postinstall": "[ \"$SKIP_LINT\" != \"true\" ] && pnpm sortpackages || true && [ \"$SKIP_LINT\" != \"true\" ] && pnpm lint:ws || true", "knip": "SKIP_ENV_VALIDATION=1 knip", "lint": "turbo run lint --continue -- --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix:no-cache": "turbo run lint --no-cache --continue -- --fix", "lint:ws": "pnpm dlx sherif@1.2.0 -r non-existant-packages && pnpm syncpack lint", "platform": "pnpm --filter=@kreios/cli run platform", "prepare": "[ \"$SKIP_LINT\" != \"true\" ] && husky || true", "sortpackages": "sort-package-json \"package.json\" \"platform/*/*/package.json\" \"apps/*/package.json\" \"packages/*/package.json\"", "test": "turbo run test --continue", "test:watch": "turbo run test:watch", "typecheck": "turbo run typecheck", "ui-add": "pnpm --filter=ui ui-add"}, "prettier": "@kreios/prettier-config", "devDependencies": {"@kreios/prettier-config": "workspace:*", "@turbo/gen": "2.1.1", "@types/lodash-es": "4.17.12", "@types/node": "20.14.10", "@vitest/coverage-v8": "2.1.1", "glob": "11.0.0", "husky": "9.0.11", "inquirer-search-checkbox": "1.0.0", "knip": "5.30.2", "lodash-es": "4.17.21", "prettier": "3.4.2", "sort-package-json": "2.10.1", "syncpack": "13.0.0", "tsx": "4.19.0", "turbo": "2.1.1", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9", "zod": "3.23.8"}, "packageManager": "pnpm@9.4.0", "engines": {"node": ">=20.12.0"}, "pnpm": {"overrides": {"react": "18.3.1", "react-dom": "18.3.1"}, "patchedDependencies": {"@types/inquirer@6.5.0": "platform/patches/@<EMAIL>", "drizzle-orm@0.39.2": "platform/patches/<EMAIL>", "parquet-wasm@0.6.1": "platform/patches/<EMAIL>", "sonner@1.5.0": "platform/patches/<EMAIL>", "vaul@0.9.2": "platform/patches/<EMAIL>"}}, "commitConfig": {"authorEmailRegex": ".*@(kreios\\.lu|users\\.noreply\\.github\\.com)$"}}