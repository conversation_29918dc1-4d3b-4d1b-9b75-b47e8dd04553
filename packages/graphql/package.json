{"name": "@impactly/graphql", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "generate:schema": "pnpm with-env generate-schema ./src/schema/index.ts", "lint": "eslint", "test": "vitest run --passWithNoTests", "test:watch": "vitest --passWithNoTests", "tsx": "pnpm with-env tsx --conditions=react-server", "tsx:debug": "pnpm with-env tsx --conditions=react-server --inspect-brk", "typecheck": "tsc --noEmit", "with-env": "dotenvx run -f ../../.env.local -f ../../.env --overload --"}, "prettier": "@kreios/prettier-config", "dependencies": {"@kreios/graphql": "workspace:*", "@pothos/core": "4.1.0", "@pothos/plugin-dataloader": "4.1.0", "@pothos/plugin-relay": "4.2.0", "@pothos/plugin-zod": "4.1.0", "@t3-oss/env-core": "0.11.1", "graphql": "16.9.0", "graphql-scalars": "1.23.0", "zod": "3.23.8"}, "devDependencies": {"@dotenvx/dotenvx": "1.14.0", "@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "eslint": "9.10.0", "prettier": "3.4.2", "tsx": "4.19.0", "typescript": "5.6.2", "vitest": "2.1.9"}}