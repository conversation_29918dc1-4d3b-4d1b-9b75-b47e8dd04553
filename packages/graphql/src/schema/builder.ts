/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import SchemaBuilder from "@pothos/core"
import DataloaderPlugin from "@pothos/plugin-dataloader"
import RelayPlugin from "@pothos/plugin-relay"
import ZodPlugin from "@pothos/plugin-zod"
import { DateTimeResolver } from "graphql-scalars"

import ZodTransformPlugin from "@kreios/graphql/plugins/zodTransformPlugin/index"

export const builder = new SchemaBuilder<{
  DefaultFieldNullability: false
  DefaultInputFieldRequiredness: true
  Scalars: {
    Date: {
      Input: Date
      Output: Date
    }
  }
}>({
  plugins: [ZodTransformPlugin, RelayPlugin, ZodPlugin, DataloaderPlugin],
  defaultFieldNullability: false,
  defaultInputFieldRequiredness: true,
  zod: {
    validationError: (zodError) => zodError,
  },
})

builder.addScalarType("Date", DateTimeResolver, {})
