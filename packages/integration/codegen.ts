/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { CodegenConfig } from "@graphql-codegen/cli"

import { env } from "./env"

const config: CodegenConfig = {
  overwrite: true,
  schema: {
    [env.BACKEND_API_URL]: {
      headers: {
        Authorization: `Bearer ${env.BACKEND_API_TOKEN}`,
      },
    },
  },
  hooks: { afterAllFileWrite: ["pnpm format --write"] },
  documents: ["src/backend/**/*.ts"],
  ignoreNoDocuments: true,
  generates: {
    "./src/backend/gql/schema.graphql": {
      plugins: ["schema-ast"],
    },
    "./src/backend/gql/": {
      preset: "client",
      presetConfig: {
        fragmentMasking: false,
      },
      config: {
        scalars: {
          DateTime: "string",
          Date: "string",
        },
      },
    },
  },
}

export default config
