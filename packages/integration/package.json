{"name": "@impactly/integration", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts", "./backend": "./src/backend/index.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "codegen": "pnpm with-env graphql-codegen", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "graphql-codegen": "pnpm with-env graphql-codegen", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit", "with-env": "dotenvx run -f ../../.env.local -f ../../.env --overload --"}, "prettier": "@kreios/prettier-config", "dependencies": {"@kreios/utils": "workspace:*", "@t3-oss/env-core": "0.11.1", "graphql": "16.9.0", "graphql-request": "6.1.0", "superjson": "2.2.1", "zod": "3.23.8"}, "devDependencies": {"@dotenvx/dotenvx": "1.14.0", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.1.0", "@graphql-codegen/schema-ast": "4.1.0", "@graphql-typed-document-node/core": "3.2.0", "@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@parcel/watcher": "2.4.1", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}