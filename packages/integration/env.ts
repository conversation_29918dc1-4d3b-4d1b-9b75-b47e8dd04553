/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { createEnv } from "@t3-oss/env-core"
import { vercel } from "@t3-oss/env-core/presets"
import { z } from "zod"

export const env = createEnv({
  extends: [vercel()],
  server: {
    BACKEND_API_URL: z.string().url(),
    BACKEND_API_TOKEN: z.string(),

    // API Performance Configuration
    CDC_API_PAGE_SIZE: z
      .string()
      .transform((val) => parseInt(val, 10))
      .pipe(z.number().int().positive())
      .default("400")
      .describe("Page size for external API requests. Higher values = fewer API calls but more memory per request"),

    // API Filtering Configuration
    CDC_COUNTRY_FILTER: z
      .string()
      .optional()
      .describe(
        "Filter companies by country code (e.g., 'EE' for Estonia, 'LT' for Lithuania). Leave empty for all countries."
      ),
  },
  runtimeEnv: process.env,
  skipValidation:
    !process.env.VERCEL &&
    (!!process.env.CI ||
      !!process.env.SKIP_ENV_VALIDATION ||
      process.env.npm_lifecycle_event === "lint" ||
      process.env.NODE_ENV === "test"),
  emptyStringAsUndefined: true,
})
