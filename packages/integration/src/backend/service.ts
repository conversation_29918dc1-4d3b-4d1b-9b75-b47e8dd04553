/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { TypedDocumentNode } from "@graphql-typed-document-node/core"
import type { OperationDefinitionNode } from "graphql"
import type { Variables } from "graphql-request"
import { Kind } from "graphql"
import { GraphQLClient } from "graphql-request"
import { z } from "zod"

import { agnosticSuperjsonParse } from "@kreios/utils/agnostic-superjson-parse"
import { decodeRelayID } from "@kreios/utils/decode-relay-id"
import { isBase64 } from "@kreios/utils/is-base64"
import { isTruthy } from "@kreios/utils/isTruthy"

import type {
  CompanyItemFragment,
  EvaluationItemFragment,
  IorderCompanyCountryChoices,
  NaceCodeItemFragment,
  PortalUserItemFragment,
  PortalUserRoleEnum,
  SurveyBatchItemFragment,
  SurveyContactRoleEnum,
  SurveyItemFragment,
  SurveyStatusEnum,
  SurveyTemplateEnum,
  TenantItemFragment,
  WatchlistItemFragment,
} from "./gql/graphql"
import { env } from "../../env"
import * as mutations from "./requests/mutations"
import * as queries from "./requests/queries"

/**
 * Connection is a generic type that represents a paginated list of items.
 * It is used to represent the result of a paginated GraphQL query.
 */
type Connection<T> = {
  pageInfo: {
    hasNextPage: boolean
    hasPreviousPage: boolean
    startCursor?: string | null
    endCursor?: string | null
  }
  edges: Array<{
    node?: T | null
  } | null>
}

/**
 * Schema for validating the file upload response from the REST API
 */
const FileUploadResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  file_path: z.string(),
  file_url: z.string().url(),
})

type FileUploadResponse = z.infer<typeof FileUploadResponseSchema>

const SubCategorySchema = z.object({
  transparency_score_status: z.string(),
  management_score_status: z.string(),
  industry_best_practices: z.string(),
  industry_best_practices_et: z.string(),
  summary_en: z.string(),
  summary_et: z.string(),
  label_en: z.string(),
  label_et: z.string(),
  recommendations: z.string(),
  recommendations_et: z.string(),
})

const CategorySchema = z.object({
  transparency_score_status: z.string(),
  management_score_status: z.string(),
  sub_categories: z.record(SubCategorySchema),
  label_en: z.string(),
  label_et: z.string(),
})

const source = z.enum(["user", "ai", "third_party"])

export const CompanyDetailScoringResponseSchema = z.object({ source_type: source, data: z.record(CategorySchema) })

type CompanyDetailScoringResponse = z.infer<typeof CompanyDetailScoringResponseSchema>

/**
 * ImpactlyService provides methods to interact with the Impactly GraphQL API.
 * It handles both queries and mutations, including pagination for list queries.
 * This service encapsulates all API operations, making it easier to manage
 * and maintain the client-side integration with the Impactly backend.
 */
export class BackendIntegrationService {
  private client: GraphQLClient
  private baseUrl: string
  private apiToken: string

  /**
   * Creates a new instance of the ImpactlyService.
   * @param options The options object containing the API URL and token.
   */
  constructor(options: { apiUrl: string; apiToken: string }) {
    this.baseUrl = options.apiUrl
    this.apiToken = options.apiToken
    this.client = new GraphQLClient(options.apiUrl, {
      headers: {
        Authorization: `Bearer ${options.apiToken}`,
      },
    })
  }
  /**
   * Transforms a survey batch by decoding its base64 relay ID if needed.
   * @param surveyBatch The survey batch fragment to transform
   * @returns The transformed survey batch with decoded ID if it was base64 encoded
   */
  private transformSurveyBatch(surveyBatch: SurveyBatchItemFragment): SurveyBatchItemFragment {
    const changes: Partial<SurveyBatchItemFragment> = {}

    if (isBase64(surveyBatch.id)) {
      const decodedId = decodeRelayID(surveyBatch.id)
      if (decodedId) changes.id = decodedId
    }

    return {
      ...surveyBatch,
      ...changes,
    }
  }

  /**
   * Transforms a survey by decoding its base64 relay ID if needed.
   * @param survey The survey fragment to transform
   * @returns The transformed survey with decoded ID if it was base64 encoded
   */
  private transformSurvey(survey: SurveyItemFragment): SurveyItemFragment {
    const changes: Partial<SurveyItemFragment> = {
      // parse the json documents so they are saved not as strings inside the cdc event
      // this has the advantage that the delta can be correctly calculated
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument
      formData: survey.formData ? agnosticSuperjsonParse<any>(survey.formData) : survey.formData,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-argument
      prefilledData: survey.prefilledData ? agnosticSuperjsonParse<any>(survey.prefilledData) : survey.prefilledData,
    }

    if (isBase64(survey.id)) {
      const decodedId = decodeRelayID(survey.id)
      if (decodedId) changes.id = decodedId
    }

    return {
      ...survey,
      ...changes,
    }
  }

  /**
   * Executes a GraphQL request with the given document and variables.
   * @param document The GraphQL document (query or mutation) to execute.
   * @param variables The variables to be passed to the GraphQL operation.
   * @returns A promise that resolves to the result of the GraphQL operation.
   */
  private async request<TResult, TVariables extends Variables | undefined>(
    document: TypedDocumentNode<TResult, TVariables>,
    variables: TVariables
  ): Promise<TResult> {
    const { operationName, operationType } = this.lookupOperationMetadata(document)
    console.log(`Executing GraphQL ${operationType}: ${operationName}`, variables)

    // Get the variable names from the document
    const variableDefinitions = document.definitions
      .filter((def): def is OperationDefinitionNode => def.kind === Kind.OPERATION_DEFINITION)
      .flatMap((def) => def.variableDefinitions ?? [])
      .map((varDef) => varDef.variable.name.value)

    // Check if all provided variables are expected
    const unexpectedVariables = Object.keys(variables ?? {}).filter((key) => !variableDefinitions.includes(key))
    if (unexpectedVariables.length > 0) {
      throw new Error(`Unexpected variables provided: ${unexpectedVariables.join(", ")}`)
    }

    return this.client.request(document, variables)
  }

  /**
   * Executes a paginated GraphQL request, yielding results one at a time.
   * @param document The GraphQL document (query) to execute.
   * @param getConnection A function to extract the connection from the query result.
   * @param variables The variables to be passed to the GraphQL operation (excluding pagination variables).
   * @param pageSize The number of items to fetch per page (default: 100).
   * @yields Individual nodes from the paginated results.
   */
  private async *paginatedRequest<TResult, TVariables extends Variables | undefined, TNode>(
    document: TypedDocumentNode<TResult, TVariables>,
    getConnection: (result: TResult) => Connection<TNode> | null | undefined,
    variables: Omit<TVariables, "first" | "after">,
    pageSize?: number
  ): AsyncGenerator<TNode, void, unknown> {
    const { operationName } = this.lookupOperationMetadata(document)
    const effectivePageSize = pageSize ?? env.CDC_API_PAGE_SIZE
    let hasNextPage = true
    let after: string | null = null
    let pageCount = 0
    const seenCursors = new Set<string>()

    while (hasNextPage) {
      pageCount++
      console.log(
        `🌐   [API] Executing paginated GraphQL request: ${operationName} (Page ${pageCount}, First: ${effectivePageSize}, After: ${after ?? "null"})`
      )

      const requestStartTime = Date.now()
      // Fetch the next page of results
      const result = await this.request(document, {
        ...variables,
        first: effectivePageSize,
        after,
      } as unknown as TVariables)
      const requestDuration = Date.now() - requestStartTime

      console.log(
        `✅   [API] GraphQL request completed: ${operationName} (Page ${pageCount}) in ${this.formatDuration(requestDuration)} (${requestDuration}ms)`
      )

      // Extract the connection from the result
      const connection = getConnection(result)
      if (!connection) {
        console.error(`⚠️    [API] No connection found for ${operationName}. Ending pagination.`)
        break
      }

      // Yield individual nodes and update pagination state
      const validEdges = connection.edges.filter((e): e is NonNullable<typeof e> => e !== null && e.node !== null)
      console.log(
        `📦   [API] Page ${pageCount} received ${connection.edges.length} edges, ${validEdges.length} valid for ${operationName}`
      )

      for (const edge of validEdges) {
        yield edge.node!
      }

      // Update pagination state
      hasNextPage = connection.pageInfo.hasNextPage
      after = connection.pageInfo.endCursor ?? null

      console.log(
        `📄   [API] Page ${pageCount} pagination: hasNextPage=${hasNextPage}, cursor=${after?.slice(0, 10)}...`
      )

      // Some sanity checks; these should never trigger in production
      if (!hasNextPage) {
        console.log(`📭   [API] No more pages for ${operationName}. Ending pagination.`)
      }
      if (validEdges.length === 0) {
        console.error(`⚠️    [API] No valid edges found for ${operationName}. Ending pagination.`)
        break
      }
      if (after) {
        if (seenCursors.has(after)) {
          console.error(`⚠️    [API] Duplicate cursor detected: ${after}. Ending pagination to avoid infinite loop.`)
          break
        }
        seenCursors.add(after)
      }
    }

    console.log(`🎉   [API] Pagination completed for ${operationName}: ${pageCount} pages processed`)
  }

  /**
   * Encodes the ID with a model prefix and converts it to base64.
   *
   * This is necessary because the Impactly backend, built with Django Graphene,
   * uses a specific format for global object identification. While it returns
   * plain numbers in its responses, it expects base64-encoded strings with
   * model prefixes when receiving requests.
   *
   * @see https://docs.graphene-python.org/projects/django/en/latest/tutorial-relay/#relay-and-nodes
   *
   * @param model The model name (e.g., 'company', 'evaluation', 'portaluser')
   * @param id The numeric or string ID of the object
   * @returns A base64 encoded string in the format 'model:id'
   */
  private encodeId(model: string, id: string): string {
    return Buffer.from(`${model}:${id}`).toString("base64")
  }

  /**
   * Formats milliseconds into a human-readable duration string.
   * @param ms The duration in milliseconds.
   * @returns A formatted duration string (e.g., "1.2s", "45.6s", "2m 15s").
   */
  private formatDuration(ms: number): string {
    if (ms < 1000) {
      return `${ms}ms`
    } else if (ms < 60000) {
      return `${(ms / 1000).toFixed(1)}s`
    } else {
      const minutes = Math.floor(ms / 60000)
      const seconds = Math.floor((ms % 60000) / 1000)
      return `${minutes}m ${seconds}s`
    }
  }

  /**
   * Extracts the operation name and type from a GraphQL document.
   * @param document The GraphQL document (query or mutation).
   * @returns An object containing the operation name and type.
   */
  private lookupOperationMetadata<T, V>(
    document: TypedDocumentNode<T, V>
  ): {
    operationName: string
    operationType: string
  } {
    const definition = document.definitions[0]
    if (definition.kind === Kind.OPERATION_DEFINITION) {
      return {
        operationName: definition.name?.value ?? "Unknown",
        operationType: definition.operation,
      }
    }
    return {
      operationName: "Unknown",
      operationType: "Unknown",
    }
  }

  // Queries

  /**
   * Retrieves a company by its ID.
   * @param variables An object containing the company ID.
   * @returns A promise that resolves to the CompanyItemFragment.
   * @throws Error if the company is not found.
   */
  async getCompany(variables: { id: string }): Promise<CompanyItemFragment> {
    const encodedId = this.encodeId("CompanyNode", variables.id)
    const result = await this.request(queries.CompanyQuery, { id: encodedId })
    if (!result.company) throw new Error(`Company with ID ${variables.id} not found`)
    return result.company
  }

  /**
   * Retrieves a paginated list of companies.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual CompanyItemFragment objects.
   */
  async *getCompanies(): AsyncGenerator<CompanyItemFragment, void, unknown> {
    const countryFilter = env.CDC_COUNTRY_FILTER

    if (!countryFilter) {
      // No filtering - yield all companies
      console.log(`🌍   [API] Fetching all companies (no country filter)`)
      yield* this.paginatedRequest(queries.CompaniesQuery, (result) => result.companies, {})
      return
    }

    // Country filtering enabled - use server-side filtering
    console.log(`🌍   [API] Server-side country filter enabled: ${countryFilter}`)
    yield* this.paginatedRequest(queries.CompaniesQuery, (result) => result.companies, {
      country: countryFilter as IorderCompanyCountryChoices,
    })
    console.log(`✅   [API] Server-side filtering completed for ${countryFilter}`)
  }

  /**
   * Retrieves an evaluation by its ID.
   * @param variables An object containing the evaluation ID.
   * @returns A promise that resolves to the EvaluationItemFragment.
   * @throws Error if the evaluation is not found.
   */
  async getEvaluation(variables: { id: string }): Promise<EvaluationItemFragment> {
    const encodedId = this.encodeId("EvaluationNode", variables.id)
    const result = await this.request(queries.EvaluationQuery, { id: encodedId })
    if (!result.evaluation) throw new Error(`Evaluation with ID ${variables.id} not found`)
    return result.evaluation
  }

  /**
   * Retrieves a paginated list of evaluations.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual EvaluationItemFragment objects.
   */
  async *getEvaluations(): AsyncGenerator<EvaluationItemFragment, void, unknown> {
    yield* this.paginatedRequest(queries.EvaluationsQuery, (result) => result.evaluations, {})
  }

  /**
   * Retrieves a NACE code by its ID.
   * @param variables An object containing the NACE code ID.
   * @returns A promise that resolves to the NaceCodeItemFragment.
   * @throws Error if the NACE code is not found.
   */
  async getNaceCode(variables: { id: string }): Promise<NaceCodeItemFragment> {
    const encodedId = this.encodeId("NaceCodeNode", variables.id)
    const result = await this.request(queries.NaceCodeQuery, { id: encodedId })
    if (!result.naceCode) throw new Error(`NACE code with ID ${variables.id} not found`)
    return result.naceCode
  }

  /**
   * Retrieves a paginated list of NACE codes.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual NaceCodeItemFragment objects.
   */
  async *getNaceCodes(): AsyncGenerator<NaceCodeItemFragment, void, unknown> {
    yield* this.paginatedRequest(queries.NaceCodesQuery, (result) => result.naceCodes, {})
  }

  /**
   * Retrieves a portal user by their ID.
   * @param variables An object containing the portal user ID.
   * @returns A promise that resolves to the PortalUserItemFragment.
   * @throws Error if the portal user is not found.
   */
  async getPortalUser(variables: { id: string }): Promise<PortalUserItemFragment> {
    const encodedId = this.encodeId("PortalUserNode", variables.id)
    const result = await this.request(queries.PortalUserQuery, { id: encodedId })
    if (!result.portalUser) throw new Error(`Portal user with ID ${variables.id} not found`)
    return result.portalUser
  }

  /**
   * Retrieves a paginated list of portal users.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual PortalUserItemFragment objects.
   */
  async *getPortalUsers(): AsyncGenerator<PortalUserItemFragment, void, unknown> {
    yield* this.paginatedRequest(queries.PortalUsersQuery, (result) => result.portalUsers, {})
  }

  /**
   * Retrieves a survey by its ID.
   * @param variables An object containing the survey ID.
   * @returns A promise that resolves to the SurveyItemFragment.
   * @throws Error if the survey is not found.
   */
  async getSurvey(variables: { id: string }): Promise<SurveyItemFragment> {
    const encodedId = this.encodeId("SurveyNode", variables.id)
    const result = await this.request(queries.SurveyQuery, { id: encodedId })
    if (!result.survey) throw new Error(`Survey with ID ${variables.id} not found`)
    return this.transformSurvey(result.survey)
  }

  /**
   * Retrieves a paginated list of surveys.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual SurveyItemFragment objects.
   */
  async *getSurveys(): AsyncGenerator<SurveyItemFragment, void, unknown> {
    for await (const survey of this.paginatedRequest(queries.SurveysQuery, (result) => result.surveys, {})) {
      yield this.transformSurvey(survey)
    }
  }

  /**
   * Retrieves a survey batch by its ID.
   * @param variables An object containing the survey batch ID.
   * @returns A promise that resolves to the SurveyBatchItemFragment.
   * @throws Error if the survey batch is not found.
   */
  async getSurveyBatch(variables: { id: string }): Promise<SurveyBatchItemFragment> {
    const encodedId = this.encodeId("SurveyBatchNode", variables.id)
    const result = await this.request(queries.SurveyBatchQuery, { id: encodedId })
    if (!result.surveyBatch) throw new Error(`Survey batch with ID ${variables.id} not found`)
    return this.transformSurveyBatch(result.surveyBatch)
  }

  /**
   * Retrieves a paginated list of survey batches.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual SurveyBatchItemFragment objects.
   */
  async *getSurveyBatches(): AsyncGenerator<SurveyBatchItemFragment, void, unknown> {
    for await (const surveyBatch of this.paginatedRequest(
      queries.SurveyBatchesQuery,
      (result) => result.surveyBatches,
      {}
    )) {
      yield this.transformSurveyBatch(surveyBatch)
    }
  }

  /**
   * Retrieves a tenant by its ID.
   * @param variables An object containing the tenant ID.
   * @returns A promise that resolves to the TenantItemFragment.
   * @throws Error if the tenant is not found.
   */
  async getTenant(variables: { id: string }): Promise<TenantItemFragment> {
    const encodedId = this.encodeId("TenantNode", variables.id)
    const result = await this.request(queries.TenantQuery, { id: encodedId })
    if (!result.tenant) throw new Error(`Tenant with ID ${variables.id} not found`)
    return result.tenant
  }

  /**
   * Retrieves a paginated list of tenants.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual TenantItemFragment objects.
   */
  async *getTenants(): AsyncGenerator<TenantItemFragment, void, unknown> {
    yield* this.paginatedRequest(queries.TenantsQuery, (result) => result.tenants, {})
  }

  /**
   * Retrieves a watchlist by its ID.
   * @param variables An object containing the watchlist ID.
   * @returns A promise that resolves to the WatchlistItemFragment.
   * @throws Error if the watchlist is not found.
   */
  async getWatchlist(variables: { id: string }): Promise<WatchlistItemFragment> {
    const encodedId = this.encodeId("WatchlistNode", variables.id)
    const result = await this.request(queries.WatchlistQuery, { id: encodedId })
    if (!result.watchlist) throw new Error(`Watchlist with ID ${variables.id} not found`)
    return result.watchlist
  }

  /**
   * Retrieves a paginated list of watchlists.
   * @param variables An object containing optional limit and offset for pagination.
   * @yields Individual WatchlistItemFragment objects.
   */
  async *getWatchlists(): AsyncGenerator<WatchlistItemFragment, void, unknown> {
    yield* this.paginatedRequest(queries.WatchlistsQuery, (result) => result.watchlists, {})
  }

  /**
   * Retrieves a file by its ID.
   * @param variables An object containing the file ID.
   * @returns A promise that resolves to the file.
   */
  async getFileUrl(variables: { filePath: string }): Promise<string> {
    const result = await this.request(queries.GetFileUrlQuery, { filePath: variables.filePath })
    if (!result.getFileUrl) throw new Error(`File with path ${variables.filePath} not found`)
    return result.getFileUrl
  }

  // Mutations

  /**
   * Adds a company to a watchlist.
   * @param variables An object containing the necessary information to add a company to a watchlist.
   * @returns A promise that resolves to the mutation result.
   */
  async addEntryToWatchlist(variables: {
    requestedBy: string
    watchlistId: string
    companyId: string
    website?: string
    contacts?: { email: string; name?: string }[]
  }) {
    return this.request(mutations.AddEntryToWatchlistMutation, variables)
  }

  /**
   * Autofills a survey with provided input documents.
   * @param variables An object containing the survey ID and optional input document URLs.
   * @returns A promise that resolves to the mutation result.
   */
  async autofillSurvey(variables: { requestedBy: string; surveyId: string; inputDocumentsUrls?: string[] }) {
    const result = await this.request(mutations.AutofillSurveyMutation, variables)
    if (!result.autofillSurvey?.survey) throw new Error(`Failed to autofill survey`)
    return this.transformSurvey(result.autofillSurvey.survey)
  }

  /**
   * Creates a new portal user.
   * @param variables An object containing the necessary information to create a portal user.
   * @returns A promise that resolves to the mutation result.
   */
  async createPortalUser(variables: {
    requestedBy: string
    email: string
    roles: { role: PortalUserRoleEnum }[]
    firstName?: string
    lastName?: string
    isActive?: boolean
    companyId?: string
  }) {
    return this.request(mutations.CreatePortalUserMutation, variables)
  }

  /**
   * Creates a new survey.
   * @param variables An object containing the necessary information to create a survey.
   * @returns A promise that resolves to the mutation result.
   */
  async createSurvey(variables: {
    requestedBy: string
    template: SurveyTemplateEnum
    batchId: string
    companySurveys: {
      companyId: string
      contactEmails: string[]
      deadline: string
      financialYear: number
    }[]
  }) {
    try {
      await this.request(mutations.CreateSurveyMutation, variables)
      return { success: true, message: "Survey created successfully" }
    } catch (error) {
      console.error("Error in Survey Creation: ", error)
      throw error
    }
  }

  /**
   * Creates a new survey batch.
   * @param variables An object containing the necessary information to create a survey batch.
   * @returns A promise that resolves to the mutation result.
   */
  async createSurveyBatch(variables: {
    name: string
    template: SurveyTemplateEnum
    tenantId: string | null
    description?: string
  }) {
    const result = await this.request(mutations.CreateSurveyBatchMutation, variables)
    if (!result.createSurveyBatch?.surveyBatch) throw new Error(`Failed to create survey batch`)

    return this.transformSurveyBatch(result.createSurveyBatch.surveyBatch)
  }

  /**
   * Creates a new watchlist.
   * @param variables An object containing the necessary information to create a watchlist.
   * @returns A promise that resolves to the mutation result.
   */
  async createWatchlist(variables: { requestedBy: string; portalUserId: string; name: string }) {
    return this.request(mutations.CreateWatchlistMutation, variables)
  }

  /**
   * Deletes a watchlist.
   * @param variables An object containing the watchlist ID to be deleted.
   * @returns A promise that resolves to the mutation result.
   */
  async deleteWatchlist(variables: { requestedBy: string; watchlistId: string }) {
    return this.request(mutations.DeleteWatchlistMutation, variables)
  }

  /**
   * Renames a watchlist.
   * @param variables An object containing the watchlist ID and the new name.
   * @returns A promise that resolves to the mutation result.
   */
  async renameWatchlist(variables: { requestedBy: string; watchlistId: string; name: string }) {
    return this.request(mutations.RenameWatchlistMutation, variables)
  }

  /**
   * Resends a survey invitation.
   * @param variables An object containing the survey ID to resend.
   * @returns A promise that resolves to the mutation result.
   */
  async resendSurvey(variables: { requestedBy: string; surveyId: string }) {
    const result = await this.request(mutations.ResendSurveyMutation, variables)
    if (!result.resendSurvey?.survey) throw new Error(`Failed to resend survey`)
    return this.transformSurvey(result.resendSurvey.survey)
  }

  /**
   * Shares a survey with additional recipients.
   * @param variables An object containing the survey ID and the list of recipients to share with.
   * @returns A promise that resolves to the mutation result.
   */
  async shareSurvey(variables: {
    requestedBy: string
    surveyId: string
    sharedWith: { email: string; name?: string; role?: SurveyContactRoleEnum }[]
  }) {
    const result = await this.request(mutations.ShareSurveyMutation, variables)
    if (!result.shareSurvey?.survey) throw new Error(`Failed to share survey`)
    return this.transformSurvey(result.shareSurvey.survey)
  }

  /**
   * Submits a completed survey.
   * @param variables An object containing the survey ID to submit.
   * @returns A promise that resolves to the mutation result.
   */
  async submitSurvey(variables: { requestedBy: string; surveyId: string; formData?: Record<string, unknown> }) {
    const result = await this.request(mutations.SubmitSurveyMutation, variables)
    if (!result.submitSurvey?.survey) throw new Error(`Failed to submit survey`)
    return this.transformSurvey(result.submitSurvey.survey)
  }

  /**
   * Updates a survey's information.
   * @param variables An object containing the survey ID and the information to update.
   * @returns A promise that resolves to the mutation result.
   */
  async updateSurvey(variables: {
    surveyId: string
    status?: SurveyStatusEnum
    progress?: number
    formData?: Record<string, unknown>
  }) {
    const result = await this.request(mutations.UpdateSurveyMutation, {
      ...variables,
      ...(!!variables.formData && {
        formData: JSON.stringify(variables.formData, (key, value: unknown) => (value === undefined ? null : value)),
      }),
    })
    if (!result.updateSurvey?.survey) throw new Error(`Failed to update survey`)
    return this.transformSurvey(result.updateSurvey.survey)
  }

  /**
   * Updates a survey's status.
   * @param variables An object containing the survey status and the list of survey IDs to update.
   * @returns A promise that resolves to the mutation result.
   */
  async updateSurveyStatus(variables: { status: SurveyStatusEnum; surveyIds: string[] }) {
    const result = await this.request(mutations.UpdateSurveyStatusMutation, variables)

    if (!result.updateSurveyStatus?.surveys) throw new Error(`Failed to create survey`)

    const surveys = result.updateSurveyStatus.surveys.filter(isTruthy).map((survey) => this.transformSurvey(survey))

    // throw an error if at least one survey was not created
    if (surveys.length < result.updateSurveyStatus.surveys.length) throw new Error(`Failed to update survey status`)

    return surveys
  }

  /**
   * Updates a portal user's information.
   * @param variables An object containing the portal user ID and the information to update.
   * @returns A promise that resolves to the mutation result.
   */
  async updatePortalUser(variables: {
    requestedBy: string
    id: string
    isActive?: boolean
    hasGivenConsent: boolean
    firstName?: string
    lastName?: string
    roles?: { role: PortalUserRoleEnum }[]
  }) {
    return this.request(mutations.UpdatePortalUserMutation, variables)
  }

  /**
   * Gets the base URL without the /graphql suffix
   * @returns The clean base URL for REST endpoints
   */
  private getBaseUrlWithoutGraphql(): string {
    return this.baseUrl.replace(/\/graphql$/, "")
  }

  /**
   * Uploads a file using the REST API endpoint.
   * @param variables An object containing the file and filename to upload.
   * @returns A promise that resolves to the file upload response with validated schema.
   */
  async uploadFile(variables: { file: File; filename: string }): Promise<FileUploadResponse> {
    const formData = new FormData()
    formData.append("file", variables.file)
    const response = await fetch(`${this.getBaseUrlWithoutGraphql()}/api/iportal/upload-file/`, {
      method: "POST",
      headers: {
        Authorization: `Token ${this.apiToken}`,
      },
      body: formData,
    })

    if (!response.ok) {
      throw new Error(`File upload failed: ${response.statusText}`)
    }

    const data: unknown = await response.json()
    return FileUploadResponseSchema.parse(data)
  }

  /**
   * Retrieves the scoring details for a company using the REST API endpoint.
   * @param variables An object containing the company ID.
   * @returns A promise that resolves to the scoring details response with validated schema.
   */
  async getCompanyDetailScoring(variables: { companyId: number }): Promise<CompanyDetailScoringResponse> {
    const url = new URL(`${this.getBaseUrlWithoutGraphql()}/api/iportal/company-detail-scoring/`)
    url.searchParams.set("company_id", variables.companyId.toString())

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        Authorization: `Token ${this.apiToken}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to retrieve company detail scoring: ${response.statusText}`)
    }

    const data = (await response.json()) as unknown as CompanyDetailScoringResponse
    return data
  }
}
