/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core"

import * as types from "./graphql"

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
  "\n  fragment EvaluationItem on EvaluationNode {\n    id\n    status\n    completedAt\n    evaluationYear\n    overallRecommendations\n    environmentalMaturity\n    socialMaturity\n    governanceMaturity\n    overallSummary\n    inputs {\n      dataDepth {\n        baseData\n        publicData\n        selfAssessment\n        advanced\n      }\n      evaluationDepth {\n        esgRiskMapping\n        aiEvaluations\n        esgRecommendations\n        enrichedEvaluation\n        fullEvaluation\n      }\n      sources {\n        businessRegistry\n        annualReport\n        website\n        media\n        questionnaire\n        proprietaryData\n      }\n    }\n    company {\n      id\n      name\n      registrationNumber\n    }\n  }\n":
    types.EvaluationItemFragmentDoc,
  "\n  fragment CompanyItem on CompanyNode {\n    id\n    name\n    about\n    aboutEn\n    aboutEt\n    registrationNumber\n    status\n    country\n    employeeNumber\n    street\n    houseNumber\n    postalCode\n    city\n    registrationDate\n    urls {\n      url\n    }\n    latestGhgEmission {\n      id\n      year\n      source\n      basis\n      multiplier\n      unit\n      value\n      scope\n    }\n    mediaSources {\n      id\n      url\n      title\n      summary\n      story\n      publishedAt\n      startedAt\n      completedAt\n      esgCategory\n      sentiment\n      tiPossibleName\n      metaData\n    }\n    latestEsgMaterialityMapping {\n      executionDate\n      relevantPositives {\n        summary\n        maturityLevel\n      }\n      relevantIssues {\n        iissue {\n          id\n          name\n          category\n          issue {\n            description\n          }\n        }\n        maturityLevel\n        gabAnalysisResults\n        summary\n        recommendations\n        sources {\n          sourceType\n          source\n        }\n      }\n    }\n    publishedEvaluations {\n      ...EvaluationItem\n    }\n    businessActivities {\n      id\n      euTaxonomyEligible\n      naceCode {\n        id\n        label\n        labelEt\n        code\n        overallEbrdRiskLevel\n        environmentalEbrdRiskLevel\n        socialEbrdRiskLevel\n      }\n      latestReportItem {\n        isMainActivity\n        businessActivityRevenue {\n          amount\n          currency\n        }\n      }\n    }\n    annualReports {\n      financialYear\n      averageFullTimeEmployeesConsolidated\n      companyRevenueConsolidated {\n        amount\n        currency\n      }\n      netProfitConsolidated {\n        amount\n        currency\n      }\n      totalAssets {\n        amount\n        currency\n      }\n    }\n  }\n":
    types.CompanyItemFragmentDoc,
  "\n  fragment NaceCodeItem on NaceCodeNode {\n    id\n    label\n    code\n    overallEbrdRiskLevel\n    environmentalEbrdRiskLevel\n    socialEbrdRiskLevel\n    parent {\n      id\n      parent {\n        id\n      }\n      label\n      code\n      overallEbrdRiskLevel\n      environmentalEbrdRiskLevel\n      socialEbrdRiskLevel\n    }\n  }\n":
    types.NaceCodeItemFragmentDoc,
  "\n  fragment PortalUserItem on PortalUserNode {\n    id\n    tenant {\n      id\n    }\n    email\n    lastName\n    firstName\n    company {\n      id\n    }\n    hasGivenConsent\n    isActive\n    dateJoined\n    lastLogin\n    createdAt\n    updatedAt\n    roles {\n      role\n    }\n  }\n":
    types.PortalUserItemFragmentDoc,
  "\n  fragment SurveyItem on SurveyNode {\n    id\n    tenant {\n      id\n      name\n    }\n    updatedAt\n    createdAt\n    submittedAt\n    company {\n      id\n      name\n    }\n    batch {\n      id\n    }\n    financialYear\n    deadline\n    template\n    status\n    readyForEdit\n    prefilledData\n    inputDocuments {\n      id\n      file\n      fileName\n      fileSize\n      isAttachment\n    }\n    lastReminder\n    formData\n    statusChangedAt\n    progress\n    langConsent\n  }\n":
    types.SurveyItemFragmentDoc,
  "\n  fragment SurveyBatchItem on SurveyBatchNode {\n    id\n    name\n    description\n    template\n    tenant {\n      id\n    }\n  }\n":
    types.SurveyBatchItemFragmentDoc,
  "\n  fragment TenantItem on TenantNode {\n    id\n    name\n    createdAt\n    updatedAt\n  }\n":
    types.TenantItemFragmentDoc,
  "\n  fragment WatchlistEntryItem on WatchlistEntryNode {\n    id\n    company {\n      ...CompanyItem\n    }\n    contacts {\n      id\n      email\n      name\n    }\n  }\n":
    types.WatchlistEntryItemFragmentDoc,
  "\n  fragment WatchlistItem on WatchlistNode {\n    id\n    tenant {\n      id\n    }\n    portalUser {\n      id\n    }\n    name\n    updatedAt\n    createdAt\n    entries {\n      ...WatchlistEntryItem\n    }\n  }\n":
    types.WatchlistItemFragmentDoc,
  "\n  mutation addEntryToWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $companyId: ID!, $website: String) {\n    addEntryToWatchlist(\n      requestedBy: $requestedBy\n      watchlistId: $watchlistId\n      companyId: $companyId\n      website: $website\n    ) {\n      entry {\n        ...WatchlistEntryItem\n      }\n    }\n  }\n":
    types.AddEntryToWatchlistMutationDocument,
  "\n  mutation autofillSurveyMutation($requestedBy: String!, $surveyId: ID!, $inputDocumentsUrls: [String]) {\n    autofillSurvey(requestedBy: $requestedBy, surveyId: $surveyId, inputDocumentsUrls: $inputDocumentsUrls) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n":
    types.AutofillSurveyMutationDocument,
  "\n  mutation createPortalUserMutation(\n    $requestedBy: String!\n    $email: String!\n    $firstName: String\n    $lastName: String\n    $isActive: Boolean\n    $roles: [RoleAssignmentInput!]!\n    $companyId: ID\n  ) {\n    createPortalUser(\n      requestedBy: $requestedBy\n      email: $email\n      firstName: $firstName\n      lastName: $lastName\n      isActive: $isActive\n      roles: $roles\n      companyId: $companyId\n    ) {\n      portalUser {\n        ...PortalUserItem\n      }\n    }\n  }\n":
    types.CreatePortalUserMutationDocument,
  "\n  mutation CreateSurveyMutation(\n    $requestedBy: String!\n    $template: SurveyTemplateEnum!\n    $batchId: ID!\n    $companySurveys: [CompanySurveysInput!]!\n  ) {\n    createSurvey(requestedBy: $requestedBy, template: $template, batchId: $batchId, companySurveys: $companySurveys) {\n      surveys {\n        id\n        deadline\n        readyForEdit\n        formData\n        prefilledData\n        company {\n          id\n          name\n        }\n        progress\n        status\n        financialYear\n      }\n    }\n  }\n":
    types.CreateSurveyMutationDocument,
  "\n  mutation CreateSurveyBatchMutation(\n    $name: String!\n    $template: SurveyTemplateEnum!\n    $tenantId: ID\n    $description: String\n  ) {\n    createSurveyBatch(name: $name, template: $template, tenantId: $tenantId, description: $description) {\n      surveyBatch {\n        ...SurveyBatchItem\n      }\n    }\n  }\n":
    types.CreateSurveyBatchMutationDocument,
  "\n  mutation CreateWatchlistMutation($name: String!, $portalUserId: ID!, $requestedBy: String!) {\n    createWatchlist(name: $name, portalUserId: $portalUserId, requestedBy: $requestedBy) {\n      watchlist {\n        ...WatchlistItem\n      }\n    }\n  }\n":
    types.CreateWatchlistMutationDocument,
  "\n  mutation DeleteWatchlistMutation($requestedBy: String!, $watchlistId: ID!) {\n    deleteWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId) {\n      success\n    }\n  }\n":
    types.DeleteWatchlistMutationDocument,
  "\n  mutation RenameWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $name: String!) {\n    renameWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId, name: $name) {\n      watchlist {\n        ...WatchlistItem\n      }\n    }\n  }\n":
    types.RenameWatchlistMutationDocument,
  "\n  mutation ResendSurveyMutation($requestedBy: String!, $surveyId: ID!) {\n    resendSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n":
    types.ResendSurveyMutationDocument,
  "\n  mutation ShareSurveyMutation($requestedBy: String!, $surveyId: ID!, $sharedWith: [SurveyContactInput!]!) {\n    shareSurvey(requestedBy: $requestedBy, surveyId: $surveyId, sharedWith: $sharedWith) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n":
    types.ShareSurveyMutationDocument,
  "\n  mutation SubmitSurveyMutation($requestedBy: String!, $surveyId: ID!) {\n    submitSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n":
    types.SubmitSurveyMutationDocument,
  "\n  mutation UpdateSurveyMutation($surveyId: ID!, $status: SurveyStatusEnum, $progress: Float, $formData: JSONString) {\n    updateSurvey(surveyId: $surveyId, status: $status, progress: $progress, formData: $formData) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n":
    types.UpdateSurveyMutationDocument,
  "\n  mutation UpdateSurveyStatusMutation($status: SurveyStatusEnum!, $surveyIds: [ID!]!) {\n    updateSurveyStatus(status: $status, surveyIds: $surveyIds) {\n      surveys {\n        ...SurveyItem\n      }\n    }\n  }\n":
    types.UpdateSurveyStatusMutationDocument,
  "\n  mutation UpdatePortalUserMutation(\n    $requestedBy: String!\n    $id: ID!\n    $isActive: Boolean\n    $roles: [RoleAssignmentInput]\n    $firstName: String\n    $lastName: String\n    $hasGivenConsent: Boolean\n  ) {\n    updatePortalUser(\n      requestedBy: $requestedBy\n      id: $id\n      isActive: $isActive\n      roles: $roles\n      firstName: $firstName\n      lastName: $lastName\n      hasGivenConsent: $hasGivenConsent\n    ) {\n      portalUser {\n        ...PortalUserItem\n      }\n    }\n  }\n":
    types.UpdatePortalUserMutationDocument,
  "\n  mutation SaveFileToGcsMutation($file: Upload!, $filename: String!) {\n    saveFileToGcs(file: $file, filename: $filename) {\n      success\n      message\n      filePath\n      fileUrl\n    }\n  }\n":
    types.SaveFileToGcsMutationDocument,
  "\n  query companyQuery($id: ID!) {\n    company(id: $id) {\n      ...CompanyItem\n    }\n  }\n":
    types.CompanyQueryDocument,
  "\n  query companiesQuery($first: Int, $after: String, $country: IorderCompanyCountryChoices) {\n    companies(first: $first, after: $after, country: $country) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...CompanyItem\n        }\n      }\n    }\n  }\n":
    types.CompaniesQueryDocument,
  "\n  query evaluationQuery($id: ID!) {\n    evaluation(id: $id) {\n      ...EvaluationItem\n    }\n  }\n":
    types.EvaluationQueryDocument,
  "\n  query evaluationsQuery($first: Int, $after: String) {\n    evaluations(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...EvaluationItem\n        }\n      }\n    }\n  }\n":
    types.EvaluationsQueryDocument,
  "\n  query NaceCodeQuery($id: ID!) {\n    naceCode(id: $id) {\n      ...NaceCodeItem\n    }\n  }\n":
    types.NaceCodeQueryDocument,
  "\n  query NaceCodesQuery($first: Int, $after: String) {\n    naceCodes(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...NaceCodeItem\n        }\n      }\n    }\n  }\n":
    types.NaceCodesQueryDocument,
  "\n  query PortalUserQuery($id: ID!) {\n    portalUser(id: $id) {\n      ...PortalUserItem\n    }\n  }\n":
    types.PortalUserQueryDocument,
  "\n  query PortalUsersQuery($first: Int, $after: String) {\n    portalUsers(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...PortalUserItem\n        }\n      }\n    }\n  }\n":
    types.PortalUsersQueryDocument,
  "\n  query SurveyQuery($id: ID!) {\n    survey(id: $id) {\n      ...SurveyItem\n    }\n  }\n":
    types.SurveyQueryDocument,
  "\n  query SurveysQuery($first: Int, $after: String) {\n    surveys(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...SurveyItem\n        }\n      }\n    }\n  }\n":
    types.SurveysQueryDocument,
  "\n  query SurveyBatchQuery($id: ID!) {\n    surveyBatch(id: $id) {\n      ...SurveyBatchItem\n    }\n  }\n":
    types.SurveyBatchQueryDocument,
  "\n  query SurveyBatchesQuery($first: Int, $after: String) {\n    surveyBatches(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...SurveyBatchItem\n        }\n      }\n    }\n  }\n":
    types.SurveyBatchesQueryDocument,
  "\n  query TenantQuery($id: ID!) {\n    tenant(id: $id) {\n      ...TenantItem\n    }\n  }\n":
    types.TenantQueryDocument,
  "\n  query TenantsQuery($first: Int, $after: String) {\n    tenants(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...TenantItem\n        }\n      }\n    }\n  }\n":
    types.TenantsQueryDocument,
  "\n  query WatchlistQuery($id: ID!) {\n    watchlist(id: $id) {\n      ...WatchlistItem\n    }\n  }\n":
    types.WatchlistQueryDocument,
  "\n  query WatchlistsQuery($first: Int, $after: String) {\n    watchlists(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...WatchlistItem\n        }\n      }\n    }\n  }\n":
    types.WatchlistsQueryDocument,
  "\n  query GetFileUrlQuery($filePath: String!) {\n    getFileUrl(filePath: $filePath)\n  }\n":
    types.GetFileUrlQueryDocument,
}

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment EvaluationItem on EvaluationNode {\n    id\n    status\n    completedAt\n    evaluationYear\n    overallRecommendations\n    environmentalMaturity\n    socialMaturity\n    governanceMaturity\n    overallSummary\n    inputs {\n      dataDepth {\n        baseData\n        publicData\n        selfAssessment\n        advanced\n      }\n      evaluationDepth {\n        esgRiskMapping\n        aiEvaluations\n        esgRecommendations\n        enrichedEvaluation\n        fullEvaluation\n      }\n      sources {\n        businessRegistry\n        annualReport\n        website\n        media\n        questionnaire\n        proprietaryData\n      }\n    }\n    company {\n      id\n      name\n      registrationNumber\n    }\n  }\n"
): (typeof documents)["\n  fragment EvaluationItem on EvaluationNode {\n    id\n    status\n    completedAt\n    evaluationYear\n    overallRecommendations\n    environmentalMaturity\n    socialMaturity\n    governanceMaturity\n    overallSummary\n    inputs {\n      dataDepth {\n        baseData\n        publicData\n        selfAssessment\n        advanced\n      }\n      evaluationDepth {\n        esgRiskMapping\n        aiEvaluations\n        esgRecommendations\n        enrichedEvaluation\n        fullEvaluation\n      }\n      sources {\n        businessRegistry\n        annualReport\n        website\n        media\n        questionnaire\n        proprietaryData\n      }\n    }\n    company {\n      id\n      name\n      registrationNumber\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment CompanyItem on CompanyNode {\n    id\n    name\n    about\n    aboutEn\n    aboutEt\n    registrationNumber\n    status\n    country\n    employeeNumber\n    street\n    houseNumber\n    postalCode\n    city\n    registrationDate\n    urls {\n      url\n    }\n    latestGhgEmission {\n      id\n      year\n      source\n      basis\n      multiplier\n      unit\n      value\n      scope\n    }\n    mediaSources {\n      id\n      url\n      title\n      summary\n      story\n      publishedAt\n      startedAt\n      completedAt\n      esgCategory\n      sentiment\n      tiPossibleName\n      metaData\n    }\n    latestEsgMaterialityMapping {\n      executionDate\n      relevantPositives {\n        summary\n        maturityLevel\n      }\n      relevantIssues {\n        iissue {\n          id\n          name\n          category\n          issue {\n            description\n          }\n        }\n        maturityLevel\n        gabAnalysisResults\n        summary\n        recommendations\n        sources {\n          sourceType\n          source\n        }\n      }\n    }\n    publishedEvaluations {\n      ...EvaluationItem\n    }\n    businessActivities {\n      id\n      euTaxonomyEligible\n      naceCode {\n        id\n        label\n        labelEt\n        code\n        overallEbrdRiskLevel\n        environmentalEbrdRiskLevel\n        socialEbrdRiskLevel\n      }\n      latestReportItem {\n        isMainActivity\n        businessActivityRevenue {\n          amount\n          currency\n        }\n      }\n    }\n    annualReports {\n      financialYear\n      averageFullTimeEmployeesConsolidated\n      companyRevenueConsolidated {\n        amount\n        currency\n      }\n      netProfitConsolidated {\n        amount\n        currency\n      }\n      totalAssets {\n        amount\n        currency\n      }\n    }\n  }\n"
): (typeof documents)["\n  fragment CompanyItem on CompanyNode {\n    id\n    name\n    about\n    aboutEn\n    aboutEt\n    registrationNumber\n    status\n    country\n    employeeNumber\n    street\n    houseNumber\n    postalCode\n    city\n    registrationDate\n    urls {\n      url\n    }\n    latestGhgEmission {\n      id\n      year\n      source\n      basis\n      multiplier\n      unit\n      value\n      scope\n    }\n    mediaSources {\n      id\n      url\n      title\n      summary\n      story\n      publishedAt\n      startedAt\n      completedAt\n      esgCategory\n      sentiment\n      tiPossibleName\n      metaData\n    }\n    latestEsgMaterialityMapping {\n      executionDate\n      relevantPositives {\n        summary\n        maturityLevel\n      }\n      relevantIssues {\n        iissue {\n          id\n          name\n          category\n          issue {\n            description\n          }\n        }\n        maturityLevel\n        gabAnalysisResults\n        summary\n        recommendations\n        sources {\n          sourceType\n          source\n        }\n      }\n    }\n    publishedEvaluations {\n      ...EvaluationItem\n    }\n    businessActivities {\n      id\n      euTaxonomyEligible\n      naceCode {\n        id\n        label\n        labelEt\n        code\n        overallEbrdRiskLevel\n        environmentalEbrdRiskLevel\n        socialEbrdRiskLevel\n      }\n      latestReportItem {\n        isMainActivity\n        businessActivityRevenue {\n          amount\n          currency\n        }\n      }\n    }\n    annualReports {\n      financialYear\n      averageFullTimeEmployeesConsolidated\n      companyRevenueConsolidated {\n        amount\n        currency\n      }\n      netProfitConsolidated {\n        amount\n        currency\n      }\n      totalAssets {\n        amount\n        currency\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment NaceCodeItem on NaceCodeNode {\n    id\n    label\n    code\n    overallEbrdRiskLevel\n    environmentalEbrdRiskLevel\n    socialEbrdRiskLevel\n    parent {\n      id\n      parent {\n        id\n      }\n      label\n      code\n      overallEbrdRiskLevel\n      environmentalEbrdRiskLevel\n      socialEbrdRiskLevel\n    }\n  }\n"
): (typeof documents)["\n  fragment NaceCodeItem on NaceCodeNode {\n    id\n    label\n    code\n    overallEbrdRiskLevel\n    environmentalEbrdRiskLevel\n    socialEbrdRiskLevel\n    parent {\n      id\n      parent {\n        id\n      }\n      label\n      code\n      overallEbrdRiskLevel\n      environmentalEbrdRiskLevel\n      socialEbrdRiskLevel\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment PortalUserItem on PortalUserNode {\n    id\n    tenant {\n      id\n    }\n    email\n    lastName\n    firstName\n    company {\n      id\n    }\n    hasGivenConsent\n    isActive\n    dateJoined\n    lastLogin\n    createdAt\n    updatedAt\n    roles {\n      role\n    }\n  }\n"
): (typeof documents)["\n  fragment PortalUserItem on PortalUserNode {\n    id\n    tenant {\n      id\n    }\n    email\n    lastName\n    firstName\n    company {\n      id\n    }\n    hasGivenConsent\n    isActive\n    dateJoined\n    lastLogin\n    createdAt\n    updatedAt\n    roles {\n      role\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment SurveyItem on SurveyNode {\n    id\n    tenant {\n      id\n      name\n    }\n    updatedAt\n    createdAt\n    submittedAt\n    company {\n      id\n      name\n    }\n    batch {\n      id\n    }\n    financialYear\n    deadline\n    template\n    status\n    readyForEdit\n    prefilledData\n    inputDocuments {\n      id\n      file\n      fileName\n      fileSize\n      isAttachment\n    }\n    lastReminder\n    formData\n    statusChangedAt\n    progress\n    langConsent\n  }\n"
): (typeof documents)["\n  fragment SurveyItem on SurveyNode {\n    id\n    tenant {\n      id\n      name\n    }\n    updatedAt\n    createdAt\n    submittedAt\n    company {\n      id\n      name\n    }\n    batch {\n      id\n    }\n    financialYear\n    deadline\n    template\n    status\n    readyForEdit\n    prefilledData\n    inputDocuments {\n      id\n      file\n      fileName\n      fileSize\n      isAttachment\n    }\n    lastReminder\n    formData\n    statusChangedAt\n    progress\n    langConsent\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment SurveyBatchItem on SurveyBatchNode {\n    id\n    name\n    description\n    template\n    tenant {\n      id\n    }\n  }\n"
): (typeof documents)["\n  fragment SurveyBatchItem on SurveyBatchNode {\n    id\n    name\n    description\n    template\n    tenant {\n      id\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment TenantItem on TenantNode {\n    id\n    name\n    createdAt\n    updatedAt\n  }\n"
): (typeof documents)["\n  fragment TenantItem on TenantNode {\n    id\n    name\n    createdAt\n    updatedAt\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment WatchlistEntryItem on WatchlistEntryNode {\n    id\n    company {\n      ...CompanyItem\n    }\n    contacts {\n      id\n      email\n      name\n    }\n  }\n"
): (typeof documents)["\n  fragment WatchlistEntryItem on WatchlistEntryNode {\n    id\n    company {\n      ...CompanyItem\n    }\n    contacts {\n      id\n      email\n      name\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  fragment WatchlistItem on WatchlistNode {\n    id\n    tenant {\n      id\n    }\n    portalUser {\n      id\n    }\n    name\n    updatedAt\n    createdAt\n    entries {\n      ...WatchlistEntryItem\n    }\n  }\n"
): (typeof documents)["\n  fragment WatchlistItem on WatchlistNode {\n    id\n    tenant {\n      id\n    }\n    portalUser {\n      id\n    }\n    name\n    updatedAt\n    createdAt\n    entries {\n      ...WatchlistEntryItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation addEntryToWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $companyId: ID!, $website: String) {\n    addEntryToWatchlist(\n      requestedBy: $requestedBy\n      watchlistId: $watchlistId\n      companyId: $companyId\n      website: $website\n    ) {\n      entry {\n        ...WatchlistEntryItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation addEntryToWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $companyId: ID!, $website: String) {\n    addEntryToWatchlist(\n      requestedBy: $requestedBy\n      watchlistId: $watchlistId\n      companyId: $companyId\n      website: $website\n    ) {\n      entry {\n        ...WatchlistEntryItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation autofillSurveyMutation($requestedBy: String!, $surveyId: ID!, $inputDocumentsUrls: [String]) {\n    autofillSurvey(requestedBy: $requestedBy, surveyId: $surveyId, inputDocumentsUrls: $inputDocumentsUrls) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation autofillSurveyMutation($requestedBy: String!, $surveyId: ID!, $inputDocumentsUrls: [String]) {\n    autofillSurvey(requestedBy: $requestedBy, surveyId: $surveyId, inputDocumentsUrls: $inputDocumentsUrls) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation createPortalUserMutation(\n    $requestedBy: String!\n    $email: String!\n    $firstName: String\n    $lastName: String\n    $isActive: Boolean\n    $roles: [RoleAssignmentInput!]!\n    $companyId: ID\n  ) {\n    createPortalUser(\n      requestedBy: $requestedBy\n      email: $email\n      firstName: $firstName\n      lastName: $lastName\n      isActive: $isActive\n      roles: $roles\n      companyId: $companyId\n    ) {\n      portalUser {\n        ...PortalUserItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation createPortalUserMutation(\n    $requestedBy: String!\n    $email: String!\n    $firstName: String\n    $lastName: String\n    $isActive: Boolean\n    $roles: [RoleAssignmentInput!]!\n    $companyId: ID\n  ) {\n    createPortalUser(\n      requestedBy: $requestedBy\n      email: $email\n      firstName: $firstName\n      lastName: $lastName\n      isActive: $isActive\n      roles: $roles\n      companyId: $companyId\n    ) {\n      portalUser {\n        ...PortalUserItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation CreateSurveyMutation(\n    $requestedBy: String!\n    $template: SurveyTemplateEnum!\n    $batchId: ID!\n    $companySurveys: [CompanySurveysInput!]!\n  ) {\n    createSurvey(requestedBy: $requestedBy, template: $template, batchId: $batchId, companySurveys: $companySurveys) {\n      surveys {\n        id\n        deadline\n        readyForEdit\n        formData\n        prefilledData\n        company {\n          id\n          name\n        }\n        progress\n        status\n        financialYear\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation CreateSurveyMutation(\n    $requestedBy: String!\n    $template: SurveyTemplateEnum!\n    $batchId: ID!\n    $companySurveys: [CompanySurveysInput!]!\n  ) {\n    createSurvey(requestedBy: $requestedBy, template: $template, batchId: $batchId, companySurveys: $companySurveys) {\n      surveys {\n        id\n        deadline\n        readyForEdit\n        formData\n        prefilledData\n        company {\n          id\n          name\n        }\n        progress\n        status\n        financialYear\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation CreateSurveyBatchMutation(\n    $name: String!\n    $template: SurveyTemplateEnum!\n    $tenantId: ID\n    $description: String\n  ) {\n    createSurveyBatch(name: $name, template: $template, tenantId: $tenantId, description: $description) {\n      surveyBatch {\n        ...SurveyBatchItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation CreateSurveyBatchMutation(\n    $name: String!\n    $template: SurveyTemplateEnum!\n    $tenantId: ID\n    $description: String\n  ) {\n    createSurveyBatch(name: $name, template: $template, tenantId: $tenantId, description: $description) {\n      surveyBatch {\n        ...SurveyBatchItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation CreateWatchlistMutation($name: String!, $portalUserId: ID!, $requestedBy: String!) {\n    createWatchlist(name: $name, portalUserId: $portalUserId, requestedBy: $requestedBy) {\n      watchlist {\n        ...WatchlistItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation CreateWatchlistMutation($name: String!, $portalUserId: ID!, $requestedBy: String!) {\n    createWatchlist(name: $name, portalUserId: $portalUserId, requestedBy: $requestedBy) {\n      watchlist {\n        ...WatchlistItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation DeleteWatchlistMutation($requestedBy: String!, $watchlistId: ID!) {\n    deleteWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId) {\n      success\n    }\n  }\n"
): (typeof documents)["\n  mutation DeleteWatchlistMutation($requestedBy: String!, $watchlistId: ID!) {\n    deleteWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId) {\n      success\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation RenameWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $name: String!) {\n    renameWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId, name: $name) {\n      watchlist {\n        ...WatchlistItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation RenameWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $name: String!) {\n    renameWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId, name: $name) {\n      watchlist {\n        ...WatchlistItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation ResendSurveyMutation($requestedBy: String!, $surveyId: ID!) {\n    resendSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation ResendSurveyMutation($requestedBy: String!, $surveyId: ID!) {\n    resendSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation ShareSurveyMutation($requestedBy: String!, $surveyId: ID!, $sharedWith: [SurveyContactInput!]!) {\n    shareSurvey(requestedBy: $requestedBy, surveyId: $surveyId, sharedWith: $sharedWith) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation ShareSurveyMutation($requestedBy: String!, $surveyId: ID!, $sharedWith: [SurveyContactInput!]!) {\n    shareSurvey(requestedBy: $requestedBy, surveyId: $surveyId, sharedWith: $sharedWith) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation SubmitSurveyMutation($requestedBy: String!, $surveyId: ID!) {\n    submitSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation SubmitSurveyMutation($requestedBy: String!, $surveyId: ID!) {\n    submitSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation UpdateSurveyMutation($surveyId: ID!, $status: SurveyStatusEnum, $progress: Float, $formData: JSONString) {\n    updateSurvey(surveyId: $surveyId, status: $status, progress: $progress, formData: $formData) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation UpdateSurveyMutation($surveyId: ID!, $status: SurveyStatusEnum, $progress: Float, $formData: JSONString) {\n    updateSurvey(surveyId: $surveyId, status: $status, progress: $progress, formData: $formData) {\n      survey {\n        ...SurveyItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation UpdateSurveyStatusMutation($status: SurveyStatusEnum!, $surveyIds: [ID!]!) {\n    updateSurveyStatus(status: $status, surveyIds: $surveyIds) {\n      surveys {\n        ...SurveyItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation UpdateSurveyStatusMutation($status: SurveyStatusEnum!, $surveyIds: [ID!]!) {\n    updateSurveyStatus(status: $status, surveyIds: $surveyIds) {\n      surveys {\n        ...SurveyItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation UpdatePortalUserMutation(\n    $requestedBy: String!\n    $id: ID!\n    $isActive: Boolean\n    $roles: [RoleAssignmentInput]\n    $firstName: String\n    $lastName: String\n    $hasGivenConsent: Boolean\n  ) {\n    updatePortalUser(\n      requestedBy: $requestedBy\n      id: $id\n      isActive: $isActive\n      roles: $roles\n      firstName: $firstName\n      lastName: $lastName\n      hasGivenConsent: $hasGivenConsent\n    ) {\n      portalUser {\n        ...PortalUserItem\n      }\n    }\n  }\n"
): (typeof documents)["\n  mutation UpdatePortalUserMutation(\n    $requestedBy: String!\n    $id: ID!\n    $isActive: Boolean\n    $roles: [RoleAssignmentInput]\n    $firstName: String\n    $lastName: String\n    $hasGivenConsent: Boolean\n  ) {\n    updatePortalUser(\n      requestedBy: $requestedBy\n      id: $id\n      isActive: $isActive\n      roles: $roles\n      firstName: $firstName\n      lastName: $lastName\n      hasGivenConsent: $hasGivenConsent\n    ) {\n      portalUser {\n        ...PortalUserItem\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  mutation SaveFileToGcsMutation($file: Upload!, $filename: String!) {\n    saveFileToGcs(file: $file, filename: $filename) {\n      success\n      message\n      filePath\n      fileUrl\n    }\n  }\n"
): (typeof documents)["\n  mutation SaveFileToGcsMutation($file: Upload!, $filename: String!) {\n    saveFileToGcs(file: $file, filename: $filename) {\n      success\n      message\n      filePath\n      fileUrl\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query companyQuery($id: ID!) {\n    company(id: $id) {\n      ...CompanyItem\n    }\n  }\n"
): (typeof documents)["\n  query companyQuery($id: ID!) {\n    company(id: $id) {\n      ...CompanyItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query companiesQuery($first: Int, $after: String, $country: IorderCompanyCountryChoices) {\n    companies(first: $first, after: $after, country: $country) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...CompanyItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query companiesQuery($first: Int, $after: String, $country: IorderCompanyCountryChoices) {\n    companies(first: $first, after: $after, country: $country) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...CompanyItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query evaluationQuery($id: ID!) {\n    evaluation(id: $id) {\n      ...EvaluationItem\n    }\n  }\n"
): (typeof documents)["\n  query evaluationQuery($id: ID!) {\n    evaluation(id: $id) {\n      ...EvaluationItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query evaluationsQuery($first: Int, $after: String) {\n    evaluations(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...EvaluationItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query evaluationsQuery($first: Int, $after: String) {\n    evaluations(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...EvaluationItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query NaceCodeQuery($id: ID!) {\n    naceCode(id: $id) {\n      ...NaceCodeItem\n    }\n  }\n"
): (typeof documents)["\n  query NaceCodeQuery($id: ID!) {\n    naceCode(id: $id) {\n      ...NaceCodeItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query NaceCodesQuery($first: Int, $after: String) {\n    naceCodes(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...NaceCodeItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query NaceCodesQuery($first: Int, $after: String) {\n    naceCodes(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...NaceCodeItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query PortalUserQuery($id: ID!) {\n    portalUser(id: $id) {\n      ...PortalUserItem\n    }\n  }\n"
): (typeof documents)["\n  query PortalUserQuery($id: ID!) {\n    portalUser(id: $id) {\n      ...PortalUserItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query PortalUsersQuery($first: Int, $after: String) {\n    portalUsers(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...PortalUserItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query PortalUsersQuery($first: Int, $after: String) {\n    portalUsers(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...PortalUserItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query SurveyQuery($id: ID!) {\n    survey(id: $id) {\n      ...SurveyItem\n    }\n  }\n"
): (typeof documents)["\n  query SurveyQuery($id: ID!) {\n    survey(id: $id) {\n      ...SurveyItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query SurveysQuery($first: Int, $after: String) {\n    surveys(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...SurveyItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query SurveysQuery($first: Int, $after: String) {\n    surveys(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...SurveyItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query SurveyBatchQuery($id: ID!) {\n    surveyBatch(id: $id) {\n      ...SurveyBatchItem\n    }\n  }\n"
): (typeof documents)["\n  query SurveyBatchQuery($id: ID!) {\n    surveyBatch(id: $id) {\n      ...SurveyBatchItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query SurveyBatchesQuery($first: Int, $after: String) {\n    surveyBatches(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...SurveyBatchItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query SurveyBatchesQuery($first: Int, $after: String) {\n    surveyBatches(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...SurveyBatchItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query TenantQuery($id: ID!) {\n    tenant(id: $id) {\n      ...TenantItem\n    }\n  }\n"
): (typeof documents)["\n  query TenantQuery($id: ID!) {\n    tenant(id: $id) {\n      ...TenantItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query TenantsQuery($first: Int, $after: String) {\n    tenants(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...TenantItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query TenantsQuery($first: Int, $after: String) {\n    tenants(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...TenantItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query WatchlistQuery($id: ID!) {\n    watchlist(id: $id) {\n      ...WatchlistItem\n    }\n  }\n"
): (typeof documents)["\n  query WatchlistQuery($id: ID!) {\n    watchlist(id: $id) {\n      ...WatchlistItem\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query WatchlistsQuery($first: Int, $after: String) {\n    watchlists(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...WatchlistItem\n        }\n      }\n    }\n  }\n"
): (typeof documents)["\n  query WatchlistsQuery($first: Int, $after: String) {\n    watchlists(first: $first, after: $after) {\n      pageInfo {\n        hasNextPage\n        hasPreviousPage\n        startCursor\n        endCursor\n      }\n      edges {\n        node {\n          ...WatchlistItem\n        }\n      }\n    }\n  }\n"]
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(
  source: "\n  query GetFileUrlQuery($filePath: String!) {\n    getFileUrl(filePath: $filePath)\n  }\n"
): (typeof documents)["\n  query GetFileUrlQuery($filePath: String!) {\n    getFileUrl(filePath: $filePath)\n  }\n"]

export function graphql(source: string) {
  return (documents as any)[source] ?? {}
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> =
  TDocumentNode extends DocumentNode<infer TType, any> ? TType : never
