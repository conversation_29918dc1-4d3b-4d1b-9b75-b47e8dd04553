/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core"

export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] }
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> }
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> }
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never }
export type Incremental<T> = T | { [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never }
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string }
  String: { input: string; output: string }
  Boolean: { input: boolean; output: boolean }
  Int: { input: number; output: number }
  Float: { input: number; output: number }
  /**
   * The `Date` scalar type represents a Date
   * value as specified by
   * [iso8601](https://en.wikipedia.org/wiki/ISO_8601).
   */
  Date: { input: string; output: string }
  /**
   * The `DateTime` scalar type represents a DateTime
   * value as specified by
   * [iso8601](https://en.wikipedia.org/wiki/ISO_8601).
   */
  DateTime: { input: string; output: string }
  /** The `Decimal` scalar type represents a python Decimal. */
  Decimal: { input: any; output: any }
  /**
   * Allows use of a JSON String for input / output from the GraphQL schema.
   *
   * Use of this type is *not recommended* as you lose the benefits of having a defined, static
   * schema (one of the key benefits of GraphQL).
   */
  JSONString: { input: any; output: any }
  /**
   * Create scalar that ignores normal serialization/deserialization, since
   * that will be handled by the multipart request spec
   */
  Upload: { input: any; output: any }
}

export type AddEntryToWatchlist = {
  __typename?: "AddEntryToWatchlist"
  entry?: Maybe<WatchlistEntryNode>
}

export type AnnualReportItemNode = {
  __typename?: "AnnualReportItemNode"
  annualReport?: Maybe<AnnualReportNode>
  businessActivity?: Maybe<BusinessActivityNode>
  businessActivityRevenue?: Maybe<MoneyNode>
  id: Scalars["ID"]["output"]
  isMainActivity?: Maybe<Scalars["Boolean"]["output"]>
  weight?: Maybe<Scalars["Decimal"]["output"]>
}

export type AnnualReportNode = {
  __typename?: "AnnualReportNode"
  averageFullTimeEmployees?: Maybe<Scalars["Int"]["output"]>
  averageFullTimeEmployeesConsolidated?: Maybe<Scalars["Int"]["output"]>
  capex?: Maybe<MoneyNode>
  capexConsolidated?: Maybe<MoneyNode>
  company?: Maybe<CompanyNode>
  companyRevenue?: Maybe<MoneyNode>
  companyRevenueConsolidated?: Maybe<MoneyNode>
  endDate?: Maybe<Scalars["Date"]["output"]>
  financialYear?: Maybe<Scalars["Int"]["output"]>
  id: Scalars["ID"]["output"]
  netProfit?: Maybe<MoneyNode>
  netProfitConsolidated?: Maybe<MoneyNode>
  startDate?: Maybe<Scalars["Date"]["output"]>
  totalAssets?: Maybe<MoneyNode>
  totalAssetsConsolidated?: Maybe<MoneyNode>
  totalStaffCost?: Maybe<Scalars["Decimal"]["output"]>
  totalStaffCostConsolidated?: Maybe<Scalars["Decimal"]["output"]>
}

export type AutofillSurvey = {
  __typename?: "AutofillSurvey"
  survey?: Maybe<SurveyNode>
}

export type BusinessActivityNode = {
  __typename?: "BusinessActivityNode"
  euTaxonomyEligible?: Maybe<Scalars["Boolean"]["output"]>
  id: Scalars["ID"]["output"]
  latestReportItem?: Maybe<AnnualReportItemNode>
  naceCode?: Maybe<NaceCodeNode>
}

/** An enumeration. */
export enum CategoryChoicesEnum {
  Environmental = "environmental",
  Governance = "governance",
  Na = "na",
  Social = "social",
}

/** An enumeration. */
export enum CollectionTypeChoicesEnum {
  Classes = "classes",
  Divisions = "divisions",
  Groups = "groups",
  Sections = "sections",
}

export type CompanyContactInput = {
  email: Scalars["String"]["input"]
  name?: InputMaybe<Scalars["String"]["input"]>
}

export type CompanyContactNode = {
  __typename?: "CompanyContactNode"
  email: Scalars["String"]["output"]
  id: Scalars["ID"]["output"]
  name?: Maybe<Scalars["String"]["output"]>
  watchlistEntry: WatchlistEntryNode
}

export type CompanyDatapointNode = Node & {
  __typename?: "CompanyDatapointNode"
  company: CompanyNode
  createdAt: Scalars["DateTime"]["output"]
  datapoint: DatapointNode
  /** The ID of the object */
  id: Scalars["ID"]["output"]
  sourceType: IportalCompanyDatapointSourceTypeChoices
  unit?: Maybe<Scalars["String"]["output"]>
  value: Scalars["JSONString"]["output"]
}

export type CompanyDatapointNodeConnection = {
  __typename?: "CompanyDatapointNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<CompanyDatapointNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `CompanyDatapointNode` and its cursor. */
export type CompanyDatapointNodeEdge = {
  __typename?: "CompanyDatapointNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<CompanyDatapointNode>
}

/** GraphQL Node for CompanyEmission model. */
export type CompanyEmissionNode = Node & {
  __typename?: "CompanyEmissionNode"
  basis?: Maybe<Scalars["String"]["output"]>
  company?: Maybe<CompanyNode>
  emissionExtraction?: Maybe<EmissionExtractionNode>
  /** The ID of the object */
  id: Scalars["ID"]["output"]
  multiplier?: Maybe<Scalars["String"]["output"]>
  scope?: Maybe<Scalars["String"]["output"]>
  source?: Maybe<Scalars["String"]["output"]>
  unit?: Maybe<Scalars["String"]["output"]>
  value?: Maybe<Scalars["String"]["output"]>
  year?: Maybe<Scalars["Int"]["output"]>
}

/** GraphQL Node for Company model. */
export type CompanyNode = Node & {
  __typename?: "CompanyNode"
  about?: Maybe<Scalars["String"]["output"]>
  aboutEn?: Maybe<Scalars["String"]["output"]>
  aboutEt?: Maybe<Scalars["String"]["output"]>
  annualReports: Array<AnnualReportNode>
  businessActivities: Array<BusinessActivityNode>
  city?: Maybe<Scalars["String"]["output"]>
  country?: Maybe<IorderCompanyCountryChoices>
  employeeNumber?: Maybe<Scalars["Int"]["output"]>
  ghggoalValue?: Maybe<Scalars["String"]["output"]>
  houseNumber?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  latestEsgMaterialityMapping?: Maybe<MaterialityMappingNode>
  latestGhgEmission?: Maybe<Array<Maybe<CompanyEmissionNode>>>
  mediaSources?: Maybe<Array<Maybe<MediaSourceDataNode>>>
  name: Scalars["String"]["output"]
  postalCode?: Maybe<Scalars["String"]["output"]>
  publishedEvaluations?: Maybe<Array<Maybe<EvaluationNode>>>
  registrationDate?: Maybe<Scalars["Date"]["output"]>
  registrationNumber?: Maybe<Scalars["String"]["output"]>
  status?: Maybe<CompanyStatusChoicesEnum>
  statusEt?: Maybe<CompanyStatusChoicesEstonianEnum>
  street?: Maybe<Scalars["String"]["output"]>
  urls: Array<UrlNode>
}

export type CompanyNodeConnection = {
  __typename?: "CompanyNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<CompanyNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `CompanyNode` and its cursor. */
export type CompanyNodeEdge = {
  __typename?: "CompanyNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<CompanyNode>
}

/** An enumeration. */
export enum CompanyStatusChoicesEnum {
  Bankrupt = "bankrupt",
  Delete = "delete",
  EnteredInTheRegister = "entered_in_the_register",
  InLiquidation = "in_liquidation",
}

/** An enumeration. */
export enum CompanyStatusChoicesEstonianEnum {
  KantudRegistrisse = "kantud_registrisse",
  Kustutatud = "kustutatud",
  Likvideerimisel = "likvideerimisel",
  Pankrotis = "pankrotis",
}

export type CompanySurveysInput = {
  companyId: Scalars["ID"]["input"]
  contactEmails: Array<InputMaybe<Scalars["String"]["input"]>>
  deadline?: InputMaybe<Scalars["DateTime"]["input"]>
  financialYear?: InputMaybe<Scalars["Int"]["input"]>
}

export type CreatePortalUser = {
  __typename?: "CreatePortalUser"
  portalUser?: Maybe<PortalUserNode>
}

export type CreateSurvey = {
  __typename?: "CreateSurvey"
  surveys?: Maybe<Array<Maybe<SurveyNode>>>
}

export type CreateSurveyBatch = {
  __typename?: "CreateSurveyBatch"
  surveyBatch?: Maybe<SurveyBatchNode>
}

export type CreateWatchlist = {
  __typename?: "CreateWatchlist"
  watchlist?: Maybe<WatchlistNode>
}

export type DataDepthNode = {
  __typename?: "DataDepthNode"
  advanced?: Maybe<Scalars["Boolean"]["output"]>
  baseData?: Maybe<Scalars["Boolean"]["output"]>
  evaluationId?: Maybe<Scalars["ID"]["output"]>
  publicData?: Maybe<Scalars["Boolean"]["output"]>
  selfAssessment?: Maybe<Scalars["Boolean"]["output"]>
}

export type DatapointNode = Node & {
  __typename?: "DatapointNode"
  description?: Maybe<Scalars["String"]["output"]>
  esgCategory?: Maybe<EsgCategoryEnum>
  esgCategoryEt?: Maybe<IportalDatapointEsgCategoryEtChoices>
  format?: Maybe<Scalars["JSONString"]["output"]>
  id: Scalars["ID"]["output"]
  name?: Maybe<Scalars["String"]["output"]>
  nameEt?: Maybe<Scalars["String"]["output"]>
  type?: Maybe<IportalDatapointTypeChoices>
}

export type DatapointNodeConnection = {
  __typename?: "DatapointNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<DatapointNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `DatapointNode` and its cursor. */
export type DatapointNodeEdge = {
  __typename?: "DatapointNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<DatapointNode>
}

export type DeleteWatchlist = {
  __typename?: "DeleteWatchlist"
  success?: Maybe<Scalars["Boolean"]["output"]>
}

/** An enumeration. */
export enum EsgCategoryEnum {
  Environment = "ENVIRONMENT",
  General = "GENERAL",
  Governance = "GOVERNANCE",
  Social = "SOCIAL",
}

/** An enumeration. */
export enum EbrdRiskLevelChoicesEnum {
  Excluded = "excluded",
  High = "high",
  Low = "low",
  Medium = "medium",
}

/** GraphQL Node for EmissionExtraction model. */
export type EmissionExtractionNode = Node & {
  __typename?: "EmissionExtractionNode"
  company?: Maybe<CompanyNode>
  companyEmissions?: Maybe<Array<Maybe<CompanyEmissionNode>>>
  completedAt?: Maybe<Scalars["DateTime"]["output"]>
  extractionMethod?: Maybe<IorderEmissionExtractionExtractionMethodChoices>
  /** The ID of the object */
  id: Scalars["ID"]["output"]
  metaData?: Maybe<Scalars["JSONString"]["output"]>
  startedAt?: Maybe<Scalars["DateTime"]["output"]>
  status?: Maybe<IorderEmissionExtractionStatusChoices>
}

export type EvaluationDepthNode = {
  __typename?: "EvaluationDepthNode"
  aiEvaluations?: Maybe<Scalars["Boolean"]["output"]>
  enrichedEvaluation?: Maybe<Scalars["Boolean"]["output"]>
  esgRecommendations?: Maybe<Scalars["Boolean"]["output"]>
  esgRiskMapping?: Maybe<Scalars["Boolean"]["output"]>
  evaluationId?: Maybe<Scalars["ID"]["output"]>
  fullEvaluation?: Maybe<Scalars["Boolean"]["output"]>
}

export type EvaluationInputsNode = {
  __typename?: "EvaluationInputsNode"
  dataDepth?: Maybe<DataDepthNode>
  evaluationDepth?: Maybe<EvaluationDepthNode>
  evaluationId?: Maybe<Scalars["ID"]["output"]>
  sources?: Maybe<EvaluationSourcesNode>
}

export type EvaluationNode = Node & {
  __typename?: "EvaluationNode"
  company: CompanyNode
  completedAt?: Maybe<Scalars["DateTime"]["output"]>
  environmentalMaturity?: Maybe<LevelChoicesEnum>
  errorMessage?: Maybe<Scalars["String"]["output"]>
  evaluationYear?: Maybe<Scalars["Int"]["output"]>
  governanceMaturity?: Maybe<LevelChoicesEnum>
  id: Scalars["ID"]["output"]
  inputs?: Maybe<EvaluationInputsNode>
  isPublished?: Maybe<Scalars["Boolean"]["output"]>
  metaData?: Maybe<Scalars["JSONString"]["output"]>
  overallRecommendations?: Maybe<Array<Scalars["String"]["output"]>>
  overallSummary?: Maybe<Scalars["String"]["output"]>
  socialMaturity?: Maybe<LevelChoicesEnum>
  startedAt: Scalars["DateTime"]["output"]
  status?: Maybe<StatusChoicesEnum>
}

export type EvaluationNodeConnection = {
  __typename?: "EvaluationNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<EvaluationNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `EvaluationNode` and its cursor. */
export type EvaluationNodeEdge = {
  __typename?: "EvaluationNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<EvaluationNode>
}

export type EvaluationSourcesNode = {
  __typename?: "EvaluationSourcesNode"
  annualReport?: Maybe<Scalars["Boolean"]["output"]>
  businessRegistry?: Maybe<Scalars["Boolean"]["output"]>
  evaluationId?: Maybe<Scalars["ID"]["output"]>
  media?: Maybe<Scalars["Boolean"]["output"]>
  proprietaryData?: Maybe<Scalars["Boolean"]["output"]>
  questionnaire?: Maybe<Scalars["Boolean"]["output"]>
  website?: Maybe<Scalars["Boolean"]["output"]>
}

export type IIssueNode = {
  __typename?: "IIssueNode"
  category?: Maybe<CategoryChoicesEnum>
  id: Scalars["ID"]["output"]
  issue?: Maybe<IssueNode>
  name?: Maybe<Scalars["String"]["output"]>
}

/** An enumeration. */
export enum IesgMediaSourceDataEsgCategoryChoices {
  /** ENVIRONMENTAL */
  Environmental = "ENVIRONMENTAL",
  /** GOVERNANCE */
  Governance = "GOVERNANCE",
  /** N/A */
  NA = "N_A",
  /** SOCIAL */
  Social = "SOCIAL",
}

/** An enumeration. */
export enum IesgMediaSourceDataTiPossibleNameChoices {
  /** Ettekirjutus */
  Ettekirjutus = "ETTEKIRJUTUS",
  /** Ettekirjutus ja Sunniraha hoiatus */
  EttekirjutusJaSunnirahaHoiatus = "ETTEKIRJUTUS_JA_SUNNIRAHA_HOIATUS",
  /** N/A */
  NA = "N_A",
  /** Sunniraha hoiatus */
  SunnirahaHoiatus = "SUNNIRAHA_HOIATUS",
  /** Sunniraha rakendamine */
  SunnirahaRakendamine = "SUNNIRAHA_RAKENDAMINE",
}

/** An enumeration. */
export enum IesgRelevantIissueSourceSourceTypeChoices {
  /** ANNUAL REPORT */
  AnnualReport = "ANNUAL_REPORT",
  /** URL */
  Url = "URL",
  /** WEBSITE */
  Website = "WEBSITE",
}

/** An enumeration. */
export enum IorderCompanyCountryChoices {
  /** Andorra */
  Ad = "AD",
  /** United Arab Emirates */
  Ae = "AE",
  /** Afghanistan */
  Af = "AF",
  /** Antigua and Barbuda */
  Ag = "AG",
  /** Anguilla */
  Ai = "AI",
  /** Albania */
  Al = "AL",
  /** Armenia */
  Am = "AM",
  /** Angola */
  Ao = "AO",
  /** Antarctica */
  Aq = "AQ",
  /** Argentina */
  Ar = "AR",
  /** American Samoa */
  As = "AS",
  /** Austria */
  At = "AT",
  /** Australia */
  Au = "AU",
  /** Aruba */
  Aw = "AW",
  /** Åland Islands */
  Ax = "AX",
  /** Azerbaijan */
  Az = "AZ",
  /** Bosnia and Herzegovina */
  Ba = "BA",
  /** Barbados */
  Bb = "BB",
  /** Bangladesh */
  Bd = "BD",
  /** Belgium */
  Be = "BE",
  /** Burkina Faso */
  Bf = "BF",
  /** Bulgaria */
  Bg = "BG",
  /** Bahrain */
  Bh = "BH",
  /** Burundi */
  Bi = "BI",
  /** Benin */
  Bj = "BJ",
  /** Saint Barthélemy */
  Bl = "BL",
  /** Bermuda */
  Bm = "BM",
  /** Brunei */
  Bn = "BN",
  /** Bolivia */
  Bo = "BO",
  /** Bonaire, Sint Eustatius and Saba */
  Bq = "BQ",
  /** Brazil */
  Br = "BR",
  /** Bahamas */
  Bs = "BS",
  /** Bhutan */
  Bt = "BT",
  /** Bouvet Island */
  Bv = "BV",
  /** Botswana */
  Bw = "BW",
  /** Belarus */
  By = "BY",
  /** Belize */
  Bz = "BZ",
  /** Canada */
  Ca = "CA",
  /** Cocos (Keeling) Islands */
  Cc = "CC",
  /** Congo (the Democratic Republic of the) */
  Cd = "CD",
  /** Central African Republic */
  Cf = "CF",
  /** Congo */
  Cg = "CG",
  /** Switzerland */
  Ch = "CH",
  /** Côte d'Ivoire */
  Ci = "CI",
  /** Cook Islands */
  Ck = "CK",
  /** Chile */
  Cl = "CL",
  /** Cameroon */
  Cm = "CM",
  /** China */
  Cn = "CN",
  /** Colombia */
  Co = "CO",
  /** Costa Rica */
  Cr = "CR",
  /** Cuba */
  Cu = "CU",
  /** Cabo Verde */
  Cv = "CV",
  /** Curaçao */
  Cw = "CW",
  /** Christmas Island */
  Cx = "CX",
  /** Cyprus */
  Cy = "CY",
  /** Czechia */
  Cz = "CZ",
  /** Germany */
  De = "DE",
  /** Djibouti */
  Dj = "DJ",
  /** Denmark */
  Dk = "DK",
  /** Dominica */
  Dm = "DM",
  /** Dominican Republic */
  Do = "DO",
  /** Algeria */
  Dz = "DZ",
  /** Ecuador */
  Ec = "EC",
  /** Estonia */
  Ee = "EE",
  /** Egypt */
  Eg = "EG",
  /** Western Sahara */
  Eh = "EH",
  /** Eritrea */
  Er = "ER",
  /** Spain */
  Es = "ES",
  /** Ethiopia */
  Et = "ET",
  /** Finland */
  Fi = "FI",
  /** Fiji */
  Fj = "FJ",
  /** Falkland Islands (Malvinas) */
  Fk = "FK",
  /** Micronesia (Federated States of) */
  Fm = "FM",
  /** Faroe Islands */
  Fo = "FO",
  /** France */
  Fr = "FR",
  /** Gabon */
  Ga = "GA",
  /** United Kingdom */
  Gb = "GB",
  /** Grenada */
  Gd = "GD",
  /** Georgia */
  Ge = "GE",
  /** French Guiana */
  Gf = "GF",
  /** Guernsey */
  Gg = "GG",
  /** Ghana */
  Gh = "GH",
  /** Gibraltar */
  Gi = "GI",
  /** Greenland */
  Gl = "GL",
  /** Gambia */
  Gm = "GM",
  /** Guinea */
  Gn = "GN",
  /** Guadeloupe */
  Gp = "GP",
  /** Equatorial Guinea */
  Gq = "GQ",
  /** Greece */
  Gr = "GR",
  /** South Georgia and the South Sandwich Islands */
  Gs = "GS",
  /** Guatemala */
  Gt = "GT",
  /** Guam */
  Gu = "GU",
  /** Guinea-Bissau */
  Gw = "GW",
  /** Guyana */
  Gy = "GY",
  /** Hong Kong */
  Hk = "HK",
  /** Heard Island and McDonald Islands */
  Hm = "HM",
  /** Honduras */
  Hn = "HN",
  /** Croatia */
  Hr = "HR",
  /** Haiti */
  Ht = "HT",
  /** Hungary */
  Hu = "HU",
  /** Indonesia */
  Id = "ID",
  /** Ireland */
  Ie = "IE",
  /** Israel */
  Il = "IL",
  /** Isle of Man */
  Im = "IM",
  /** India */
  In = "IN",
  /** British Indian Ocean Territory */
  Io = "IO",
  /** Iraq */
  Iq = "IQ",
  /** Iran */
  Ir = "IR",
  /** Iceland */
  Is = "IS",
  /** Italy */
  It = "IT",
  /** Jersey */
  Je = "JE",
  /** Jamaica */
  Jm = "JM",
  /** Jordan */
  Jo = "JO",
  /** Japan */
  Jp = "JP",
  /** Kenya */
  Ke = "KE",
  /** Kyrgyzstan */
  Kg = "KG",
  /** Cambodia */
  Kh = "KH",
  /** Kiribati */
  Ki = "KI",
  /** Comoros */
  Km = "KM",
  /** Saint Kitts and Nevis */
  Kn = "KN",
  /** North Korea */
  Kp = "KP",
  /** South Korea */
  Kr = "KR",
  /** Kuwait */
  Kw = "KW",
  /** Cayman Islands */
  Ky = "KY",
  /** Kazakhstan */
  Kz = "KZ",
  /** Laos */
  La = "LA",
  /** Lebanon */
  Lb = "LB",
  /** Saint Lucia */
  Lc = "LC",
  /** Liechtenstein */
  Li = "LI",
  /** Sri Lanka */
  Lk = "LK",
  /** Liberia */
  Lr = "LR",
  /** Lesotho */
  Ls = "LS",
  /** Lithuania */
  Lt = "LT",
  /** Luxembourg */
  Lu = "LU",
  /** Latvia */
  Lv = "LV",
  /** Libya */
  Ly = "LY",
  /** Morocco */
  Ma = "MA",
  /** Monaco */
  Mc = "MC",
  /** Moldova */
  Md = "MD",
  /** Montenegro */
  Me = "ME",
  /** Saint Martin (French part) */
  Mf = "MF",
  /** Madagascar */
  Mg = "MG",
  /** Marshall Islands */
  Mh = "MH",
  /** North Macedonia */
  Mk = "MK",
  /** Mali */
  Ml = "ML",
  /** Myanmar */
  Mm = "MM",
  /** Mongolia */
  Mn = "MN",
  /** Macao */
  Mo = "MO",
  /** Northern Mariana Islands */
  Mp = "MP",
  /** Martinique */
  Mq = "MQ",
  /** Mauritania */
  Mr = "MR",
  /** Montserrat */
  Ms = "MS",
  /** Malta */
  Mt = "MT",
  /** Mauritius */
  Mu = "MU",
  /** Maldives */
  Mv = "MV",
  /** Malawi */
  Mw = "MW",
  /** Mexico */
  Mx = "MX",
  /** Malaysia */
  My = "MY",
  /** Mozambique */
  Mz = "MZ",
  /** Namibia */
  Na = "NA",
  /** New Caledonia */
  Nc = "NC",
  /** Niger */
  Ne = "NE",
  /** Norfolk Island */
  Nf = "NF",
  /** Nigeria */
  Ng = "NG",
  /** Nicaragua */
  Ni = "NI",
  /** Netherlands */
  Nl = "NL",
  /** Norway */
  No = "NO",
  /** Nepal */
  Np = "NP",
  /** Nauru */
  Nr = "NR",
  /** Niue */
  Nu = "NU",
  /** New Zealand */
  Nz = "NZ",
  /** Oman */
  Om = "OM",
  /** Panama */
  Pa = "PA",
  /** Peru */
  Pe = "PE",
  /** French Polynesia */
  Pf = "PF",
  /** Papua New Guinea */
  Pg = "PG",
  /** Philippines */
  Ph = "PH",
  /** Pakistan */
  Pk = "PK",
  /** Poland */
  Pl = "PL",
  /** Saint Pierre and Miquelon */
  Pm = "PM",
  /** Pitcairn */
  Pn = "PN",
  /** Puerto Rico */
  Pr = "PR",
  /** Palestine, State of */
  Ps = "PS",
  /** Portugal */
  Pt = "PT",
  /** Palau */
  Pw = "PW",
  /** Paraguay */
  Py = "PY",
  /** Qatar */
  Qa = "QA",
  /** Réunion */
  Re = "RE",
  /** Romania */
  Ro = "RO",
  /** Serbia */
  Rs = "RS",
  /** Russia */
  Ru = "RU",
  /** Rwanda */
  Rw = "RW",
  /** Saudi Arabia */
  Sa = "SA",
  /** Solomon Islands */
  Sb = "SB",
  /** Seychelles */
  Sc = "SC",
  /** Sudan */
  Sd = "SD",
  /** Sweden */
  Se = "SE",
  /** Singapore */
  Sg = "SG",
  /** Saint Helena, Ascension and Tristan da Cunha */
  Sh = "SH",
  /** Slovenia */
  Si = "SI",
  /** Svalbard and Jan Mayen */
  Sj = "SJ",
  /** Slovakia */
  Sk = "SK",
  /** Sierra Leone */
  Sl = "SL",
  /** San Marino */
  Sm = "SM",
  /** Senegal */
  Sn = "SN",
  /** Somalia */
  So = "SO",
  /** Suriname */
  Sr = "SR",
  /** South Sudan */
  Ss = "SS",
  /** Sao Tome and Principe */
  St = "ST",
  /** El Salvador */
  Sv = "SV",
  /** Sint Maarten (Dutch part) */
  Sx = "SX",
  /** Syria */
  Sy = "SY",
  /** Eswatini */
  Sz = "SZ",
  /** Turks and Caicos Islands */
  Tc = "TC",
  /** Chad */
  Td = "TD",
  /** French Southern Territories */
  Tf = "TF",
  /** Togo */
  Tg = "TG",
  /** Thailand */
  Th = "TH",
  /** Tajikistan */
  Tj = "TJ",
  /** Tokelau */
  Tk = "TK",
  /** Timor-Leste */
  Tl = "TL",
  /** Turkmenistan */
  Tm = "TM",
  /** Tunisia */
  Tn = "TN",
  /** Tonga */
  To = "TO",
  /** Türkiye */
  Tr = "TR",
  /** Trinidad and Tobago */
  Tt = "TT",
  /** Tuvalu */
  Tv = "TV",
  /** Taiwan */
  Tw = "TW",
  /** Tanzania */
  Tz = "TZ",
  /** Ukraine */
  Ua = "UA",
  /** Uganda */
  Ug = "UG",
  /** United States Minor Outlying Islands */
  Um = "UM",
  /** United States of America */
  Us = "US",
  /** Uruguay */
  Uy = "UY",
  /** Uzbekistan */
  Uz = "UZ",
  /** Holy See */
  Va = "VA",
  /** Saint Vincent and the Grenadines */
  Vc = "VC",
  /** Venezuela */
  Ve = "VE",
  /** Virgin Islands (British) */
  Vg = "VG",
  /** Virgin Islands (U.S.) */
  Vi = "VI",
  /** Vietnam */
  Vn = "VN",
  /** Vanuatu */
  Vu = "VU",
  /** Wallis and Futuna */
  Wf = "WF",
  /** Samoa */
  Ws = "WS",
  /** Yemen */
  Ye = "YE",
  /** Mayotte */
  Yt = "YT",
  /** South Africa */
  Za = "ZA",
  /** Zambia */
  Zm = "ZM",
  /** Zimbabwe */
  Zw = "ZW",
}

/** An enumeration. */
export enum IorderEmissionExtractionExtractionMethodChoices {
  /** AI */
  Ai = "AI",
  /** MANUAL */
  Manual = "MANUAL",
}

/** An enumeration. */
export enum IorderEmissionExtractionStatusChoices {
  /** DONE */
  Done = "DONE",
  /** ERROR */
  Error = "ERROR",
  /** IN-PROGRESS */
  InProgress = "IN_PROGRESS",
  /** STARTED */
  Started = "STARTED",
}

/** An enumeration. */
export enum IportalCompanyDatapointSourceTypeChoices {
  /** AI */
  Ai = "AI",
  /** Third Party */
  ThirdParty = "THIRD_PARTY",
  /** User */
  User = "USER",
}

/** An enumeration. */
export enum IportalDatapointEsgCategoryEtChoices {
  /** Keskkond */
  Environment = "ENVIRONMENT",
  /** Üldine */
  General = "GENERAL",
  /** Juhtimine */
  Governance = "GOVERNANCE",
  /** Sotsiaalne */
  Social = "SOCIAL",
}

/** An enumeration. */
export enum IportalDatapointTypeChoices {
  /** Boolean */
  Boolean = "BOOLEAN",
  /** Date */
  Date = "DATE",
  /** File */
  File = "FILE",
  /** JSON */
  Json = "JSON",
  /** MultiSelect */
  Multiselect = "MULTISELECT",
  /** Numerical */
  Numerical = "NUMERICAL",
  /** Select */
  Select = "SELECT",
  /** Text */
  Text = "TEXT",
}

/** An enumeration. */
export enum IportalPortalUserLanguageChoices {
  /** English */
  En = "EN",
  /** Estonian */
  Et = "ET",
}

/** An enumeration. */
export enum IportalSurveyBatchTemplateChoices {
  /**  Self Assessment Unified Questionnaire V1 */
  SelfAssessmentUnifiedQuestionnaireV1 = "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1",
  /** Unified Questionnaire V1 */
  UnifiedQuestionnaireV1 = "UNIFIED_QUESTIONNAIRE_V1",
}

export type IssueNode = {
  __typename?: "IssueNode"
  category?: Maybe<CategoryChoicesEnum>
  description?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  name?: Maybe<Scalars["String"]["output"]>
}

/** An enumeration. */
export enum LevelChoicesEnum {
  High = "high",
  Low = "low",
  Medium = "medium",
  VeryHigh = "very_high",
  VeryLow = "very_low",
}

export type MaterialityMappingNode = {
  __typename?: "MaterialityMappingNode"
  company?: Maybe<CompanyNode>
  executionDate?: Maybe<Scalars["Date"]["output"]>
  id: Scalars["ID"]["output"]
  relevantIssues?: Maybe<Array<Maybe<RelevantIissueNode>>>
  relevantPositives?: Maybe<Array<Maybe<RelevantPositiveNode>>>
}

export type MediaSourceDataNode = Node & {
  __typename?: "MediaSourceDataNode"
  completedAt?: Maybe<Scalars["DateTime"]["output"]>
  esgCategory?: Maybe<IesgMediaSourceDataEsgCategoryChoices>
  /** The ID of the object */
  id: Scalars["ID"]["output"]
  metaData?: Maybe<Scalars["JSONString"]["output"]>
  publishedAt?: Maybe<Scalars["DateTime"]["output"]>
  sentiment?: Maybe<Scalars["String"]["output"]>
  startedAt: Scalars["DateTime"]["output"]
  story?: Maybe<Scalars["String"]["output"]>
  summary: Scalars["String"]["output"]
  tiPossibleName?: Maybe<IesgMediaSourceDataTiPossibleNameChoices>
  title: Scalars["String"]["output"]
  url: Scalars["String"]["output"]
}

/** GraphQL Node for Money type. */
export type MoneyNode = {
  __typename?: "MoneyNode"
  amount: Scalars["Float"]["output"]
  currency: Scalars["String"]["output"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type Mutation = {
  __typename?: "Mutation"
  addEntryToWatchlist?: Maybe<AddEntryToWatchlist>
  autofillSurvey?: Maybe<AutofillSurvey>
  createPortalUser?: Maybe<CreatePortalUser>
  createSurvey?: Maybe<CreateSurvey>
  createSurveyBatch?: Maybe<CreateSurveyBatch>
  createWatchlist?: Maybe<CreateWatchlist>
  deleteWatchlist?: Maybe<DeleteWatchlist>
  renameWatchlist?: Maybe<RenameWatchlist>
  resendSurvey?: Maybe<ResendSurvey>
  saveFileToGcs?: Maybe<SaveFileToGcs>
  shareSurvey?: Maybe<ShareSurvey>
  submitSurvey?: Maybe<SubmitSurvey>
  survey?: Maybe<SurveyNode>
  surveyBatch?: Maybe<SurveyBatchNode>
  updatePortalUser?: Maybe<UpdatePortalUser>
  updateSurvey?: Maybe<UpdateSurvey>
  updateSurveyBatch?: Maybe<UpdateSurveyBatch>
  updateSurveyStatus?: Maybe<UpdateSurveyStatus>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationAddEntryToWatchlistArgs = {
  companyId: Scalars["ID"]["input"]
  contacts?: InputMaybe<Array<InputMaybe<CompanyContactInput>>>
  requestedBy: Scalars["String"]["input"]
  watchlistId: Scalars["ID"]["input"]
  website?: InputMaybe<Scalars["String"]["input"]>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationAutofillSurveyArgs = {
  inputDocumentsUrls?: InputMaybe<Array<InputMaybe<Scalars["String"]["input"]>>>
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationCreatePortalUserArgs = {
  companyId?: InputMaybe<Scalars["ID"]["input"]>
  email: Scalars["String"]["input"]
  firstName?: InputMaybe<Scalars["String"]["input"]>
  hasGivenConsent?: InputMaybe<Scalars["Boolean"]["input"]>
  isActive?: InputMaybe<Scalars["Boolean"]["input"]>
  language?: InputMaybe<Scalars["String"]["input"]>
  lastName?: InputMaybe<Scalars["String"]["input"]>
  requestedBy: Scalars["String"]["input"]
  roles: Array<InputMaybe<RoleAssignmentInput>>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationCreateSurveyArgs = {
  batchId: Scalars["ID"]["input"]
  companySurveys?: InputMaybe<Array<InputMaybe<CompanySurveysInput>>>
  formData?: InputMaybe<Scalars["JSONString"]["input"]>
  langConsent?: InputMaybe<Scalars["String"]["input"]>
  lastReminder?: InputMaybe<Scalars["DateTime"]["input"]>
  prefilledData?: InputMaybe<Scalars["JSONString"]["input"]>
  progress?: InputMaybe<Scalars["Float"]["input"]>
  readyForEdit?: InputMaybe<Scalars["Boolean"]["input"]>
  requestedBy: Scalars["String"]["input"]
  status?: InputMaybe<SurveyStatusEnum>
  statusChangedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  surveyTemplateId?: InputMaybe<Scalars["ID"]["input"]>
  template: SurveyTemplateEnum
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationCreateSurveyBatchArgs = {
  description?: InputMaybe<Scalars["String"]["input"]>
  name: Scalars["String"]["input"]
  template: SurveyTemplateEnum
  tenantId?: InputMaybe<Scalars["ID"]["input"]>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationCreateWatchlistArgs = {
  name: Scalars["String"]["input"]
  portalUserId: Scalars["ID"]["input"]
  requestedBy: Scalars["String"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationDeleteWatchlistArgs = {
  requestedBy: Scalars["String"]["input"]
  watchlistId: Scalars["ID"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationRenameWatchlistArgs = {
  name: Scalars["String"]["input"]
  requestedBy: Scalars["String"]["input"]
  watchlistId: Scalars["ID"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationResendSurveyArgs = {
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationSaveFileToGcsArgs = {
  file: Scalars["Upload"]["input"]
  filename: Scalars["String"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationShareSurveyArgs = {
  requestedBy: Scalars["String"]["input"]
  sharedWith: Array<InputMaybe<SurveyContactInput>>
  surveyId: Scalars["ID"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationSubmitSurveyArgs = {
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationUpdatePortalUserArgs = {
  companyId?: InputMaybe<Scalars["ID"]["input"]>
  email?: InputMaybe<Scalars["String"]["input"]>
  firstName?: InputMaybe<Scalars["String"]["input"]>
  hasGivenConsent?: InputMaybe<Scalars["Boolean"]["input"]>
  id: Scalars["ID"]["input"]
  isActive?: InputMaybe<Scalars["Boolean"]["input"]>
  language?: InputMaybe<Scalars["String"]["input"]>
  lastName?: InputMaybe<Scalars["String"]["input"]>
  requestedBy: Scalars["String"]["input"]
  roles?: InputMaybe<Array<InputMaybe<RoleAssignmentInput>>>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationUpdateSurveyArgs = {
  companyId?: InputMaybe<Scalars["ID"]["input"]>
  deadline?: InputMaybe<Scalars["DateTime"]["input"]>
  financialYear?: InputMaybe<Scalars["Int"]["input"]>
  formData?: InputMaybe<Scalars["JSONString"]["input"]>
  langConsent?: InputMaybe<Scalars["String"]["input"]>
  lastReminder?: InputMaybe<Scalars["DateTime"]["input"]>
  progress?: InputMaybe<Scalars["Float"]["input"]>
  readyForEdit?: InputMaybe<Scalars["Boolean"]["input"]>
  status?: InputMaybe<SurveyStatusEnum>
  statusChangedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  surveyId: Scalars["ID"]["input"]
  template?: InputMaybe<SurveyTemplateEnum>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationUpdateSurveyBatchArgs = {
  description?: InputMaybe<Scalars["String"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  surveyBatchId: Scalars["ID"]["input"]
  template?: InputMaybe<SurveyTemplateEnum>
}

/**
 * The Mutation class consolidates all the mutation operations available in the GraphQL API.
 * Additional app-specific mutations can be added by including them in the list of extended classes.
 */
export type MutationUpdateSurveyStatusArgs = {
  status: SurveyStatusEnum
  surveyIds: Array<InputMaybe<Scalars["ID"]["input"]>>
}

export type NaceCodeNode = Node & {
  __typename?: "NaceCodeNode"
  caseLawIfApplicable?: Maybe<Scalars["String"]["output"]>
  children: NaceCodeNodeConnection
  code?: Maybe<Scalars["String"]["output"]>
  collectionType?: Maybe<CollectionTypeChoicesEnum>
  description?: Maybe<Scalars["String"]["output"]>
  environmentalEbrdRiskLevel?: Maybe<EbrdRiskLevelChoicesEnum>
  excludes?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  label?: Maybe<Scalars["String"]["output"]>
  labelEt?: Maybe<Scalars["String"]["output"]>
  overallEbrdRiskLevel?: Maybe<EbrdRiskLevelChoicesEnum>
  parent?: Maybe<NaceCodeNode>
  socialEbrdRiskLevel?: Maybe<EbrdRiskLevelChoicesEnum>
}

export type NaceCodeNodeChildrenArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  code?: InputMaybe<Scalars["String"]["input"]>
  code_Icontains?: InputMaybe<Scalars["String"]["input"]>
  code_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  label?: InputMaybe<Scalars["String"]["input"]>
  label_Icontains?: InputMaybe<Scalars["String"]["input"]>
  label_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
}

export type NaceCodeNodeConnection = {
  __typename?: "NaceCodeNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<NaceCodeNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `NaceCodeNode` and its cursor. */
export type NaceCodeNodeEdge = {
  __typename?: "NaceCodeNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<NaceCodeNode>
}

/** An object with an ID */
export type Node = {
  /** The ID of the object */
  id: Scalars["ID"]["output"]
}

/** The Relay compliant `PageInfo` type, containing data necessary to paginate this connection. */
export type PageInfo = {
  __typename?: "PageInfo"
  /** When paginating forwards, the cursor to continue. */
  endCursor?: Maybe<Scalars["String"]["output"]>
  /** When paginating forwards, are there more items? */
  hasNextPage: Scalars["Boolean"]["output"]
  /** When paginating backwards, are there more items? */
  hasPreviousPage: Scalars["Boolean"]["output"]
  /** When paginating backwards, the cursor to continue. */
  startCursor?: Maybe<Scalars["String"]["output"]>
}

export type PortalUserNode = Node & {
  __typename?: "PortalUserNode"
  company?: Maybe<CompanyNode>
  createdAt?: Maybe<Scalars["DateTime"]["output"]>
  dateJoined: Scalars["DateTime"]["output"]
  email: Scalars["String"]["output"]
  firstName?: Maybe<Scalars["String"]["output"]>
  hasGivenConsent: Scalars["Boolean"]["output"]
  id: Scalars["ID"]["output"]
  isActive: Scalars["Boolean"]["output"]
  language: IportalPortalUserLanguageChoices
  lastLogin?: Maybe<Scalars["DateTime"]["output"]>
  lastName?: Maybe<Scalars["String"]["output"]>
  roles: Array<RoleAssignmentNode>
  tenant?: Maybe<TenantNode>
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>
}

export type PortalUserNodeConnection = {
  __typename?: "PortalUserNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<PortalUserNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `PortalUserNode` and its cursor. */
export type PortalUserNodeEdge = {
  __typename?: "PortalUserNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<PortalUserNode>
}

/** An enumeration. */
export enum PortalUserRoleEnum {
  Admin = "ADMIN",
  ClientAdmin = "CLIENT_ADMIN",
  CompanyUser = "COMPANY_USER",
  DataViewer = "DATA_VIEWER",
  Observer = "OBSERVER",
  SurveyManager = "SURVEY_MANAGER",
  WatchlistManager = "WATCHLIST_MANAGER",
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type Query = {
  __typename?: "Query"
  companies?: Maybe<CompanyNodeConnection>
  company?: Maybe<CompanyNode>
  companyDatapoint?: Maybe<CompanyDatapointNode>
  companyDatapoints?: Maybe<CompanyDatapointNodeConnection>
  datapoint?: Maybe<DatapointNode>
  datapoints?: Maybe<DatapointNodeConnection>
  evaluation?: Maybe<EvaluationNode>
  evaluations?: Maybe<EvaluationNodeConnection>
  /** Get the downloadable URL for a given file path. */
  getFileUrl?: Maybe<Scalars["String"]["output"]>
  naceCode?: Maybe<NaceCodeNode>
  naceCodes?: Maybe<NaceCodeNodeConnection>
  portalUser?: Maybe<PortalUserNode>
  portalUsers?: Maybe<PortalUserNodeConnection>
  survey?: Maybe<SurveyNode>
  surveyBatch?: Maybe<SurveyBatchNode>
  surveyBatches?: Maybe<SurveyBatchNodeConnection>
  surveys?: Maybe<SurveyNodeConnection>
  tenant?: Maybe<TenantNode>
  tenants?: Maybe<TenantNodeConnection>
  watchlist?: Maybe<WatchlistNode>
  watchlists?: Maybe<WatchlistNodeConnection>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryCompaniesArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  country?: InputMaybe<IorderCompanyCountryChoices>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  name_Icontains?: InputMaybe<Scalars["String"]["input"]>
  name_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  registrationNumber?: InputMaybe<Scalars["String"]["input"]>
  registrationNumber_Icontains?: InputMaybe<Scalars["String"]["input"]>
  registrationNumber_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  status?: InputMaybe<CompanyStatusChoicesEnum>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryCompanyArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryCompanyDatapointArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryCompanyDatapointsArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  company_Id?: InputMaybe<Scalars["ID"]["input"]>
  createdAt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  datapoint_Id?: InputMaybe<Scalars["ID"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  sourceType_Icontains?: InputMaybe<IportalCompanyDatapointSourceTypeChoices>
  unit?: InputMaybe<Scalars["String"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryDatapointArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryDatapointsArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  description_Icontains?: InputMaybe<Scalars["String"]["input"]>
  esgCategory?: InputMaybe<EsgCategoryEnum>
  esgCategoryEt?: InputMaybe<IportalDatapointEsgCategoryEtChoices>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  nameEt_Icontains?: InputMaybe<Scalars["String"]["input"]>
  nameEt_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  name_Icontains?: InputMaybe<Scalars["String"]["input"]>
  name_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  systemKey?: InputMaybe<Scalars["String"]["input"]>
  topicCategory?: InputMaybe<Scalars["ID"]["input"]>
  type?: InputMaybe<IportalDatapointTypeChoices>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryEvaluationArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryEvaluationsArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryGetFileUrlArgs = {
  filePath: Scalars["String"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryNaceCodeArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryNaceCodesArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  code?: InputMaybe<Scalars["String"]["input"]>
  code_Icontains?: InputMaybe<Scalars["String"]["input"]>
  code_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  label?: InputMaybe<Scalars["String"]["input"]>
  label_Icontains?: InputMaybe<Scalars["String"]["input"]>
  label_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryPortalUserArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryPortalUsersArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  company_Id?: InputMaybe<Scalars["ID"]["input"]>
  dateJoined?: InputMaybe<Scalars["DateTime"]["input"]>
  dateJoined_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  dateJoined_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  dateJoined_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  dateJoined_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  email?: InputMaybe<Scalars["String"]["input"]>
  email_Icontains?: InputMaybe<Scalars["String"]["input"]>
  email_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  firstName?: InputMaybe<Scalars["String"]["input"]>
  firstName_Icontains?: InputMaybe<Scalars["String"]["input"]>
  firstName_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  hasGivenConsent?: InputMaybe<Scalars["Boolean"]["input"]>
  isActive?: InputMaybe<Scalars["Boolean"]["input"]>
  language?: InputMaybe<IportalPortalUserLanguageChoices>
  language_Icontains?: InputMaybe<IportalPortalUserLanguageChoices>
  language_Istartswith?: InputMaybe<IportalPortalUserLanguageChoices>
  last?: InputMaybe<Scalars["Int"]["input"]>
  lastLogin?: InputMaybe<Scalars["DateTime"]["input"]>
  lastLogin_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  lastLogin_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  lastLogin_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  lastLogin_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  lastName?: InputMaybe<Scalars["String"]["input"]>
  lastName_Icontains?: InputMaybe<Scalars["String"]["input"]>
  lastName_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  roles_Role?: InputMaybe<PortalUserRoleEnum>
  tenant_Id?: InputMaybe<Scalars["ID"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QuerySurveyArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QuerySurveyBatchArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QuerySurveyBatchesArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  createdAt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  description?: InputMaybe<Scalars["String"]["input"]>
  description_Icontains?: InputMaybe<Scalars["String"]["input"]>
  description_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  name_Icontains?: InputMaybe<Scalars["String"]["input"]>
  name_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  template?: InputMaybe<IportalSurveyBatchTemplateChoices>
  updatedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QuerySurveysArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  batch_Id?: InputMaybe<Scalars["ID"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  company_Id?: InputMaybe<Scalars["ID"]["input"]>
  createdAt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  createdAt_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  deadline?: InputMaybe<Scalars["DateTime"]["input"]>
  deadline_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  deadline_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  deadline_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  deadline_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  financialYear?: InputMaybe<Scalars["Int"]["input"]>
  financialYear_Gt?: InputMaybe<Scalars["Int"]["input"]>
  financialYear_Gte?: InputMaybe<Scalars["Int"]["input"]>
  financialYear_Lt?: InputMaybe<Scalars["Int"]["input"]>
  financialYear_Lte?: InputMaybe<Scalars["Int"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  langConsent?: InputMaybe<Scalars["String"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  progress?: InputMaybe<Scalars["Float"]["input"]>
  readyForEdit?: InputMaybe<Scalars["Boolean"]["input"]>
  status?: InputMaybe<SurveyStatusEnum>
  statusChangedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  statusChangedAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  statusChangedAt_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  statusChangedAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  statusChangedAt_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  submittedAt_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
  surveyTemplate_Type?: InputMaybe<Scalars["String"]["input"]>
  template?: InputMaybe<SurveyTemplateEnum>
  tenant_Id?: InputMaybe<Scalars["ID"]["input"]>
  updatedAt?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Gt?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Gte?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Lt?: InputMaybe<Scalars["DateTime"]["input"]>
  updatedAt_Lte?: InputMaybe<Scalars["DateTime"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryTenantArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryTenantsArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  name_Icontains?: InputMaybe<Scalars["String"]["input"]>
  name_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryWatchlistArgs = {
  id: Scalars["ID"]["input"]
}

/**
 * The Query class consolidates all the query operations available in the GraphQL API.
 * Additional app-specific queries can be added by including them in the list of extended classes.
 */
export type QueryWatchlistsArgs = {
  after?: InputMaybe<Scalars["String"]["input"]>
  before?: InputMaybe<Scalars["String"]["input"]>
  entries_Company_Name?: InputMaybe<Scalars["String"]["input"]>
  entries_Company_Name_Icontains?: InputMaybe<Scalars["String"]["input"]>
  entries_Company_Name_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  first?: InputMaybe<Scalars["Int"]["input"]>
  id?: InputMaybe<Scalars["ID"]["input"]>
  last?: InputMaybe<Scalars["Int"]["input"]>
  name?: InputMaybe<Scalars["String"]["input"]>
  name_Icontains?: InputMaybe<Scalars["String"]["input"]>
  name_Istartswith?: InputMaybe<Scalars["String"]["input"]>
  offset?: InputMaybe<Scalars["Int"]["input"]>
  tenant_Id?: InputMaybe<Scalars["ID"]["input"]>
}

export type RelevantIissueNode = {
  __typename?: "RelevantIissueNode"
  esgMapping?: Maybe<MaterialityMappingNode>
  gabAnalysisResults?: Maybe<Scalars["String"]["output"]>
  id: Scalars["ID"]["output"]
  iissue?: Maybe<IIssueNode>
  maturityLevel?: Maybe<LevelChoicesEnum>
  recommendations?: Maybe<Array<Scalars["String"]["output"]>>
  sources?: Maybe<Array<Maybe<RelevantIissueSourceNode>>>
  summary?: Maybe<Scalars["String"]["output"]>
}

export type RelevantIissueSourceNode = {
  __typename?: "RelevantIissueSourceNode"
  id: Scalars["ID"]["output"]
  source?: Maybe<Scalars["String"]["output"]>
  sourceType?: Maybe<IesgRelevantIissueSourceSourceTypeChoices>
}

export type RelevantPositiveNode = {
  __typename?: "RelevantPositiveNode"
  maturityLevel?: Maybe<Scalars["String"]["output"]>
  summary?: Maybe<Scalars["String"]["output"]>
}

export type RenameWatchlist = {
  __typename?: "RenameWatchlist"
  watchlist?: Maybe<WatchlistNode>
}

export type ResendSurvey = {
  __typename?: "ResendSurvey"
  survey?: Maybe<SurveyNode>
}

export type RoleAssignmentInput = {
  role?: InputMaybe<PortalUserRoleEnum>
}

export type RoleAssignmentNode = {
  __typename?: "RoleAssignmentNode"
  id: Scalars["ID"]["output"]
  role?: Maybe<PortalUserRoleEnum>
  user: PortalUserNode
}

export type SaveFileToGcs = {
  __typename?: "SaveFileToGCS"
  filePath?: Maybe<Scalars["String"]["output"]>
  fileUrl?: Maybe<Scalars["String"]["output"]>
  message?: Maybe<Scalars["String"]["output"]>
  success?: Maybe<Scalars["Boolean"]["output"]>
}

export type ShareSurvey = {
  __typename?: "ShareSurvey"
  survey?: Maybe<SurveyNode>
}

/** An enumeration. */
export enum StatusChoicesEnum {
  Done = "DONE",
  Error = "ERROR",
  InProgress = "IN_PROGRESS",
  IssuesEvaluated = "ISSUES_EVALUATED",
  Started = "STARTED",
}

export type SubmitSurvey = {
  __typename?: "SubmitSurvey"
  survey?: Maybe<SurveyNode>
}

export type SurveyBatchNode = Node & {
  __typename?: "SurveyBatchNode"
  createdAt?: Maybe<Scalars["DateTime"]["output"]>
  description: Scalars["String"]["output"]
  /** The ID of the object */
  id: Scalars["ID"]["output"]
  name?: Maybe<Scalars["String"]["output"]>
  surveys?: Maybe<Array<Maybe<SurveyNode>>>
  template?: Maybe<IportalSurveyBatchTemplateChoices>
  tenant?: Maybe<TenantNode>
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>
}

export type SurveyBatchNodeConnection = {
  __typename?: "SurveyBatchNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<SurveyBatchNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `SurveyBatchNode` and its cursor. */
export type SurveyBatchNodeEdge = {
  __typename?: "SurveyBatchNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<SurveyBatchNode>
}

export type SurveyContactInput = {
  email: Scalars["String"]["input"]
  name?: InputMaybe<Scalars["String"]["input"]>
  role?: InputMaybe<SurveyContactRoleEnum>
}

/** An enumeration. */
export enum SurveyContactRoleEnum {
  Contributor = "CONTRIBUTOR",
  Submitter = "SUBMITTER",
}

export type SurveyDocumentNode = {
  __typename?: "SurveyDocumentNode"
  file: Scalars["String"]["output"]
  fileName: Scalars["String"]["output"]
  fileSize: Scalars["Int"]["output"]
  id: Scalars["ID"]["output"]
  isAttachment: Scalars["Boolean"]["output"]
}

export type SurveyNode = Node & {
  __typename?: "SurveyNode"
  attachedDocuments?: Maybe<Array<Maybe<SurveyDocumentNode>>>
  batch?: Maybe<SurveyBatchNode>
  company?: Maybe<CompanyNode>
  createdAt?: Maybe<Scalars["DateTime"]["output"]>
  deadline?: Maybe<Scalars["DateTime"]["output"]>
  financialYear?: Maybe<Scalars["Int"]["output"]>
  formData?: Maybe<Scalars["JSONString"]["output"]>
  id: Scalars["ID"]["output"]
  inputDocuments?: Maybe<Array<Maybe<SurveyDocumentNode>>>
  langConsent?: Maybe<Scalars["String"]["output"]>
  lastReminder?: Maybe<Scalars["DateTime"]["output"]>
  prefilledData?: Maybe<Scalars["JSONString"]["output"]>
  progress?: Maybe<Scalars["Float"]["output"]>
  readyForEdit: Scalars["Boolean"]["output"]
  status?: Maybe<SurveyStatusEnum>
  statusChangedAt?: Maybe<Scalars["DateTime"]["output"]>
  submittedAt?: Maybe<Scalars["DateTime"]["output"]>
  surveyPath?: Maybe<Scalars["String"]["output"]>
  template?: Maybe<SurveyTemplateEnum>
  tenant?: Maybe<TenantNode>
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>
}

export type SurveyNodeConnection = {
  __typename?: "SurveyNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<SurveyNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `SurveyNode` and its cursor. */
export type SurveyNodeEdge = {
  __typename?: "SurveyNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<SurveyNode>
}

/** An enumeration. */
export enum SurveyStatusEnum {
  Cancelled = "CANCELLED",
  Complete = "COMPLETE",
  InProgress = "IN_PROGRESS",
  NotStarted = "NOT_STARTED",
  Open = "OPEN",
  Pending = "PENDING",
  Processing = "PROCESSING",
  Sent = "SENT",
  Submitted = "SUBMITTED",
}

/** An enumeration. */
export enum SurveyTemplateEnum {
  SelfAssessmentUnifiedQuestionnaireV1 = "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1",
  UnifiedQuestionnaireV1 = "UNIFIED_QUESTIONNAIRE_V1",
}

export type TenantNode = Node & {
  __typename?: "TenantNode"
  createdAt?: Maybe<Scalars["DateTime"]["output"]>
  id: Scalars["ID"]["output"]
  name?: Maybe<Scalars["String"]["output"]>
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>
}

export type TenantNodeConnection = {
  __typename?: "TenantNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<TenantNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `TenantNode` and its cursor. */
export type TenantNodeEdge = {
  __typename?: "TenantNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<TenantNode>
}

export type UrlNode = {
  __typename?: "URLNode"
  id: Scalars["ID"]["output"]
  url: Scalars["String"]["output"]
}

export type UpdatePortalUser = {
  __typename?: "UpdatePortalUser"
  portalUser?: Maybe<PortalUserNode>
}

export type UpdateSurvey = {
  __typename?: "UpdateSurvey"
  survey?: Maybe<SurveyNode>
}

export type UpdateSurveyBatch = {
  __typename?: "UpdateSurveyBatch"
  surveyBatch?: Maybe<SurveyBatchNode>
}

export type UpdateSurveyStatus = {
  __typename?: "UpdateSurveyStatus"
  message?: Maybe<Scalars["String"]["output"]>
  success?: Maybe<Scalars["Boolean"]["output"]>
  surveys?: Maybe<Array<Maybe<SurveyNode>>>
}

export type WatchlistEntryNode = {
  __typename?: "WatchlistEntryNode"
  company: CompanyNode
  contacts: Array<CompanyContactNode>
  id: Scalars["ID"]["output"]
  watchlist: WatchlistNode
}

export type WatchlistNode = Node & {
  __typename?: "WatchlistNode"
  createdAt?: Maybe<Scalars["DateTime"]["output"]>
  entries: Array<WatchlistEntryNode>
  id: Scalars["ID"]["output"]
  name: Scalars["String"]["output"]
  portalUser?: Maybe<PortalUserNode>
  tenant?: Maybe<TenantNode>
  updatedAt?: Maybe<Scalars["DateTime"]["output"]>
}

export type WatchlistNodeConnection = {
  __typename?: "WatchlistNodeConnection"
  /** Contains the nodes in this connection. */
  edges: Array<Maybe<WatchlistNodeEdge>>
  /** Pagination data for this connection. */
  pageInfo: PageInfo
}

/** A Relay edge containing a `WatchlistNode` and its cursor. */
export type WatchlistNodeEdge = {
  __typename?: "WatchlistNodeEdge"
  /** A cursor for use in pagination */
  cursor: Scalars["String"]["output"]
  /** The item at the end of the edge */
  node?: Maybe<WatchlistNode>
}

export type EvaluationItemFragment = {
  __typename?: "EvaluationNode"
  id: string
  status?: StatusChoicesEnum | null
  completedAt?: string | null
  evaluationYear?: number | null
  overallRecommendations?: Array<string> | null
  environmentalMaturity?: LevelChoicesEnum | null
  socialMaturity?: LevelChoicesEnum | null
  governanceMaturity?: LevelChoicesEnum | null
  overallSummary?: string | null
  inputs?: {
    __typename?: "EvaluationInputsNode"
    dataDepth?: {
      __typename?: "DataDepthNode"
      baseData?: boolean | null
      publicData?: boolean | null
      selfAssessment?: boolean | null
      advanced?: boolean | null
    } | null
    evaluationDepth?: {
      __typename?: "EvaluationDepthNode"
      esgRiskMapping?: boolean | null
      aiEvaluations?: boolean | null
      esgRecommendations?: boolean | null
      enrichedEvaluation?: boolean | null
      fullEvaluation?: boolean | null
    } | null
    sources?: {
      __typename?: "EvaluationSourcesNode"
      businessRegistry?: boolean | null
      annualReport?: boolean | null
      website?: boolean | null
      media?: boolean | null
      questionnaire?: boolean | null
      proprietaryData?: boolean | null
    } | null
  } | null
  company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
}

export type CompanyItemFragment = {
  __typename?: "CompanyNode"
  id: string
  name: string
  about?: string | null
  aboutEn?: string | null
  aboutEt?: string | null
  registrationNumber?: string | null
  status?: CompanyStatusChoicesEnum | null
  country?: IorderCompanyCountryChoices | null
  employeeNumber?: number | null
  street?: string | null
  houseNumber?: string | null
  postalCode?: string | null
  city?: string | null
  registrationDate?: string | null
  urls: Array<{ __typename?: "URLNode"; url: string }>
  latestGhgEmission?: Array<{
    __typename?: "CompanyEmissionNode"
    id: string
    year?: number | null
    source?: string | null
    basis?: string | null
    multiplier?: string | null
    unit?: string | null
    value?: string | null
    scope?: string | null
  } | null> | null
  mediaSources?: Array<{
    __typename?: "MediaSourceDataNode"
    id: string
    url: string
    title: string
    summary: string
    story?: string | null
    publishedAt?: string | null
    startedAt: string
    completedAt?: string | null
    esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
    sentiment?: string | null
    tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
    metaData?: any | null
  } | null> | null
  latestEsgMaterialityMapping?: {
    __typename?: "MaterialityMappingNode"
    executionDate?: string | null
    relevantPositives?: Array<{
      __typename?: "RelevantPositiveNode"
      summary?: string | null
      maturityLevel?: string | null
    } | null> | null
    relevantIssues?: Array<{
      __typename?: "RelevantIissueNode"
      maturityLevel?: LevelChoicesEnum | null
      gabAnalysisResults?: string | null
      summary?: string | null
      recommendations?: Array<string> | null
      iissue?: {
        __typename?: "IIssueNode"
        id: string
        name?: string | null
        category?: CategoryChoicesEnum | null
        issue?: { __typename?: "IssueNode"; description?: string | null } | null
      } | null
      sources?: Array<{
        __typename?: "RelevantIissueSourceNode"
        sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
        source?: string | null
      } | null> | null
    } | null> | null
  } | null
  publishedEvaluations?: Array<{
    __typename?: "EvaluationNode"
    id: string
    status?: StatusChoicesEnum | null
    completedAt?: string | null
    evaluationYear?: number | null
    overallRecommendations?: Array<string> | null
    environmentalMaturity?: LevelChoicesEnum | null
    socialMaturity?: LevelChoicesEnum | null
    governanceMaturity?: LevelChoicesEnum | null
    overallSummary?: string | null
    inputs?: {
      __typename?: "EvaluationInputsNode"
      dataDepth?: {
        __typename?: "DataDepthNode"
        baseData?: boolean | null
        publicData?: boolean | null
        selfAssessment?: boolean | null
        advanced?: boolean | null
      } | null
      evaluationDepth?: {
        __typename?: "EvaluationDepthNode"
        esgRiskMapping?: boolean | null
        aiEvaluations?: boolean | null
        esgRecommendations?: boolean | null
        enrichedEvaluation?: boolean | null
        fullEvaluation?: boolean | null
      } | null
      sources?: {
        __typename?: "EvaluationSourcesNode"
        businessRegistry?: boolean | null
        annualReport?: boolean | null
        website?: boolean | null
        media?: boolean | null
        questionnaire?: boolean | null
        proprietaryData?: boolean | null
      } | null
    } | null
    company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
  } | null> | null
  businessActivities: Array<{
    __typename?: "BusinessActivityNode"
    id: string
    euTaxonomyEligible?: boolean | null
    naceCode?: {
      __typename?: "NaceCodeNode"
      id: string
      label?: string | null
      labelEt?: string | null
      code?: string | null
      overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    } | null
    latestReportItem?: {
      __typename?: "AnnualReportItemNode"
      isMainActivity?: boolean | null
      businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
    } | null
  }>
  annualReports: Array<{
    __typename?: "AnnualReportNode"
    financialYear?: number | null
    averageFullTimeEmployeesConsolidated?: number | null
    companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
    netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
    totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
  }>
}

export type NaceCodeItemFragment = {
  __typename?: "NaceCodeNode"
  id: string
  label?: string | null
  code?: string | null
  overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
  environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
  socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
  parent?: {
    __typename?: "NaceCodeNode"
    id: string
    label?: string | null
    code?: string | null
    overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    parent?: { __typename?: "NaceCodeNode"; id: string } | null
  } | null
}

export type PortalUserItemFragment = {
  __typename?: "PortalUserNode"
  id: string
  email: string
  lastName?: string | null
  firstName?: string | null
  hasGivenConsent: boolean
  isActive: boolean
  dateJoined: string
  lastLogin?: string | null
  createdAt?: string | null
  updatedAt?: string | null
  tenant?: { __typename?: "TenantNode"; id: string } | null
  company?: { __typename?: "CompanyNode"; id: string } | null
  roles: Array<{ __typename?: "RoleAssignmentNode"; role?: PortalUserRoleEnum | null }>
}

export type SurveyItemFragment = {
  __typename?: "SurveyNode"
  id: string
  updatedAt?: string | null
  createdAt?: string | null
  submittedAt?: string | null
  financialYear?: number | null
  deadline?: string | null
  template?: SurveyTemplateEnum | null
  status?: SurveyStatusEnum | null
  readyForEdit: boolean
  prefilledData?: any | null
  lastReminder?: string | null
  formData?: any | null
  statusChangedAt?: string | null
  progress?: number | null
  langConsent?: string | null
  tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
  company?: { __typename?: "CompanyNode"; id: string; name: string } | null
  batch?: { __typename?: "SurveyBatchNode"; id: string } | null
  inputDocuments?: Array<{
    __typename?: "SurveyDocumentNode"
    id: string
    file: string
    fileName: string
    fileSize: number
    isAttachment: boolean
  } | null> | null
}

export type SurveyBatchItemFragment = {
  __typename?: "SurveyBatchNode"
  id: string
  name?: string | null
  description: string
  template?: IportalSurveyBatchTemplateChoices | null
  tenant?: { __typename?: "TenantNode"; id: string } | null
}

export type TenantItemFragment = {
  __typename?: "TenantNode"
  id: string
  name?: string | null
  createdAt?: string | null
  updatedAt?: string | null
}

export type WatchlistEntryItemFragment = {
  __typename?: "WatchlistEntryNode"
  id: string
  company: {
    __typename?: "CompanyNode"
    id: string
    name: string
    about?: string | null
    aboutEn?: string | null
    aboutEt?: string | null
    registrationNumber?: string | null
    status?: CompanyStatusChoicesEnum | null
    country?: IorderCompanyCountryChoices | null
    employeeNumber?: number | null
    street?: string | null
    houseNumber?: string | null
    postalCode?: string | null
    city?: string | null
    registrationDate?: string | null
    urls: Array<{ __typename?: "URLNode"; url: string }>
    latestGhgEmission?: Array<{
      __typename?: "CompanyEmissionNode"
      id: string
      year?: number | null
      source?: string | null
      basis?: string | null
      multiplier?: string | null
      unit?: string | null
      value?: string | null
      scope?: string | null
    } | null> | null
    mediaSources?: Array<{
      __typename?: "MediaSourceDataNode"
      id: string
      url: string
      title: string
      summary: string
      story?: string | null
      publishedAt?: string | null
      startedAt: string
      completedAt?: string | null
      esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
      sentiment?: string | null
      tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
      metaData?: any | null
    } | null> | null
    latestEsgMaterialityMapping?: {
      __typename?: "MaterialityMappingNode"
      executionDate?: string | null
      relevantPositives?: Array<{
        __typename?: "RelevantPositiveNode"
        summary?: string | null
        maturityLevel?: string | null
      } | null> | null
      relevantIssues?: Array<{
        __typename?: "RelevantIissueNode"
        maturityLevel?: LevelChoicesEnum | null
        gabAnalysisResults?: string | null
        summary?: string | null
        recommendations?: Array<string> | null
        iissue?: {
          __typename?: "IIssueNode"
          id: string
          name?: string | null
          category?: CategoryChoicesEnum | null
          issue?: { __typename?: "IssueNode"; description?: string | null } | null
        } | null
        sources?: Array<{
          __typename?: "RelevantIissueSourceNode"
          sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
          source?: string | null
        } | null> | null
      } | null> | null
    } | null
    publishedEvaluations?: Array<{
      __typename?: "EvaluationNode"
      id: string
      status?: StatusChoicesEnum | null
      completedAt?: string | null
      evaluationYear?: number | null
      overallRecommendations?: Array<string> | null
      environmentalMaturity?: LevelChoicesEnum | null
      socialMaturity?: LevelChoicesEnum | null
      governanceMaturity?: LevelChoicesEnum | null
      overallSummary?: string | null
      inputs?: {
        __typename?: "EvaluationInputsNode"
        dataDepth?: {
          __typename?: "DataDepthNode"
          baseData?: boolean | null
          publicData?: boolean | null
          selfAssessment?: boolean | null
          advanced?: boolean | null
        } | null
        evaluationDepth?: {
          __typename?: "EvaluationDepthNode"
          esgRiskMapping?: boolean | null
          aiEvaluations?: boolean | null
          esgRecommendations?: boolean | null
          enrichedEvaluation?: boolean | null
          fullEvaluation?: boolean | null
        } | null
        sources?: {
          __typename?: "EvaluationSourcesNode"
          businessRegistry?: boolean | null
          annualReport?: boolean | null
          website?: boolean | null
          media?: boolean | null
          questionnaire?: boolean | null
          proprietaryData?: boolean | null
        } | null
      } | null
      company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
    } | null> | null
    businessActivities: Array<{
      __typename?: "BusinessActivityNode"
      id: string
      euTaxonomyEligible?: boolean | null
      naceCode?: {
        __typename?: "NaceCodeNode"
        id: string
        label?: string | null
        labelEt?: string | null
        code?: string | null
        overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      } | null
      latestReportItem?: {
        __typename?: "AnnualReportItemNode"
        isMainActivity?: boolean | null
        businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      } | null
    }>
    annualReports: Array<{
      __typename?: "AnnualReportNode"
      financialYear?: number | null
      averageFullTimeEmployeesConsolidated?: number | null
      companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
    }>
  }
  contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
}

export type WatchlistItemFragment = {
  __typename?: "WatchlistNode"
  id: string
  name: string
  updatedAt?: string | null
  createdAt?: string | null
  tenant?: { __typename?: "TenantNode"; id: string } | null
  portalUser?: { __typename?: "PortalUserNode"; id: string } | null
  entries: Array<{
    __typename?: "WatchlistEntryNode"
    id: string
    company: {
      __typename?: "CompanyNode"
      id: string
      name: string
      about?: string | null
      aboutEn?: string | null
      aboutEt?: string | null
      registrationNumber?: string | null
      status?: CompanyStatusChoicesEnum | null
      country?: IorderCompanyCountryChoices | null
      employeeNumber?: number | null
      street?: string | null
      houseNumber?: string | null
      postalCode?: string | null
      city?: string | null
      registrationDate?: string | null
      urls: Array<{ __typename?: "URLNode"; url: string }>
      latestGhgEmission?: Array<{
        __typename?: "CompanyEmissionNode"
        id: string
        year?: number | null
        source?: string | null
        basis?: string | null
        multiplier?: string | null
        unit?: string | null
        value?: string | null
        scope?: string | null
      } | null> | null
      mediaSources?: Array<{
        __typename?: "MediaSourceDataNode"
        id: string
        url: string
        title: string
        summary: string
        story?: string | null
        publishedAt?: string | null
        startedAt: string
        completedAt?: string | null
        esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
        sentiment?: string | null
        tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
        metaData?: any | null
      } | null> | null
      latestEsgMaterialityMapping?: {
        __typename?: "MaterialityMappingNode"
        executionDate?: string | null
        relevantPositives?: Array<{
          __typename?: "RelevantPositiveNode"
          summary?: string | null
          maturityLevel?: string | null
        } | null> | null
        relevantIssues?: Array<{
          __typename?: "RelevantIissueNode"
          maturityLevel?: LevelChoicesEnum | null
          gabAnalysisResults?: string | null
          summary?: string | null
          recommendations?: Array<string> | null
          iissue?: {
            __typename?: "IIssueNode"
            id: string
            name?: string | null
            category?: CategoryChoicesEnum | null
            issue?: { __typename?: "IssueNode"; description?: string | null } | null
          } | null
          sources?: Array<{
            __typename?: "RelevantIissueSourceNode"
            sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
            source?: string | null
          } | null> | null
        } | null> | null
      } | null
      publishedEvaluations?: Array<{
        __typename?: "EvaluationNode"
        id: string
        status?: StatusChoicesEnum | null
        completedAt?: string | null
        evaluationYear?: number | null
        overallRecommendations?: Array<string> | null
        environmentalMaturity?: LevelChoicesEnum | null
        socialMaturity?: LevelChoicesEnum | null
        governanceMaturity?: LevelChoicesEnum | null
        overallSummary?: string | null
        inputs?: {
          __typename?: "EvaluationInputsNode"
          dataDepth?: {
            __typename?: "DataDepthNode"
            baseData?: boolean | null
            publicData?: boolean | null
            selfAssessment?: boolean | null
            advanced?: boolean | null
          } | null
          evaluationDepth?: {
            __typename?: "EvaluationDepthNode"
            esgRiskMapping?: boolean | null
            aiEvaluations?: boolean | null
            esgRecommendations?: boolean | null
            enrichedEvaluation?: boolean | null
            fullEvaluation?: boolean | null
          } | null
          sources?: {
            __typename?: "EvaluationSourcesNode"
            businessRegistry?: boolean | null
            annualReport?: boolean | null
            website?: boolean | null
            media?: boolean | null
            questionnaire?: boolean | null
            proprietaryData?: boolean | null
          } | null
        } | null
        company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
      } | null> | null
      businessActivities: Array<{
        __typename?: "BusinessActivityNode"
        id: string
        euTaxonomyEligible?: boolean | null
        naceCode?: {
          __typename?: "NaceCodeNode"
          id: string
          label?: string | null
          labelEt?: string | null
          code?: string | null
          overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        } | null
        latestReportItem?: {
          __typename?: "AnnualReportItemNode"
          isMainActivity?: boolean | null
          businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
        } | null
      }>
      annualReports: Array<{
        __typename?: "AnnualReportNode"
        financialYear?: number | null
        averageFullTimeEmployeesConsolidated?: number | null
        companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
        netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
        totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      }>
    }
    contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
  }>
}

export type AddEntryToWatchlistMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  watchlistId: Scalars["ID"]["input"]
  companyId: Scalars["ID"]["input"]
  website?: InputMaybe<Scalars["String"]["input"]>
}>

export type AddEntryToWatchlistMutationMutation = {
  __typename?: "Mutation"
  addEntryToWatchlist?: {
    __typename?: "AddEntryToWatchlist"
    entry?: {
      __typename?: "WatchlistEntryNode"
      id: string
      company: {
        __typename?: "CompanyNode"
        id: string
        name: string
        about?: string | null
        aboutEn?: string | null
        aboutEt?: string | null
        registrationNumber?: string | null
        status?: CompanyStatusChoicesEnum | null
        country?: IorderCompanyCountryChoices | null
        employeeNumber?: number | null
        street?: string | null
        houseNumber?: string | null
        postalCode?: string | null
        city?: string | null
        registrationDate?: string | null
        urls: Array<{ __typename?: "URLNode"; url: string }>
        latestGhgEmission?: Array<{
          __typename?: "CompanyEmissionNode"
          id: string
          year?: number | null
          source?: string | null
          basis?: string | null
          multiplier?: string | null
          unit?: string | null
          value?: string | null
          scope?: string | null
        } | null> | null
        mediaSources?: Array<{
          __typename?: "MediaSourceDataNode"
          id: string
          url: string
          title: string
          summary: string
          story?: string | null
          publishedAt?: string | null
          startedAt: string
          completedAt?: string | null
          esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
          sentiment?: string | null
          tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
          metaData?: any | null
        } | null> | null
        latestEsgMaterialityMapping?: {
          __typename?: "MaterialityMappingNode"
          executionDate?: string | null
          relevantPositives?: Array<{
            __typename?: "RelevantPositiveNode"
            summary?: string | null
            maturityLevel?: string | null
          } | null> | null
          relevantIssues?: Array<{
            __typename?: "RelevantIissueNode"
            maturityLevel?: LevelChoicesEnum | null
            gabAnalysisResults?: string | null
            summary?: string | null
            recommendations?: Array<string> | null
            iissue?: {
              __typename?: "IIssueNode"
              id: string
              name?: string | null
              category?: CategoryChoicesEnum | null
              issue?: { __typename?: "IssueNode"; description?: string | null } | null
            } | null
            sources?: Array<{
              __typename?: "RelevantIissueSourceNode"
              sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
              source?: string | null
            } | null> | null
          } | null> | null
        } | null
        publishedEvaluations?: Array<{
          __typename?: "EvaluationNode"
          id: string
          status?: StatusChoicesEnum | null
          completedAt?: string | null
          evaluationYear?: number | null
          overallRecommendations?: Array<string> | null
          environmentalMaturity?: LevelChoicesEnum | null
          socialMaturity?: LevelChoicesEnum | null
          governanceMaturity?: LevelChoicesEnum | null
          overallSummary?: string | null
          inputs?: {
            __typename?: "EvaluationInputsNode"
            dataDepth?: {
              __typename?: "DataDepthNode"
              baseData?: boolean | null
              publicData?: boolean | null
              selfAssessment?: boolean | null
              advanced?: boolean | null
            } | null
            evaluationDepth?: {
              __typename?: "EvaluationDepthNode"
              esgRiskMapping?: boolean | null
              aiEvaluations?: boolean | null
              esgRecommendations?: boolean | null
              enrichedEvaluation?: boolean | null
              fullEvaluation?: boolean | null
            } | null
            sources?: {
              __typename?: "EvaluationSourcesNode"
              businessRegistry?: boolean | null
              annualReport?: boolean | null
              website?: boolean | null
              media?: boolean | null
              questionnaire?: boolean | null
              proprietaryData?: boolean | null
            } | null
          } | null
          company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
        } | null> | null
        businessActivities: Array<{
          __typename?: "BusinessActivityNode"
          id: string
          euTaxonomyEligible?: boolean | null
          naceCode?: {
            __typename?: "NaceCodeNode"
            id: string
            label?: string | null
            labelEt?: string | null
            code?: string | null
            overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          } | null
          latestReportItem?: {
            __typename?: "AnnualReportItemNode"
            isMainActivity?: boolean | null
            businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          } | null
        }>
        annualReports: Array<{
          __typename?: "AnnualReportNode"
          financialYear?: number | null
          averageFullTimeEmployeesConsolidated?: number | null
          companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
        }>
      }
      contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
    } | null
  } | null
}

export type AutofillSurveyMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
  inputDocumentsUrls?: InputMaybe<
    Array<InputMaybe<Scalars["String"]["input"]>> | InputMaybe<Scalars["String"]["input"]>
  >
}>

export type AutofillSurveyMutationMutation = {
  __typename?: "Mutation"
  autofillSurvey?: {
    __typename?: "AutofillSurvey"
    survey?: {
      __typename?: "SurveyNode"
      id: string
      updatedAt?: string | null
      createdAt?: string | null
      submittedAt?: string | null
      financialYear?: number | null
      deadline?: string | null
      template?: SurveyTemplateEnum | null
      status?: SurveyStatusEnum | null
      readyForEdit: boolean
      prefilledData?: any | null
      lastReminder?: string | null
      formData?: any | null
      statusChangedAt?: string | null
      progress?: number | null
      langConsent?: string | null
      tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
      batch?: { __typename?: "SurveyBatchNode"; id: string } | null
      inputDocuments?: Array<{
        __typename?: "SurveyDocumentNode"
        id: string
        file: string
        fileName: string
        fileSize: number
        isAttachment: boolean
      } | null> | null
    } | null
  } | null
}

export type CreatePortalUserMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  email: Scalars["String"]["input"]
  firstName?: InputMaybe<Scalars["String"]["input"]>
  lastName?: InputMaybe<Scalars["String"]["input"]>
  isActive?: InputMaybe<Scalars["Boolean"]["input"]>
  roles: Array<RoleAssignmentInput> | RoleAssignmentInput
  companyId?: InputMaybe<Scalars["ID"]["input"]>
}>

export type CreatePortalUserMutationMutation = {
  __typename?: "Mutation"
  createPortalUser?: {
    __typename?: "CreatePortalUser"
    portalUser?: {
      __typename?: "PortalUserNode"
      id: string
      email: string
      lastName?: string | null
      firstName?: string | null
      hasGivenConsent: boolean
      isActive: boolean
      dateJoined: string
      lastLogin?: string | null
      createdAt?: string | null
      updatedAt?: string | null
      tenant?: { __typename?: "TenantNode"; id: string } | null
      company?: { __typename?: "CompanyNode"; id: string } | null
      roles: Array<{ __typename?: "RoleAssignmentNode"; role?: PortalUserRoleEnum | null }>
    } | null
  } | null
}

export type CreateSurveyMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  template: SurveyTemplateEnum
  batchId: Scalars["ID"]["input"]
  companySurveys: Array<CompanySurveysInput> | CompanySurveysInput
}>

export type CreateSurveyMutationMutation = {
  __typename?: "Mutation"
  createSurvey?: {
    __typename?: "CreateSurvey"
    surveys?: Array<{
      __typename?: "SurveyNode"
      id: string
      deadline?: string | null
      readyForEdit: boolean
      formData?: any | null
      prefilledData?: any | null
      progress?: number | null
      status?: SurveyStatusEnum | null
      financialYear?: number | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
    } | null> | null
  } | null
}

export type CreateSurveyBatchMutationMutationVariables = Exact<{
  name: Scalars["String"]["input"]
  template: SurveyTemplateEnum
  tenantId?: InputMaybe<Scalars["ID"]["input"]>
  description?: InputMaybe<Scalars["String"]["input"]>
}>

export type CreateSurveyBatchMutationMutation = {
  __typename?: "Mutation"
  createSurveyBatch?: {
    __typename?: "CreateSurveyBatch"
    surveyBatch?: {
      __typename?: "SurveyBatchNode"
      id: string
      name?: string | null
      description: string
      template?: IportalSurveyBatchTemplateChoices | null
      tenant?: { __typename?: "TenantNode"; id: string } | null
    } | null
  } | null
}

export type CreateWatchlistMutationMutationVariables = Exact<{
  name: Scalars["String"]["input"]
  portalUserId: Scalars["ID"]["input"]
  requestedBy: Scalars["String"]["input"]
}>

export type CreateWatchlistMutationMutation = {
  __typename?: "Mutation"
  createWatchlist?: {
    __typename?: "CreateWatchlist"
    watchlist?: {
      __typename?: "WatchlistNode"
      id: string
      name: string
      updatedAt?: string | null
      createdAt?: string | null
      tenant?: { __typename?: "TenantNode"; id: string } | null
      portalUser?: { __typename?: "PortalUserNode"; id: string } | null
      entries: Array<{
        __typename?: "WatchlistEntryNode"
        id: string
        company: {
          __typename?: "CompanyNode"
          id: string
          name: string
          about?: string | null
          aboutEn?: string | null
          aboutEt?: string | null
          registrationNumber?: string | null
          status?: CompanyStatusChoicesEnum | null
          country?: IorderCompanyCountryChoices | null
          employeeNumber?: number | null
          street?: string | null
          houseNumber?: string | null
          postalCode?: string | null
          city?: string | null
          registrationDate?: string | null
          urls: Array<{ __typename?: "URLNode"; url: string }>
          latestGhgEmission?: Array<{
            __typename?: "CompanyEmissionNode"
            id: string
            year?: number | null
            source?: string | null
            basis?: string | null
            multiplier?: string | null
            unit?: string | null
            value?: string | null
            scope?: string | null
          } | null> | null
          mediaSources?: Array<{
            __typename?: "MediaSourceDataNode"
            id: string
            url: string
            title: string
            summary: string
            story?: string | null
            publishedAt?: string | null
            startedAt: string
            completedAt?: string | null
            esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
            sentiment?: string | null
            tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
            metaData?: any | null
          } | null> | null
          latestEsgMaterialityMapping?: {
            __typename?: "MaterialityMappingNode"
            executionDate?: string | null
            relevantPositives?: Array<{
              __typename?: "RelevantPositiveNode"
              summary?: string | null
              maturityLevel?: string | null
            } | null> | null
            relevantIssues?: Array<{
              __typename?: "RelevantIissueNode"
              maturityLevel?: LevelChoicesEnum | null
              gabAnalysisResults?: string | null
              summary?: string | null
              recommendations?: Array<string> | null
              iissue?: {
                __typename?: "IIssueNode"
                id: string
                name?: string | null
                category?: CategoryChoicesEnum | null
                issue?: { __typename?: "IssueNode"; description?: string | null } | null
              } | null
              sources?: Array<{
                __typename?: "RelevantIissueSourceNode"
                sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
                source?: string | null
              } | null> | null
            } | null> | null
          } | null
          publishedEvaluations?: Array<{
            __typename?: "EvaluationNode"
            id: string
            status?: StatusChoicesEnum | null
            completedAt?: string | null
            evaluationYear?: number | null
            overallRecommendations?: Array<string> | null
            environmentalMaturity?: LevelChoicesEnum | null
            socialMaturity?: LevelChoicesEnum | null
            governanceMaturity?: LevelChoicesEnum | null
            overallSummary?: string | null
            inputs?: {
              __typename?: "EvaluationInputsNode"
              dataDepth?: {
                __typename?: "DataDepthNode"
                baseData?: boolean | null
                publicData?: boolean | null
                selfAssessment?: boolean | null
                advanced?: boolean | null
              } | null
              evaluationDepth?: {
                __typename?: "EvaluationDepthNode"
                esgRiskMapping?: boolean | null
                aiEvaluations?: boolean | null
                esgRecommendations?: boolean | null
                enrichedEvaluation?: boolean | null
                fullEvaluation?: boolean | null
              } | null
              sources?: {
                __typename?: "EvaluationSourcesNode"
                businessRegistry?: boolean | null
                annualReport?: boolean | null
                website?: boolean | null
                media?: boolean | null
                questionnaire?: boolean | null
                proprietaryData?: boolean | null
              } | null
            } | null
            company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
          } | null> | null
          businessActivities: Array<{
            __typename?: "BusinessActivityNode"
            id: string
            euTaxonomyEligible?: boolean | null
            naceCode?: {
              __typename?: "NaceCodeNode"
              id: string
              label?: string | null
              labelEt?: string | null
              code?: string | null
              overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
              environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
              socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            } | null
            latestReportItem?: {
              __typename?: "AnnualReportItemNode"
              isMainActivity?: boolean | null
              businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            } | null
          }>
          annualReports: Array<{
            __typename?: "AnnualReportNode"
            financialYear?: number | null
            averageFullTimeEmployeesConsolidated?: number | null
            companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          }>
        }
        contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
      }>
    } | null
  } | null
}

export type DeleteWatchlistMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  watchlistId: Scalars["ID"]["input"]
}>

export type DeleteWatchlistMutationMutation = {
  __typename?: "Mutation"
  deleteWatchlist?: { __typename?: "DeleteWatchlist"; success?: boolean | null } | null
}

export type RenameWatchlistMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  watchlistId: Scalars["ID"]["input"]
  name: Scalars["String"]["input"]
}>

export type RenameWatchlistMutationMutation = {
  __typename?: "Mutation"
  renameWatchlist?: {
    __typename?: "RenameWatchlist"
    watchlist?: {
      __typename?: "WatchlistNode"
      id: string
      name: string
      updatedAt?: string | null
      createdAt?: string | null
      tenant?: { __typename?: "TenantNode"; id: string } | null
      portalUser?: { __typename?: "PortalUserNode"; id: string } | null
      entries: Array<{
        __typename?: "WatchlistEntryNode"
        id: string
        company: {
          __typename?: "CompanyNode"
          id: string
          name: string
          about?: string | null
          aboutEn?: string | null
          aboutEt?: string | null
          registrationNumber?: string | null
          status?: CompanyStatusChoicesEnum | null
          country?: IorderCompanyCountryChoices | null
          employeeNumber?: number | null
          street?: string | null
          houseNumber?: string | null
          postalCode?: string | null
          city?: string | null
          registrationDate?: string | null
          urls: Array<{ __typename?: "URLNode"; url: string }>
          latestGhgEmission?: Array<{
            __typename?: "CompanyEmissionNode"
            id: string
            year?: number | null
            source?: string | null
            basis?: string | null
            multiplier?: string | null
            unit?: string | null
            value?: string | null
            scope?: string | null
          } | null> | null
          mediaSources?: Array<{
            __typename?: "MediaSourceDataNode"
            id: string
            url: string
            title: string
            summary: string
            story?: string | null
            publishedAt?: string | null
            startedAt: string
            completedAt?: string | null
            esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
            sentiment?: string | null
            tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
            metaData?: any | null
          } | null> | null
          latestEsgMaterialityMapping?: {
            __typename?: "MaterialityMappingNode"
            executionDate?: string | null
            relevantPositives?: Array<{
              __typename?: "RelevantPositiveNode"
              summary?: string | null
              maturityLevel?: string | null
            } | null> | null
            relevantIssues?: Array<{
              __typename?: "RelevantIissueNode"
              maturityLevel?: LevelChoicesEnum | null
              gabAnalysisResults?: string | null
              summary?: string | null
              recommendations?: Array<string> | null
              iissue?: {
                __typename?: "IIssueNode"
                id: string
                name?: string | null
                category?: CategoryChoicesEnum | null
                issue?: { __typename?: "IssueNode"; description?: string | null } | null
              } | null
              sources?: Array<{
                __typename?: "RelevantIissueSourceNode"
                sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
                source?: string | null
              } | null> | null
            } | null> | null
          } | null
          publishedEvaluations?: Array<{
            __typename?: "EvaluationNode"
            id: string
            status?: StatusChoicesEnum | null
            completedAt?: string | null
            evaluationYear?: number | null
            overallRecommendations?: Array<string> | null
            environmentalMaturity?: LevelChoicesEnum | null
            socialMaturity?: LevelChoicesEnum | null
            governanceMaturity?: LevelChoicesEnum | null
            overallSummary?: string | null
            inputs?: {
              __typename?: "EvaluationInputsNode"
              dataDepth?: {
                __typename?: "DataDepthNode"
                baseData?: boolean | null
                publicData?: boolean | null
                selfAssessment?: boolean | null
                advanced?: boolean | null
              } | null
              evaluationDepth?: {
                __typename?: "EvaluationDepthNode"
                esgRiskMapping?: boolean | null
                aiEvaluations?: boolean | null
                esgRecommendations?: boolean | null
                enrichedEvaluation?: boolean | null
                fullEvaluation?: boolean | null
              } | null
              sources?: {
                __typename?: "EvaluationSourcesNode"
                businessRegistry?: boolean | null
                annualReport?: boolean | null
                website?: boolean | null
                media?: boolean | null
                questionnaire?: boolean | null
                proprietaryData?: boolean | null
              } | null
            } | null
            company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
          } | null> | null
          businessActivities: Array<{
            __typename?: "BusinessActivityNode"
            id: string
            euTaxonomyEligible?: boolean | null
            naceCode?: {
              __typename?: "NaceCodeNode"
              id: string
              label?: string | null
              labelEt?: string | null
              code?: string | null
              overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
              environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
              socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            } | null
            latestReportItem?: {
              __typename?: "AnnualReportItemNode"
              isMainActivity?: boolean | null
              businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            } | null
          }>
          annualReports: Array<{
            __typename?: "AnnualReportNode"
            financialYear?: number | null
            averageFullTimeEmployeesConsolidated?: number | null
            companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          }>
        }
        contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
      }>
    } | null
  } | null
}

export type ResendSurveyMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
}>

export type ResendSurveyMutationMutation = {
  __typename?: "Mutation"
  resendSurvey?: {
    __typename?: "ResendSurvey"
    survey?: {
      __typename?: "SurveyNode"
      id: string
      updatedAt?: string | null
      createdAt?: string | null
      submittedAt?: string | null
      financialYear?: number | null
      deadline?: string | null
      template?: SurveyTemplateEnum | null
      status?: SurveyStatusEnum | null
      readyForEdit: boolean
      prefilledData?: any | null
      lastReminder?: string | null
      formData?: any | null
      statusChangedAt?: string | null
      progress?: number | null
      langConsent?: string | null
      tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
      batch?: { __typename?: "SurveyBatchNode"; id: string } | null
      inputDocuments?: Array<{
        __typename?: "SurveyDocumentNode"
        id: string
        file: string
        fileName: string
        fileSize: number
        isAttachment: boolean
      } | null> | null
    } | null
  } | null
}

export type ShareSurveyMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
  sharedWith: Array<SurveyContactInput> | SurveyContactInput
}>

export type ShareSurveyMutationMutation = {
  __typename?: "Mutation"
  shareSurvey?: {
    __typename?: "ShareSurvey"
    survey?: {
      __typename?: "SurveyNode"
      id: string
      updatedAt?: string | null
      createdAt?: string | null
      submittedAt?: string | null
      financialYear?: number | null
      deadline?: string | null
      template?: SurveyTemplateEnum | null
      status?: SurveyStatusEnum | null
      readyForEdit: boolean
      prefilledData?: any | null
      lastReminder?: string | null
      formData?: any | null
      statusChangedAt?: string | null
      progress?: number | null
      langConsent?: string | null
      tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
      batch?: { __typename?: "SurveyBatchNode"; id: string } | null
      inputDocuments?: Array<{
        __typename?: "SurveyDocumentNode"
        id: string
        file: string
        fileName: string
        fileSize: number
        isAttachment: boolean
      } | null> | null
    } | null
  } | null
}

export type SubmitSurveyMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  surveyId: Scalars["ID"]["input"]
}>

export type SubmitSurveyMutationMutation = {
  __typename?: "Mutation"
  submitSurvey?: {
    __typename?: "SubmitSurvey"
    survey?: {
      __typename?: "SurveyNode"
      id: string
      updatedAt?: string | null
      createdAt?: string | null
      submittedAt?: string | null
      financialYear?: number | null
      deadline?: string | null
      template?: SurveyTemplateEnum | null
      status?: SurveyStatusEnum | null
      readyForEdit: boolean
      prefilledData?: any | null
      lastReminder?: string | null
      formData?: any | null
      statusChangedAt?: string | null
      progress?: number | null
      langConsent?: string | null
      tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
      batch?: { __typename?: "SurveyBatchNode"; id: string } | null
      inputDocuments?: Array<{
        __typename?: "SurveyDocumentNode"
        id: string
        file: string
        fileName: string
        fileSize: number
        isAttachment: boolean
      } | null> | null
    } | null
  } | null
}

export type UpdateSurveyMutationMutationVariables = Exact<{
  surveyId: Scalars["ID"]["input"]
  status?: InputMaybe<SurveyStatusEnum>
  progress?: InputMaybe<Scalars["Float"]["input"]>
  formData?: InputMaybe<Scalars["JSONString"]["input"]>
}>

export type UpdateSurveyMutationMutation = {
  __typename?: "Mutation"
  updateSurvey?: {
    __typename?: "UpdateSurvey"
    survey?: {
      __typename?: "SurveyNode"
      id: string
      updatedAt?: string | null
      createdAt?: string | null
      submittedAt?: string | null
      financialYear?: number | null
      deadline?: string | null
      template?: SurveyTemplateEnum | null
      status?: SurveyStatusEnum | null
      readyForEdit: boolean
      prefilledData?: any | null
      lastReminder?: string | null
      formData?: any | null
      statusChangedAt?: string | null
      progress?: number | null
      langConsent?: string | null
      tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
      batch?: { __typename?: "SurveyBatchNode"; id: string } | null
      inputDocuments?: Array<{
        __typename?: "SurveyDocumentNode"
        id: string
        file: string
        fileName: string
        fileSize: number
        isAttachment: boolean
      } | null> | null
    } | null
  } | null
}

export type UpdateSurveyStatusMutationMutationVariables = Exact<{
  status: SurveyStatusEnum
  surveyIds: Array<Scalars["ID"]["input"]> | Scalars["ID"]["input"]
}>

export type UpdateSurveyStatusMutationMutation = {
  __typename?: "Mutation"
  updateSurveyStatus?: {
    __typename?: "UpdateSurveyStatus"
    surveys?: Array<{
      __typename?: "SurveyNode"
      id: string
      updatedAt?: string | null
      createdAt?: string | null
      submittedAt?: string | null
      financialYear?: number | null
      deadline?: string | null
      template?: SurveyTemplateEnum | null
      status?: SurveyStatusEnum | null
      readyForEdit: boolean
      prefilledData?: any | null
      lastReminder?: string | null
      formData?: any | null
      statusChangedAt?: string | null
      progress?: number | null
      langConsent?: string | null
      tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
      company?: { __typename?: "CompanyNode"; id: string; name: string } | null
      batch?: { __typename?: "SurveyBatchNode"; id: string } | null
      inputDocuments?: Array<{
        __typename?: "SurveyDocumentNode"
        id: string
        file: string
        fileName: string
        fileSize: number
        isAttachment: boolean
      } | null> | null
    } | null> | null
  } | null
}

export type UpdatePortalUserMutationMutationVariables = Exact<{
  requestedBy: Scalars["String"]["input"]
  id: Scalars["ID"]["input"]
  isActive?: InputMaybe<Scalars["Boolean"]["input"]>
  roles?: InputMaybe<Array<InputMaybe<RoleAssignmentInput>> | InputMaybe<RoleAssignmentInput>>
  firstName?: InputMaybe<Scalars["String"]["input"]>
  lastName?: InputMaybe<Scalars["String"]["input"]>
  hasGivenConsent?: InputMaybe<Scalars["Boolean"]["input"]>
}>

export type UpdatePortalUserMutationMutation = {
  __typename?: "Mutation"
  updatePortalUser?: {
    __typename?: "UpdatePortalUser"
    portalUser?: {
      __typename?: "PortalUserNode"
      id: string
      email: string
      lastName?: string | null
      firstName?: string | null
      hasGivenConsent: boolean
      isActive: boolean
      dateJoined: string
      lastLogin?: string | null
      createdAt?: string | null
      updatedAt?: string | null
      tenant?: { __typename?: "TenantNode"; id: string } | null
      company?: { __typename?: "CompanyNode"; id: string } | null
      roles: Array<{ __typename?: "RoleAssignmentNode"; role?: PortalUserRoleEnum | null }>
    } | null
  } | null
}

export type SaveFileToGcsMutationMutationVariables = Exact<{
  file: Scalars["Upload"]["input"]
  filename: Scalars["String"]["input"]
}>

export type SaveFileToGcsMutationMutation = {
  __typename?: "Mutation"
  saveFileToGcs?: {
    __typename?: "SaveFileToGCS"
    success?: boolean | null
    message?: string | null
    filePath?: string | null
    fileUrl?: string | null
  } | null
}

export type CompanyQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type CompanyQueryQuery = {
  __typename?: "Query"
  company?: {
    __typename?: "CompanyNode"
    id: string
    name: string
    about?: string | null
    aboutEn?: string | null
    aboutEt?: string | null
    registrationNumber?: string | null
    status?: CompanyStatusChoicesEnum | null
    country?: IorderCompanyCountryChoices | null
    employeeNumber?: number | null
    street?: string | null
    houseNumber?: string | null
    postalCode?: string | null
    city?: string | null
    registrationDate?: string | null
    urls: Array<{ __typename?: "URLNode"; url: string }>
    latestGhgEmission?: Array<{
      __typename?: "CompanyEmissionNode"
      id: string
      year?: number | null
      source?: string | null
      basis?: string | null
      multiplier?: string | null
      unit?: string | null
      value?: string | null
      scope?: string | null
    } | null> | null
    mediaSources?: Array<{
      __typename?: "MediaSourceDataNode"
      id: string
      url: string
      title: string
      summary: string
      story?: string | null
      publishedAt?: string | null
      startedAt: string
      completedAt?: string | null
      esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
      sentiment?: string | null
      tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
      metaData?: any | null
    } | null> | null
    latestEsgMaterialityMapping?: {
      __typename?: "MaterialityMappingNode"
      executionDate?: string | null
      relevantPositives?: Array<{
        __typename?: "RelevantPositiveNode"
        summary?: string | null
        maturityLevel?: string | null
      } | null> | null
      relevantIssues?: Array<{
        __typename?: "RelevantIissueNode"
        maturityLevel?: LevelChoicesEnum | null
        gabAnalysisResults?: string | null
        summary?: string | null
        recommendations?: Array<string> | null
        iissue?: {
          __typename?: "IIssueNode"
          id: string
          name?: string | null
          category?: CategoryChoicesEnum | null
          issue?: { __typename?: "IssueNode"; description?: string | null } | null
        } | null
        sources?: Array<{
          __typename?: "RelevantIissueSourceNode"
          sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
          source?: string | null
        } | null> | null
      } | null> | null
    } | null
    publishedEvaluations?: Array<{
      __typename?: "EvaluationNode"
      id: string
      status?: StatusChoicesEnum | null
      completedAt?: string | null
      evaluationYear?: number | null
      overallRecommendations?: Array<string> | null
      environmentalMaturity?: LevelChoicesEnum | null
      socialMaturity?: LevelChoicesEnum | null
      governanceMaturity?: LevelChoicesEnum | null
      overallSummary?: string | null
      inputs?: {
        __typename?: "EvaluationInputsNode"
        dataDepth?: {
          __typename?: "DataDepthNode"
          baseData?: boolean | null
          publicData?: boolean | null
          selfAssessment?: boolean | null
          advanced?: boolean | null
        } | null
        evaluationDepth?: {
          __typename?: "EvaluationDepthNode"
          esgRiskMapping?: boolean | null
          aiEvaluations?: boolean | null
          esgRecommendations?: boolean | null
          enrichedEvaluation?: boolean | null
          fullEvaluation?: boolean | null
        } | null
        sources?: {
          __typename?: "EvaluationSourcesNode"
          businessRegistry?: boolean | null
          annualReport?: boolean | null
          website?: boolean | null
          media?: boolean | null
          questionnaire?: boolean | null
          proprietaryData?: boolean | null
        } | null
      } | null
      company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
    } | null> | null
    businessActivities: Array<{
      __typename?: "BusinessActivityNode"
      id: string
      euTaxonomyEligible?: boolean | null
      naceCode?: {
        __typename?: "NaceCodeNode"
        id: string
        label?: string | null
        labelEt?: string | null
        code?: string | null
        overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      } | null
      latestReportItem?: {
        __typename?: "AnnualReportItemNode"
        isMainActivity?: boolean | null
        businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      } | null
    }>
    annualReports: Array<{
      __typename?: "AnnualReportNode"
      financialYear?: number | null
      averageFullTimeEmployeesConsolidated?: number | null
      companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
      totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
    }>
  } | null
}

export type CompaniesQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
  country?: InputMaybe<IorderCompanyCountryChoices>
}>

export type CompaniesQueryQuery = {
  __typename?: "Query"
  companies?: {
    __typename?: "CompanyNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "CompanyNodeEdge"
      node?: {
        __typename?: "CompanyNode"
        id: string
        name: string
        about?: string | null
        aboutEn?: string | null
        aboutEt?: string | null
        registrationNumber?: string | null
        status?: CompanyStatusChoicesEnum | null
        country?: IorderCompanyCountryChoices | null
        employeeNumber?: number | null
        street?: string | null
        houseNumber?: string | null
        postalCode?: string | null
        city?: string | null
        registrationDate?: string | null
        urls: Array<{ __typename?: "URLNode"; url: string }>
        latestGhgEmission?: Array<{
          __typename?: "CompanyEmissionNode"
          id: string
          year?: number | null
          source?: string | null
          basis?: string | null
          multiplier?: string | null
          unit?: string | null
          value?: string | null
          scope?: string | null
        } | null> | null
        mediaSources?: Array<{
          __typename?: "MediaSourceDataNode"
          id: string
          url: string
          title: string
          summary: string
          story?: string | null
          publishedAt?: string | null
          startedAt: string
          completedAt?: string | null
          esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
          sentiment?: string | null
          tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
          metaData?: any | null
        } | null> | null
        latestEsgMaterialityMapping?: {
          __typename?: "MaterialityMappingNode"
          executionDate?: string | null
          relevantPositives?: Array<{
            __typename?: "RelevantPositiveNode"
            summary?: string | null
            maturityLevel?: string | null
          } | null> | null
          relevantIssues?: Array<{
            __typename?: "RelevantIissueNode"
            maturityLevel?: LevelChoicesEnum | null
            gabAnalysisResults?: string | null
            summary?: string | null
            recommendations?: Array<string> | null
            iissue?: {
              __typename?: "IIssueNode"
              id: string
              name?: string | null
              category?: CategoryChoicesEnum | null
              issue?: { __typename?: "IssueNode"; description?: string | null } | null
            } | null
            sources?: Array<{
              __typename?: "RelevantIissueSourceNode"
              sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
              source?: string | null
            } | null> | null
          } | null> | null
        } | null
        publishedEvaluations?: Array<{
          __typename?: "EvaluationNode"
          id: string
          status?: StatusChoicesEnum | null
          completedAt?: string | null
          evaluationYear?: number | null
          overallRecommendations?: Array<string> | null
          environmentalMaturity?: LevelChoicesEnum | null
          socialMaturity?: LevelChoicesEnum | null
          governanceMaturity?: LevelChoicesEnum | null
          overallSummary?: string | null
          inputs?: {
            __typename?: "EvaluationInputsNode"
            dataDepth?: {
              __typename?: "DataDepthNode"
              baseData?: boolean | null
              publicData?: boolean | null
              selfAssessment?: boolean | null
              advanced?: boolean | null
            } | null
            evaluationDepth?: {
              __typename?: "EvaluationDepthNode"
              esgRiskMapping?: boolean | null
              aiEvaluations?: boolean | null
              esgRecommendations?: boolean | null
              enrichedEvaluation?: boolean | null
              fullEvaluation?: boolean | null
            } | null
            sources?: {
              __typename?: "EvaluationSourcesNode"
              businessRegistry?: boolean | null
              annualReport?: boolean | null
              website?: boolean | null
              media?: boolean | null
              questionnaire?: boolean | null
              proprietaryData?: boolean | null
            } | null
          } | null
          company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
        } | null> | null
        businessActivities: Array<{
          __typename?: "BusinessActivityNode"
          id: string
          euTaxonomyEligible?: boolean | null
          naceCode?: {
            __typename?: "NaceCodeNode"
            id: string
            label?: string | null
            labelEt?: string | null
            code?: string | null
            overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          } | null
          latestReportItem?: {
            __typename?: "AnnualReportItemNode"
            isMainActivity?: boolean | null
            businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          } | null
        }>
        annualReports: Array<{
          __typename?: "AnnualReportNode"
          financialYear?: number | null
          averageFullTimeEmployeesConsolidated?: number | null
          companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
        }>
      } | null
    } | null>
  } | null
}

export type EvaluationQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type EvaluationQueryQuery = {
  __typename?: "Query"
  evaluation?: {
    __typename?: "EvaluationNode"
    id: string
    status?: StatusChoicesEnum | null
    completedAt?: string | null
    evaluationYear?: number | null
    overallRecommendations?: Array<string> | null
    environmentalMaturity?: LevelChoicesEnum | null
    socialMaturity?: LevelChoicesEnum | null
    governanceMaturity?: LevelChoicesEnum | null
    overallSummary?: string | null
    inputs?: {
      __typename?: "EvaluationInputsNode"
      dataDepth?: {
        __typename?: "DataDepthNode"
        baseData?: boolean | null
        publicData?: boolean | null
        selfAssessment?: boolean | null
        advanced?: boolean | null
      } | null
      evaluationDepth?: {
        __typename?: "EvaluationDepthNode"
        esgRiskMapping?: boolean | null
        aiEvaluations?: boolean | null
        esgRecommendations?: boolean | null
        enrichedEvaluation?: boolean | null
        fullEvaluation?: boolean | null
      } | null
      sources?: {
        __typename?: "EvaluationSourcesNode"
        businessRegistry?: boolean | null
        annualReport?: boolean | null
        website?: boolean | null
        media?: boolean | null
        questionnaire?: boolean | null
        proprietaryData?: boolean | null
      } | null
    } | null
    company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
  } | null
}

export type EvaluationsQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type EvaluationsQueryQuery = {
  __typename?: "Query"
  evaluations?: {
    __typename?: "EvaluationNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "EvaluationNodeEdge"
      node?: {
        __typename?: "EvaluationNode"
        id: string
        status?: StatusChoicesEnum | null
        completedAt?: string | null
        evaluationYear?: number | null
        overallRecommendations?: Array<string> | null
        environmentalMaturity?: LevelChoicesEnum | null
        socialMaturity?: LevelChoicesEnum | null
        governanceMaturity?: LevelChoicesEnum | null
        overallSummary?: string | null
        inputs?: {
          __typename?: "EvaluationInputsNode"
          dataDepth?: {
            __typename?: "DataDepthNode"
            baseData?: boolean | null
            publicData?: boolean | null
            selfAssessment?: boolean | null
            advanced?: boolean | null
          } | null
          evaluationDepth?: {
            __typename?: "EvaluationDepthNode"
            esgRiskMapping?: boolean | null
            aiEvaluations?: boolean | null
            esgRecommendations?: boolean | null
            enrichedEvaluation?: boolean | null
            fullEvaluation?: boolean | null
          } | null
          sources?: {
            __typename?: "EvaluationSourcesNode"
            businessRegistry?: boolean | null
            annualReport?: boolean | null
            website?: boolean | null
            media?: boolean | null
            questionnaire?: boolean | null
            proprietaryData?: boolean | null
          } | null
        } | null
        company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
      } | null
    } | null>
  } | null
}

export type NaceCodeQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type NaceCodeQueryQuery = {
  __typename?: "Query"
  naceCode?: {
    __typename?: "NaceCodeNode"
    id: string
    label?: string | null
    code?: string | null
    overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
    parent?: {
      __typename?: "NaceCodeNode"
      id: string
      label?: string | null
      code?: string | null
      overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
      parent?: { __typename?: "NaceCodeNode"; id: string } | null
    } | null
  } | null
}

export type NaceCodesQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type NaceCodesQueryQuery = {
  __typename?: "Query"
  naceCodes?: {
    __typename?: "NaceCodeNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "NaceCodeNodeEdge"
      node?: {
        __typename?: "NaceCodeNode"
        id: string
        label?: string | null
        code?: string | null
        overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
        parent?: {
          __typename?: "NaceCodeNode"
          id: string
          label?: string | null
          code?: string | null
          overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          parent?: { __typename?: "NaceCodeNode"; id: string } | null
        } | null
      } | null
    } | null>
  } | null
}

export type PortalUserQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type PortalUserQueryQuery = {
  __typename?: "Query"
  portalUser?: {
    __typename?: "PortalUserNode"
    id: string
    email: string
    lastName?: string | null
    firstName?: string | null
    hasGivenConsent: boolean
    isActive: boolean
    dateJoined: string
    lastLogin?: string | null
    createdAt?: string | null
    updatedAt?: string | null
    tenant?: { __typename?: "TenantNode"; id: string } | null
    company?: { __typename?: "CompanyNode"; id: string } | null
    roles: Array<{ __typename?: "RoleAssignmentNode"; role?: PortalUserRoleEnum | null }>
  } | null
}

export type PortalUsersQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type PortalUsersQueryQuery = {
  __typename?: "Query"
  portalUsers?: {
    __typename?: "PortalUserNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "PortalUserNodeEdge"
      node?: {
        __typename?: "PortalUserNode"
        id: string
        email: string
        lastName?: string | null
        firstName?: string | null
        hasGivenConsent: boolean
        isActive: boolean
        dateJoined: string
        lastLogin?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        tenant?: { __typename?: "TenantNode"; id: string } | null
        company?: { __typename?: "CompanyNode"; id: string } | null
        roles: Array<{ __typename?: "RoleAssignmentNode"; role?: PortalUserRoleEnum | null }>
      } | null
    } | null>
  } | null
}

export type SurveyQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type SurveyQueryQuery = {
  __typename?: "Query"
  survey?: {
    __typename?: "SurveyNode"
    id: string
    updatedAt?: string | null
    createdAt?: string | null
    submittedAt?: string | null
    financialYear?: number | null
    deadline?: string | null
    template?: SurveyTemplateEnum | null
    status?: SurveyStatusEnum | null
    readyForEdit: boolean
    prefilledData?: any | null
    lastReminder?: string | null
    formData?: any | null
    statusChangedAt?: string | null
    progress?: number | null
    langConsent?: string | null
    tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
    company?: { __typename?: "CompanyNode"; id: string; name: string } | null
    batch?: { __typename?: "SurveyBatchNode"; id: string } | null
    inputDocuments?: Array<{
      __typename?: "SurveyDocumentNode"
      id: string
      file: string
      fileName: string
      fileSize: number
      isAttachment: boolean
    } | null> | null
  } | null
}

export type SurveysQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type SurveysQueryQuery = {
  __typename?: "Query"
  surveys?: {
    __typename?: "SurveyNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "SurveyNodeEdge"
      node?: {
        __typename?: "SurveyNode"
        id: string
        updatedAt?: string | null
        createdAt?: string | null
        submittedAt?: string | null
        financialYear?: number | null
        deadline?: string | null
        template?: SurveyTemplateEnum | null
        status?: SurveyStatusEnum | null
        readyForEdit: boolean
        prefilledData?: any | null
        lastReminder?: string | null
        formData?: any | null
        statusChangedAt?: string | null
        progress?: number | null
        langConsent?: string | null
        tenant?: { __typename?: "TenantNode"; id: string; name?: string | null } | null
        company?: { __typename?: "CompanyNode"; id: string; name: string } | null
        batch?: { __typename?: "SurveyBatchNode"; id: string } | null
        inputDocuments?: Array<{
          __typename?: "SurveyDocumentNode"
          id: string
          file: string
          fileName: string
          fileSize: number
          isAttachment: boolean
        } | null> | null
      } | null
    } | null>
  } | null
}

export type SurveyBatchQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type SurveyBatchQueryQuery = {
  __typename?: "Query"
  surveyBatch?: {
    __typename?: "SurveyBatchNode"
    id: string
    name?: string | null
    description: string
    template?: IportalSurveyBatchTemplateChoices | null
    tenant?: { __typename?: "TenantNode"; id: string } | null
  } | null
}

export type SurveyBatchesQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type SurveyBatchesQueryQuery = {
  __typename?: "Query"
  surveyBatches?: {
    __typename?: "SurveyBatchNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "SurveyBatchNodeEdge"
      node?: {
        __typename?: "SurveyBatchNode"
        id: string
        name?: string | null
        description: string
        template?: IportalSurveyBatchTemplateChoices | null
        tenant?: { __typename?: "TenantNode"; id: string } | null
      } | null
    } | null>
  } | null
}

export type TenantQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type TenantQueryQuery = {
  __typename?: "Query"
  tenant?: {
    __typename?: "TenantNode"
    id: string
    name?: string | null
    createdAt?: string | null
    updatedAt?: string | null
  } | null
}

export type TenantsQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type TenantsQueryQuery = {
  __typename?: "Query"
  tenants?: {
    __typename?: "TenantNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "TenantNodeEdge"
      node?: {
        __typename?: "TenantNode"
        id: string
        name?: string | null
        createdAt?: string | null
        updatedAt?: string | null
      } | null
    } | null>
  } | null
}

export type WatchlistQueryQueryVariables = Exact<{
  id: Scalars["ID"]["input"]
}>

export type WatchlistQueryQuery = {
  __typename?: "Query"
  watchlist?: {
    __typename?: "WatchlistNode"
    id: string
    name: string
    updatedAt?: string | null
    createdAt?: string | null
    tenant?: { __typename?: "TenantNode"; id: string } | null
    portalUser?: { __typename?: "PortalUserNode"; id: string } | null
    entries: Array<{
      __typename?: "WatchlistEntryNode"
      id: string
      company: {
        __typename?: "CompanyNode"
        id: string
        name: string
        about?: string | null
        aboutEn?: string | null
        aboutEt?: string | null
        registrationNumber?: string | null
        status?: CompanyStatusChoicesEnum | null
        country?: IorderCompanyCountryChoices | null
        employeeNumber?: number | null
        street?: string | null
        houseNumber?: string | null
        postalCode?: string | null
        city?: string | null
        registrationDate?: string | null
        urls: Array<{ __typename?: "URLNode"; url: string }>
        latestGhgEmission?: Array<{
          __typename?: "CompanyEmissionNode"
          id: string
          year?: number | null
          source?: string | null
          basis?: string | null
          multiplier?: string | null
          unit?: string | null
          value?: string | null
          scope?: string | null
        } | null> | null
        mediaSources?: Array<{
          __typename?: "MediaSourceDataNode"
          id: string
          url: string
          title: string
          summary: string
          story?: string | null
          publishedAt?: string | null
          startedAt: string
          completedAt?: string | null
          esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
          sentiment?: string | null
          tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
          metaData?: any | null
        } | null> | null
        latestEsgMaterialityMapping?: {
          __typename?: "MaterialityMappingNode"
          executionDate?: string | null
          relevantPositives?: Array<{
            __typename?: "RelevantPositiveNode"
            summary?: string | null
            maturityLevel?: string | null
          } | null> | null
          relevantIssues?: Array<{
            __typename?: "RelevantIissueNode"
            maturityLevel?: LevelChoicesEnum | null
            gabAnalysisResults?: string | null
            summary?: string | null
            recommendations?: Array<string> | null
            iissue?: {
              __typename?: "IIssueNode"
              id: string
              name?: string | null
              category?: CategoryChoicesEnum | null
              issue?: { __typename?: "IssueNode"; description?: string | null } | null
            } | null
            sources?: Array<{
              __typename?: "RelevantIissueSourceNode"
              sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
              source?: string | null
            } | null> | null
          } | null> | null
        } | null
        publishedEvaluations?: Array<{
          __typename?: "EvaluationNode"
          id: string
          status?: StatusChoicesEnum | null
          completedAt?: string | null
          evaluationYear?: number | null
          overallRecommendations?: Array<string> | null
          environmentalMaturity?: LevelChoicesEnum | null
          socialMaturity?: LevelChoicesEnum | null
          governanceMaturity?: LevelChoicesEnum | null
          overallSummary?: string | null
          inputs?: {
            __typename?: "EvaluationInputsNode"
            dataDepth?: {
              __typename?: "DataDepthNode"
              baseData?: boolean | null
              publicData?: boolean | null
              selfAssessment?: boolean | null
              advanced?: boolean | null
            } | null
            evaluationDepth?: {
              __typename?: "EvaluationDepthNode"
              esgRiskMapping?: boolean | null
              aiEvaluations?: boolean | null
              esgRecommendations?: boolean | null
              enrichedEvaluation?: boolean | null
              fullEvaluation?: boolean | null
            } | null
            sources?: {
              __typename?: "EvaluationSourcesNode"
              businessRegistry?: boolean | null
              annualReport?: boolean | null
              website?: boolean | null
              media?: boolean | null
              questionnaire?: boolean | null
              proprietaryData?: boolean | null
            } | null
          } | null
          company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
        } | null> | null
        businessActivities: Array<{
          __typename?: "BusinessActivityNode"
          id: string
          euTaxonomyEligible?: boolean | null
          naceCode?: {
            __typename?: "NaceCodeNode"
            id: string
            label?: string | null
            labelEt?: string | null
            code?: string | null
            overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
            socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
          } | null
          latestReportItem?: {
            __typename?: "AnnualReportItemNode"
            isMainActivity?: boolean | null
            businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          } | null
        }>
        annualReports: Array<{
          __typename?: "AnnualReportNode"
          financialYear?: number | null
          averageFullTimeEmployeesConsolidated?: number | null
          companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
          totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
        }>
      }
      contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
    }>
  } | null
}

export type WatchlistsQueryQueryVariables = Exact<{
  first?: InputMaybe<Scalars["Int"]["input"]>
  after?: InputMaybe<Scalars["String"]["input"]>
}>

export type WatchlistsQueryQuery = {
  __typename?: "Query"
  watchlists?: {
    __typename?: "WatchlistNodeConnection"
    pageInfo: {
      __typename?: "PageInfo"
      hasNextPage: boolean
      hasPreviousPage: boolean
      startCursor?: string | null
      endCursor?: string | null
    }
    edges: Array<{
      __typename?: "WatchlistNodeEdge"
      node?: {
        __typename?: "WatchlistNode"
        id: string
        name: string
        updatedAt?: string | null
        createdAt?: string | null
        tenant?: { __typename?: "TenantNode"; id: string } | null
        portalUser?: { __typename?: "PortalUserNode"; id: string } | null
        entries: Array<{
          __typename?: "WatchlistEntryNode"
          id: string
          company: {
            __typename?: "CompanyNode"
            id: string
            name: string
            about?: string | null
            aboutEn?: string | null
            aboutEt?: string | null
            registrationNumber?: string | null
            status?: CompanyStatusChoicesEnum | null
            country?: IorderCompanyCountryChoices | null
            employeeNumber?: number | null
            street?: string | null
            houseNumber?: string | null
            postalCode?: string | null
            city?: string | null
            registrationDate?: string | null
            urls: Array<{ __typename?: "URLNode"; url: string }>
            latestGhgEmission?: Array<{
              __typename?: "CompanyEmissionNode"
              id: string
              year?: number | null
              source?: string | null
              basis?: string | null
              multiplier?: string | null
              unit?: string | null
              value?: string | null
              scope?: string | null
            } | null> | null
            mediaSources?: Array<{
              __typename?: "MediaSourceDataNode"
              id: string
              url: string
              title: string
              summary: string
              story?: string | null
              publishedAt?: string | null
              startedAt: string
              completedAt?: string | null
              esgCategory?: IesgMediaSourceDataEsgCategoryChoices | null
              sentiment?: string | null
              tiPossibleName?: IesgMediaSourceDataTiPossibleNameChoices | null
              metaData?: any | null
            } | null> | null
            latestEsgMaterialityMapping?: {
              __typename?: "MaterialityMappingNode"
              executionDate?: string | null
              relevantPositives?: Array<{
                __typename?: "RelevantPositiveNode"
                summary?: string | null
                maturityLevel?: string | null
              } | null> | null
              relevantIssues?: Array<{
                __typename?: "RelevantIissueNode"
                maturityLevel?: LevelChoicesEnum | null
                gabAnalysisResults?: string | null
                summary?: string | null
                recommendations?: Array<string> | null
                iissue?: {
                  __typename?: "IIssueNode"
                  id: string
                  name?: string | null
                  category?: CategoryChoicesEnum | null
                  issue?: { __typename?: "IssueNode"; description?: string | null } | null
                } | null
                sources?: Array<{
                  __typename?: "RelevantIissueSourceNode"
                  sourceType?: IesgRelevantIissueSourceSourceTypeChoices | null
                  source?: string | null
                } | null> | null
              } | null> | null
            } | null
            publishedEvaluations?: Array<{
              __typename?: "EvaluationNode"
              id: string
              status?: StatusChoicesEnum | null
              completedAt?: string | null
              evaluationYear?: number | null
              overallRecommendations?: Array<string> | null
              environmentalMaturity?: LevelChoicesEnum | null
              socialMaturity?: LevelChoicesEnum | null
              governanceMaturity?: LevelChoicesEnum | null
              overallSummary?: string | null
              inputs?: {
                __typename?: "EvaluationInputsNode"
                dataDepth?: {
                  __typename?: "DataDepthNode"
                  baseData?: boolean | null
                  publicData?: boolean | null
                  selfAssessment?: boolean | null
                  advanced?: boolean | null
                } | null
                evaluationDepth?: {
                  __typename?: "EvaluationDepthNode"
                  esgRiskMapping?: boolean | null
                  aiEvaluations?: boolean | null
                  esgRecommendations?: boolean | null
                  enrichedEvaluation?: boolean | null
                  fullEvaluation?: boolean | null
                } | null
                sources?: {
                  __typename?: "EvaluationSourcesNode"
                  businessRegistry?: boolean | null
                  annualReport?: boolean | null
                  website?: boolean | null
                  media?: boolean | null
                  questionnaire?: boolean | null
                  proprietaryData?: boolean | null
                } | null
              } | null
              company: { __typename?: "CompanyNode"; id: string; name: string; registrationNumber?: string | null }
            } | null> | null
            businessActivities: Array<{
              __typename?: "BusinessActivityNode"
              id: string
              euTaxonomyEligible?: boolean | null
              naceCode?: {
                __typename?: "NaceCodeNode"
                id: string
                label?: string | null
                labelEt?: string | null
                code?: string | null
                overallEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
                environmentalEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
                socialEbrdRiskLevel?: EbrdRiskLevelChoicesEnum | null
              } | null
              latestReportItem?: {
                __typename?: "AnnualReportItemNode"
                isMainActivity?: boolean | null
                businessActivityRevenue?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
              } | null
            }>
            annualReports: Array<{
              __typename?: "AnnualReportNode"
              financialYear?: number | null
              averageFullTimeEmployeesConsolidated?: number | null
              companyRevenueConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
              netProfitConsolidated?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
              totalAssets?: { __typename?: "MoneyNode"; amount: number; currency: string } | null
            }>
          }
          contacts: Array<{ __typename?: "CompanyContactNode"; id: string; email: string; name?: string | null }>
        }>
      } | null
    } | null>
  } | null
}

export type GetFileUrlQueryQueryVariables = Exact<{
  filePath: Scalars["String"]["input"]
}>

export type GetFileUrlQueryQuery = { __typename?: "Query"; getFileUrl?: string | null }

export const NaceCodeItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "NaceCodeItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "NaceCodeNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "label" } },
          { kind: "Field", name: { kind: "Name", value: "code" } },
          { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
          { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "parent" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "parent" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "label" } },
                { kind: "Field", name: { kind: "Name", value: "code" } },
                { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<NaceCodeItemFragment, unknown>
export const PortalUserItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "PortalUserItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "PortalUserNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "email" } },
          { kind: "Field", name: { kind: "Name", value: "lastName" } },
          { kind: "Field", name: { kind: "Name", value: "firstName" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "hasGivenConsent" } },
          { kind: "Field", name: { kind: "Name", value: "isActive" } },
          { kind: "Field", name: { kind: "Name", value: "dateJoined" } },
          { kind: "Field", name: { kind: "Name", value: "lastLogin" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "roles" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "role" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<PortalUserItemFragment, unknown>
export const SurveyItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SurveyItemFragment, unknown>
export const SurveyBatchItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyBatchItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyBatchNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "description" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SurveyBatchItemFragment, unknown>
export const TenantItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "TenantItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "TenantNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<TenantItemFragment, unknown>
export const EvaluationItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<EvaluationItemFragment, unknown>
export const CompanyItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CompanyItemFragment, unknown>
export const WatchlistEntryItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<WatchlistEntryItemFragment, unknown>
export const WatchlistItemFragmentDoc = {
  kind: "Document",
  definitions: [
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUser" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "entries" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistEntryItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<WatchlistItemFragment, unknown>
export const AddEntryToWatchlistMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "addEntryToWatchlistMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "watchlistId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "companyId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "website" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "addEntryToWatchlist" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "watchlistId" },
                value: { kind: "Variable", name: { kind: "Name", value: "watchlistId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "companyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "companyId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "website" },
                value: { kind: "Variable", name: { kind: "Name", value: "website" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "entry" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistEntryItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<AddEntryToWatchlistMutationMutation, AddEntryToWatchlistMutationMutationVariables>
export const AutofillSurveyMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "autofillSurveyMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "inputDocumentsUrls" } },
          type: { kind: "ListType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "autofillSurvey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "surveyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "inputDocumentsUrls" },
                value: { kind: "Variable", name: { kind: "Name", value: "inputDocumentsUrls" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "survey" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<AutofillSurveyMutationMutation, AutofillSurveyMutationMutationVariables>
export const CreatePortalUserMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "createPortalUserMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "email" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "firstName" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "lastName" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "isActive" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Boolean" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "roles" } },
          type: {
            kind: "NonNullType",
            type: {
              kind: "ListType",
              type: {
                kind: "NonNullType",
                type: { kind: "NamedType", name: { kind: "Name", value: "RoleAssignmentInput" } },
              },
            },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "companyId" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "createPortalUser" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "email" },
                value: { kind: "Variable", name: { kind: "Name", value: "email" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "firstName" },
                value: { kind: "Variable", name: { kind: "Name", value: "firstName" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "lastName" },
                value: { kind: "Variable", name: { kind: "Name", value: "lastName" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "isActive" },
                value: { kind: "Variable", name: { kind: "Name", value: "isActive" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "roles" },
                value: { kind: "Variable", name: { kind: "Name", value: "roles" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "companyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "companyId" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "portalUser" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "PortalUserItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "PortalUserItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "PortalUserNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "email" } },
          { kind: "Field", name: { kind: "Name", value: "lastName" } },
          { kind: "Field", name: { kind: "Name", value: "firstName" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "hasGivenConsent" } },
          { kind: "Field", name: { kind: "Name", value: "isActive" } },
          { kind: "Field", name: { kind: "Name", value: "dateJoined" } },
          { kind: "Field", name: { kind: "Name", value: "lastLogin" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "roles" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "role" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CreatePortalUserMutationMutation, CreatePortalUserMutationMutationVariables>
export const CreateSurveyMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CreateSurveyMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "template" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "SurveyTemplateEnum" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "batchId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "companySurveys" } },
          type: {
            kind: "NonNullType",
            type: {
              kind: "ListType",
              type: {
                kind: "NonNullType",
                type: { kind: "NamedType", name: { kind: "Name", value: "CompanySurveysInput" } },
              },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "createSurvey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "template" },
                value: { kind: "Variable", name: { kind: "Name", value: "template" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "batchId" },
                value: { kind: "Variable", name: { kind: "Name", value: "batchId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "companySurveys" },
                value: { kind: "Variable", name: { kind: "Name", value: "companySurveys" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "surveys" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "deadline" } },
                      { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
                      { kind: "Field", name: { kind: "Name", value: "formData" } },
                      { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "company" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "progress" } },
                      { kind: "Field", name: { kind: "Name", value: "status" } },
                      { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CreateSurveyMutationMutation, CreateSurveyMutationMutationVariables>
export const CreateSurveyBatchMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CreateSurveyBatchMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "name" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "template" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "SurveyTemplateEnum" } },
          },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "tenantId" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "description" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "createSurveyBatch" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "name" },
                value: { kind: "Variable", name: { kind: "Name", value: "name" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "template" },
                value: { kind: "Variable", name: { kind: "Name", value: "template" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "tenantId" },
                value: { kind: "Variable", name: { kind: "Name", value: "tenantId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "description" },
                value: { kind: "Variable", name: { kind: "Name", value: "description" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "surveyBatch" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyBatchItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyBatchItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyBatchNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "description" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CreateSurveyBatchMutationMutation, CreateSurveyBatchMutationMutationVariables>
export const CreateWatchlistMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CreateWatchlistMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "name" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "portalUserId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "createWatchlist" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "name" },
                value: { kind: "Variable", name: { kind: "Name", value: "name" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "portalUserId" },
                value: { kind: "Variable", name: { kind: "Name", value: "portalUserId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "watchlist" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUser" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "entries" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistEntryItem" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CreateWatchlistMutationMutation, CreateWatchlistMutationMutationVariables>
export const DeleteWatchlistMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "DeleteWatchlistMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "watchlistId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "deleteWatchlist" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "watchlistId" },
                value: { kind: "Variable", name: { kind: "Name", value: "watchlistId" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "success" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<DeleteWatchlistMutationMutation, DeleteWatchlistMutationMutationVariables>
export const RenameWatchlistMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "RenameWatchlistMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "watchlistId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "name" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "renameWatchlist" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "watchlistId" },
                value: { kind: "Variable", name: { kind: "Name", value: "watchlistId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "name" },
                value: { kind: "Variable", name: { kind: "Name", value: "name" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "watchlist" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUser" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "entries" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistEntryItem" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<RenameWatchlistMutationMutation, RenameWatchlistMutationMutationVariables>
export const ResendSurveyMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "ResendSurveyMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "resendSurvey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "surveyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "survey" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<ResendSurveyMutationMutation, ResendSurveyMutationMutationVariables>
export const ShareSurveyMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "ShareSurveyMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "sharedWith" } },
          type: {
            kind: "NonNullType",
            type: {
              kind: "ListType",
              type: {
                kind: "NonNullType",
                type: { kind: "NamedType", name: { kind: "Name", value: "SurveyContactInput" } },
              },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "shareSurvey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "surveyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "sharedWith" },
                value: { kind: "Variable", name: { kind: "Name", value: "sharedWith" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "survey" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<ShareSurveyMutationMutation, ShareSurveyMutationMutationVariables>
export const SubmitSurveyMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "SubmitSurveyMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "submitSurvey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "surveyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "survey" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SubmitSurveyMutationMutation, SubmitSurveyMutationMutationVariables>
export const UpdateSurveyMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "UpdateSurveyMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "status" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "SurveyStatusEnum" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "progress" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Float" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "formData" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "JSONString" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateSurvey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "surveyId" },
                value: { kind: "Variable", name: { kind: "Name", value: "surveyId" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "status" },
                value: { kind: "Variable", name: { kind: "Name", value: "status" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "progress" },
                value: { kind: "Variable", name: { kind: "Name", value: "progress" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "formData" },
                value: { kind: "Variable", name: { kind: "Name", value: "formData" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "survey" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<UpdateSurveyMutationMutation, UpdateSurveyMutationMutationVariables>
export const UpdateSurveyStatusMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "UpdateSurveyStatusMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "status" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "SurveyStatusEnum" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "surveyIds" } },
          type: {
            kind: "NonNullType",
            type: {
              kind: "ListType",
              type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updateSurveyStatus" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "status" },
                value: { kind: "Variable", name: { kind: "Name", value: "status" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "surveyIds" },
                value: { kind: "Variable", name: { kind: "Name", value: "surveyIds" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "surveys" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<UpdateSurveyStatusMutationMutation, UpdateSurveyStatusMutationMutationVariables>
export const UpdatePortalUserMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "UpdatePortalUserMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "isActive" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Boolean" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "roles" } },
          type: { kind: "ListType", type: { kind: "NamedType", name: { kind: "Name", value: "RoleAssignmentInput" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "firstName" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "lastName" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "hasGivenConsent" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Boolean" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "updatePortalUser" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "requestedBy" },
                value: { kind: "Variable", name: { kind: "Name", value: "requestedBy" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "isActive" },
                value: { kind: "Variable", name: { kind: "Name", value: "isActive" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "roles" },
                value: { kind: "Variable", name: { kind: "Name", value: "roles" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "firstName" },
                value: { kind: "Variable", name: { kind: "Name", value: "firstName" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "lastName" },
                value: { kind: "Variable", name: { kind: "Name", value: "lastName" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "hasGivenConsent" },
                value: { kind: "Variable", name: { kind: "Name", value: "hasGivenConsent" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "portalUser" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "PortalUserItem" } }],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "PortalUserItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "PortalUserNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "email" } },
          { kind: "Field", name: { kind: "Name", value: "lastName" } },
          { kind: "Field", name: { kind: "Name", value: "firstName" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "hasGivenConsent" } },
          { kind: "Field", name: { kind: "Name", value: "isActive" } },
          { kind: "Field", name: { kind: "Name", value: "dateJoined" } },
          { kind: "Field", name: { kind: "Name", value: "lastLogin" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "roles" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "role" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<UpdatePortalUserMutationMutation, UpdatePortalUserMutationMutationVariables>
export const SaveFileToGcsMutationDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "SaveFileToGcsMutation" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "file" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "Upload" } } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "filename" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "saveFileToGcs" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "file" },
                value: { kind: "Variable", name: { kind: "Name", value: "file" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "filename" },
                value: { kind: "Variable", name: { kind: "Name", value: "filename" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "success" } },
                { kind: "Field", name: { kind: "Name", value: "message" } },
                { kind: "Field", name: { kind: "Name", value: "filePath" } },
                { kind: "Field", name: { kind: "Name", value: "fileUrl" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SaveFileToGcsMutationMutation, SaveFileToGcsMutationMutationVariables>
export const CompanyQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "companyQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CompanyQueryQuery, CompanyQueryQueryVariables>
export const CompaniesQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "companiesQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "country" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "IorderCompanyCountryChoices" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companies" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "country" },
                value: { kind: "Variable", name: { kind: "Name", value: "country" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<CompaniesQueryQuery, CompaniesQueryQueryVariables>
export const EvaluationQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "evaluationQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "evaluation" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<EvaluationQueryQuery, EvaluationQueryQueryVariables>
export const EvaluationsQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "evaluationsQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "evaluations" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<EvaluationsQueryQuery, EvaluationsQueryQueryVariables>
export const NaceCodeQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "NaceCodeQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "naceCode" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "NaceCodeItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "NaceCodeItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "NaceCodeNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "label" } },
          { kind: "Field", name: { kind: "Name", value: "code" } },
          { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
          { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "parent" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "parent" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "label" } },
                { kind: "Field", name: { kind: "Name", value: "code" } },
                { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<NaceCodeQueryQuery, NaceCodeQueryQueryVariables>
export const NaceCodesQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "NaceCodesQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "naceCodes" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "NaceCodeItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "NaceCodeItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "NaceCodeNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "label" } },
          { kind: "Field", name: { kind: "Name", value: "code" } },
          { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
          { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "parent" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "parent" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "label" } },
                { kind: "Field", name: { kind: "Name", value: "code" } },
                { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<NaceCodesQueryQuery, NaceCodesQueryQueryVariables>
export const PortalUserQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "PortalUserQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUser" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "PortalUserItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "PortalUserItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "PortalUserNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "email" } },
          { kind: "Field", name: { kind: "Name", value: "lastName" } },
          { kind: "Field", name: { kind: "Name", value: "firstName" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "hasGivenConsent" } },
          { kind: "Field", name: { kind: "Name", value: "isActive" } },
          { kind: "Field", name: { kind: "Name", value: "dateJoined" } },
          { kind: "Field", name: { kind: "Name", value: "lastLogin" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "roles" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "role" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<PortalUserQueryQuery, PortalUserQueryQueryVariables>
export const PortalUsersQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "PortalUsersQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUsers" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "PortalUserItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "PortalUserItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "PortalUserNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "email" } },
          { kind: "Field", name: { kind: "Name", value: "lastName" } },
          { kind: "Field", name: { kind: "Name", value: "firstName" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "hasGivenConsent" } },
          { kind: "Field", name: { kind: "Name", value: "isActive" } },
          { kind: "Field", name: { kind: "Name", value: "dateJoined" } },
          { kind: "Field", name: { kind: "Name", value: "lastLogin" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "roles" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "role" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<PortalUsersQueryQuery, PortalUsersQueryQueryVariables>
export const SurveyQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "SurveyQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "survey" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SurveyQueryQuery, SurveyQueryQueryVariables>
export const SurveysQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "SurveysQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "surveys" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "submittedAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "batch" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "financialYear" } },
          { kind: "Field", name: { kind: "Name", value: "deadline" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "readyForEdit" } },
          { kind: "Field", name: { kind: "Name", value: "prefilledData" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputDocuments" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "file" } },
                { kind: "Field", name: { kind: "Name", value: "fileName" } },
                { kind: "Field", name: { kind: "Name", value: "fileSize" } },
                { kind: "Field", name: { kind: "Name", value: "isAttachment" } },
              ],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "lastReminder" } },
          { kind: "Field", name: { kind: "Name", value: "formData" } },
          { kind: "Field", name: { kind: "Name", value: "statusChangedAt" } },
          { kind: "Field", name: { kind: "Name", value: "progress" } },
          { kind: "Field", name: { kind: "Name", value: "langConsent" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SurveysQueryQuery, SurveysQueryQueryVariables>
export const SurveyBatchQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "SurveyBatchQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "surveyBatch" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyBatchItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyBatchItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyBatchNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "description" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SurveyBatchQueryQuery, SurveyBatchQueryQueryVariables>
export const SurveyBatchesQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "SurveyBatchesQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "surveyBatches" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "SurveyBatchItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "SurveyBatchItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "SurveyBatchNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "description" } },
          { kind: "Field", name: { kind: "Name", value: "template" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<SurveyBatchesQueryQuery, SurveyBatchesQueryQueryVariables>
export const TenantQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "TenantQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "TenantItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "TenantItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "TenantNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<TenantQueryQuery, TenantQueryQueryVariables>
export const TenantsQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "TenantsQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "tenants" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "TenantItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "TenantItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "TenantNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
        ],
      },
    },
  ],
} as unknown as DocumentNode<TenantsQueryQuery, TenantsQueryQueryVariables>
export const WatchlistQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "WatchlistQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "ID" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "watchlist" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: { kind: "Variable", name: { kind: "Name", value: "id" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistItem" } }],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUser" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "entries" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistEntryItem" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<WatchlistQueryQuery, WatchlistQueryQueryVariables>
export const WatchlistsQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "WatchlistsQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "first" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "Int" } },
        },
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "after" } },
          type: { kind: "NamedType", name: { kind: "Name", value: "String" } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "watchlists" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "first" },
                value: { kind: "Variable", name: { kind: "Name", value: "first" } },
              },
              {
                kind: "Argument",
                name: { kind: "Name", value: "after" },
                value: { kind: "Variable", name: { kind: "Name", value: "after" } },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pageInfo" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "hasNextPage" } },
                      { kind: "Field", name: { kind: "Name", value: "hasPreviousPage" } },
                      { kind: "Field", name: { kind: "Name", value: "startCursor" } },
                      { kind: "Field", name: { kind: "Name", value: "endCursor" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "edges" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "node" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistItem" } }],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "EvaluationItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "EvaluationNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "completedAt" } },
          { kind: "Field", name: { kind: "Name", value: "evaluationYear" } },
          { kind: "Field", name: { kind: "Name", value: "overallRecommendations" } },
          { kind: "Field", name: { kind: "Name", value: "environmentalMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "socialMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "governanceMaturity" } },
          { kind: "Field", name: { kind: "Name", value: "overallSummary" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "inputs" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dataDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "baseData" } },
                      { kind: "Field", name: { kind: "Name", value: "publicData" } },
                      { kind: "Field", name: { kind: "Name", value: "selfAssessment" } },
                      { kind: "Field", name: { kind: "Name", value: "advanced" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "evaluationDepth" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "esgRiskMapping" } },
                      { kind: "Field", name: { kind: "Name", value: "aiEvaluations" } },
                      { kind: "Field", name: { kind: "Name", value: "esgRecommendations" } },
                      { kind: "Field", name: { kind: "Name", value: "enrichedEvaluation" } },
                      { kind: "Field", name: { kind: "Name", value: "fullEvaluation" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "sources" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "businessRegistry" } },
                      { kind: "Field", name: { kind: "Name", value: "annualReport" } },
                      { kind: "Field", name: { kind: "Name", value: "website" } },
                      { kind: "Field", name: { kind: "Name", value: "media" } },
                      { kind: "Field", name: { kind: "Name", value: "questionnaire" } },
                      { kind: "Field", name: { kind: "Name", value: "proprietaryData" } },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "CompanyItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "CompanyNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "about" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEn" } },
          { kind: "Field", name: { kind: "Name", value: "aboutEt" } },
          { kind: "Field", name: { kind: "Name", value: "registrationNumber" } },
          { kind: "Field", name: { kind: "Name", value: "status" } },
          { kind: "Field", name: { kind: "Name", value: "country" } },
          { kind: "Field", name: { kind: "Name", value: "employeeNumber" } },
          { kind: "Field", name: { kind: "Name", value: "street" } },
          { kind: "Field", name: { kind: "Name", value: "houseNumber" } },
          { kind: "Field", name: { kind: "Name", value: "postalCode" } },
          { kind: "Field", name: { kind: "Name", value: "city" } },
          { kind: "Field", name: { kind: "Name", value: "registrationDate" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "urls" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "url" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestGhgEmission" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "year" } },
                { kind: "Field", name: { kind: "Name", value: "source" } },
                { kind: "Field", name: { kind: "Name", value: "basis" } },
                { kind: "Field", name: { kind: "Name", value: "multiplier" } },
                { kind: "Field", name: { kind: "Name", value: "unit" } },
                { kind: "Field", name: { kind: "Name", value: "value" } },
                { kind: "Field", name: { kind: "Name", value: "scope" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "mediaSources" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "url" } },
                { kind: "Field", name: { kind: "Name", value: "title" } },
                { kind: "Field", name: { kind: "Name", value: "summary" } },
                { kind: "Field", name: { kind: "Name", value: "story" } },
                { kind: "Field", name: { kind: "Name", value: "publishedAt" } },
                { kind: "Field", name: { kind: "Name", value: "startedAt" } },
                { kind: "Field", name: { kind: "Name", value: "completedAt" } },
                { kind: "Field", name: { kind: "Name", value: "esgCategory" } },
                { kind: "Field", name: { kind: "Name", value: "sentiment" } },
                { kind: "Field", name: { kind: "Name", value: "tiPossibleName" } },
                { kind: "Field", name: { kind: "Name", value: "metaData" } },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "latestEsgMaterialityMapping" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "executionDate" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantPositives" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "relevantIssues" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "iissue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "id" } },
                            { kind: "Field", name: { kind: "Name", value: "name" } },
                            { kind: "Field", name: { kind: "Name", value: "category" } },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "issue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [{ kind: "Field", name: { kind: "Name", value: "description" } }],
                              },
                            },
                          ],
                        },
                      },
                      { kind: "Field", name: { kind: "Name", value: "maturityLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "gabAnalysisResults" } },
                      { kind: "Field", name: { kind: "Name", value: "summary" } },
                      { kind: "Field", name: { kind: "Name", value: "recommendations" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sources" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "sourceType" } },
                            { kind: "Field", name: { kind: "Name", value: "source" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "publishedEvaluations" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "EvaluationItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "businessActivities" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "euTaxonomyEligible" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "naceCode" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "labelEt" } },
                      { kind: "Field", name: { kind: "Name", value: "code" } },
                      { kind: "Field", name: { kind: "Name", value: "overallEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "environmentalEbrdRiskLevel" } },
                      { kind: "Field", name: { kind: "Name", value: "socialEbrdRiskLevel" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "latestReportItem" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "isMainActivity" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "businessActivityRevenue" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            { kind: "Field", name: { kind: "Name", value: "amount" } },
                            { kind: "Field", name: { kind: "Name", value: "currency" } },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "annualReports" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "financialYear" } },
                { kind: "Field", name: { kind: "Name", value: "averageFullTimeEmployeesConsolidated" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "companyRevenueConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "netProfitConsolidated" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "totalAssets" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "amount" } },
                      { kind: "Field", name: { kind: "Name", value: "currency" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistEntryItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistEntryNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "company" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "CompanyItem" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "contacts" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
              ],
            },
          },
        ],
      },
    },
    {
      kind: "FragmentDefinition",
      name: { kind: "Name", value: "WatchlistItem" },
      typeCondition: { kind: "NamedType", name: { kind: "Name", value: "WatchlistNode" } },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          { kind: "Field", name: { kind: "Name", value: "id" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "tenant" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          {
            kind: "Field",
            name: { kind: "Name", value: "portalUser" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "Field", name: { kind: "Name", value: "id" } }],
            },
          },
          { kind: "Field", name: { kind: "Name", value: "name" } },
          { kind: "Field", name: { kind: "Name", value: "updatedAt" } },
          { kind: "Field", name: { kind: "Name", value: "createdAt" } },
          {
            kind: "Field",
            name: { kind: "Name", value: "entries" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [{ kind: "FragmentSpread", name: { kind: "Name", value: "WatchlistEntryItem" } }],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<WatchlistsQueryQuery, WatchlistsQueryQueryVariables>
export const GetFileUrlQueryDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetFileUrlQuery" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "filePath" } },
          type: { kind: "NonNullType", type: { kind: "NamedType", name: { kind: "Name", value: "String" } } },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getFileUrl" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "filePath" },
                value: { kind: "Variable", name: { kind: "Name", value: "filePath" } },
              },
            ],
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetFileUrlQueryQuery, GetFileUrlQueryQueryVariables>
