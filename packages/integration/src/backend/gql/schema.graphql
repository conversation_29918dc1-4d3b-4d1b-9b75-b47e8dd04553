type AddEntryToWatchlist {
  entry: WatchlistEntryNode
}

type AnnualReportItemNode {
  annualReport: AnnualReportNode
  businessActivity: BusinessActivityNode
  businessActivityRevenue: MoneyNode
  id: ID!
  isMainActivity: Boolean
  weight: Decimal
}

type AnnualReportNode {
  averageFullTimeEmployees: Int
  averageFullTimeEmployeesConsolidated: Int
  capex: MoneyNode
  capexConsolidated: MoneyNode
  company: CompanyNode
  companyRevenue: MoneyNode
  companyRevenueConsolidated: MoneyNode
  endDate: Date
  financialYear: Int
  id: ID!
  netProfit: MoneyNode
  netProfitConsolidated: MoneyNode
  startDate: Date
  totalAssets: MoneyNode
  totalAssetsConsolidated: MoneyNode
  totalStaffCost: Decimal
  totalStaffCostConsolidated: Decimal
}

type AutofillSurvey {
  survey: SurveyNode
}

type BusinessActivityNode {
  euTaxonomyEligible: Boolean
  id: ID!
  latestReportItem: AnnualReportItemNode
  naceCode: NaceCodeNode
}

"""
An enumeration.
"""
enum CategoryChoicesEnum {
  environmental
  governance
  na
  social
}

"""
An enumeration.
"""
enum CollectionTypeChoicesEnum {
  classes
  divisions
  groups
  sections
}

input CompanyContactInput {
  email: String!
  name: String
}

type CompanyContactNode {
  email: String!
  id: ID!
  name: String
  watchlistEntry: WatchlistEntryNode!
}

type CompanyDatapointNode implements Node {
  company: CompanyNode!
  createdAt: DateTime!
  datapoint: DatapointNode!

  """
  The ID of the object
  """
  id: ID!
  sourceType: IportalCompanyDatapointSourceTypeChoices!
  unit: String
  value: JSONString!
}

type CompanyDatapointNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [CompanyDatapointNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `CompanyDatapointNode` and its cursor.
"""
type CompanyDatapointNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: CompanyDatapointNode
}

"""
GraphQL Node for CompanyEmission model.
"""
type CompanyEmissionNode implements Node {
  basis: String
  company: CompanyNode
  emissionExtraction: EmissionExtractionNode

  """
  The ID of the object
  """
  id: ID!
  multiplier: String
  scope: String
  source: String
  unit: String
  value: String
  year: Int
}

"""
GraphQL Node for Company model.
"""
type CompanyNode implements Node {
  about: String
  aboutEn: String
  aboutEt: String
  annualReports: [AnnualReportNode!]!
  businessActivities: [BusinessActivityNode!]!
  city: String
  country: IorderCompanyCountryChoices
  employeeNumber: Int
  ghggoalValue: String
  houseNumber: String
  id: ID!
  latestEsgMaterialityMapping: MaterialityMappingNode
  latestGhgEmission: [CompanyEmissionNode]
  mediaSources: [MediaSourceDataNode]
  name: String!
  postalCode: String
  publishedEvaluations: [EvaluationNode]
  registrationDate: Date
  registrationNumber: String
  status: CompanyStatusChoicesEnum
  statusEt: CompanyStatusChoicesEstonianEnum
  street: String
  urls: [URLNode!]!
}

type CompanyNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [CompanyNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `CompanyNode` and its cursor.
"""
type CompanyNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: CompanyNode
}

"""
An enumeration.
"""
enum CompanyStatusChoicesEnum {
  bankrupt
  delete
  entered_in_the_register
  in_liquidation
}

"""
An enumeration.
"""
enum CompanyStatusChoicesEstonianEnum {
  kantud_registrisse
  kustutatud
  likvideerimisel
  pankrotis
}

input CompanySurveysInput {
  companyId: ID!
  contactEmails: [String]!
  deadline: DateTime
  financialYear: Int
}

type CreatePortalUser {
  portalUser: PortalUserNode
}

type CreateSurvey {
  surveys: [SurveyNode]
}

type CreateSurveyBatch {
  surveyBatch: SurveyBatchNode
}

type CreateWatchlist {
  watchlist: WatchlistNode
}

type DataDepthNode {
  advanced: Boolean
  baseData: Boolean
  evaluationId: ID
  publicData: Boolean
  selfAssessment: Boolean
}

type DatapointNode implements Node {
  description: String
  esgCategory: ESGCategoryEnum
  esgCategoryEt: IportalDatapointEsgCategoryEtChoices
  format: JSONString
  id: ID!
  name: String
  nameEt: String
  type: IportalDatapointTypeChoices
}

type DatapointNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [DatapointNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `DatapointNode` and its cursor.
"""
type DatapointNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: DatapointNode
}

"""
The `Date` scalar type represents a Date
value as specified by
[iso8601](https://en.wikipedia.org/wiki/ISO_8601).
"""
scalar Date

"""
The `DateTime` scalar type represents a DateTime
value as specified by
[iso8601](https://en.wikipedia.org/wiki/ISO_8601).
"""
scalar DateTime

"""
The `Decimal` scalar type represents a python Decimal.
"""
scalar Decimal

type DeleteWatchlist {
  success: Boolean
}

"""
An enumeration.
"""
enum ESGCategoryEnum {
  ENVIRONMENT
  GENERAL
  GOVERNANCE
  SOCIAL
}

"""
An enumeration.
"""
enum EbrdRiskLevelChoicesEnum {
  excluded
  high
  low
  medium
}

"""
GraphQL Node for EmissionExtraction model.
"""
type EmissionExtractionNode implements Node {
  company: CompanyNode
  companyEmissions: [CompanyEmissionNode]
  completedAt: DateTime
  extractionMethod: IorderEmissionExtractionExtractionMethodChoices

  """
  The ID of the object
  """
  id: ID!
  metaData: JSONString
  startedAt: DateTime
  status: IorderEmissionExtractionStatusChoices
}

type EvaluationDepthNode {
  aiEvaluations: Boolean
  enrichedEvaluation: Boolean
  esgRecommendations: Boolean
  esgRiskMapping: Boolean
  evaluationId: ID
  fullEvaluation: Boolean
}

type EvaluationInputsNode {
  dataDepth: DataDepthNode
  evaluationDepth: EvaluationDepthNode
  evaluationId: ID
  sources: EvaluationSourcesNode
}

type EvaluationNode implements Node {
  company: CompanyNode!
  completedAt: DateTime
  environmentalMaturity: LevelChoicesEnum
  errorMessage: String
  evaluationYear: Int
  governanceMaturity: LevelChoicesEnum
  id: ID!
  inputs: EvaluationInputsNode
  isPublished: Boolean
  metaData: JSONString
  overallRecommendations: [String!]
  overallSummary: String
  socialMaturity: LevelChoicesEnum
  startedAt: DateTime!
  status: StatusChoicesEnum
}

type EvaluationNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [EvaluationNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `EvaluationNode` and its cursor.
"""
type EvaluationNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: EvaluationNode
}

type EvaluationSourcesNode {
  annualReport: Boolean
  businessRegistry: Boolean
  evaluationId: ID
  media: Boolean
  proprietaryData: Boolean
  questionnaire: Boolean
  website: Boolean
}

type IIssueNode {
  category: CategoryChoicesEnum
  id: ID!
  issue: IssueNode
  name: String
}

"""
An enumeration.
"""
enum IesgMediaSourceDataEsgCategoryChoices {
  """
  ENVIRONMENTAL
  """
  ENVIRONMENTAL

  """
  GOVERNANCE
  """
  GOVERNANCE

  """
  N/A
  """
  N_A

  """
  SOCIAL
  """
  SOCIAL
}

"""
An enumeration.
"""
enum IesgMediaSourceDataTiPossibleNameChoices {
  """
  Ettekirjutus
  """
  ETTEKIRJUTUS

  """
  Ettekirjutus ja Sunniraha hoiatus
  """
  ETTEKIRJUTUS_JA_SUNNIRAHA_HOIATUS

  """
  N/A
  """
  N_A

  """
  Sunniraha hoiatus
  """
  SUNNIRAHA_HOIATUS

  """
  Sunniraha rakendamine
  """
  SUNNIRAHA_RAKENDAMINE
}

"""
An enumeration.
"""
enum IesgRelevantIissueSourceSourceTypeChoices {
  """
  ANNUAL REPORT
  """
  ANNUAL_REPORT

  """
  URL
  """
  URL

  """
  WEBSITE
  """
  WEBSITE
}

"""
An enumeration.
"""
enum IorderCompanyCountryChoices {
  """
  Andorra
  """
  AD

  """
  United Arab Emirates
  """
  AE

  """
  Afghanistan
  """
  AF

  """
  Antigua and Barbuda
  """
  AG

  """
  Anguilla
  """
  AI

  """
  Albania
  """
  AL

  """
  Armenia
  """
  AM

  """
  Angola
  """
  AO

  """
  Antarctica
  """
  AQ

  """
  Argentina
  """
  AR

  """
  American Samoa
  """
  AS

  """
  Austria
  """
  AT

  """
  Australia
  """
  AU

  """
  Aruba
  """
  AW

  """
  Åland Islands
  """
  AX

  """
  Azerbaijan
  """
  AZ

  """
  Bosnia and Herzegovina
  """
  BA

  """
  Barbados
  """
  BB

  """
  Bangladesh
  """
  BD

  """
  Belgium
  """
  BE

  """
  Burkina Faso
  """
  BF

  """
  Bulgaria
  """
  BG

  """
  Bahrain
  """
  BH

  """
  Burundi
  """
  BI

  """
  Benin
  """
  BJ

  """
  Saint Barthélemy
  """
  BL

  """
  Bermuda
  """
  BM

  """
  Brunei
  """
  BN

  """
  Bolivia
  """
  BO

  """
  Bonaire, Sint Eustatius and Saba
  """
  BQ

  """
  Brazil
  """
  BR

  """
  Bahamas
  """
  BS

  """
  Bhutan
  """
  BT

  """
  Bouvet Island
  """
  BV

  """
  Botswana
  """
  BW

  """
  Belarus
  """
  BY

  """
  Belize
  """
  BZ

  """
  Canada
  """
  CA

  """
  Cocos (Keeling) Islands
  """
  CC

  """
  Congo (the Democratic Republic of the)
  """
  CD

  """
  Central African Republic
  """
  CF

  """
  Congo
  """
  CG

  """
  Switzerland
  """
  CH

  """
  Côte d'Ivoire
  """
  CI

  """
  Cook Islands
  """
  CK

  """
  Chile
  """
  CL

  """
  Cameroon
  """
  CM

  """
  China
  """
  CN

  """
  Colombia
  """
  CO

  """
  Costa Rica
  """
  CR

  """
  Cuba
  """
  CU

  """
  Cabo Verde
  """
  CV

  """
  Curaçao
  """
  CW

  """
  Christmas Island
  """
  CX

  """
  Cyprus
  """
  CY

  """
  Czechia
  """
  CZ

  """
  Germany
  """
  DE

  """
  Djibouti
  """
  DJ

  """
  Denmark
  """
  DK

  """
  Dominica
  """
  DM

  """
  Dominican Republic
  """
  DO

  """
  Algeria
  """
  DZ

  """
  Ecuador
  """
  EC

  """
  Estonia
  """
  EE

  """
  Egypt
  """
  EG

  """
  Western Sahara
  """
  EH

  """
  Eritrea
  """
  ER

  """
  Spain
  """
  ES

  """
  Ethiopia
  """
  ET

  """
  Finland
  """
  FI

  """
  Fiji
  """
  FJ

  """
  Falkland Islands (Malvinas)
  """
  FK

  """
  Micronesia (Federated States of)
  """
  FM

  """
  Faroe Islands
  """
  FO

  """
  France
  """
  FR

  """
  Gabon
  """
  GA

  """
  United Kingdom
  """
  GB

  """
  Grenada
  """
  GD

  """
  Georgia
  """
  GE

  """
  French Guiana
  """
  GF

  """
  Guernsey
  """
  GG

  """
  Ghana
  """
  GH

  """
  Gibraltar
  """
  GI

  """
  Greenland
  """
  GL

  """
  Gambia
  """
  GM

  """
  Guinea
  """
  GN

  """
  Guadeloupe
  """
  GP

  """
  Equatorial Guinea
  """
  GQ

  """
  Greece
  """
  GR

  """
  South Georgia and the South Sandwich Islands
  """
  GS

  """
  Guatemala
  """
  GT

  """
  Guam
  """
  GU

  """
  Guinea-Bissau
  """
  GW

  """
  Guyana
  """
  GY

  """
  Hong Kong
  """
  HK

  """
  Heard Island and McDonald Islands
  """
  HM

  """
  Honduras
  """
  HN

  """
  Croatia
  """
  HR

  """
  Haiti
  """
  HT

  """
  Hungary
  """
  HU

  """
  Indonesia
  """
  ID

  """
  Ireland
  """
  IE

  """
  Israel
  """
  IL

  """
  Isle of Man
  """
  IM

  """
  India
  """
  IN

  """
  British Indian Ocean Territory
  """
  IO

  """
  Iraq
  """
  IQ

  """
  Iran
  """
  IR

  """
  Iceland
  """
  IS

  """
  Italy
  """
  IT

  """
  Jersey
  """
  JE

  """
  Jamaica
  """
  JM

  """
  Jordan
  """
  JO

  """
  Japan
  """
  JP

  """
  Kenya
  """
  KE

  """
  Kyrgyzstan
  """
  KG

  """
  Cambodia
  """
  KH

  """
  Kiribati
  """
  KI

  """
  Comoros
  """
  KM

  """
  Saint Kitts and Nevis
  """
  KN

  """
  North Korea
  """
  KP

  """
  South Korea
  """
  KR

  """
  Kuwait
  """
  KW

  """
  Cayman Islands
  """
  KY

  """
  Kazakhstan
  """
  KZ

  """
  Laos
  """
  LA

  """
  Lebanon
  """
  LB

  """
  Saint Lucia
  """
  LC

  """
  Liechtenstein
  """
  LI

  """
  Sri Lanka
  """
  LK

  """
  Liberia
  """
  LR

  """
  Lesotho
  """
  LS

  """
  Lithuania
  """
  LT

  """
  Luxembourg
  """
  LU

  """
  Latvia
  """
  LV

  """
  Libya
  """
  LY

  """
  Morocco
  """
  MA

  """
  Monaco
  """
  MC

  """
  Moldova
  """
  MD

  """
  Montenegro
  """
  ME

  """
  Saint Martin (French part)
  """
  MF

  """
  Madagascar
  """
  MG

  """
  Marshall Islands
  """
  MH

  """
  North Macedonia
  """
  MK

  """
  Mali
  """
  ML

  """
  Myanmar
  """
  MM

  """
  Mongolia
  """
  MN

  """
  Macao
  """
  MO

  """
  Northern Mariana Islands
  """
  MP

  """
  Martinique
  """
  MQ

  """
  Mauritania
  """
  MR

  """
  Montserrat
  """
  MS

  """
  Malta
  """
  MT

  """
  Mauritius
  """
  MU

  """
  Maldives
  """
  MV

  """
  Malawi
  """
  MW

  """
  Mexico
  """
  MX

  """
  Malaysia
  """
  MY

  """
  Mozambique
  """
  MZ

  """
  Namibia
  """
  NA

  """
  New Caledonia
  """
  NC

  """
  Niger
  """
  NE

  """
  Norfolk Island
  """
  NF

  """
  Nigeria
  """
  NG

  """
  Nicaragua
  """
  NI

  """
  Netherlands
  """
  NL

  """
  Norway
  """
  NO

  """
  Nepal
  """
  NP

  """
  Nauru
  """
  NR

  """
  Niue
  """
  NU

  """
  New Zealand
  """
  NZ

  """
  Oman
  """
  OM

  """
  Panama
  """
  PA

  """
  Peru
  """
  PE

  """
  French Polynesia
  """
  PF

  """
  Papua New Guinea
  """
  PG

  """
  Philippines
  """
  PH

  """
  Pakistan
  """
  PK

  """
  Poland
  """
  PL

  """
  Saint Pierre and Miquelon
  """
  PM

  """
  Pitcairn
  """
  PN

  """
  Puerto Rico
  """
  PR

  """
  Palestine, State of
  """
  PS

  """
  Portugal
  """
  PT

  """
  Palau
  """
  PW

  """
  Paraguay
  """
  PY

  """
  Qatar
  """
  QA

  """
  Réunion
  """
  RE

  """
  Romania
  """
  RO

  """
  Serbia
  """
  RS

  """
  Russia
  """
  RU

  """
  Rwanda
  """
  RW

  """
  Saudi Arabia
  """
  SA

  """
  Solomon Islands
  """
  SB

  """
  Seychelles
  """
  SC

  """
  Sudan
  """
  SD

  """
  Sweden
  """
  SE

  """
  Singapore
  """
  SG

  """
  Saint Helena, Ascension and Tristan da Cunha
  """
  SH

  """
  Slovenia
  """
  SI

  """
  Svalbard and Jan Mayen
  """
  SJ

  """
  Slovakia
  """
  SK

  """
  Sierra Leone
  """
  SL

  """
  San Marino
  """
  SM

  """
  Senegal
  """
  SN

  """
  Somalia
  """
  SO

  """
  Suriname
  """
  SR

  """
  South Sudan
  """
  SS

  """
  Sao Tome and Principe
  """
  ST

  """
  El Salvador
  """
  SV

  """
  Sint Maarten (Dutch part)
  """
  SX

  """
  Syria
  """
  SY

  """
  Eswatini
  """
  SZ

  """
  Turks and Caicos Islands
  """
  TC

  """
  Chad
  """
  TD

  """
  French Southern Territories
  """
  TF

  """
  Togo
  """
  TG

  """
  Thailand
  """
  TH

  """
  Tajikistan
  """
  TJ

  """
  Tokelau
  """
  TK

  """
  Timor-Leste
  """
  TL

  """
  Turkmenistan
  """
  TM

  """
  Tunisia
  """
  TN

  """
  Tonga
  """
  TO

  """
  Türkiye
  """
  TR

  """
  Trinidad and Tobago
  """
  TT

  """
  Tuvalu
  """
  TV

  """
  Taiwan
  """
  TW

  """
  Tanzania
  """
  TZ

  """
  Ukraine
  """
  UA

  """
  Uganda
  """
  UG

  """
  United States Minor Outlying Islands
  """
  UM

  """
  United States of America
  """
  US

  """
  Uruguay
  """
  UY

  """
  Uzbekistan
  """
  UZ

  """
  Holy See
  """
  VA

  """
  Saint Vincent and the Grenadines
  """
  VC

  """
  Venezuela
  """
  VE

  """
  Virgin Islands (British)
  """
  VG

  """
  Virgin Islands (U.S.)
  """
  VI

  """
  Vietnam
  """
  VN

  """
  Vanuatu
  """
  VU

  """
  Wallis and Futuna
  """
  WF

  """
  Samoa
  """
  WS

  """
  Yemen
  """
  YE

  """
  Mayotte
  """
  YT

  """
  South Africa
  """
  ZA

  """
  Zambia
  """
  ZM

  """
  Zimbabwe
  """
  ZW
}

"""
An enumeration.
"""
enum IorderEmissionExtractionExtractionMethodChoices {
  """
  AI
  """
  AI

  """
  MANUAL
  """
  MANUAL
}

"""
An enumeration.
"""
enum IorderEmissionExtractionStatusChoices {
  """
  DONE
  """
  DONE

  """
  ERROR
  """
  ERROR

  """
  IN-PROGRESS
  """
  IN_PROGRESS

  """
  STARTED
  """
  STARTED
}

"""
An enumeration.
"""
enum IportalCompanyDatapointSourceTypeChoices {
  """
  AI
  """
  AI

  """
  Third Party
  """
  THIRD_PARTY

  """
  User
  """
  USER
}

"""
An enumeration.
"""
enum IportalDatapointEsgCategoryEtChoices {
  """
  Keskkond
  """
  ENVIRONMENT

  """
  Üldine
  """
  GENERAL

  """
  Juhtimine
  """
  GOVERNANCE

  """
  Sotsiaalne
  """
  SOCIAL
}

"""
An enumeration.
"""
enum IportalDatapointTypeChoices {
  """
  Boolean
  """
  BOOLEAN

  """
  Date
  """
  DATE

  """
  File
  """
  FILE

  """
  JSON
  """
  JSON

  """
  MultiSelect
  """
  MULTISELECT

  """
  Numerical
  """
  NUMERICAL

  """
  Select
  """
  SELECT

  """
  Text
  """
  TEXT
}

"""
An enumeration.
"""
enum IportalPortalUserLanguageChoices {
  """
  English
  """
  EN

  """
  Estonian
  """
  ET
}

"""
An enumeration.
"""
enum IportalSurveyBatchTemplateChoices {
  """
  Self Assessment Unified Questionnaire V1
  """
  SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1

  """
  Unified Questionnaire V1
  """
  UNIFIED_QUESTIONNAIRE_V1
}

type IssueNode {
  category: CategoryChoicesEnum
  description: String
  id: ID!
  name: String
}

"""
Allows use of a JSON String for input / output from the GraphQL schema.

Use of this type is *not recommended* as you lose the benefits of having a defined, static
schema (one of the key benefits of GraphQL).
"""
scalar JSONString

"""
An enumeration.
"""
enum LevelChoicesEnum {
  high
  low
  medium
  very_high
  very_low
}

type MaterialityMappingNode {
  company: CompanyNode
  executionDate: Date
  id: ID!
  relevantIssues: [RelevantIissueNode]
  relevantPositives: [RelevantPositiveNode]
}

type MediaSourceDataNode implements Node {
  completedAt: DateTime
  esgCategory: IesgMediaSourceDataEsgCategoryChoices

  """
  The ID of the object
  """
  id: ID!
  metaData: JSONString
  publishedAt: DateTime
  sentiment: String
  startedAt: DateTime!
  story: String
  summary: String!
  tiPossibleName: IesgMediaSourceDataTiPossibleNameChoices
  title: String!
  url: String!
}

"""
GraphQL Node for Money type.
"""
type MoneyNode {
  amount: Float!
  currency: String!
}

"""
The Mutation class consolidates all the mutation operations available in the GraphQL API.
Additional app-specific mutations can be added by including them in the list of extended classes.
"""
type Mutation {
  addEntryToWatchlist(
    companyId: ID!
    contacts: [CompanyContactInput]

    """
    Email of the user who requested this action
    """
    requestedBy: String!
    watchlistId: ID!
    website: String
  ): AddEntryToWatchlist
  autofillSurvey(
    inputDocumentsUrls: [String]

    """
    Email of the user who requested this action
    """
    requestedBy: String!
    surveyId: ID!
  ): AutofillSurvey
  createPortalUser(
    companyId: ID
    email: String!
    firstName: String
    hasGivenConsent: Boolean
    isActive: Boolean
    language: String
    lastName: String

    """
    Email of the user who requested this action
    """
    requestedBy: String!
    roles: [RoleAssignmentInput]!
  ): CreatePortalUser
  createSurvey(
    """
    ID of the batch to which the survey belongs
    """
    batchId: ID!

    """
    List of Company Surveys
    """
    companySurveys: [CompanySurveysInput]
    formData: JSONString
    langConsent: String
    lastReminder: DateTime
    prefilledData: JSONString
    progress: Float
    readyForEdit: Boolean

    """
    Email of the user who requested this action
    """
    requestedBy: String!
    status: SurveyStatusEnum
    statusChangedAt: DateTime
    submittedAt: DateTime
    surveyTemplateId: ID
    template: SurveyTemplateEnum!
  ): CreateSurvey
  createSurveyBatch(description: String, name: String!, template: SurveyTemplateEnum!, tenantId: ID): CreateSurveyBatch
  createWatchlist(
    name: String!
    portalUserId: ID!

    """
    Email of the user who requested this action
    """
    requestedBy: String!
  ): CreateWatchlist
  deleteWatchlist(
    """
    Email of the user who requested this action
    """
    requestedBy: String!
    watchlistId: ID!
  ): DeleteWatchlist
  renameWatchlist(
    name: String!

    """
    Email of the user who requested this action
    """
    requestedBy: String!
    watchlistId: ID!
  ): RenameWatchlist
  resendSurvey(
    """
    Email of the user who requested this action
    """
    requestedBy: String!
    surveyId: ID!
  ): ResendSurvey
  saveFileToGcs(
    """
    The file to upload.
    """
    file: Upload!

    """
    Original filename including extension.
    """
    filename: String!
  ): SaveFileToGCS
  shareSurvey(
    """
    Email of the user who requested this action
    """
    requestedBy: String!
    sharedWith: [SurveyContactInput]!
    surveyId: ID!
  ): ShareSurvey
  submitSurvey(
    """
    Email of the user who requested this action
    """
    requestedBy: String!
    surveyId: ID!
  ): SubmitSurvey
  survey: SurveyNode
  surveyBatch: SurveyBatchNode
  updatePortalUser(
    companyId: ID
    email: String
    firstName: String
    hasGivenConsent: Boolean
    id: ID!
    isActive: Boolean
    language: String
    lastName: String

    """
    Email of the user who requested this action
    """
    requestedBy: String!
    roles: [RoleAssignmentInput]
  ): UpdatePortalUser
  updateSurvey(
    companyId: ID
    deadline: DateTime
    financialYear: Int
    formData: JSONString
    langConsent: String
    lastReminder: DateTime
    progress: Float
    readyForEdit: Boolean
    status: SurveyStatusEnum
    statusChangedAt: DateTime
    submittedAt: DateTime
    surveyId: ID!
    template: SurveyTemplateEnum
  ): UpdateSurvey
  updateSurveyBatch(
    """
    New description for the survey batch.
    """
    description: String

    """
    New name of the survey batch.
    """
    name: String

    """
    ID of the survey batch to update.
    """
    surveyBatchId: ID!

    """
    New template for the survey batch.
    """
    template: SurveyTemplateEnum
  ): UpdateSurveyBatch
  updateSurveyStatus(
    """
    New status to set for the surveys.
    """
    status: SurveyStatusEnum!

    """
    List of survey IDs to update.
    """
    surveyIds: [ID]!
  ): UpdateSurveyStatus
}

type NaceCodeNode implements Node {
  caseLawIfApplicable: String
  children(
    after: String
    before: String
    code: String
    code_Icontains: String
    code_Istartswith: String
    first: Int
    id: ID
    label: String
    label_Icontains: String
    label_Istartswith: String
    last: Int
    offset: Int
  ): NaceCodeNodeConnection!
  code: String
  collectionType: CollectionTypeChoicesEnum
  description: String
  environmentalEbrdRiskLevel: EbrdRiskLevelChoicesEnum
  excludes: String
  id: ID!
  label: String
  labelEt: String
  overallEbrdRiskLevel: EbrdRiskLevelChoicesEnum
  parent: NaceCodeNode
  socialEbrdRiskLevel: EbrdRiskLevelChoicesEnum
}

type NaceCodeNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [NaceCodeNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `NaceCodeNode` and its cursor.
"""
type NaceCodeNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: NaceCodeNode
}

"""
An object with an ID
"""
interface Node {
  """
  The ID of the object
  """
  id: ID!
}

"""
The Relay compliant `PageInfo` type, containing data necessary to paginate this connection.
"""
type PageInfo {
  """
  When paginating forwards, the cursor to continue.
  """
  endCursor: String

  """
  When paginating forwards, are there more items?
  """
  hasNextPage: Boolean!

  """
  When paginating backwards, are there more items?
  """
  hasPreviousPage: Boolean!

  """
  When paginating backwards, the cursor to continue.
  """
  startCursor: String
}

type PortalUserNode implements Node {
  company: CompanyNode
  createdAt: DateTime
  dateJoined: DateTime!
  email: String!
  firstName: String
  hasGivenConsent: Boolean!
  id: ID!
  isActive: Boolean!
  language: IportalPortalUserLanguageChoices!
  lastLogin: DateTime
  lastName: String
  roles: [RoleAssignmentNode!]!
  tenant: TenantNode
  updatedAt: DateTime
}

type PortalUserNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [PortalUserNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `PortalUserNode` and its cursor.
"""
type PortalUserNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: PortalUserNode
}

"""
An enumeration.
"""
enum PortalUserRoleEnum {
  ADMIN
  CLIENT_ADMIN
  COMPANY_USER
  DATA_VIEWER
  OBSERVER
  SURVEY_MANAGER
  WATCHLIST_MANAGER
}

"""
The Query class consolidates all the query operations available in the GraphQL API.
Additional app-specific queries can be added by including them in the list of extended classes.
"""
type Query {
  companies(
    after: String
    before: String
    country: IorderCompanyCountryChoices
    first: Int
    id: ID
    last: Int
    name: String
    name_Icontains: String
    name_Istartswith: String
    offset: Int
    registrationNumber: String
    registrationNumber_Icontains: String
    registrationNumber_Istartswith: String
    status: CompanyStatusChoicesEnum
  ): CompanyNodeConnection
  company(
    """
    The ID of the object
    """
    id: ID!
  ): CompanyNode
  companyDatapoint(
    """
    The ID of the object
    """
    id: ID!
  ): CompanyDatapointNode
  companyDatapoints(
    after: String
    before: String
    company_Id: ID
    createdAt: DateTime
    createdAt_Gt: DateTime
    createdAt_Lt: DateTime
    datapoint_Id: ID
    first: Int
    last: Int
    offset: Int
    sourceType_Icontains: IportalCompanyDatapointSourceTypeChoices
    unit: String
  ): CompanyDatapointNodeConnection
  datapoint(
    """
    The ID of the object
    """
    id: ID!
  ): DatapointNode
  datapoints(
    after: String
    before: String
    description_Icontains: String
    esgCategory: ESGCategoryEnum
    esgCategoryEt: IportalDatapointEsgCategoryEtChoices
    first: Int
    id: ID
    last: Int
    nameEt_Icontains: String
    nameEt_Istartswith: String
    name_Icontains: String
    name_Istartswith: String
    offset: Int
    systemKey: String
    topicCategory: ID
    type: IportalDatapointTypeChoices
  ): DatapointNodeConnection
  evaluation(
    """
    The ID of the object
    """
    id: ID!
  ): EvaluationNode
  evaluations(after: String, before: String, first: Int, id: ID, last: Int, offset: Int): EvaluationNodeConnection

  """
  Get the downloadable URL for a given file path.
  """
  getFileUrl(filePath: String!): String
  naceCode(
    """
    The ID of the object
    """
    id: ID!
  ): NaceCodeNode
  naceCodes(
    after: String
    before: String
    code: String
    code_Icontains: String
    code_Istartswith: String
    first: Int
    id: ID
    label: String
    label_Icontains: String
    label_Istartswith: String
    last: Int
    offset: Int
  ): NaceCodeNodeConnection
  portalUser(
    """
    The ID of the object
    """
    id: ID!
  ): PortalUserNode
  portalUsers(
    after: String
    before: String
    company_Id: ID
    dateJoined: DateTime
    dateJoined_Gt: DateTime
    dateJoined_Gte: DateTime
    dateJoined_Lt: DateTime
    dateJoined_Lte: DateTime
    email: String
    email_Icontains: String
    email_Istartswith: String
    first: Int
    firstName: String
    firstName_Icontains: String
    firstName_Istartswith: String
    hasGivenConsent: Boolean
    isActive: Boolean
    language: IportalPortalUserLanguageChoices
    language_Icontains: IportalPortalUserLanguageChoices
    language_Istartswith: IportalPortalUserLanguageChoices
    last: Int
    lastLogin: DateTime
    lastLogin_Gt: DateTime
    lastLogin_Gte: DateTime
    lastLogin_Lt: DateTime
    lastLogin_Lte: DateTime
    lastName: String
    lastName_Icontains: String
    lastName_Istartswith: String
    offset: Int
    roles_Role: PortalUserRoleEnum
    tenant_Id: ID
  ): PortalUserNodeConnection
  survey(
    """
    The ID of the object
    """
    id: ID!
  ): SurveyNode
  surveyBatch(
    """
    The ID of the object
    """
    id: ID!
  ): SurveyBatchNode
  surveyBatches(
    after: String
    before: String
    createdAt: DateTime
    createdAt_Gt: DateTime
    createdAt_Gte: DateTime
    createdAt_Lt: DateTime
    createdAt_Lte: DateTime
    description: String
    description_Icontains: String
    description_Istartswith: String
    first: Int
    id: ID
    last: Int
    name: String
    name_Icontains: String
    name_Istartswith: String
    offset: Int
    template: IportalSurveyBatchTemplateChoices
    updatedAt: DateTime
    updatedAt_Gt: DateTime
    updatedAt_Gte: DateTime
    updatedAt_Lt: DateTime
    updatedAt_Lte: DateTime
  ): SurveyBatchNodeConnection
  surveys(
    after: String
    batch_Id: ID
    before: String
    company_Id: ID
    createdAt: DateTime
    createdAt_Gt: DateTime
    createdAt_Gte: DateTime
    createdAt_Lt: DateTime
    createdAt_Lte: DateTime
    deadline: DateTime
    deadline_Gt: DateTime
    deadline_Gte: DateTime
    deadline_Lt: DateTime
    deadline_Lte: DateTime
    financialYear: Int
    financialYear_Gt: Int
    financialYear_Gte: Int
    financialYear_Lt: Int
    financialYear_Lte: Int
    first: Int
    id: ID
    langConsent: String
    last: Int
    offset: Int
    progress: Float
    readyForEdit: Boolean
    status: SurveyStatusEnum
    statusChangedAt: DateTime
    statusChangedAt_Gt: DateTime
    statusChangedAt_Gte: DateTime
    statusChangedAt_Lt: DateTime
    statusChangedAt_Lte: DateTime
    submittedAt: DateTime
    submittedAt_Gt: DateTime
    submittedAt_Gte: DateTime
    submittedAt_Lt: DateTime
    submittedAt_Lte: DateTime
    surveyTemplate_Type: String
    template: SurveyTemplateEnum
    tenant_Id: ID
    updatedAt: DateTime
    updatedAt_Gt: DateTime
    updatedAt_Gte: DateTime
    updatedAt_Lt: DateTime
    updatedAt_Lte: DateTime
  ): SurveyNodeConnection
  tenant(
    """
    The ID of the object
    """
    id: ID!
  ): TenantNode
  tenants(
    after: String
    before: String
    first: Int
    id: ID
    last: Int
    name: String
    name_Icontains: String
    name_Istartswith: String
    offset: Int
  ): TenantNodeConnection
  watchlist(
    """
    The ID of the object
    """
    id: ID!
  ): WatchlistNode
  watchlists(
    after: String
    before: String
    entries_Company_Name: String
    entries_Company_Name_Icontains: String
    entries_Company_Name_Istartswith: String
    first: Int
    id: ID
    last: Int
    name: String
    name_Icontains: String
    name_Istartswith: String
    offset: Int
    tenant_Id: ID
  ): WatchlistNodeConnection
}

type RelevantIissueNode {
  esgMapping: MaterialityMappingNode
  gabAnalysisResults: String
  id: ID!
  iissue: IIssueNode
  maturityLevel: LevelChoicesEnum
  recommendations: [String!]
  sources: [RelevantIissueSourceNode]
  summary: String
}

type RelevantIissueSourceNode {
  id: ID!
  source: String
  sourceType: IesgRelevantIissueSourceSourceTypeChoices
}

type RelevantPositiveNode {
  maturityLevel: String
  summary: String
}

type RenameWatchlist {
  watchlist: WatchlistNode
}

type ResendSurvey {
  survey: SurveyNode
}

input RoleAssignmentInput {
  role: PortalUserRoleEnum = null
}

type RoleAssignmentNode {
  id: ID!
  role: PortalUserRoleEnum
  user: PortalUserNode!
}

type SaveFileToGCS {
  filePath: String
  fileUrl: String
  message: String
  success: Boolean
}

type ShareSurvey {
  survey: SurveyNode
}

"""
An enumeration.
"""
enum StatusChoicesEnum {
  DONE
  ERROR
  IN_PROGRESS
  ISSUES_EVALUATED
  STARTED
}

type SubmitSurvey {
  survey: SurveyNode
}

type SurveyBatchNode implements Node {
  createdAt: DateTime
  description: String!

  """
  The ID of the object
  """
  id: ID!
  name: String
  surveys: [SurveyNode]
  template: IportalSurveyBatchTemplateChoices
  tenant: TenantNode
  updatedAt: DateTime
}

type SurveyBatchNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [SurveyBatchNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `SurveyBatchNode` and its cursor.
"""
type SurveyBatchNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: SurveyBatchNode
}

input SurveyContactInput {
  email: String!
  name: String
  role: SurveyContactRoleEnum = null
}

"""
An enumeration.
"""
enum SurveyContactRoleEnum {
  CONTRIBUTOR
  SUBMITTER
}

type SurveyDocumentNode {
  file: String!
  fileName: String!
  fileSize: Int!
  id: ID!
  isAttachment: Boolean!
}

type SurveyNode implements Node {
  attachedDocuments: [SurveyDocumentNode]
  batch: SurveyBatchNode
  company: CompanyNode
  createdAt: DateTime
  deadline: DateTime
  financialYear: Int
  formData: JSONString
  id: ID!
  inputDocuments: [SurveyDocumentNode]
  langConsent: String
  lastReminder: DateTime
  prefilledData: JSONString
  progress: Float
  readyForEdit: Boolean!
  status: SurveyStatusEnum
  statusChangedAt: DateTime
  submittedAt: DateTime
  surveyPath: String
  template: SurveyTemplateEnum
  tenant: TenantNode
  updatedAt: DateTime
}

type SurveyNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [SurveyNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `SurveyNode` and its cursor.
"""
type SurveyNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: SurveyNode
}

"""
An enumeration.
"""
enum SurveyStatusEnum {
  CANCELLED
  COMPLETE
  IN_PROGRESS
  NOT_STARTED
  OPEN
  PENDING
  PROCESSING
  SENT
  SUBMITTED
}

"""
An enumeration.
"""
enum SurveyTemplateEnum {
  SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1
  UNIFIED_QUESTIONNAIRE_V1
}

type TenantNode implements Node {
  createdAt: DateTime
  id: ID!
  name: String
  updatedAt: DateTime
}

type TenantNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [TenantNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `TenantNode` and its cursor.
"""
type TenantNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: TenantNode
}

type URLNode {
  id: ID!
  url: String!
}

type UpdatePortalUser {
  portalUser: PortalUserNode
}

type UpdateSurvey {
  survey: SurveyNode
}

type UpdateSurveyBatch {
  surveyBatch: SurveyBatchNode
}

type UpdateSurveyStatus {
  message: String
  success: Boolean
  surveys: [SurveyNode]
}

"""
Create scalar that ignores normal serialization/deserialization, since
that will be handled by the multipart request spec
"""
scalar Upload

type WatchlistEntryNode {
  company: CompanyNode!
  contacts: [CompanyContactNode!]!
  id: ID!
  watchlist: WatchlistNode!
}

type WatchlistNode implements Node {
  createdAt: DateTime
  entries: [WatchlistEntryNode!]!
  id: ID!
  name: String!
  portalUser: PortalUserNode
  tenant: TenantNode
  updatedAt: DateTime
}

type WatchlistNodeConnection {
  """
  Contains the nodes in this connection.
  """
  edges: [WatchlistNodeEdge]!

  """
  Pagination data for this connection.
  """
  pageInfo: PageInfo!
}

"""
A Relay edge containing a `WatchlistNode` and its cursor.
"""
type WatchlistNodeEdge {
  """
  A cursor for use in pagination
  """
  cursor: String!

  """
  The item at the end of the edge
  """
  node: WatchlistNode
}
