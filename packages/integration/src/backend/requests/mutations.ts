/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { graphql } from "../gql"

export const AddEntryToWatchlistMutation = graphql(/* GraphQL */ `
  mutation addEntryToWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $companyId: ID!, $website: String) {
    addEntryToWatchlist(
      requestedBy: $requestedBy
      watchlistId: $watchlistId
      companyId: $companyId
      website: $website
    ) {
      entry {
        ...WatchlistEntryItem
      }
    }
  }
`)

export const AutofillSurveyMutation = graphql(/* GraphQL */ `
  mutation autofillSurveyMutation($requestedBy: String!, $surveyId: ID!, $inputDocumentsUrls: [String]) {
    autofillSurvey(requestedBy: $requestedBy, surveyId: $surveyId, inputDocumentsUrls: $inputDocumentsUrls) {
      survey {
        ...SurveyItem
      }
    }
  }
`)

export const CreatePortalUserMutation = graphql(/* GraphQL */ `
  mutation createPortalUserMutation(
    $requestedBy: String!
    $email: String!
    $firstName: String
    $lastName: String
    $isActive: Boolean
    $roles: [RoleAssignmentInput!]!
    $companyId: ID
  ) {
    createPortalUser(
      requestedBy: $requestedBy
      email: $email
      firstName: $firstName
      lastName: $lastName
      isActive: $isActive
      roles: $roles
      companyId: $companyId
    ) {
      portalUser {
        ...PortalUserItem
      }
    }
  }
`)

export const CreateSurveyMutation = graphql(/* GraphQL */ `
  mutation CreateSurveyMutation(
    $requestedBy: String!
    $template: SurveyTemplateEnum!
    $batchId: ID!
    $companySurveys: [CompanySurveysInput!]!
  ) {
    createSurvey(requestedBy: $requestedBy, template: $template, batchId: $batchId, companySurveys: $companySurveys) {
      surveys {
        id
        deadline
        readyForEdit
        formData
        prefilledData
        company {
          id
          name
        }
        progress
        status
        financialYear
      }
    }
  }
`)

export const CreateSurveyBatchMutation = graphql(/* GraphQL */ `
  mutation CreateSurveyBatchMutation(
    $name: String!
    $template: SurveyTemplateEnum!
    $tenantId: ID
    $description: String
  ) {
    createSurveyBatch(name: $name, template: $template, tenantId: $tenantId, description: $description) {
      surveyBatch {
        ...SurveyBatchItem
      }
    }
  }
`)

export const CreateWatchlistMutation = graphql(/* GraphQL */ `
  mutation CreateWatchlistMutation($name: String!, $portalUserId: ID!, $requestedBy: String!) {
    createWatchlist(name: $name, portalUserId: $portalUserId, requestedBy: $requestedBy) {
      watchlist {
        ...WatchlistItem
      }
    }
  }
`)

export const DeleteWatchlistMutation = graphql(/* GraphQL */ `
  mutation DeleteWatchlistMutation($requestedBy: String!, $watchlistId: ID!) {
    deleteWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId) {
      success
    }
  }
`)

export const RenameWatchlistMutation = graphql(/* GraphQL */ `
  mutation RenameWatchlistMutation($requestedBy: String!, $watchlistId: ID!, $name: String!) {
    renameWatchlist(requestedBy: $requestedBy, watchlistId: $watchlistId, name: $name) {
      watchlist {
        ...WatchlistItem
      }
    }
  }
`)

export const ResendSurveyMutation = graphql(/* GraphQL */ `
  mutation ResendSurveyMutation($requestedBy: String!, $surveyId: ID!) {
    resendSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {
      survey {
        ...SurveyItem
      }
    }
  }
`)

export const ShareSurveyMutation = graphql(/* GraphQL */ `
  mutation ShareSurveyMutation($requestedBy: String!, $surveyId: ID!, $sharedWith: [SurveyContactInput!]!) {
    shareSurvey(requestedBy: $requestedBy, surveyId: $surveyId, sharedWith: $sharedWith) {
      survey {
        ...SurveyItem
      }
    }
  }
`)

export const SubmitSurveyMutation = graphql(/* GraphQL */ `
  mutation SubmitSurveyMutation($requestedBy: String!, $surveyId: ID!) {
    submitSurvey(requestedBy: $requestedBy, surveyId: $surveyId) {
      survey {
        ...SurveyItem
      }
    }
  }
`)

export const UpdateSurveyMutation = graphql(/* GraphQL */ `
  mutation UpdateSurveyMutation($surveyId: ID!, $status: SurveyStatusEnum, $progress: Float, $formData: JSONString) {
    updateSurvey(surveyId: $surveyId, status: $status, progress: $progress, formData: $formData) {
      survey {
        ...SurveyItem
      }
    }
  }
`)

export const UpdateSurveyStatusMutation = graphql(/* GraphQL */ `
  mutation UpdateSurveyStatusMutation($status: SurveyStatusEnum!, $surveyIds: [ID!]!) {
    updateSurveyStatus(status: $status, surveyIds: $surveyIds) {
      surveys {
        ...SurveyItem
      }
    }
  }
`)

export const UpdatePortalUserMutation = graphql(/* GraphQL */ `
  mutation UpdatePortalUserMutation(
    $requestedBy: String!
    $id: ID!
    $isActive: Boolean
    $roles: [RoleAssignmentInput]
    $firstName: String
    $lastName: String
    $hasGivenConsent: Boolean
  ) {
    updatePortalUser(
      requestedBy: $requestedBy
      id: $id
      isActive: $isActive
      roles: $roles
      firstName: $firstName
      lastName: $lastName
      hasGivenConsent: $hasGivenConsent
    ) {
      portalUser {
        ...PortalUserItem
      }
    }
  }
`)

export const SaveFileToGcsMutation = graphql(/* GraphQL */ `
  mutation SaveFileToGcsMutation($file: Upload!, $filename: String!) {
    saveFileToGcs(file: $file, filename: $filename) {
      success
      message
      filePath
      fileUrl
    }
  }
`)
