/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { graphql } from "../gql"

export const EvaluationItem = graphql(/* GraphQL */ `
  fragment EvaluationItem on EvaluationNode {
    id
    status
    completedAt
    evaluationYear
    overallRecommendations
    environmentalMaturity
    socialMaturity
    governanceMaturity
    overallSummary
    inputs {
      dataDepth {
        baseData
        publicData
        selfAssessment
        advanced
      }
      evaluationDepth {
        esgRiskMapping
        aiEvaluations
        esgRecommendations
        enrichedEvaluation
        fullEvaluation
      }
      sources {
        businessRegistry
        annualReport
        website
        media
        questionnaire
        proprietaryData
      }
    }
    company {
      id
      name
      registrationNumber
    }
  }
`)

export const CompanyItem = graphql(/* GraphQL */ `
  fragment CompanyItem on CompanyNode {
    id
    name
    about
    aboutEn
    aboutEt
    registrationNumber
    status
    country
    employeeNumber
    street
    houseNumber
    postalCode
    city
    registrationDate
    urls {
      url
    }
    latestGhgEmission {
      id
      year
      source
      basis
      multiplier
      unit
      value
      scope
    }
    mediaSources {
      id
      url
      title
      summary
      story
      publishedAt
      startedAt
      completedAt
      esgCategory
      sentiment
      tiPossibleName
      metaData
    }
    latestEsgMaterialityMapping {
      executionDate
      relevantPositives {
        summary
        maturityLevel
      }
      relevantIssues {
        iissue {
          id
          name
          category
          issue {
            description
          }
        }
        maturityLevel
        gabAnalysisResults
        summary
        recommendations
        sources {
          sourceType
          source
        }
      }
    }
    publishedEvaluations {
      ...EvaluationItem
    }
    businessActivities {
      id
      euTaxonomyEligible
      naceCode {
        id
        label
        labelEt
        code
        overallEbrdRiskLevel
        environmentalEbrdRiskLevel
        socialEbrdRiskLevel
      }
      latestReportItem {
        isMainActivity
        businessActivityRevenue {
          amount
          currency
        }
      }
    }
    annualReports {
      financialYear
      averageFullTimeEmployeesConsolidated
      companyRevenueConsolidated {
        amount
        currency
      }
      netProfitConsolidated {
        amount
        currency
      }
      totalAssets {
        amount
        currency
      }
    }
  }
`)

export const NaceCodeItem = graphql(/* GraphQL */ `
  fragment NaceCodeItem on NaceCodeNode {
    id
    label
    code
    overallEbrdRiskLevel
    environmentalEbrdRiskLevel
    socialEbrdRiskLevel
    parent {
      id
      parent {
        id
      }
      label
      code
      overallEbrdRiskLevel
      environmentalEbrdRiskLevel
      socialEbrdRiskLevel
    }
  }
`)

export const PortalUserItem = graphql(/* GraphQL */ `
  fragment PortalUserItem on PortalUserNode {
    id
    tenant {
      id
    }
    email
    lastName
    firstName
    company {
      id
    }
    hasGivenConsent
    isActive
    dateJoined
    lastLogin
    createdAt
    updatedAt
    roles {
      role
    }
  }
`)

export const SurveyItem = graphql(/* GraphQL */ `
  fragment SurveyItem on SurveyNode {
    id
    tenant {
      id
      name
    }
    updatedAt
    createdAt
    submittedAt
    company {
      id
      name
    }
    batch {
      id
    }
    financialYear
    deadline
    template
    status
    readyForEdit
    prefilledData
    inputDocuments {
      id
      file
      fileName
      fileSize
      isAttachment
    }
    lastReminder
    formData
    statusChangedAt
    progress
    langConsent
  }
`)

export const SurveyBatchItem = graphql(/* GraphQL */ `
  fragment SurveyBatchItem on SurveyBatchNode {
    id
    name
    description
    template
    tenant {
      id
    }
  }
`)

export const TenantItem = graphql(/* GraphQL */ `
  fragment TenantItem on TenantNode {
    id
    name
    createdAt
    updatedAt
  }
`)

export const WatchlistEntryItem = graphql(/* GraphQL */ `
  fragment WatchlistEntryItem on WatchlistEntryNode {
    id
    company {
      ...CompanyItem
    }
    contacts {
      id
      email
      name
    }
  }
`)

export const WatchlistItem = graphql(/* GraphQL */ `
  fragment WatchlistItem on WatchlistNode {
    id
    tenant {
      id
    }
    portalUser {
      id
    }
    name
    updatedAt
    createdAt
    entries {
      ...WatchlistEntryItem
    }
  }
`)
