/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { graphql } from "../gql"

export const CompanyQuery = graphql(/* GraphQL */ `
  query companyQuery($id: ID!) {
    company(id: $id) {
      ...CompanyItem
    }
  }
`)

export const CompaniesQuery = graphql(/* GraphQL */ `
  query companiesQuery($first: Int, $after: String, $country: IorderCompanyCountryChoices) {
    companies(first: $first, after: $after, country: $country) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...CompanyItem
        }
      }
    }
  }
`)

export const EvaluationQuery = graphql(/* GraphQL */ `
  query evaluationQuery($id: ID!) {
    evaluation(id: $id) {
      ...EvaluationItem
    }
  }
`)

export const EvaluationsQuery = graphql(/* GraphQL */ `
  query evaluationsQuery($first: Int, $after: String) {
    evaluations(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...EvaluationItem
        }
      }
    }
  }
`)

export const NaceCodeQuery = graphql(/* GraphQL */ `
  query NaceCodeQuery($id: ID!) {
    naceCode(id: $id) {
      ...NaceCodeItem
    }
  }
`)

export const NaceCodesQuery = graphql(/* GraphQL */ `
  query NaceCodesQuery($first: Int, $after: String) {
    naceCodes(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...NaceCodeItem
        }
      }
    }
  }
`)

export const PortalUserQuery = graphql(/* GraphQL */ `
  query PortalUserQuery($id: ID!) {
    portalUser(id: $id) {
      ...PortalUserItem
    }
  }
`)

export const PortalUsersQuery = graphql(/* GraphQL */ `
  query PortalUsersQuery($first: Int, $after: String) {
    portalUsers(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...PortalUserItem
        }
      }
    }
  }
`)

export const SurveyQuery = graphql(/* GraphQL */ `
  query SurveyQuery($id: ID!) {
    survey(id: $id) {
      ...SurveyItem
    }
  }
`)

export const SurveysQuery = graphql(/* GraphQL */ `
  query SurveysQuery($first: Int, $after: String) {
    surveys(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...SurveyItem
        }
      }
    }
  }
`)

export const SurveyBatchQuery = graphql(/* GraphQL */ `
  query SurveyBatchQuery($id: ID!) {
    surveyBatch(id: $id) {
      ...SurveyBatchItem
    }
  }
`)

export const SurveyBatchesQuery = graphql(/* GraphQL */ `
  query SurveyBatchesQuery($first: Int, $after: String) {
    surveyBatches(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...SurveyBatchItem
        }
      }
    }
  }
`)

export const TenantQuery = graphql(/* GraphQL */ `
  query TenantQuery($id: ID!) {
    tenant(id: $id) {
      ...TenantItem
    }
  }
`)

export const TenantsQuery = graphql(/* GraphQL */ `
  query TenantsQuery($first: Int, $after: String) {
    tenants(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...TenantItem
        }
      }
    }
  }
`)

export const WatchlistQuery = graphql(/* GraphQL */ `
  query WatchlistQuery($id: ID!) {
    watchlist(id: $id) {
      ...WatchlistItem
    }
  }
`)

export const WatchlistsQuery = graphql(/* GraphQL */ `
  query WatchlistsQuery($first: Int, $after: String) {
    watchlists(first: $first, after: $after) {
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          ...WatchlistItem
        }
      }
    }
  }
`)

export const GetFileUrlQuery = graphql(/* GraphQL */ `
  query GetFileUrlQuery($filePath: String!) {
    getFileUrl(filePath: $filePath)
  }
`)
