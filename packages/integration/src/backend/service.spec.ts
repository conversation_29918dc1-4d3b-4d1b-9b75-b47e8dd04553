/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { beforeAll, describe, expect, it } from "vitest"

import { env } from "../../env"
import { PortalUserRoleEnum } from "./gql/graphql"
import { BackendIntegrationService } from "./service"

// Get the environment variables
const apiUrl = env.BACKEND_API_URL
const apiToken = env.BACKEND_API_TOKEN

// Skip tests if the required environment variables are not present
describe.runIf(apiUrl && apiToken)("BackendIntegrationService", () => {
  let service: BackendIntegrationService

  beforeAll(() => {
    service = new BackendIntegrationService({ apiUrl, apiToken })
  })

  describe("Queries", () => {
    it("should fetch a single company", async () => {
      const company = await service.getCompany({ id: "1" })
      expect(company).toBeDefined()
      expect(company.id).toBe("1")
    })

    it("should fetch the first company from the list", async () => {
      const firstCompany = await getFirstRecord(service.getCompanies())
      expect(firstCompany).toBeDefined()
      expect(firstCompany.id).toBeDefined()
    })

    it("should fetch a single evaluation", async () => {
      const evaluation = await service.getEvaluation({ id: "1" })
      expect(evaluation).toBeDefined()
      expect(evaluation.id).toBe("1")
    })

    it("should fetch the first evaluation from the list", async () => {
      const firstEvaluation = await getFirstRecord(service.getEvaluations())
      expect(firstEvaluation).toBeDefined()
      expect(firstEvaluation.id).toBeDefined()
    })

    it("should fetch a single NACE code", async () => {
      const naceCode = await service.getNaceCode({ id: "1271" })
      expect(naceCode).toBeDefined()
      expect(naceCode.id).toBe("1271")
    })

    it("should fetch the first NACE code from the list", async () => {
      const firstNaceCode = await getFirstRecord(service.getNaceCodes())
      expect(firstNaceCode).toBeDefined()
      expect(firstNaceCode.id).toBeDefined()
    })

    it("should fetch a single portal user", async () => {
      const portalUser = await service.getPortalUser({ id: "1" })
      expect(portalUser).toBeDefined()
      expect(portalUser.id).toBe("1")
    })

    it("should fetch the first portal user from the list", async () => {
      const firstPortalUser = await getFirstRecord(service.getPortalUsers())
      expect(firstPortalUser).toBeDefined()
      expect(firstPortalUser.id).toBeDefined()
    })

    it("should fetch a single survey", async () => {
      const survey = await service.getSurvey({ id: "1" })
      expect(survey).toBeDefined()
      expect(survey.id).toBe("1")
    })

    it("should fetch the first survey from the list", async () => {
      const firstSurvey = await getFirstRecord(service.getSurveys())
      expect(firstSurvey).toBeDefined()
      expect(firstSurvey.id).toBeDefined()
    })

    it("should fetch a single tenant", async () => {
      const tenant = await service.getTenant({ id: "1" })
      expect(tenant).toBeDefined()
      expect(tenant.id).toBe("1")
    })

    it("should fetch all tenants", async () => {
      const tenants = await asArray(service.getTenants())
      expect(tenants).toBeDefined()
      expect(tenants.length).toBeGreaterThan(0)
    })

    it("should fetch a single watchlist", async () => {
      const watchlist = await service.getWatchlist({ id: "1" })
      expect(watchlist).toBeDefined()
      expect(watchlist.id).toBe("1")
    })

    it("should fetch the first watchlist from the list", async () => {
      const firstWatchlist = await getFirstRecord(service.getWatchlists())
      expect(firstWatchlist).toBeDefined()
      expect(firstWatchlist.id).toBeDefined()
    })
  })

  // Mutation tests
  describe("Mutations", () => {
    const requestedBy = "<EMAIL>"

    // it("should add an entry to a watchlist", async () => {
    //   // Create a watchlist
    //   const createWatchlistResult = await service.createWatchlist({
    //     requestedBy,
    //     name: `Test Watchlist ${Date.now()}`,
    //     portalUserId: "1", // Assuming portal user with ID 1 exists
    //   })
    //   const watchlistId = createWatchlistResult.createWatchlist?.watchlist?.id

    //   // Get the first company
    //   const company = await getFirstRecord(service.getCompanies())

    //   const result = await service.addEntryToWatchlist({
    //     requestedBy,
    //     watchlistId: watchlistId!,
    //     companyId: company.id,
    //   })

    //   expect(result.addEntryToWatchlist?.entry).toBeDefined()
    //   expect(result.addEntryToWatchlist?.entry?.company.id).toBe(company.id)
    // })

    // it("should autofill a survey", async () => {
    //   // Create a survey
    //   const surveys = await service.createSurvey({
    //     batchId: "batch1",
    //     companySurveys: [{ companyId: "company1", contactEmails: [], deadline: "2024-12-31", financialYear: 2024 }],
    //     requestedBy,
    //     template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
    //   })
    //   const surveyId = surveys[0]?.id

    //   const survey = await service.autofillSurvey({
    //     requestedBy,
    //     surveyId: surveyId,
    //     inputDocumentsUrls: ["https://example.com/document.pdf"],
    //   })

    //   expect(survey).toBeDefined()
    //   expect(survey.id).toBe(surveyId)
    // })

    it("should create a portal user", async () => {
      const result = await service.createPortalUser({
        requestedBy,
        email: `test-${Date.now()}@example.com`,
        firstName: "Test",
        lastName: "User",
        isActive: true,
        roles: [{ role: PortalUserRoleEnum.CompanyUser }],
      })

      expect(result.createPortalUser?.portalUser).toBeDefined()
      expect(result.createPortalUser?.portalUser?.email).toContain("test-")
    })

    // it("should create a survey", async () => {
    //   const surveys = await service.createSurvey({
    //     batchId: "batch1",
    //     companyIds: ["company1"],
    //     status: SurveyStatusEnum.Open,
    //     requestedBy,
    //     template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
    //     financialYear: 2024,
    //     deadline: new Date("2024-12-31"),
    //   })

    //   expect(surveys).toBeDefined()
    //   expect(surveys[0]?.company).toBeDefined()
    // })

    it("should create and delete a watchlist", async () => {
      // Create a watchlist
      const createResult = await service.createWatchlist({
        requestedBy,
        name: `Test Watchlist ${Date.now()}`,
        portalUserId: "1", // Assuming portal user with ID 1 exists
      })

      expect(createResult.createWatchlist?.watchlist).toBeDefined()
      expect(createResult.createWatchlist?.watchlist?.name).toContain("Test Watchlist")

      const watchlistId = createResult.createWatchlist?.watchlist?.id

      // Delete the watchlist
      const deleteResult = await service.deleteWatchlist({
        requestedBy,
        watchlistId: watchlistId!,
      })

      expect(deleteResult.deleteWatchlist?.success).toBe(true)
    })

    it("should rename a watchlist", async () => {
      // Create a watchlist
      const createResult = await service.createWatchlist({
        requestedBy,
        name: `Test Watchlist ${Date.now()}`,
        portalUserId: "1", // Assuming portal user with ID 1 exists
      })

      const watchlistId = createResult.createWatchlist?.watchlist?.id
      const newName = `Renamed Watchlist ${Date.now()}`

      const result = await service.renameWatchlist({
        requestedBy,
        watchlistId: watchlistId!,
        name: newName,
      })

      expect(result.renameWatchlist?.watchlist).toBeDefined()
      expect(result.renameWatchlist?.watchlist?.name).toBe(newName)
    })

    // it("should resend a survey", async () => {
    //   // Create a survey
    //   const surveys = await service.createSurvey({
    //     batchId: "batch1",
    //     companyIds: ["company1"],
    //     status: SurveyStatusEnum.Open,
    //     requestedBy,
    //     template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
    //     financialYear: 2024,
    //     deadline: new Date("2024-12-31"),
    //   })
    //   const surveyId = surveys[0]?.id

    //   const survey = await service.resendSurvey({
    //     requestedBy,
    //     surveyId: surveyId,
    //   })

    //   expect(survey).toBeDefined()
    //   expect(survey.id).toBe(surveyId)
    // })

    // it.skip("should share a survey", async () => {
    //   // Create a survey
    //   const surveys = await service.createSurvey({
    //     batchId: "batch1",
    //     companyIds: ["company1"],
    //     status: SurveyStatusEnum.Open,
    //     requestedBy,
    //     template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
    //     financialYear: 2024,
    //     deadline: new Date("2024-12-31"),
    //   })
    //   const surveyId = surveys[0]?.id

    //   const survey = await service.shareSurvey({
    //     requestedBy,
    //     surveyId: surveyId,
    //     sharedWith: [{ email: "<EMAIL>", role: SurveyContactRoleEnum.Contributor }],
    //   })

    //   expect(survey).toBeDefined()
    //   expect(survey.id).toBe(surveyId)
    // })

    // it.skip("should submit a survey", async () => {
    //   // Create a survey
    //   const surveys = await service.createSurvey({
    //     batchId: "batch1",
    //     companyIds: ["company1"],
    //     status: SurveyStatusEnum.Open,
    //     requestedBy,
    //     template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
    //     financialYear: 2024,
    //     deadline: new Date("2024-12-31"),
    //   })
    //   const surveyId = surveys[0]?.id

    //   const survey = await service.submitSurvey({
    //     requestedBy,
    //     surveyId: surveyId,
    //   })

    //   expect(survey).toBeDefined()
    //   expect(survey.id).toBe(surveyId)
    //   expect(survey.status).toBe("SUBMITTED")
    // })

    it("should update a portal user", async () => {
      // Create a portal user
      const createUserResult = await service.createPortalUser({
        requestedBy,
        email: `test-${Date.now()}@example.com`,
        firstName: "Test",
        lastName: "User",
        isActive: true,
        roles: [{ role: PortalUserRoleEnum.CompanyUser }],
      })
      const userId = createUserResult.createPortalUser?.portalUser?.id

      const result = await service.updatePortalUser({
        requestedBy,
        id: userId!,
        isActive: false,
        roles: [{ role: PortalUserRoleEnum.ClientAdmin }],
        hasGivenConsent: true,
      })

      expect(result.updatePortalUser?.portalUser).toBeDefined()
      expect(result.updatePortalUser?.portalUser?.id).toBe(userId)
      expect(result.updatePortalUser?.portalUser?.isActive).toBe(false)
      expect(result.updatePortalUser?.portalUser?.roles).toContainEqual(
        expect.objectContaining({ role: PortalUserRoleEnum.ClientAdmin })
      )
    })
  })
})

// Helper functions

/**
 * Retrieves the first record from an async generator and acts as a type guard.
 *
 * @param gen The async generator to retrieve the first record from
 * @returns A promise that resolves to the first record
 * @throws AssertionError if the generator is empty
 */
async function getFirstRecord<T>(gen: AsyncGenerator<T, void, unknown>): Promise<T> {
  const { value, done } = await gen.next()
  if (done) {
    throw new Error("AssertionError: Expected at least one record, but the generator was empty")
  }
  return value
}

/**
 * Converts an async generator to an array.
 *
 * @param gen The async generator to convert to an array
 * @returns A promise that resolves to an array of the generator's values
 */
async function asArray<T>(gen: AsyncGenerator<T, void, unknown>): Promise<T[]> {
  const result: T[] = []
  for await (const item of gen) {
    result.push(item)
  }
  return result
}
