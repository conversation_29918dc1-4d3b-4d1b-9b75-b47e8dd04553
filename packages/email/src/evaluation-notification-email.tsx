/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { FC } from "react"

import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@kreios/mail-sender/utils"

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "580px",
}

const logoContainer = {
  display: "flex",
  alignItems: "center",
  marginBottom: "24px",
}

const logo = {
  marginRight: "12px",
}

const heading = {
  fontSize: "32px",
  fontWeight: "bold",
  color: "#22c55e",
}

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
}

const button = {
  backgroundColor: "#22c55e",
  borderRadius: "4px",
  color: "#fff",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "100%",
  padding: "12px 20px",
  fontSize: "16px",
  boxShadow: "0 4px 6px rgba(34, 197, 94, 0.25)",
  border: "1px solid #1ea550",
  transition: "all 0.2s ease-in-out",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
}

export const EvaluationNotificationEmail: FC<{ firstName?: string; link: string }> = ({ firstName, link }) => {
  return (
    <Html>
      <Head />
      <Preview>Your ESG assessment is complete - Access your report now</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png"
              width="80"
              height="80"
              alt="Impactly Logo"
              style={logo}
            />
            <Heading style={heading}>Impactly</Heading>
          </Section>
          <Text style={paragraph}>Hello {firstName}</Text>
          <Text style={paragraph}>
            Your company's ESG assessment is now complete! You can view the full report by clicking the link below:
          </Text>
          <Button style={button} href={link}>
            Access Your ESG Assessment
          </Button>
          <Text style={paragraph}>If you have any questions, feel free to reach out.</Text>
          <Text style={paragraph}>
            Best regards,
            <br />
            The Impactly Team
          </Text>
          <Hr style={hr} />
          <Text style={footer}>
            Disclaimer: This report was generated using AI based on publicly available information and has not been
            verified by a human. It is intended for demonstration purposes only and should not be used for commercial
            purposes or redistributed. Impactly accepts no liability for the accuracy, completeness, or reliability of
            the content.
          </Text>
        </Container>
      </Body>
    </Html>
  )
}
