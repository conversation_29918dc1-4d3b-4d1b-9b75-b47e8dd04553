/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import * as React from "react"

import { Body, Container, Head, Heading, Hr, Html, Img, Preview, Section, Text } from "@kreios/mail-sender/utils"

export const InitialNotificationAdminEmail: React.FC<{ companyName: string; userEmail: string }> = ({
  companyName,
  userEmail,
}) => {
  return (
    <Html>
      <Head />
      <Preview>New assessment request submitted via Impactly Portal</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png"
              width="80"
              height="80"
              alt="Impactly Logo"
              style={logo}
            />
            <Heading style={heading}>Impactly</Heading>
          </Section>
          <Text style={paragraph}>Hello Impactly Team,</Text>
          <Text style={paragraph}>
            A new assessment request has been submitted via the Impactly Portal for {companyName} by {userEmail}
            {/* You can view the details by clicking on
            the link below: */}
          </Text>
          {/* <Button    style={button} href="https://impactly.com/assessment-requests">
            View Assessment Request
          </Button> */}
          <Hr style={hr} />
          <Text style={footer}>This is an automated message. Please do not reply directly to this email.</Text>
        </Container>
      </Body>
    </Html>
  )
}

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "580px",
}

const logoContainer = {
  display: "flex",
  alignItems: "center",
  marginBottom: "24px",
}

const logo = {
  marginRight: "12px",
}

const heading = {
  fontSize: "32px",
  fontWeight: "bold",
  color: "#22c55e",
}

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
}
