/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import * as React from "react"

import { Body, Container, Head, Heading, Hr, Html, Img, Preview, Section, Text } from "@kreios/mail-sender/utils"

interface RequestAccessNotificationEmailProps {
  userEmail: string
  companyName: string
  requestDate?: string
}

export const RequestAccessNotificationEmail: React.FC<RequestAccessNotificationEmailProps> = ({
  userEmail,
  companyName,
  requestDate = new Date().toLocaleDateString(),
}) => {
  return (
    <Html>
      <Head />
      <Preview>New access request for Impactly Client Portal</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png"
              width="80"
              height="80"
              alt="Impactly Logo"
              style={logo}
            />
            <Heading style={heading}>Impactly Client Portal</Heading>
          </Section>

          <Heading style={subHeading}>New Access Request</Heading>

          <Text style={paragraph}>A new user has requested access to the Impactly Client Portal.</Text>

          <Section style={detailsContainer}>
            <Text style={detailLabel}>Email Address:</Text>
            <Text style={detailValue}>{userEmail}</Text>

            <Text style={detailLabel}>Company Name:</Text>
            <Text style={detailValue}>{companyName}</Text>

            <Text style={detailLabel}>Request Date:</Text>
            <Text style={detailValue}>{requestDate}</Text>
          </Section>

          <Hr style={hr} />

          <Text style={paragraph}>
            Please review this request and take appropriate action to grant or deny access to the user.
          </Text>

          <Text style={paragraph}>To manage user access, please log in to the admin panel.</Text>

          <Text style={footer}>
            Best regards,
            <br />
            Impactly Team
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

// Styles
const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
}

const logoContainer = {
  display: "flex",
  flexDirection: "column" as const,
  alignItems: "center",
  marginBottom: "32px",
}

const logo = {
  margin: "0 auto",
}

const heading = {
  fontSize: "24px",
  letterSpacing: "-0.5px",
  lineHeight: "1.3",
  fontWeight: "400",
  color: "#484848",
  padding: "17px 0 0",
  margin: "0",
  textAlign: "center" as const,
}

const subHeading = {
  fontSize: "20px",
  lineHeight: "1.4",
  fontWeight: "500",
  color: "#484848",
  margin: "24px 0 16px",
}

const paragraph = {
  fontSize: "15px",
  lineHeight: "1.4",
  color: "#3c4149",
  margin: "16px 0",
}

const detailsContainer = {
  backgroundColor: "#f6f9fc",
  borderRadius: "8px",
  padding: "20px",
  margin: "24px 0",
}

const detailLabel = {
  fontSize: "14px",
  fontWeight: "600",
  color: "#6b7280",
  margin: "8px 0 4px",
}

const detailValue = {
  fontSize: "15px",
  color: "#1f2937",
  margin: "0 0 16px",
  fontWeight: "500",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  fontSize: "14px",
  lineHeight: "1.4",
  color: "#6b7280",
  margin: "32px 0 0",
}
