/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import * as React from "react"

import { Body, Container, Head, Heading, Hr, Html, Img, Preview, Section, Text } from "@kreios/mail-sender/utils"

interface RequestAccessNotificationEmailProps {
  userEmail: string
  companyName: string
  requestDate?: string
}

export const RequestAccessNotificationEmail: React.FC<RequestAccessNotificationEmailProps> = ({
  userEmail,
  companyName,
  requestDate = new Date().toLocaleDateString(),
}) => {
  return (
    <Html>
      <Head />
      <Preview>Uus ligipääsutaotlus Impactly kliendi portaalile</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png"
              width="80"
              height="80"
              alt="Impactly Logo"
              style={logo}
            />
            <Heading style={heading}>Impactly Kliendi Portaal</Heading>
          </Section>

          <Heading style={subHeading}>Uus Ligipääsutaotlus</Heading>

          <Text style={paragraph}>Uus kasutaja on taotlenud ligipääsu Impactly kliendi portaalile.</Text>

          <Section style={detailsContainer}>
            <Text style={detailLabel}>E-posti aadress:</Text>
            <Text style={detailValue}>{userEmail}</Text>

            <Text style={detailLabel}>Ettevõtte nimi:</Text>
            <Text style={detailValue}>{companyName}</Text>

            <Text style={detailLabel}>Taotluse kuupäev:</Text>
            <Text style={detailValue}>{requestDate}</Text>
          </Section>

          <Hr style={hr} />

          <Text style={paragraph}>
            Palun vaadake see taotlus üle ja võtke vastavad meetmed kasutajale ligipääsu andmiseks või keelamiseks.
          </Text>

          <Text style={paragraph}>Kasutajate ligipääsu haldamiseks logige sisse administraatori paneelile.</Text>

          <Text style={footer}>
            Lugupidamisega,
            <br />
            Impactly Meeskond
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

// Styles
const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
}

const logoContainer = {
  display: "flex",
  flexDirection: "column" as const,
  alignItems: "center",
  marginBottom: "32px",
}

const logo = {
  margin: "0 auto",
}

const heading = {
  fontSize: "24px",
  letterSpacing: "-0.5px",
  lineHeight: "1.3",
  fontWeight: "400",
  color: "#484848",
  padding: "17px 0 0",
  margin: "0",
  textAlign: "center" as const,
}

const subHeading = {
  fontSize: "20px",
  lineHeight: "1.4",
  fontWeight: "500",
  color: "#484848",
  margin: "24px 0 16px",
}

const paragraph = {
  fontSize: "15px",
  lineHeight: "1.4",
  color: "#3c4149",
  margin: "16px 0",
}

const detailsContainer = {
  backgroundColor: "#f6f9fc",
  borderRadius: "8px",
  padding: "20px",
  margin: "24px 0",
}

const detailLabel = {
  fontSize: "14px",
  fontWeight: "600",
  color: "#6b7280",
  margin: "8px 0 4px",
}

const detailValue = {
  fontSize: "15px",
  color: "#1f2937",
  margin: "0 0 16px",
  fontWeight: "500",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  fontSize: "14px",
  lineHeight: "1.4",
  color: "#6b7280",
  margin: "32px 0 0",
}
