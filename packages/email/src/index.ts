/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { EvaluationRequestAggregate } from "@impactly/domain"

import { render } from "@kreios/mail-sender/utils"

import { EnhancedSurveyInvitationEmail } from "./enhanced-survey-invitation-email"
import { EvaluationNotificationEmail } from "./evaluation-notification-email"
import { InitialUserNotificationEmail } from "./inital-notification-email"
import { InitialNotificationAdminEmail } from "./intial-notification-admin-email"
import { RequestAccessNotificationEmail } from "./request-access-notification-email"

export const name = "@impactly/email"

/**
 * Generate email content for survey invitations
 *
 * @param companyName The name of the company
 * @param surveyTemplate The template type of the survey
 * @param surveyLink The link to access the survey
 * @returns An object containing the subject and HTML content of the email
 */
/**
 * Generate email content for survey invitations using the basic template
 */
export async function generateSurveyInvitationEmail(
  companyName: string,
  surveyTemplate: string,
  surveyLink: string
): Promise<{ subject: string; html: string; text: string }> {
  // Format the template name for the subject
  const templateName =
    surveyTemplate === "UNIFIED_QUESTIONNAIRE_V1"
      ? "Unified Questionnaire"
      : surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
        ? "Self Assessment Unified Questionnaire"
        : surveyTemplate

  // Generate the HTML content
  const html = await render(
    EnhancedSurveyInvitationEmail({
      companyName,
      surveyTemplate,
      surveyLink,
    }) as React.ReactElement
  )

  // Generate a plain text version
  const isSelfAssessment = surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"

  const text = `
${isSelfAssessment ? "Self-Assessment" : "Survey"} Invitation: ${templateName} for ${companyName}

You have been invited to complete ${isSelfAssessment ? "a self-assessment" : "a survey"} for ${companyName}.

${isSelfAssessment ? "Self-Assessment Details:" : "Survey Details:"}
- Type: ${templateName}

Please access the ${isSelfAssessment ? "self-assessment" : "survey"} at: ${surveyLink}

Thank you for your participation.
The Impactly Team
  `.trim()

  return {
    subject: `${isSelfAssessment ? "Self-Assessment" : "Survey"} Invitation: ${templateName} for ${companyName}`,
    html,
    text,
  }
}

export async function generateEmailContent(
  type: "request-received" | "request-fulfilled" | "team-notification",
  evaluationRequest: EvaluationRequestAggregate
): Promise<{ subject: string; html: string }> {
  switch (type) {
    case "request-received":
      return {
        subject: "Your Evaluation Request Has Been Received",
        html: await render(
          InitialUserNotificationEmail({ name: evaluationRequest.portalUserName }) as React.ReactElement
        ),
      }
    case "request-fulfilled":
      return {
        subject: "Your Evaluation Request Has Been Fulfilled",
        html: await render(
          EvaluationNotificationEmail({ firstName: evaluationRequest.portalUserName, link: "" }) as React.ReactElement
        ),
      }
    case "team-notification":
      return {
        subject: "New Evaluation Request Received",
        html: await render(
          InitialNotificationAdminEmail({
            companyName: evaluationRequest.companyName,
            userEmail: evaluationRequest.portalUserEmail,
          }) as React.ReactElement
        ),
      }
    default:
      throw new Error(`Invalid notification type`)
  }
}

/**
 * Generates a request access notification email for administrators
 * @param userEmail The email address of the user requesting access
 * @param companyName The company name provided by the user
 * @returns An object containing the subject, HTML content, and plain text version
 */
export async function generateRequestAccessNotificationEmail(
  userEmail: string,
  companyName: string
): Promise<{ subject: string; html: string; text: string }> {
  // Generate the subject line
  const subject = `New Access Request: ${companyName} (${userEmail})`

  // Generate the HTML content
  const html = await render(
    RequestAccessNotificationEmail({
      userEmail,
      companyName,
    }) as React.ReactElement
  )

  // Generate a plain text version
  const text = `
New Access Request for Impactly Client Portal

Email Address: ${userEmail}
Company Name: ${companyName}
Request Date: ${new Date().toLocaleDateString()}

A new user has requested access to the Impactly Client Portal. Please review this request and take appropriate action to grant or deny access to the user.

To manage user access, please log in to the admin panel.

Best regards,
Impactly Team
  `.trim()

  return {
    subject,
    html,
    text,
  }
}
