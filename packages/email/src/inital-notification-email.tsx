/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import * as React from "react"

import { Body, Container, Head, Heading, Hr, Html, Img, Preview, Section, Text } from "@kreios/mail-sender/utils"

export const InitialUserNotificationEmail: React.FC<{ name?: string }> = ({ name }) => {
  return (
    <Html>
      <Head />
      <Preview>Your ESG assessment has been requested</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png"
              width="80"
              height="80"
              alt="Impactly Logo"
              style={logo}
            />
            <Heading style={heading}>Impactly</Heading>
          </Section>
          <Text style={paragraph}>Hello {name}</Text>
          <Text style={paragraph}>
            Thank you for your submission. Your company's ESG assessment has been requested.
          </Text>
          <Text style={paragraph}>We will reach out to you as soon as the report is available.</Text>
          <Text style={paragraph}>
            Best regards,
            <br />
            The Impactly Team
          </Text>
          <Hr style={hr} />
          <Text style={footer}>
            Disclaimer: This email is in response to your ESG assessment request. The assessment will be generated using
            AI based on publicly available information and will be verified by our team. Impactly accepts no liability
            for the accuracy, completeness, or reliability of the content until the final report is delivered.
          </Text>
        </Container>
      </Body>
    </Html>
  )
}

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "580px",
}

const logoContainer = {
  display: "flex",
  alignItems: "center",
  marginBottom: "24px",
}

const logo = {
  marginRight: "12px",
}

const heading = {
  fontSize: "32px",
  fontWeight: "bold",
  color: "#22c55e",
}

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
}
