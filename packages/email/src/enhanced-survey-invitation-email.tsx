/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { FC } from "react"

import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@kreios/mail-sender/utils"

// Define styles for the email template
const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "580px",
}

const logoContainer = {
  display: "flex",
  alignItems: "center",
  marginBottom: "24px",
}

const logo = {
  marginRight: "12px",
}

const heading = {
  fontSize: "32px",
  fontWeight: "bold",
  color: "#18181B",
}

const paragraph = {
  fontSize: "16px",
  lineHeight: "26px",
}

const button = {
  backgroundColor: "#22c55e",
  borderRadius: "4px",
  color: "#fff",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "100%",
  padding: "12px 20px",
  fontSize: "16px",
  boxShadow: "0 4px 6px rgba(34, 197, 94, 0.25)",
  transition: "all 0.2s ease-in-out",
}

const hr = {
  borderColor: "#cccccc",
  margin: "20px 0",
}

const footer = {
  color: "#8898aa",
  fontSize: "12px",
}

const infoBox = {
  backgroundColor: "#f8fafc",
  border: "1px solid #e2e8f0",
  borderRadius: "4px",
  padding: "16px",
  marginBottom: "20px",
}

const infoHeading = {
  fontSize: "18px",
  fontWeight: "bold",
  marginBottom: "8px",
  color: "#334155",
}

// Define the props for the email template
interface EnhancedSurveyInvitationEmailProps {
  companyName: string
  surveyTemplate: string
  surveyLink: string
  deadline?: string
  recipientName?: string
  logoUrl?: string
}

/**
 * Enhanced email template for survey invitations
 */
export const EnhancedSurveyInvitationEmail: FC<EnhancedSurveyInvitationEmailProps> = ({
  companyName,
  surveyTemplate,
  surveyLink,
  deadline,
  recipientName,
  logoUrl = "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-ELWb3Keeayd904WemgAFMeITHlfceC.png",
}) => {
  // Format the template name for display
  const templateName =
    surveyTemplate === "UNIFIED_QUESTIONNAIRE_V1"
      ? "Unified Questionnaire"
      : surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
        ? "Self Assessment Unified Questionnaire"
        : surveyTemplate

  // Format the deadline if provided
  const formattedDeadline = deadline
    ? new Date(deadline).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : null

  return (
    <Html>
      <Head />
      <Preview>
        {surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1" ? "Self-Assessment" : "Survey"} Invitation:{" "}
        {templateName} for {companyName}
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            {logoUrl && <Img src={logoUrl} width="80" height="80" alt="Impactly Logo" style={logo} />}
            <Heading style={heading}>Impactly</Heading>
          </Section>
          <Text style={paragraph}>Hello {recipientName ? recipientName : ""},</Text>
          <Text style={paragraph}>
            {surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
              ? "You have been invited to complete a self-assessment for "
              : "You have been invited to complete a survey for "}
            <strong>{companyName}</strong>.
          </Text>

          <Section style={infoBox}>
            <Text style={infoHeading}>
              {surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
                ? "Self-Assessment Details"
                : "Survey Details"}
            </Text>
            <Text style={paragraph}>
              <strong>Type:</strong> {templateName}
            </Text>
            {formattedDeadline && (
              <Text style={paragraph}>
                <strong>Deadline:</strong> {formattedDeadline}
              </Text>
            )}
          </Section>

          <Text style={paragraph}>
            {surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"
              ? "Please click the button below to access and complete the self-assessment:"
              : "Please click the button below to access and complete the survey:"}
          </Text>
          <Button style={button} href={surveyLink}>
            {surveyTemplate === "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1" ? "Access Self-Assessment" : "Access Survey"}
          </Button>
          <Hr style={hr} />
          <Text style={footer}>
            This is an automated message from Impactly. If you received this email in error, please disregard it. For
            any questions or assistance, please contact our support team.
          </Text>
        </Container>
      </Body>
    </Html>
  )
}
