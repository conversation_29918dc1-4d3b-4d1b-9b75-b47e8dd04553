/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { createRe<PERSON>Hand<PERSON>, JobDispatcher } from "@kreios/jobs"

import { env } from "../env"
import { cdcJob } from "./cdc"
import { sendNotifications } from "./send-notifications"

export const name = "@impactly/jobs"

// Define your job handlers here
export const handlers = [cdcJob, sendNotifications]

// The dispatcher is the central hub for scheduling and executing jobs
export const dispatcher = new JobDispatcher(handlers, {
  baseUrl: `https://${env.VERCEL_URL}/api/jobs`,
  qstashToken: env.QSTASH_TOKEN,
})

// The (Next.js) request handler that will route incoming requests to the dispatcher
//export const requestHandler = createRequestHandler(dispatcher, { skipSignatureVerification: !env.VERCEL })
export const requestHandler = createRequestHandler(dispatcher, { skipSignatureVerification: true }) // TODO: Hack to make it work with Vercel Cron
