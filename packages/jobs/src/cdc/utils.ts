/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { UTCDate } from "@date-fns/utc"
import { parseISO } from "date-fns"

import { calculateDelta } from "@kreios/eventsourcing/cdc"

// TODO: We should drop this hacky solution once we have refactored the CDC to keep its own records of the most recent source snapshot that is has seen.
const aggregateBaseProperties = ["aggregateId", "version", "createdAt", "updatedAt", "deletedAt", "deleted"]

/**
 * Compares two snapshots of an aggregate, ignoring base properties.
 *
 * @param oldSnapshot - The current state of the aggregate.
 * @param newSnapshot - The new state to compare against.
 * @returns True if there are changes between the snapshots, false otherwise.
 */
export function hasChanges<T extends Record<string, unknown>>(oldSnapshot: T, newSnapshot: Partial<T>): boolean {
  // Filter out aggregate base properties from both snapshots
  const filteredOldSnapshot = Object.fromEntries(
    Object.entries(oldSnapshot).filter(([key]) => !aggregateBaseProperties.includes(key))
  ) as T
  const filteredNewSnapshot = Object.fromEntries(
    Object.entries(newSnapshot).filter(([key]) => !aggregateBaseProperties.includes(key))
  ) as Partial<T>

  // Calculate the delta between the filtered snapshots
  const delta = calculateDelta(filteredOldSnapshot, filteredNewSnapshot)
  return delta.length > 0
}

/**
 * Converts a GraphQL date string to a UTC Date object.
 *
 * @param dateString - The GraphQL date string to convert.
 * @returns A UTC Date object, or undefined if the input is null or undefined.
 */
export function toUTCDate(dateString: string | null | undefined): Date | undefined {
  if (!dateString) return undefined
  return new UTCDate(parseISO(dateString))
}
