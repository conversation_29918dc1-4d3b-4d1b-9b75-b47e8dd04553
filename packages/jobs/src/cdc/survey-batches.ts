/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyBatchAggregate, SurveyBatchEventStore } from "@impactly/domain"
import type { BackendIntegrationService, SurveyBatchItemFragment } from "@impactly/integration/backend"
import { captureSurveyBatchChangesCommand } from "@impactly/domain/survey-batches/commands"

import type { CaptureChangeProvider } from "./service"
import { env } from "../../env"

export class SurveyBatchCaptureChangeProvider
  implements CaptureChangeProvider<"surveybatches", SurveyBatchItemFragment, SurveyBatchAggregate>
{
  public readonly indexName = "surveybatches"

  constructor(
    private readonly integrationService: BackendIntegrationService,
    private readonly eventStore: SurveyBatchEventStore
  ) {}

  async captureChangeOnItem(id: string, batch: SurveyBatchItemFragment | null) {
    const { changesDetected } = await captureSurveyBatchChangesCommand.handler(
      {
        aggregateId: id,
        sourceId: id,
        sourceData: batch,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [this.eventStore],
      {}
    )

    // If the company exists in the event store and the data is the same, do nothing
    return changesDetected ? "updated" : "unchanged"
  }

  public async getItem(id: string) {
    return await this.integrationService.getSurveyBatch({
      id,
    })
  }

  public getAggregate(id: string) {
    return this.eventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate)
  }

  public async getAggregateIds() {
    return (await this.eventStore.listAggregateIds()).aggregateIds.map((entry) => entry.aggregateId)
  }

  public getItems() {
    return this.integrationService.getSurveyBatches()
  }
}
