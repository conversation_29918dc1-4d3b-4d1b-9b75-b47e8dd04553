/* eslint-disable @typescript-eslint/unbound-method */
/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { beforeEach, describe, expect, it, vi } from "vitest"

import type { ElasticsearchGateway, GatewayConfig } from "@kreios/elasticsearch"
import type { AggregateBase } from "@kreios/eventsourcing"

import type { CaptureChangeProvider } from "./service"
import { CaptureChangeService } from "./service"

// Mock provider implementation for testing
class Mock<PERSON>rovider implements CaptureChangeProvider<"test-index", { id: string }, AggregateBase> {
  indexName = "test-index" as const

  constructor(
    private items: { id: string }[] = [],
    private aggregates: AggregateBase[] = [],
    private aggregateIds: string[] = []
  ) {}

  async getAggregateIds(): Promise<string[]> {
    return this.aggregateIds
  }

  async getAggregate(id: string): Promise<AggregateBase> {
    const aggregate = this.aggregates.find((a) => a.aggregateId === id)
    if (!aggregate) throw new Error(`Aggregate not found: ${id}`)
    return aggregate
  }

  async getItem(id: string): Promise<{ id: string }> {
    const item = this.items.find((i) => i.id === id)
    if (!item) throw new Error(`Item not found: ${id}`)
    return item
  }

  async *getItems(): AsyncIterable<{ id: string }> {
    for (const item of this.items) {
      yield item
    }
  }

  async captureChangeOnItem(id: string, item: { id: string } | null): Promise<"updated" | "unchanged"> {
    return item ? "updated" : "unchanged"
  }
}

describe("CaptureChangeService", () => {
  let mockGateway: ElasticsearchGateway<GatewayConfig>
  let mockProvider: MockProvider
  let service: CaptureChangeService<GatewayConfig, { test: MockProvider }>

  const createAggregate = (id: string): AggregateBase => ({
    aggregateId: id,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
    deleted: false,
  })

  beforeEach(() => {
    mockGateway = {
      reindex: vi.fn().mockResolvedValue(undefined),
      delete: vi.fn().mockResolvedValue(undefined),
    } as unknown as ElasticsearchGateway<GatewayConfig>

    mockProvider = new MockProvider(
      [{ id: "1" }, { id: "2" }],
      [createAggregate("1"), createAggregate("2")],
      ["1", "2"]
    )

    service = new CaptureChangeService(mockGateway, {
      test: mockProvider,
    })
  })

  describe("captureChange", () => {
    it("should capture change for a single item", async () => {
      const spy = vi.spyOn(mockProvider, "captureChangeOnItem").mockImplementation(async () => "updated")
      const result = await service.captureChange("test", { id: "1" })

      expect(result).toBe("updated")
      expect(spy).toHaveBeenCalledWith("1", { id: "1" })
    })

    it("should reindex after capturing changes", async () => {
      vi.spyOn(mockProvider, "captureChangeOnItem").mockImplementation(async () => "updated")
      await service.captureChangesForIds("test", ["1"])

      expect(mockGateway.reindex).toHaveBeenCalledWith(
        "test-index",
        expect.arrayContaining([
          expect.objectContaining({
            aggregateId: "1",
            version: 1,
          }),
        ])
      )
    })
  })

  describe("captureChangesForIds", () => {
    it("should capture changes for multiple ids", async () => {
      const spy = vi.spyOn(mockProvider, "captureChangeOnItem").mockImplementation(async () => "updated")
      await service.captureChangesForIds("test", ["1", "2"])

      expect(spy).toHaveBeenCalledTimes(2)
      expect(spy).toHaveBeenCalledWith("1", { id: "1" })
      expect(spy).toHaveBeenCalledWith("2", { id: "2" })
    })

    it("should reindex only changed items", async () => {
      const mockProviderWithUnchanged = new MockProvider(
        [{ id: "1" }, { id: "2" }],
        [createAggregate("1"), createAggregate("2")],
        ["1", "2"]
      )
      mockProviderWithUnchanged.captureChangeOnItem = async (id) => (id === "1" ? "updated" : "unchanged")

      const serviceWithUnchanged = new CaptureChangeService(mockGateway, {
        test: mockProviderWithUnchanged,
      })

      await serviceWithUnchanged.captureChangesForIds("test", ["1", "2"])

      expect(mockGateway.reindex).toHaveBeenCalledWith(
        "test-index",
        expect.arrayContaining([
          expect.objectContaining({
            aggregateId: "1",
            version: 1,
          }),
        ])
      )
    })

    it("should throw error if any item capture fails", async () => {
      const mockProviderWithError = new MockProvider()
      mockProviderWithError.captureChangeOnItem = async () => {
        throw new Error("Failed to capture change")
      }

      const serviceWithError = new CaptureChangeService(mockGateway, {
        test: mockProviderWithError,
      })

      await expect(serviceWithError.captureChangesForIds("test", ["1"])).rejects.toThrow(
        "Failed to capture changes for some items"
      )
    })
  })

  describe("captureChanges", () => {
    it("should capture changes for all items", async () => {
      const spy = vi.spyOn(mockProvider, "captureChangeOnItem").mockImplementation(async () => "updated")
      await service.captureChanges("test")

      expect(spy).toHaveBeenCalledTimes(2)
      expect(spy).toHaveBeenCalledWith("1", { id: "1" })
      expect(spy).toHaveBeenCalledWith("2", { id: "2" })
    })

    it("should handle deletion of items not in source", async () => {
      // Create a provider with no items in source but existing aggregates
      const mockProviderWithDeletions = new MockProvider([], [createAggregate("1")], ["1"])
      mockProviderWithDeletions.captureChangeOnItem = async (id, item) => (item === null ? "updated" : "unchanged")

      const serviceWithDeletions = new CaptureChangeService(mockGateway, {
        test: mockProviderWithDeletions,
      })

      await serviceWithDeletions.captureChanges("test")

      expect(mockGateway.delete).toHaveBeenCalledWith("test-index", ["1"])
    })

    it("should process items in batches", async () => {
      const items = Array.from({ length: 250 }, (_, i) => ({ id: String(i) }))
      const aggregates = items.map((item) => createAggregate(item.id))
      const mockProviderWithManyItems = new MockProvider(
        items,
        aggregates,
        items.map((item) => item.id)
      )
      const spy = vi.spyOn(mockProviderWithManyItems, "captureChangeOnItem").mockImplementation(async () => "updated")

      const serviceWithManyItems = new CaptureChangeService(mockGateway, {
        test: mockProviderWithManyItems,
      })

      await serviceWithManyItems.captureChanges("test")

      expect(spy).toHaveBeenCalledTimes(250)
    })

    it("should log summary statistics", async () => {
      const mockLog = vi.fn()
      vi.spyOn(console, "log").mockImplementation(mockLog)
      await service.captureChanges("test")

      expect(mockLog).toHaveBeenCalledWith(expect.stringContaining("TEST CDC SUMMARY"))
      expect(mockLog).toHaveBeenCalledWith(expect.stringContaining("Total"))
      expect(mockLog).toHaveBeenCalledWith(expect.stringContaining("Updated"))
      expect(mockLog).toHaveBeenCalledWith(expect.stringContaining("Deleted"))
      expect(mockLog).toHaveBeenCalledWith(expect.stringContaining("Unchanged"))
    })
  })
})
