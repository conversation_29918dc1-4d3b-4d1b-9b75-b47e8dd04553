# Change Data Capture (CDC) System

The CDC system is responsible for synchronizing data between external data sources and the internal event store, ensuring that changes are properly captured and reflected in the Elasticsearch indices.

## Overview

The CDC system implements a Change Data Capture pattern that:

1. Detects changes in external data sources
2. Updates the internal event store accordingly
3. Maintains Elasticsearch indices for efficient querying

## Calling the CDC Job

The CDC job can be triggered via HTTP endpoints in two ways:

### POST Request

Send a POST request to the job endpoint with the job input in the request body:

1. To sync specific entities:

```http
POST /api/jobs/cdc
Authorization: Bearer ${CRON_SECRET}
Content-Type: application/json

{
  "entities": ["companies", "surveys", "watchlists"]
}
```

2. To sync specific IDs for different entities:

```http
POST /api/jobs/cdc
Authorization: Bearer ${CRON_SECRET}
Content-Type: application/json

{
  "companies": ["company-id-1", "company-id-2"],
  "surveys": ["survey-id-1"]
}
```

### GET Request (for Vercel Cron)

For Vercel Cron integration, you can use GET requests with base64 encoded input:

```http
GET /api/jobs/cdc?input=base64_encoded_input
Authorization: Bearer ${CRON_SECRET}
```

### Supported Entities

The following entity types can be specified:

- `nace-codes`
- `portal-users`
- `watchlists`
- `companies`
- `survey-batches`
- `surveys`

### Authentication

All requests must include the `Authorization` header with a Bearer token matching the `CRON_SECRET` environment variable.

### Response

The job will return:

- 200 status code on success
- 401 status code for unauthorized requests
- 500 status code with error details if the job fails
