/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { CompanyAggregate, CompanyEventStore } from "@impactly/domain"
import type { BackendIntegrationService, CompanyItemFragment } from "@impactly/integration/backend"
import { captureCompanyChangesCommand } from "@impactly/domain/companies/commands"

import type { CaptureChangeProvider } from "./service"
import { env } from "../../env"

export class CompanyCaptureChangeProvider
  implements CaptureChangeProvider<"companies", CompanyItemFragment, CompanyAggregate>
{
  public readonly indexName = "companies"

  constructor(
    private readonly integrationService: BackendIntegrationService,
    private readonly eventStore: CompanyEventStore
  ) {}

  async captureChangeOnItem(id: string, company: CompanyItemFragment | null) {
    const { changesDetected } = await captureCompanyChangesCommand.handler(
      {
        aggregateId: id,
        sourceId: id,
        sourceData: company,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [this.eventStore],
      {}
    )

    // If the company exists in the event store and the data is the same, do nothing
    return changesDetected ? "updated" : "unchanged"
  }

  public getAggregate(id: string) {
    return this.eventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate)
  }

  public getItem(id: string) {
    return this.integrationService.getCompany({
      id,
    })
  }

  public async getAggregateIds() {
    return (await this.eventStore.listAggregateIds()).aggregateIds.map((entry) => entry.aggregateId)
  }

  public getItems() {
    return this.integrationService.getCompanies()
  }
}
