/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { PortalUserEventStore } from "@impactly/domain"
import type { BackendIntegrationService, PortalUserItemFragment } from "@impactly/integration/backend"
import { EventStore } from "@castore/core"
import { InMemoryEventStorageAdapter } from "@castore/event-storage-adapter-in-memory"
import { capturePortalUserChangesCommand } from "@impactly/domain/portal-users/commands"
import { portalUserEvents } from "@impactly/domain/portal-users/events"
import { portalUserReducer } from "@impactly/domain/portal-users/reducer"
import { PortalUserRoleEnum } from "@impactly/integration/backend"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { env } from "../../env"
import { PortalUserCaptureChangeProvider } from "./portal-users"

void vi.hoisted(() => {
  // @ts-expect-error mocking Di container
  globalThis.container = {
    resolve: vi.fn().mockReturnValue({
      delete: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      status: vi.fn().mockResolvedValue({ status: "green", version: "8.0.0" }),
      listIndices: vi.fn().mockResolvedValue([]),
      reindexAll: vi.fn().mockResolvedValue([]),
      ping: vi.fn().mockResolvedValue(true),
      config: {
        url: "http://localhost:9200",
        apiKey: "mockApiKey",
        indexPrefix: "mockPrefix",
        indices: [
          {
            name: "portalusers",
          },
        ],
      },
    }),
  }
})

vi.mock("@kreios/elasticsearch/utils", () => ({
  createReindexDataLoaderFunction: vi.fn().mockReturnValue(vi.fn()),
}))

// Mock the db module
vi.mock("@impactly/domain/db", () => ({
  db: {},
}))

// Mock the DrizzleEventStorageAdapter
vi.mock("@kreios/eventsourcing/drizzle", async () => {
  const { MockDrizzleEventStorageAdapter } = await import("@kreios/eventsourcing/drizzle/mocks/mock-drizzle-adapter")
  return {
    DrizzleEventStorageAdapter: MockDrizzleEventStorageAdapter,
  }
})

describe("PortalUserCaptureChangeProvider", () => {
  let mockIntegrationService: BackendIntegrationService
  let portalUserEventStore: PortalUserEventStore
  let provider: PortalUserCaptureChangeProvider

  const existingUser = {
    id: "1",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    isActive: true,
    dateJoined: new Date("2023-01-01").toISOString(),
    roles: [{ role: PortalUserRoleEnum.CompanyUser }],
    tenant: { id: "tenant1" },
    hasGivenConsent: false,
    company: { id: "**********" },
  } satisfies PortalUserItemFragment

  beforeEach(async () => {
    mockIntegrationService = {
      getPortalUsers: vi.fn(),
    } as unknown as BackendIntegrationService

    portalUserEventStore = new EventStore({
      eventStoreId: "portalUsers",
      eventStorageAdapter: new InMemoryEventStorageAdapter(),
      eventTypes: portalUserEvents,
      reducer: portalUserReducer,
    })

    provider = new PortalUserCaptureChangeProvider(mockIntegrationService, portalUserEventStore)

    // Prefill the event store with an existing user
    await capturePortalUserChangesCommand.handler(
      {
        aggregateId: existingUser.id,
        sourceId: existingUser.id,
        sourceData: existingUser,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [portalUserEventStore],
      {}
    )
  })

  it("should create new portal users", async () => {
    const newUser = {
      id: "2",
      email: "<EMAIL>",
      firstName: "Jane",
      lastName: "Smith",
      isActive: true,
      dateJoined: new Date("2023-06-01").toISOString(),
      roles: [{ role: PortalUserRoleEnum.Admin }],
      tenant: { id: "tenant2" },
      hasGivenConsent: false,
      company: { id: "**********" },
    } satisfies PortalUserItemFragment

    mockIntegrationService.getPortalUsers = vi.fn().mockImplementation(function* () {
      yield existingUser
      yield newUser
    })

    // Capture changes using the provider
    for await (const user of provider.getItems()) {
      await provider.captureChangeOnItem(user.id, user)
    }

    const { aggregate } = await portalUserEventStore.getAggregate("2")
    expect(aggregate).toBeDefined()
    expect(aggregate?.email).toBe("<EMAIL>")
  })

  it("should update existing portal users", async () => {
    const updatedUser = { ...existingUser, lastName: "UpdatedDoe" }
    mockIntegrationService.getPortalUsers = vi.fn().mockImplementation(function* () {
      yield updatedUser
    })

    // Capture changes using the provider
    for await (const user of provider.getItems()) {
      await provider.captureChangeOnItem(user.id, user)
    }

    const { aggregate } = await portalUserEventStore.getAggregate("1")
    expect(aggregate).toBeDefined()
    expect(aggregate?.lastName).toBe("UpdatedDoe")
  })

  it("should delete portal users that no longer exist in the source", async () => {
    mockIntegrationService.getPortalUsers = vi.fn().mockImplementation(function* () {
      // Yield nothing, simulating that the existing user has been deleted
    })

    // Get existing IDs and delete those not in source
    const existingIds = await provider.getAggregateIds()
    for (const id of existingIds) {
      await provider.captureChangeOnItem(id, null)
    }

    const { aggregate } = await portalUserEventStore.getAggregate("1")
    expect(aggregate?.deleted).toBe(true)
  })

  it("should not make any changes if nothing has changed in the source", async () => {
    mockIntegrationService.getPortalUsers = vi.fn().mockImplementation(function* () {
      yield existingUser
    })

    const beforeState = await portalUserEventStore.getAggregate("1")

    // Capture changes using the provider
    for await (const user of provider.getItems()) {
      await provider.captureChangeOnItem(user.id, user)
    }

    const afterState = await portalUserEventStore.getAggregate("1")

    expect(beforeState).toEqual(afterState)
  })
})
