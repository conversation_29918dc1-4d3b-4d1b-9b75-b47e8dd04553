/* eslint-disable @typescript-eslint/unbound-method */
/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { WatchlistEventStore } from "@impactly/domain"
import type { BackendIntegrationService, WatchlistItemFragment } from "@impactly/integration/backend"
import { EventStore } from "@castore/core"
import { InMemoryEventStorageAdapter } from "@castore/event-storage-adapter-in-memory"
import { captureWatchlistChangesCommand } from "@impactly/domain/watchlists/commands"
import { watchlistEvents } from "@impactly/domain/watchlists/events"
import { watchlistReducer } from "@impactly/domain/watchlists/reducer"
import { IorderCompanyCountryChoices } from "@impactly/integration/backend"
import { beforeEach, describe, expect, it, vi } from "vitest"

import type { GatewayConfig } from "@kreios/elasticsearch"

import type { CompanyCaptureChangeProvider } from "./companies"
import type { CaptureChangeService } from "./service"
import { env } from "../../env"
import { WatchlistCaptureChangeProvider } from "./watchlists"

void vi.hoisted(() => {
  // @ts-expect-error mocking Di container
  globalThis.container = {
    resolve: vi.fn().mockReturnValue({
      delete: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      status: vi.fn().mockResolvedValue({ status: "green", version: "8.0.0" }),
      listIndices: vi.fn().mockResolvedValue([]),
      reindexAll: vi.fn().mockResolvedValue([]),
      ping: vi.fn().mockResolvedValue(true),
      config: {
        url: "http://localhost:9200",
        apiKey: "mockApiKey",
        indexPrefix: "mockPrefix",
        indices: [
          {
            name: "watchlists",
          },
          {
            name: "companies",
          },
        ],
      },
    }),
  }
})

// Mock the db module
vi.mock("@impactly/domain/db", () => ({
  db: {},
}))

// Mock the DrizzleEventStorageAdapter
vi.mock("@kreios/eventsourcing/drizzle", async () => {
  const { MockDrizzleEventStorageAdapter } = await import("@kreios/eventsourcing/drizzle/mocks/mock-drizzle-adapter")
  return {
    DrizzleEventStorageAdapter: MockDrizzleEventStorageAdapter,
  }
})

vi.mock("@kreios/elasticsearch/utils", () => ({
  createReindexDataLoaderFunction: vi.fn().mockReturnValue(vi.fn()),
}))

describe("WatchlistCaptureChangeProvider", () => {
  let mockIntegrationService: BackendIntegrationService
  let watchlistEventStore: WatchlistEventStore
  let provider: WatchlistCaptureChangeProvider
  let mockCaptureChangesService: CaptureChangeService<GatewayConfig, { companies: CompanyCaptureChangeProvider }>

  const existingWatchlist = {
    id: "1",
    name: "Test Watchlist",
    tenant: { id: "1" },
    portalUser: { id: "1" },
    entries: [
      {
        id: "1",
        company: {
          id: "1",
          name: "Test Company",
          registrationNumber: "12345678",
          country: IorderCompanyCountryChoices.Ee,
          urls: [],
          businessActivities: [],
          annualReports: [],
        },
        contacts: [{ id: "1", email: "<EMAIL>", name: "Test User" }],
      },
    ],
  } satisfies WatchlistItemFragment

  beforeEach(async () => {
    mockIntegrationService = {
      getWatchlists: vi.fn(),
    } as unknown as BackendIntegrationService

    watchlistEventStore = new EventStore({
      eventStoreId: "watchlists",
      eventStorageAdapter: new InMemoryEventStorageAdapter(),
      eventTypes: watchlistEvents,
      reducer: watchlistReducer,
    })

    mockCaptureChangesService = {
      captureChange: vi.fn().mockImplementation(async () => undefined),
      captureChanges: vi.fn().mockImplementation(async () => undefined),
      captureChangesForIds: vi.fn().mockImplementation(async () => undefined),
    } as unknown as CaptureChangeService<GatewayConfig, { companies: CompanyCaptureChangeProvider }>

    provider = new WatchlistCaptureChangeProvider(
      mockIntegrationService,
      watchlistEventStore,
      mockCaptureChangesService
    )

    // Prefill the event store with an existing watchlist
    await captureWatchlistChangesCommand.handler(
      {
        aggregateId: existingWatchlist.id,
        sourceId: existingWatchlist.id,
        sourceData: existingWatchlist,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [watchlistEventStore],
      {}
    )
  })

  it("should create new watchlists", async () => {
    const newWatchlist = {
      id: "2",
      name: "New Watchlist",
      tenant: { id: "1" },
      portalUser: { id: "1" },
      entries: [],
    } satisfies WatchlistItemFragment

    mockIntegrationService.getWatchlists = vi.fn().mockImplementation(function* () {
      yield existingWatchlist
      yield newWatchlist
    })

    // Capture changes using the provider
    for await (const watchlist of provider.getItems()) {
      await provider.captureChangeOnItem(watchlist.id, watchlist)
    }

    const { aggregate } = await watchlistEventStore.getAggregate("2")
    expect(aggregate).toBeDefined()
    expect(aggregate?.name).toBe("New Watchlist")
  })

  it("should update existing watchlists", async () => {
    const updatedWatchlist = { ...existingWatchlist, name: "Updated Watchlist" }
    mockIntegrationService.getWatchlists = vi.fn().mockImplementation(function* () {
      yield updatedWatchlist
    })

    // Capture changes using the provider
    for await (const watchlist of provider.getItems()) {
      await provider.captureChangeOnItem(watchlist.id, watchlist)
    }

    const { aggregate } = await watchlistEventStore.getAggregate("1")
    expect(aggregate).toBeDefined()
    expect(aggregate?.name).toBe("Updated Watchlist")
  })

  it("should delete watchlists that no longer exist in the source", async () => {
    mockIntegrationService.getWatchlists = vi.fn().mockImplementation(function* () {
      // Yield nothing, simulating that the existing watchlist has been deleted
    })

    // Get existing IDs and delete those not in source
    const existingIds = await provider.getAggregateIds()
    for (const id of existingIds) {
      await provider.captureChangeOnItem(id, null)
    }

    const { aggregate } = await watchlistEventStore.getAggregate("1")
    expect(aggregate?.deleted).toBe(true)
  })

  it("should not make any changes if nothing has changed in the source", async () => {
    mockIntegrationService.getWatchlists = vi.fn().mockImplementation(function* () {
      yield existingWatchlist
    })

    const beforeState = await watchlistEventStore.getAggregate("1")

    // Capture changes using the provider
    for await (const watchlist of provider.getItems()) {
      await provider.captureChangeOnItem(watchlist.id, watchlist)
    }

    const afterState = await watchlistEventStore.getAggregate("1")

    expect(beforeState).toEqual(afterState)
  })

  it("should capture changes for companies in watchlist entries", async () => {
    const watchlistWithCompany = {
      ...existingWatchlist,
      entries: [
        {
          id: "1",
          company: {
            id: "1",
            name: "Test Company",
            registrationNumber: "12345678",
            country: IorderCompanyCountryChoices.Ee,
            urls: [],
            businessActivities: [],
            annualReports: [],
          },
          contacts: [],
        },
      ],
    }

    mockIntegrationService.getWatchlists = vi.fn().mockImplementation(function* () {
      yield watchlistWithCompany
    })

    // Capture changes using the provider
    for await (const watchlist of provider.getItems()) {
      await provider.captureChangeOnItem(watchlist.id, watchlist)
    }

    expect(mockCaptureChangesService.captureChangesForIds).toHaveBeenCalledWith("companies", ["1"])
  })
})
