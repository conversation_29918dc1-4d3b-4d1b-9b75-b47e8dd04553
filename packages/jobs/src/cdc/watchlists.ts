/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { WatchlistAggregate, WatchlistEventStore } from "@impactly/domain"
import type {
  BackendIntegrationService,
  CompanyItemFragment,
  WatchlistItemFragment,
} from "@impactly/integration/backend"
import { captureWatchlistChangesCommand } from "@impactly/domain/watchlists/commands"

import type { GatewayConfig } from "@kreios/elasticsearch"
import { DataLoader } from "@kreios/utils/dataloader"

import type { CompanyCaptureChangeProvider } from "./companies"
import type { CaptureChangeProvider, CaptureChangeService } from "./service"
import { env } from "../../env"

export class WatchlistCaptureChangeProvider
  implements CaptureChangeProvider<"watchlists", WatchlistItemFragment, WatchlistAggregate>
{
  public readonly indexName = "watchlists"

  private readonly companyDataloader: DataLoader<CompanyItemFragment, undefined>

  constructor(
    private readonly integrationService: BackendIntegrationService,
    private readonly eventStore: WatchlistEventStore,
    private readonly captureChangeService: CaptureChangeService<
      GatewayConfig,
      {
        companies: CompanyCaptureChangeProvider
      }
    >
  ) {
    this.companyDataloader = new DataLoader<CompanyItemFragment, undefined>(
      async (companies) => {
        await this.captureChangeService.captureChangesForIds(
          "companies",
          companies.map((company) => company.id)
        )

        return companies.map(() => undefined)
      },
      {
        name: "captureCompanyDataLoader",
        cache: false,
      }
    )
  }

  async captureChangeOnItem(id: string, watchlist: WatchlistItemFragment | null) {
    await Promise.all(watchlist?.entries.map(async (entry) => this.companyDataloader.load(entry.company)) ?? [])
    const { changesDetected } = await captureWatchlistChangesCommand.handler(
      {
        aggregateId: id,
        sourceId: id,
        sourceData: watchlist,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [this.eventStore],
      {}
    )

    // If the company exists in the event store and the data is the same, do nothing
    return changesDetected ? "updated" : "unchanged"
  }

  public getAggregate(id: string) {
    return this.eventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate)
  }

  public getItem(id: string) {
    return this.integrationService.getWatchlist({
      id,
    })
  }

  public async getAggregateIds() {
    return (await this.eventStore.listAggregateIds()).aggregateIds.map((entry) => entry.aggregateId)
  }

  public getItems() {
    return this.integrationService.getWatchlists()
  }
}
