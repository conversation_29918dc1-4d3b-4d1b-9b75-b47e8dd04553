/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import batch from "it-batch"

import type { ElasticsearchGateway, GatewayConfig, IndexName } from "@kreios/elasticsearch"
import type { AggregateBase } from "@kreios/eventsourcing"

import { env } from "../../env"

/**
 * Formats milliseconds into a human-readable duration string.
 * @param ms The duration in milliseconds.
 * @returns A formatted duration string (e.g., "1.2s", "45.6s", "2m 15s").
 */
function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

export interface CaptureChangeProvider<
  TIndexName extends string,
  TItem extends { id: string },
  TAggregate extends AggregateBase,
> {
  /**
   * The name of the elasticsearch index
   */
  indexName: TIndexName

  /**
   * Returns the ids of all the items in the event store.
   *
   * @returns An array of all the item ids in the event store.
   */
  getAggregateIds(): Promise<string[]>

  /**
   * Returns the aggregate for a given id.
   *
   * @param id - The id of the aggregate to return.
   * @returns The aggregate for the given id.
   */
  getAggregate(id: string): Promise<TAggregate>

  /**
   * Returns the external item for a given id.
   *
   * @param id - The id of the item to return.
   * @returns The external item for the given id.
   */
  getItem(id: string): Promise<TItem>

  /**
   * Returns an async iterable of all the items in the event store.
   *
   * @returns An async iterable of all the items in the event store.
   */
  getItems(): AsyncIterable<TItem>
  /**
   * Compares a given GraphQL item node with the current state of the item aggregate in the event store.
   *
   * If the item does not exist in the event store, a new item is created.
   * If the item exists in the event store but the data is different, the item is updated.
   * If the item exists in the event store and the data is the same, nothing is done.
   *
   * @param item - The item to capture a change on.
   * @returns The result of the capture change operation.
   */
  captureChangeOnItem(id: string, item: TItem | null): Promise<"updated" | "unchanged">
}

export class CaptureChangeService<
  TConfig extends GatewayConfig,
  TProviders extends Record<
    string,
    CaptureChangeProvider<
      IndexName<TConfig>,
      {
        id: string
      },
      AggregateBase
    >
  >,
> {
  constructor(
    private readonly gateway: ElasticsearchGateway<TConfig>,
    private readonly providers: TProviders
  ) {}

  public async captureChange(
    name: keyof TProviders,
    item: NonNullable<Parameters<TProviders[keyof TProviders]["captureChangeOnItem"]>[1]>
  ) {
    console.log(`🔄   [CDC] Starting single item capture for ${name.toString()}: ${item.id}`)
    const startTime = Date.now()

    try {
      const provider = this.providers[name]
      console.log(`📝   [CDC] Processing change detection for ${name.toString()}: ${item.id}`)

      const result = await provider.captureChangeOnItem(item.id, item)
      console.log(`✅   [CDC] Change detection completed for ${name.toString()}: ${item.id} - Result: ${result}`)

      console.log(`🔍   [CDC] Starting reindex for ${name.toString()}: ${item.id}`)
      await this.reindex(name, [item.id])
      console.log(`✅   [CDC] Reindex completed for ${name.toString()}: ${item.id}`)

      const duration = Date.now() - startTime
      console.log(`🎉   [CDC] Single item capture completed for ${name.toString()}: ${item.id} in ${duration}ms`)

      return result
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(
        `❌   [CDC] Single item capture failed for ${name.toString()}: ${item.id} after ${duration}ms`,
        error
      )
      throw error
    }
  }

  async captureChangesForIds(name: keyof TProviders, ids: string[]) {
    console.log(`🔄   [CDC] Starting batch capture for ${name.toString()}: ${ids.length} items`)
    const startTime = Date.now()

    try {
      const provider = this.providers[name]
      const changedIds = new Array<string>()

      console.log(`📋   [CDC] Processing ${ids.length} specific IDs for ${name.toString()}`)
      console.log(
        `📋   [CDC] IDs: ${ids.slice(0, 5).join(", ")}${ids.length > 5 ? ` ... and ${ids.length - 5} more` : ""}`
      )

      const results = await Promise.allSettled(
        ids.map(async (id, index) => {
          console.log(`📝   [CDC] Processing item ${index + 1}/${ids.length} for ${name.toString()}: ${id}`)

          const element = await provider.getItem(id)
          const result = await provider.captureChangeOnItem(id, element)

          if (result !== "unchanged") {
            changedIds.push(id)
            console.log(`✅   [CDC] Item ${index + 1}/${ids.length} changed: ${id} - ${result}`)
          } else {
            console.log(`⏭️    [CDC] Item ${index + 1}/${ids.length} unchanged: ${id}`)
          }

          return result
        })
      )

      const rejectedResults = results.filter((result) => result.status === "rejected")
      const successfulResults = results.filter((result) => result.status === "fulfilled")

      console.log(`📊   [CDC] Batch processing results for ${name.toString()}:`)
      console.log(`✅   Successful: ${successfulResults.length}`)
      console.log(`❌   Failed: ${rejectedResults.length}`)
      console.log(`🔄   Changed: ${changedIds.length}`)

      if (rejectedResults.length > 0) {
        console.error(`❌   [CDC] Some items failed processing for ${name.toString()}:`)
        rejectedResults.forEach((result, index) => {
          console.error(`  ${index + 1}. ${result.reason}`)
        })

        throw new Error(
          `Failed to capture changes for some items: ${rejectedResults.map((result) => result.reason as string).join(", ")}`
        )
      }

      if (changedIds.length > 0) {
        console.log(`🔍   [CDC] Starting reindex for ${changedIds.length} changed items in ${name.toString()}`)
        await this.reindex(name, changedIds)
        console.log(`✅   [CDC] Reindex completed for ${changedIds.length} items in ${name.toString()}`)
      } else {
        console.log(`⏭️   [CDC] No items changed, skipping reindex for ${name.toString()}`)
      }

      const duration = Date.now() - startTime
      console.log(
        `🎉   [CDC] Batch capture completed for ${name.toString()}: ${ids.length} items processed in ${duration}ms`
      )
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`❌   [CDC] Batch capture failed for ${name.toString()} after ${duration}ms`, error)
      throw error
    }
  }

  async reindex(name: keyof TProviders, ids: string[]) {
    if (ids.length === 0) {
      console.log(`⏭️    [CDC] No items to reindex for ${name.toString()}`)
      return
    }

    console.log(`🔍   [CDC] Starting reindex for ${name.toString()}: ${ids.length} items`)
    const startTime = Date.now()

    try {
      const provider = this.providers[name]
      console.log(
        `📋   [CDC] Re-indexing items: ${ids.slice(0, 5).join(", ")}${ids.length > 5 ? ` ... and ${ids.length - 5} more` : ""}`
      )

      console.log(`📝   [CDC] Fetching aggregates for ${ids.length} items from event store`)
      const aggregates = await Promise.all(ids.map((id) => provider.getAggregate(id)))
      console.log(`✅   [CDC] Fetched ${aggregates.length} aggregates from event store`)

      console.log(`🔍   [CDC] Indexing ${aggregates.length} documents to Elasticsearch index: ${provider.indexName}`)
      await this.gateway.reindex(provider.indexName, aggregates)

      const duration = Date.now() - startTime
      console.log(`✅   [CDC] Reindex completed for ${name.toString()}: ${ids.length} items indexed in ${duration}ms`)
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`❌   [CDC] Reindex failed for ${name.toString()} after ${duration}ms`, error)
      throw error
    }
  }

  async captureChanges(name: keyof TProviders) {
    console.log(`🚀   [CDC] Starting full capture for ${name.toString()}`)
    const overallStartTime = Date.now()

    try {
      const provider = this.providers[name]

      // Initialize counters
      const counters = {
        total: 0,
        updated: 0,
        deleted: 0,
        unchanged: 0,
      }

      console.log(`📊   [CDC] Fetching existing aggregate IDs for ${name.toString()}`)
      const existingIds = new Set(await provider.getAggregateIds())
      const changedIds = new Array<string>()

      console.log(`📋   [CDC] Found ${existingIds.size} existing ${name.toString()} in event store`)

      // Capture changes on all the elements that can be retrieved from the backend integration service
      console.log(`🔄   [CDC] Starting to fetch and process items from external API for ${name.toString()}`)
      let batchNumber = 0
      let totalProcessed = 0

      for await (const elementBatch of batch(provider.getItems(), env.CDC_BATCH_SIZE)) {
        batchNumber++
        const batchStartTime = Date.now()

        console.log(`📦   [CDC] Processing batch ${batchNumber} for ${name.toString()}: ${elementBatch.length} items`)
        console.log(
          `📦   [CDC] Batch ${batchNumber} items: ${elementBatch
            .slice(0, 3)
            .map((e) => e.id)
            .join(", ")}${elementBatch.length > 3 ? ` ... and ${elementBatch.length - 3} more` : ""}`
        )

        const results = await Promise.all(
          elementBatch.map(async (element, index) => {
            const result = await provider.captureChangeOnItem(element.id, element)
            if (index < 3 || result !== "unchanged") {
              // Log first 3 items or any changes
              console.log(`📝   [CDC] Item ${totalProcessed + index + 1}: ${element.id} - ${result}`)
            }
            return result
          })
        )

        // Update counters and tracking
        results.forEach((result, index) => {
          counters.total++
          counters[result]++
          if (result !== "unchanged") changedIds.push(elementBatch[index].id)
          existingIds.delete(elementBatch[index].id)
        })

        totalProcessed += elementBatch.length
        const batchDuration = Date.now() - batchStartTime

        console.log(
          `✅   [CDC] Batch ${batchNumber} completed for ${name.toString()} in ${formatDuration(batchDuration)} (${batchDuration}ms)`
        )
        console.log(
          `📊   [CDC] Batch ${batchNumber} results: ${results.filter((r) => r === "updated").length} updated, ${results.filter((r) => r === "unchanged").length} unchanged`
        )
        console.log(`📈 [CDC] Progress: ${totalProcessed} items processed, ${changedIds.length} changes detected`)
      }

      console.log(
        `✅   [CDC] External API processing completed for ${name.toString()}: ${totalProcessed} items processed`
      )
      console.log(
        `📊   [CDC] Current counters: Total: ${counters.total}, Updated: ${counters.updated}, Unchanged: ${counters.unchanged}`
      )

      // Delete elements that no longer exist in the backend
      console.log(
        `🗑️   [CDC] Processing deletions for ${name.toString()}: ${existingIds.size} items no longer exist in external API`
      )

      if (existingIds.size > 0) {
        console.log(
          `🗑️  [CDC] Items to delete: ${Array.from(existingIds).slice(0, 5).join(", ")}${existingIds.size > 5 ? ` ... and ${existingIds.size - 5} more` : ""}`
        )

        await Promise.all(
          Array.from(existingIds).map(async (deletedItemId) => {
            console.log(`🗑️    [CDC] Processing deletion for: ${deletedItemId}`)
            const result = await provider.captureChangeOnItem(deletedItemId, null)
            if (result === "unchanged") existingIds.delete(deletedItemId)
            counters[result]++
            counters.total++
            console.log(`✅   [CDC] Deletion processed for ${deletedItemId}: ${result}`)
          })
        )

        console.log(`✅   [CDC] Deletion processing completed for ${name.toString()}`)
      } else {
        console.log(`⏭️   [CDC] No deletions needed for ${name.toString()}`)
      }

      // reindex changed items
      console.log(`🔄   [CDC] Starting final reindex and cleanup for ${name.toString()}`)
      console.log(`🔍   [CDC] Items to reindex: ${changedIds.length}`)
      console.log(`🗑️   [CDC] Items to delete from index: ${existingIds.size}`)

      await Promise.all([
        this.reindex(name, changedIds),
        existingIds.size > 0 ? this.gateway.delete(provider.indexName, Array.from(existingIds)) : Promise.resolve(),
      ])

      const overallDuration = Date.now() - overallStartTime

      console.log()
      console.log(`🎉   [CDC] ===== ${name.toString().toUpperCase()} CDC SUMMARY =====`)
      console.log(`⏱️   Total Duration: ${formatDuration(overallDuration)} (${overallDuration}ms)`)
      console.log(`📊   Total ${name.toString()} processed: ${counters.total}`)
      console.log(`✅   Updated: ${counters.updated}`)
      console.log(`🗑️   Deleted: ${counters.deleted}`)
      console.log(`⏭️   Unchanged: ${counters.unchanged}`)
      console.log(`🔄   Items reindexed: ${changedIds.length}`)
      console.log(`📈   Performance: ${Math.round(counters.total / (overallDuration / 1000))} items/second`)
      console.log(`🎉   ===== ${name.toString().toUpperCase()} CDC COMPLETED =====`)
      console.log()
    } catch (error) {
      const overallDuration = Date.now() - overallStartTime
      console.error(`❌   [CDC] Full capture failed for ${name.toString()} after ${overallDuration}ms`, error)
      throw error
    }
  }
}
