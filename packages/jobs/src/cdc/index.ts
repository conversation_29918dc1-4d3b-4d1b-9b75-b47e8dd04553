/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import {
  companyEventStore,
  naceCodeEventStore,
  portalUserEventStore,
  surveyBatchEventStore,
  surveyEventStore,
  watchlistEventStore,
} from "@impactly/domain"
import { service as integrationService } from "@impactly/integration/backend"
import { z } from "zod"

import { jobType } from "@kreios/jobs"
import { entries } from "@kreios/utils/entries"
import { isTruthy } from "@kreios/utils/isTruthy"

import { CompanyCaptureChangeProvider } from "./companies"
import { NaceCodeCaptureChangeProvider } from "./nace-codes"
import { PortalUserCaptureChangeProvider } from "./portal-users"
import { CaptureChangeService } from "./service"
import { SurveyBatchCaptureChangeProvider } from "./survey-batches"
import { SurveyCaptureChangeProvider } from "./surveys"
import { WatchlistCaptureChangeProvider } from "./watchlists"

const options = z.enum(["nace-codes", "portal-users", "watchlists", "companies", "survey-batches", "surveys"])

export const cdcJob = jobType("cdc")
  .description("Capture changes in various entities from the backend")
  .input(
    z.union([
      z
        .object({
          entities: options.array(),
        })
        .describe("Capture changes for a list of entities"),
      z.record(options, z.string().array()).describe("A map of entity names to ids to capture changes for"),
    ])
  )
  .handler(async (input) => {
    const gateway = container.resolve("gateway")

    const captureChangeService = new CaptureChangeService(gateway, {
      companies: new CompanyCaptureChangeProvider(integrationService, companyEventStore),
      "nace-codes": new NaceCodeCaptureChangeProvider(integrationService, naceCodeEventStore),
      "portal-users": new PortalUserCaptureChangeProvider(integrationService, portalUserEventStore),
      "survey-batches": new SurveyBatchCaptureChangeProvider(integrationService, surveyBatchEventStore),
      surveys: new SurveyCaptureChangeProvider(integrationService, surveyEventStore),
      watchlists: new WatchlistCaptureChangeProvider(
        integrationService,
        watchlistEventStore,
        new CaptureChangeService(gateway, {
          companies: new CompanyCaptureChangeProvider(integrationService, companyEventStore),
        })
      ),
    })

    const entities =
      "entities" in input
        ? input.entities.map((entity) => [entity, undefined] as const)
        : entries(input).filter(isTruthy)

    for (const [entity, ids] of entities) {
      if (ids) {
        await captureChangeService.captureChangesForIds(entity, ids)
      } else {
        await captureChangeService.captureChanges(entity)
      }
    }
    console.log("CDC job completed successfully")
  })
  .build()
