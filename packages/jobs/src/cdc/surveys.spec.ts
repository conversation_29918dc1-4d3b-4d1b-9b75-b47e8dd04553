/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyEventStore } from "@impactly/domain"
import type { BackendIntegrationService, SurveyItemFragment } from "@impactly/integration/backend"
import { EventStore } from "@castore/core"
import { InMemoryEventStorageAdapter } from "@castore/event-storage-adapter-in-memory"
import { captureSurveyChangesCommand } from "@impactly/domain/surveys/commands"
import { surveyEvents } from "@impactly/domain/surveys/events"
import { surveyReducer } from "@impactly/domain/surveys/reducer"
import { SurveyStatusEnum, SurveyTemplateEnum } from "@impactly/integration/backend"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { env } from "../../env"
import { SurveyCaptureChangeProvider } from "./surveys"

void vi.hoisted(() => {
  // @ts-expect-error mocking Di container
  globalThis.container = {
    resolve: vi.fn().mockReturnValue({
      delete: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      status: vi.fn().mockResolvedValue({ status: "green", version: "8.0.0" }),
      listIndices: vi.fn().mockResolvedValue([]),
      reindexAll: vi.fn().mockResolvedValue([]),
      ping: vi.fn().mockResolvedValue(true),
      config: {
        url: "http://localhost:9200",
        apiKey: "mockApiKey",
        indexPrefix: "mockPrefix",
        indices: [
          {
            name: "surveys",
          },
        ],
      },
    }),
  }
})

vi.mock("@kreios/elasticsearch/utils", () => ({
  createReindexDataLoaderFunction: vi.fn().mockReturnValue(vi.fn()),
}))

// Mock the db module
vi.mock("@impactly/domain/db", () => ({
  db: {},
}))

// Mock the DrizzleEventStorageAdapter
vi.mock("@kreios/eventsourcing/drizzle", async () => {
  const { MockDrizzleEventStorageAdapter } = await import("@kreios/eventsourcing/drizzle/mocks/mock-drizzle-adapter")
  return {
    DrizzleEventStorageAdapter: MockDrizzleEventStorageAdapter,
  }
})

describe("SurveyCaptureChangeProvider", () => {
  let mockIntegrationService: BackendIntegrationService
  let surveyEventStore: SurveyEventStore
  let provider: SurveyCaptureChangeProvider

  const existingSurveyEventPayload = {
    id: "1",
    financialYear: 2024,
    readyForEdit: true,
    formData: null,
    tenant: { id: "tenant1", name: "tenant1" },
    company: { id: "company1", name: "company1" },
    batch: { id: "batch1" },
    progress: 0,
    inputDocuments: [],
    status: SurveyStatusEnum.NotStarted,
    template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
  } satisfies SurveyItemFragment

  beforeEach(async () => {
    mockIntegrationService = {
      getSurveys: vi.fn(),
    } as unknown as BackendIntegrationService

    surveyEventStore = new EventStore({
      eventStoreId: "surveys",
      eventStorageAdapter: new InMemoryEventStorageAdapter(),
      eventTypes: surveyEvents,
      reducer: surveyReducer,
    })

    provider = new SurveyCaptureChangeProvider(mockIntegrationService, surveyEventStore)

    // Prefill the event store with an existing survey
    await captureSurveyChangesCommand.handler(
      {
        aggregateId: existingSurveyEventPayload.id,
        sourceId: existingSurveyEventPayload.id,
        sourceData: existingSurveyEventPayload,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [surveyEventStore],
      {}
    )
  })

  it("should create new surveys", async () => {
    const newSurvey = {
      id: "2",
      readyForEdit: true,
      tenant: { id: "tenant2", name: "tenant2" },
      company: { id: "company2", name: "company2" },
      batch: { id: "batch2" },
      progress: 0,
      inputDocuments: [],
      status: SurveyStatusEnum.NotStarted,
      template: SurveyTemplateEnum.UnifiedQuestionnaireV1,
    } satisfies SurveyItemFragment

    mockIntegrationService.getSurveys = vi.fn().mockImplementation(function* () {
      yield existingSurveyEventPayload
      yield newSurvey
    })

    // Capture changes using the provider
    for await (const survey of provider.getItems()) {
      await provider.captureChangeOnItem(survey.id, survey)
    }

    const { aggregate } = await surveyEventStore.getAggregate("2")
    expect(aggregate).toBeDefined()
    expect(aggregate?.companyId).toBe("company2")
  })

  it("should update existing surveys", async () => {
    const updatedSurvey = { ...existingSurveyEventPayload, companyId: "company1" }
    mockIntegrationService.getSurveys = vi.fn().mockImplementation(function* () {
      yield updatedSurvey
    })

    // Capture changes using the provider
    for await (const survey of provider.getItems()) {
      await provider.captureChangeOnItem(survey.id, survey)
    }

    const { aggregate } = await surveyEventStore.getAggregate("1")
    expect(aggregate).toBeDefined()
    expect(aggregate?.companyId).toBe("company1")
  })

  it("should delete surveys that no longer exist in the source", async () => {
    mockIntegrationService.getSurveys = vi.fn().mockImplementation(function* () {
      // Yield nothing, simulating that the existing survey has been deleted
    })

    // Get existing IDs and delete those not in source
    const existingIds = await provider.getAggregateIds()
    for (const id of existingIds) {
      await provider.captureChangeOnItem(id, null)
    }

    const { aggregate } = await surveyEventStore.getAggregate("1")
    expect(aggregate?.deleted).toBe(true)
  })

  it("should not make any changes if nothing has changed in the source", async () => {
    mockIntegrationService.getSurveys = vi.fn().mockImplementation(function* () {
      yield existingSurveyEventPayload
    })

    const beforeState = await surveyEventStore.getAggregate("1")

    // Capture changes using the provider
    for await (const survey of provider.getItems()) {
      await provider.captureChangeOnItem(survey.id, survey)
    }

    const afterState = await surveyEventStore.getAggregate("1")

    expect(beforeState).toEqual(afterState)
  })
})
