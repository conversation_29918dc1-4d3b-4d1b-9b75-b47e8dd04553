/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { NaceCodeEventStore } from "@impactly/domain"
import type { BackendIntegrationService, NaceCodeItemFragment } from "@impactly/integration/backend"
import { EventStore } from "@castore/core"
import { InMemoryEventStorageAdapter } from "@castore/event-storage-adapter-in-memory"
import { captureNaceCodeChangesCommand } from "@impactly/domain/nace-codes/commands"
import { naceCodeEvents } from "@impactly/domain/nace-codes/events"
import { naceCodeReducer } from "@impactly/domain/nace-codes/reducer"
import { EbrdRiskLevelChoicesEnum } from "@impactly/integration/backend"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { env } from "../../env"
import { NaceCodeCaptureChangeProvider } from "./nace-codes"

void vi.hoisted(() => {
  // @ts-expect-error mocking Di container
  globalThis.container = {
    resolve: vi.fn().mockReturnValue({
      delete: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      status: vi.fn().mockResolvedValue({ status: "green", version: "8.0.0" }),
      listIndices: vi.fn().mockResolvedValue([]),
      reindexAll: vi.fn().mockResolvedValue([]),
      ping: vi.fn().mockResolvedValue(true),
      config: {
        url: "http://localhost:9200",
        apiKey: "mockApiKey",
        indexPrefix: "mockPrefix",
        indices: [
          {
            name: "nacecodes",
          },
        ],
      },
    }),
  }
})

vi.mock("@kreios/elasticsearch/utils", () => ({
  createReindexDataLoaderFunction: vi.fn().mockReturnValue(vi.fn()),
}))

// Mock the db module
vi.mock("@impactly/domain/db", () => ({
  db: {},
}))

// Mock the DrizzleEventStorageAdapter
vi.mock("@kreios/eventsourcing/drizzle", async () => {
  const { MockDrizzleEventStorageAdapter } = await import("@kreios/eventsourcing/drizzle/mocks/mock-drizzle-adapter")
  return {
    DrizzleEventStorageAdapter: MockDrizzleEventStorageAdapter,
  }
})

describe("NaceCodeCaptureChangeProvider", () => {
  let mockIntegrationService: BackendIntegrationService
  let naceCodeEventStore: NaceCodeEventStore
  let provider: NaceCodeCaptureChangeProvider

  const existingNaceCode = {
    id: "1",
    code: "A01",
    label: "Agriculture",
    overallEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Low,
    environmentalEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Low,
    socialEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Low,
  } satisfies NaceCodeItemFragment

  beforeEach(async () => {
    mockIntegrationService = {
      getNaceCodes: vi.fn(),
    } as unknown as BackendIntegrationService

    naceCodeEventStore = new EventStore({
      eventStoreId: "naceCodes",
      eventStorageAdapter: new InMemoryEventStorageAdapter(),
      eventTypes: naceCodeEvents,
      reducer: naceCodeReducer,
    })

    provider = new NaceCodeCaptureChangeProvider(mockIntegrationService, naceCodeEventStore)

    // Prefill the event store with an existing NACE code

    await captureNaceCodeChangesCommand.handler(
      {
        aggregateId: existingNaceCode.id,
        sourceId: existingNaceCode.id,
        sourceData: existingNaceCode,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [naceCodeEventStore],
      {}
    )
  })

  it("should create new NACE codes", async () => {
    const newNaceCode = {
      id: "2",
      code: "B02",
      label: "Mining",
      overallEbrdRiskLevel: EbrdRiskLevelChoicesEnum.High,
      environmentalEbrdRiskLevel: EbrdRiskLevelChoicesEnum.High,
      socialEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Medium,
    } satisfies NaceCodeItemFragment

    mockIntegrationService.getNaceCodes = vi.fn().mockImplementation(function* () {
      yield existingNaceCode
      yield newNaceCode
    })

    // Capture changes using the provider
    for await (const naceCode of provider.getItems()) {
      await provider.captureChangeOnItem(naceCode.id, naceCode)
    }

    const { aggregate } = await naceCodeEventStore.getAggregate("2")
    expect(aggregate).toBeDefined()
    expect(aggregate?.code).toBe("B02")
  })

  it("should update existing NACE codes", async () => {
    const updatedNaceCode = { ...existingNaceCode, label: "Updated Agriculture" }
    mockIntegrationService.getNaceCodes = vi.fn().mockImplementation(function* () {
      yield updatedNaceCode
    })

    // Capture changes using the provider
    for await (const naceCode of provider.getItems()) {
      await provider.captureChangeOnItem(naceCode.id, naceCode)
    }

    const { aggregate } = await naceCodeEventStore.getAggregate("1")
    expect(aggregate).toBeDefined()
    expect(aggregate?.label).toBe("Updated Agriculture")
  })

  it("should delete NACE codes that no longer exist in the source", async () => {
    mockIntegrationService.getNaceCodes = vi.fn().mockImplementation(function* () {
      // Yield nothing, simulating that the existing NACE code has been deleted
    })

    // Get existing IDs and delete those not in source
    const existingIds = await provider.getAggregateIds()
    for (const id of existingIds) {
      await provider.captureChangeOnItem(id, null)
    }

    const { aggregate } = await naceCodeEventStore.getAggregate("1")
    expect(aggregate?.deleted).toBe(true)
  })

  it("should not make any changes if nothing has changed in the source", async () => {
    mockIntegrationService.getNaceCodes = vi.fn().mockImplementation(function* () {
      yield existingNaceCode
    })

    const beforeState = await naceCodeEventStore.getAggregate("1")

    // Capture changes using the provider
    for await (const naceCode of provider.getItems()) {
      await provider.captureChangeOnItem(naceCode.id, naceCode)
    }

    const afterState = await naceCodeEventStore.getAggregate("1")

    expect(beforeState).toEqual(afterState)
  })
})
