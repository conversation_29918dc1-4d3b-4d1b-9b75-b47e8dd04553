/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { PortalUserAggregate, PortalUserEventStore } from "@impactly/domain"
import type { BackendIntegrationService, PortalUserItemFragment } from "@impactly/integration/backend"
import { capturePortalUserChangesCommand } from "@impactly/domain/portal-users/commands"

import type { CaptureChangeProvider } from "./service"
import { env } from "../../env"

export class PortalUser<PERSON>aptureChangeProvider
  implements CaptureChangeProvider<"portalusers", PortalUserItemFragment, PortalUserAggregate>
{
  public readonly indexName = "portalusers"

  constructor(
    private readonly integrationService: BackendIntegrationService,
    private readonly eventStore: PortalUserEventStore
  ) {}

  async captureChangeOnItem(id: string, user: PortalUserItemFragment | null) {
    const { changesDetected } = await capturePortalUserChangesCommand.handler(
      {
        aggregateId: id,
        sourceId: id,
        sourceData: user,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [this.eventStore],
      {}
    )

    // If the company exists in the event store and the data is the same, do nothing
    return changesDetected ? "updated" : "unchanged"
  }

  public getAggregate(id: string) {
    return this.eventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate)
  }

  public getItem(id: string) {
    return this.integrationService.getPortalUser({
      id,
    })
  }

  public async getAggregateIds() {
    return (await this.eventStore.listAggregateIds()).aggregateIds.map((entry) => entry.aggregateId)
  }

  public getItems() {
    return this.integrationService.getPortalUsers()
  }
}
