/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyBatchEventStore } from "@impactly/domain"
import type { BackendIntegrationService, SurveyBatchItemFragment } from "@impactly/integration/backend"
import { EventStore } from "@castore/core"
import { InMemoryEventStorageAdapter } from "@castore/event-storage-adapter-in-memory"
import { captureSurveyBatchChangesCommand } from "@impactly/domain/survey-batches/commands"
import { surveyBatchEvents } from "@impactly/domain/survey-batches/events"
import { surveyBatchReducer } from "@impactly/domain/survey-batches/reducer"
import { IportalSurveyBatchTemplateChoices } from "@impactly/integration/backend"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { env } from "../../env"
import { SurveyBatchCaptureChangeProvider } from "./survey-batches"

void vi.hoisted(() => {
  // @ts-expect-error mocking Di container
  globalThis.container = {
    resolve: vi.fn().mockReturnValue({
      delete: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      status: vi.fn().mockResolvedValue({ status: "green", version: "8.0.0" }),
      listIndices: vi.fn().mockResolvedValue([]),
      reindexAll: vi.fn().mockResolvedValue([]),
      ping: vi.fn().mockResolvedValue(true),
      config: {
        url: "http://localhost:9200",
        apiKey: "mockApiKey",
        indexPrefix: "mockPrefix",
        indices: [
          {
            name: "surveybatches",
          },
        ],
      },
    }),
  }
})

vi.mock("@kreios/elasticsearch/utils", () => ({
  createReindexDataLoaderFunction: vi.fn().mockReturnValue(vi.fn()),
}))

// Mock the db module
vi.mock("@impactly/domain/db", () => ({
  db: {},
}))

// Mock the DrizzleEventStorageAdapter
vi.mock("@kreios/eventsourcing/drizzle", async () => {
  const { MockDrizzleEventStorageAdapter } = await import("@kreios/eventsourcing/drizzle/mocks/mock-drizzle-adapter")
  return {
    DrizzleEventStorageAdapter: MockDrizzleEventStorageAdapter,
  }
})

describe("SurveyBatchCaptureChangeProvider", () => {
  let mockIntegrationService: BackendIntegrationService
  let surveyBatchEventStore: SurveyBatchEventStore
  let provider: SurveyBatchCaptureChangeProvider

  const existingSurveyBatchEventPayload = {
    id: "1",
    name: "Test Survey Batch",
    description: "A test survey batch",
    tenant: { id: "tenant1" },
    template: IportalSurveyBatchTemplateChoices.UnifiedQuestionnaireV1,
  } satisfies SurveyBatchItemFragment

  beforeEach(async () => {
    mockIntegrationService = {
      getSurveyBatches: vi.fn(),
    } as unknown as BackendIntegrationService

    surveyBatchEventStore = new EventStore({
      eventStoreId: "surveyBatches",
      eventStorageAdapter: new InMemoryEventStorageAdapter(),
      eventTypes: surveyBatchEvents,
      reducer: surveyBatchReducer,
    })

    provider = new SurveyBatchCaptureChangeProvider(mockIntegrationService, surveyBatchEventStore)

    // Prefill the event store with an existing survey batch
    await captureSurveyBatchChangesCommand.handler(
      {
        aggregateId: existingSurveyBatchEventPayload.id,
        sourceId: existingSurveyBatchEventPayload.id,
        sourceData: existingSurveyBatchEventPayload,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [surveyBatchEventStore],
      {}
    )
  })

  it("should create new survey batches", async () => {
    const newSurveyBatch = {
      id: "2",
      name: "New Survey Batch",
      description: "A new survey batch",
      tenant: { id: "tenant1" },
      template: IportalSurveyBatchTemplateChoices.UnifiedQuestionnaireV1,
    } satisfies SurveyBatchItemFragment

    mockIntegrationService.getSurveyBatches = vi.fn().mockImplementation(function* () {
      yield existingSurveyBatchEventPayload
      yield newSurveyBatch
    })

    // Capture changes using the provider
    for await (const batch of provider.getItems()) {
      await provider.captureChangeOnItem(batch.id, batch)
    }

    const { aggregate } = await surveyBatchEventStore.getAggregate("2")
    expect(aggregate).toBeDefined()
    expect(aggregate?.name).toBe("New Survey Batch")
  })

  it("should update existing survey batches", async () => {
    const updatedSurveyBatch = { ...existingSurveyBatchEventPayload, name: "Updated Survey Batch" }
    mockIntegrationService.getSurveyBatches = vi.fn().mockImplementation(function* () {
      yield updatedSurveyBatch
    })

    // Capture changes using the provider
    for await (const batch of provider.getItems()) {
      await provider.captureChangeOnItem(batch.id, batch)
    }

    const { aggregate } = await surveyBatchEventStore.getAggregate("1")
    expect(aggregate).toBeDefined()
    expect(aggregate?.name).toBe("Updated Survey Batch")
  })

  it("should delete survey batches that no longer exist in the source", async () => {
    mockIntegrationService.getSurveyBatches = vi.fn().mockImplementation(function* () {
      // Yield nothing, simulating that the existing survey batch has been deleted
    })

    // Get existing IDs and delete those not in source
    const existingIds = await provider.getAggregateIds()
    for (const id of existingIds) {
      await provider.captureChangeOnItem(id, null)
    }

    const { aggregate } = await surveyBatchEventStore.getAggregate("1")
    expect(aggregate?.deleted).toBe(true)
  })

  it("should not make any changes if nothing has changed in the source", async () => {
    mockIntegrationService.getSurveyBatches = vi.fn().mockImplementation(function* () {
      yield existingSurveyBatchEventPayload
    })

    const beforeState = await surveyBatchEventStore.getAggregate("1")

    // Capture changes using the provider
    for await (const batch of provider.getItems()) {
      await provider.captureChangeOnItem(batch.id, batch)
    }

    const afterState = await surveyBatchEventStore.getAggregate("1")

    expect(beforeState).toEqual(afterState)
  })
})
