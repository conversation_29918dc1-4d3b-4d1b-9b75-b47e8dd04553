/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { CompanyEventStore } from "@impactly/domain"
import type { BackendIntegrationService, CompanyItemFragment } from "@impactly/integration/backend"
import { EventStore } from "@castore/core"
import { InMemoryEventStorageAdapter } from "@castore/event-storage-adapter-in-memory"
import { captureCompanyChangesCommand } from "@impactly/domain/companies/commands"
import { companyEvents } from "@impactly/domain/companies/events"
import { companyReducer } from "@impactly/domain/companies/reducer"
import {
  CategoryChoicesEnum,
  CompanyStatusChoicesEnum,
  EbrdRiskLevelChoicesEnum,
  IesgRelevantIissueSourceSourceTypeChoices,
  IorderCompanyCountryChoices,
  LevelChoicesEnum,
  StatusChoicesEnum,
} from "@impactly/integration/backend"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { env } from "../../env"
import { CompanyCaptureChangeProvider } from "./companies"

void vi.hoisted(() => {
  // @ts-expect-error mocking Di container
  globalThis.container = {
    resolve: vi.fn().mockReturnValue({
      delete: vi.fn().mockResolvedValue(undefined),
      close: vi.fn().mockResolvedValue(undefined),
      status: vi.fn().mockResolvedValue({ status: "green", version: "8.0.0" }),
      listIndices: vi.fn().mockResolvedValue([]),
      reindexAll: vi.fn().mockResolvedValue([]),
      ping: vi.fn().mockResolvedValue(true),
      config: {
        url: "http://localhost:9200",
        apiKey: "mockApiKey",
        indexPrefix: "mockPrefix",
        indices: [
          {
            name: "companies",
          },
        ],
      },
    }),
  }
})

vi.mock("@kreios/elasticsearch/utils", () => ({
  createReindexDataLoaderFunction: vi.fn().mockReturnValue(vi.fn()),
}))

// Mock the db module
vi.mock("@impactly/domain/db", () => ({
  db: {},
}))

// Mock the DrizzleEventStorageAdapter
vi.mock("@kreios/eventsourcing/drizzle", async () => {
  const { MockDrizzleEventStorageAdapter } = await import("@kreios/eventsourcing/drizzle/mocks/mock-drizzle-adapter")
  return {
    DrizzleEventStorageAdapter: MockDrizzleEventStorageAdapter,
  }
})

describe("CompanyCaptureChangeProvider", () => {
  let mockIntegrationService: BackendIntegrationService
  let companyEventStore: CompanyEventStore
  let provider: CompanyCaptureChangeProvider

  const existingCompany = {
    id: "1",
    name: "Test Company",
    about: "A test company",
    registrationNumber: "12345678",
    status: CompanyStatusChoicesEnum.EnteredInTheRegister,
    country: IorderCompanyCountryChoices.Ee,
    employeeNumber: 100,
    street: "Test Street",
    houseNumber: "1",
    postalCode: "12345",
    city: "Test City",
    registrationDate: "2023-01-01",
    urls: [{ url: "https://testcompany.com" }],
    businessActivities: [
      {
        id: "1",
        euTaxonomyEligible: true,
        naceCode: {
          id: "1234",
          label: "Test NACE Code",
          overallEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Low,
          environmentalEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Low,
          socialEbrdRiskLevel: EbrdRiskLevelChoicesEnum.Low,
        },
        latestReportItem: {
          isMainActivity: true,
          businessActivityRevenue: {
            amount: 1000000,
            currency: "EUR",
          },
        },
      },
    ],
    annualReports: [
      {
        financialYear: 2022,
        averageFullTimeEmployeesConsolidated: 95,
        companyRevenueConsolidated: {
          amount: 1000000,
          currency: "EUR",
        },
        netProfitConsolidated: {
          amount: 100000,
          currency: "EUR",
        },
        totalAssets: {
          amount: 2000000,
          currency: "EUR",
        },
      },
    ],
    latestEsgMaterialityMapping: {
      executionDate: "2023-06-01",
      relevantPositives: [
        {
          summary: "Positive impact",
          maturityLevel: LevelChoicesEnum.High,
        },
      ],
      relevantIssues: [
        {
          iissue: {
            id: "1",
            name: "Environmental Issue",
            category: CategoryChoicesEnum.Environmental,
            issue: {
              description: "An environmental issue",
            },
          },
          maturityLevel: LevelChoicesEnum.Medium,
          gabAnalysisResults: "Analysis results",
          summary: "Summary of the issue",
          recommendations: ["Recommendation 1", "Recommendation 2"],
          sources: [
            {
              sourceType: IesgRelevantIissueSourceSourceTypeChoices.AnnualReport,
              source: "Annual Report 2022",
            },
          ],
        },
      ],
    },
    publishedEvaluations: [
      {
        id: "1",
        company: {
          id: "1",
          name: "Test Company",
          registrationNumber: "12345678",
        },
        status: StatusChoicesEnum.Done,
        completedAt: "2023-07-01",
        evaluationYear: 2023,
        overallRecommendations: ["Recommendation 1", "Recommendation 2"],
        environmentalMaturity: LevelChoicesEnum.Medium,
        socialMaturity: LevelChoicesEnum.High,
        governanceMaturity: LevelChoicesEnum.Medium,
        overallSummary: "Overall summary of the evaluation",
        inputs: {
          dataDepth: {
            baseData: true,
            publicData: true,
            selfAssessment: false,
            advanced: false,
          },
          evaluationDepth: {
            esgRiskMapping: true,
            aiEvaluations: true,
            esgRecommendations: true,
            enrichedEvaluation: false,
            fullEvaluation: false,
          },
          sources: {
            businessRegistry: true,
            annualReport: true,
            website: true,
            media: false,
            questionnaire: false,
            proprietaryData: false,
          },
        },
      },
    ],
  } satisfies CompanyItemFragment

  beforeEach(async () => {
    mockIntegrationService = {
      getCompanies: vi.fn(),
    } as unknown as BackendIntegrationService

    companyEventStore = new EventStore({
      eventStoreId: "companies",
      eventStorageAdapter: new InMemoryEventStorageAdapter(),
      eventTypes: companyEvents,
      reducer: companyReducer,
    })

    provider = new CompanyCaptureChangeProvider(mockIntegrationService, companyEventStore)

    // Prefill the event store with an existing company
    await captureCompanyChangesCommand.handler(
      {
        aggregateId: existingCompany.id,
        sourceId: existingCompany.id,
        sourceData: existingCompany,
        forceFresh: env.CDC_FORCE_FRESH_MODE,
      },
      [companyEventStore],
      {}
    )
  })

  it("should create new companies", async () => {
    const newCompany = {
      id: "2",
      name: "New Company",
      registrationNumber: "87654321",
      status: CompanyStatusChoicesEnum.EnteredInTheRegister,
      country: IorderCompanyCountryChoices.Ee,
      employeeNumber: 50,
      street: "New Street",
      houseNumber: "2",
      postalCode: "54321",
      city: "New City",
      registrationDate: "2023-06-01",
      urls: [{ url: "https://newcompany.com" }],
      businessActivities: [],
      annualReports: [],
      mediaSources: [],
    } satisfies CompanyItemFragment

    mockIntegrationService.getCompanies = vi.fn().mockImplementation(function* () {
      yield existingCompany
      yield newCompany
    })

    // Capture changes using the provider
    for await (const company of provider.getItems()) {
      await provider.captureChangeOnItem(company.id, company)
    }

    const result = await companyEventStore.getAggregate("2")
    expect(result.aggregate).toBeDefined()
    expect(result.aggregate?.name).toBe("New Company")
  })

  it("should update existing companies", async () => {
    const updatedCompany = { ...existingCompany, name: "Updated Company" }
    mockIntegrationService.getCompanies = vi.fn().mockImplementation(function* () {
      yield updatedCompany
    })

    // Capture changes using the provider
    for await (const company of provider.getItems()) {
      await provider.captureChangeOnItem(company.id, company)
    }

    const { aggregate } = await companyEventStore.getAggregate("1")
    expect(aggregate).toBeDefined()
    expect(aggregate?.name).toBe("Updated Company")
  })

  it("should delete companies that no longer exist in the source", async () => {
    mockIntegrationService.getCompanies = vi.fn().mockImplementation(function* () {
      // Yield nothing, simulating that the existing company has been deleted
    })

    // Get existing IDs and delete those not in source
    const existingIds = await provider.getAggregateIds()
    for (const id of existingIds) {
      await provider.captureChangeOnItem(id, null)
    }

    const { aggregate } = await companyEventStore.getAggregate("1")
    expect(aggregate?.deleted).toBe(true)
  })

  it("should not make any changes if nothing has changed in the source", async () => {
    mockIntegrationService.getCompanies = vi.fn().mockImplementation(function* () {
      yield existingCompany
    })

    const beforeState = await companyEventStore.getAggregate("1")

    // Capture changes using the provider
    for await (const company of provider.getItems()) {
      await provider.captureChangeOnItem(company.id, company)
    }

    const afterState = await companyEventStore.getAggregate("1")

    expect(beforeState).toEqual(afterState)
  })
})
