/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { EvaluationRequestAggregate } from "@impactly/domain"
import {
  evaluationRequestEventStore,
  portalUserEventStore,
  recordEvaluationRequestNotificationSentCommand,
} from "@impactly/domain"
import { generateEmailContent } from "@impactly/email"
import { z } from "zod"

import { jobType } from "@kreios/jobs"
import { MailSender } from "@kreios/mail-sender"
import { AzureProvider, ResendProvider } from "@kreios/mail-sender/providers"

import { env } from "../../env"

const TEAM_EMAIL = env.IMPACTLY_TEAM_EMAIL

/**
 * Job to send notifications for a specific evaluation request
 */
export const sendNotifications = jobType("send-notifications")
  .description("Send notifications for a specific evaluation request")
  .input(z.object({ evaluationRequestId: z.string() }))
  .handler(async ({ evaluationRequestId }) => {
    // Fetch the evaluation request from the event store
    const { aggregate: evaluationRequest } = await evaluationRequestEventStore.getAggregate(evaluationRequestId)
    if (!evaluationRequest) {
      throw new Error(`Evaluation request not found: ${evaluationRequestId}`)
    }

    // Fetch the portal user associated with this request
    const { aggregate: portalUser } = await portalUserEventStore.getAggregate(evaluationRequest.portalUserId)
    if (!portalUser) {
      throw new Error(`Portal user not found: ${evaluationRequest.portalUserId}`)
    }

    // For new requests: send notification to requestor and team
    const requestorEmail = portalUser.email
    if (
      evaluationRequest.status === "pending" &&
      !evaluationRequest.notifications.some((n) => n.type === "request-received")
    ) {
      await sendAndRecordNotification("request-received", requestorEmail, evaluationRequest)
      if (TEAM_EMAIL) await sendAndRecordNotification("team-notification", TEAM_EMAIL, evaluationRequest)
    }

    // For fulfilled requests: send notification to requestor
    if (
      evaluationRequest.status === "fulfilled" &&
      !evaluationRequest.notifications.some((n) => n.type === "request-fulfilled")
    ) {
      await sendAndRecordNotification("request-fulfilled", requestorEmail, evaluationRequest)
    }

    console.info(`Notifications processed for evaluation request ${evaluationRequestId}`)
  })
  .build()

/**
 * Sends a notification email and records it in the evaluation request aggregate
 * @param type The type of notification
 * @param to The recipient's email address
 * @param evaluationRequest The evaluation request details
 */
async function sendAndRecordNotification(
  type: "request-received" | "request-fulfilled" | "team-notification",
  to: string,
  evaluationRequest: EvaluationRequestAggregate
) {
  const { subject, html } = await generateEmailContent(type, evaluationRequest)
  const mailSender = initializeMailSender()
  const messageId = await mailSender.send({
    to: [
      {
        email: to,
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        name: type === "team-notification" ? "IMPACTLY Team" : evaluationRequest.portalUserName || "Portal User",
      },
    ],
    subject,
    html,
  })

  // Only record notifications sent to the requestor, not team notifications
  if (type !== "team-notification") {
    await recordEvaluationRequestNotificationSentCommand.handler(
      {
        aggregateId: evaluationRequest.aggregateId,
        email: to,
        type: type,
        messageId,
        sentAt: new Date(),
      },
      [evaluationRequestEventStore],
      {}
    )
  }
}

/**
 * Helper function to initialize the MailSender with the appropriate provider (Resend or Azure).
 *
 * @returns MailSender - An instance of the MailSender class
 * @throws Error if neither Resend nor Azure Communication Services credentials are provided.
 */
function initializeMailSender(): MailSender {
  if (env.RESEND_API_KEY && env.RESEND_FROM_EMAIL) {
    const resendProvider = new ResendProvider(env.RESEND_API_KEY)
    console.info("Resend provider selected for password-less authentication via email in NextAuth.")
    return new MailSender(resendProvider, { name: "Impactly Client Portal", email: env.RESEND_FROM_EMAIL })
  } else if (env.AZURE_COMMUNICATION_CONNECTION_STRING && env.AZURE_COMMUNICATION_FROM_EMAIL) {
    const azureProvider = new AzureProvider(env.AZURE_COMMUNICATION_CONNECTION_STRING)
    console.info("Azure Communication Services selected for password-less authentication via email in NextAuth.")
    return new MailSender(azureProvider, { name: "Impactly Client Portal", email: env.AZURE_COMMUNICATION_FROM_EMAIL })
  } else {
    throw new Error(
      "Neither Resend nor Azure Communication Services credentials were provided. Please set the necessary environment variables."
    )
  }
}
