/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { createEnv } from "@t3-oss/env-core"
import { vercel } from "@t3-oss/env-core/presets"
import { z } from "zod"

import { env as jobsEnv } from "@kreios/jobs/env"

export const env = createEnv({
  extends: [jobsEnv, vercel()],
  server: {
    IMPACTLY_TEAM_EMAIL: z
      .string()
      .email()
      .optional()
      .describe("Email to send notifications to when a user requests an evaluation"),
    RESEND_API_KEY: z
      .string()
      .optional()
      .describe("API key for the resend API see https://resend.com/docs/send-with-nextjs"),
    RESEND_FROM_EMAIL: z.string().optional().describe("Resend sender email (needs to use a validated domain)"),
    AZURE_COMMUNICATION_CONNECTION_STRING: z.string().optional().describe("Azure Communication connection string"),
    AZURE_COMMUNICATION_FROM_EMAIL: z
      .string()
      .optional()
      .describe("Azure Communication sender email (needs to use a validated domain)"),

    // CDC Performance Configuration
    CDC_BATCH_SIZE: z
      .string()
      .transform((val) => parseInt(val, 10))
      .pipe(z.number().int().positive())
      .default("200")
      .describe(
        "Batch size for CDC processing (companies per batch). Higher values = faster processing but more memory usage"
      ),

    CDC_API_PAGE_SIZE: z
      .string()
      .transform((val) => parseInt(val, 10))
      .pipe(z.number().int().positive())
      .default("400")
      .describe("Page size for external API requests. Higher values = fewer API calls but more memory per request"),

    // Elasticsearch Performance Configuration
    ES_BATCH_SIZE: z
      .string()
      .transform((val) => parseInt(val, 10))
      .pipe(z.number().int().positive())
      .default("10000")
      .describe("Batch size for Elasticsearch bulk operations. Higher values = faster indexing but more memory usage"),

    ES_MAX_BATCH_SIZE: z
      .string()
      .transform((val) => parseInt(val, 10))
      .pipe(z.number().int().positive())
      .default("10000")
      .describe("Maximum batch size for Elasticsearch index configurations"),

    // Database Performance Configuration
    DB_BATCH_SIZE: z
      .string()
      .transform((val) => parseInt(val, 10))
      .pipe(z.number().int().positive())
      .default("10922")
      .describe("Batch size for database operations (event store writes). Optimized for PostgreSQL parameter limits"),

    // Production Optimization Flags
    PRODUCTION_OPTIMIZATION_MODE: z
      .enum(["development", "migration", "production"])
      .default("development")
      .describe(
        "Optimization mode: 'development' (safe defaults), 'migration' (high performance), 'production' (balanced)"
      ),

    // CDC Behavior Flags
    CDC_FORCE_FRESH_MODE: z
      .string()
      .transform((val) => val.toLowerCase() === "true")
      .pipe(z.boolean())
      .default("false")
      .describe(
        "Skip change detection and treat all items as new/updated. Use for fresh data loads or when you know all data has changed."
      ),

    // CDC Filtering Options
    CDC_COUNTRY_FILTER: z
      .string()
      .optional()
      .describe(
        "Filter companies by country code (e.g., 'EE' for Estonia, 'LT' for Lithuania). Leave empty for all countries."
      ),
  },
  runtimeEnv: process.env,
  skipValidation:
    !process.env.VERCEL &&
    (!!process.env.CI ||
      !!process.env.SKIP_ENV_VALIDATION ||
      process.env.npm_lifecycle_event === "lint" ||
      process.env.NODE_ENV === "test"),
  emptyStringAsUndefined: true,
})
