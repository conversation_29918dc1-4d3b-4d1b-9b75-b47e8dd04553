CREATE TABLE IF NOT EXISTS "surveyBatches" (
	"aggregateid" varchar(36) NOT NULL,
	"version" integer NOT NULL,
	"type" varchar(50) NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"payload" jsonb NOT NULL,
	"metadata" jsonb NOT NULL,
	CONSTRAINT "surveyBatches_aggregateid_version_pk" PRIMARY KEY("aggregateid","version")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "surveys" (
	"aggregateid" varchar(36) NOT NULL,
	"version" integer NOT NULL,
	"type" varchar(50) NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"payload" jsonb NOT NULL,
	"metadata" jsonb NOT NULL,
	CONSTRAINT "surveys_aggregateid_version_pk" PRIMARY KEY("aggregateid","version")
);
