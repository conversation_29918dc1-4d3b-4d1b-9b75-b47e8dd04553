{"id": "5e5d9edf-f74f-4e36-be59-3f343009f144", "prevId": "a7339bcc-9f24-45b0-bd55-5a11240c573b", "version": "7", "dialect": "postgresql", "tables": {"public.companies": {"name": "companies", "schema": "", "columns": {"aggregateid": {"name": "aggregateid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"companies_aggregateid_version_pk": {"name": "companies_aggregateid_version_pk", "columns": ["aggregateid", "version"]}}, "uniqueConstraints": {}}, "public.evaluationRequests": {"name": "evaluationRequests", "schema": "", "columns": {"aggregateid": {"name": "aggregateid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"evaluationRequests_aggregateid_version_pk": {"name": "evaluationRequests_aggregateid_version_pk", "columns": ["aggregateid", "version"]}}, "uniqueConstraints": {}}, "public.naceCodes": {"name": "naceCodes", "schema": "", "columns": {"aggregateid": {"name": "aggregateid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"naceCodes_aggregateid_version_pk": {"name": "naceCodes_aggregateid_version_pk", "columns": ["aggregateid", "version"]}}, "uniqueConstraints": {}}, "public.portalUsers": {"name": "portalUsers", "schema": "", "columns": {"aggregateid": {"name": "aggregateid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"portalUsers_aggregateid_version_pk": {"name": "portalUsers_aggregateid_version_pk", "columns": ["aggregateid", "version"]}}, "uniqueConstraints": {}}, "public.watchlists": {"name": "watchlists", "schema": "", "columns": {"aggregateid": {"name": "aggregateid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "jsonb", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"watchlists_aggregateid_version_pk": {"name": "watchlists_aggregateid_version_pk", "columns": ["aggregateid", "version"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}