CREATE TABLE IF NOT EXISTS "companies" (
	"aggregateid" varchar(36) NOT NULL,
	"version" integer NOT NULL,
	"type" varchar(50) NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"payload" jsonb NOT NULL,
	"metadata" jsonb NOT NULL,
	CONSTRAINT "companies_aggregateid_version_pk" PRIMARY KEY("aggregateid","version")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "portalUsers" (
	"aggregateid" varchar(36) NOT NULL,
	"version" integer NOT NULL,
	"type" varchar(50) NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"payload" jsonb NOT NULL,
	"metadata" jsonb NOT NULL,
	CONSTRAINT "portalUsers_aggregateid_version_pk" PRIMARY KEY("aggregateid","version")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "watchlists" (
	"aggregateid" varchar(36) NOT NULL,
	"version" integer NOT NULL,
	"type" varchar(50) NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"payload" jsonb NOT NULL,
	"metadata" jsonb NOT NULL,
	CONSTRAINT "watchlists_aggregateid_version_pk" PRIMARY KEY("aggregateid","version")
);
