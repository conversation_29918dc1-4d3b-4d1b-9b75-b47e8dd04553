{"name": "@impactly/domain", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./elastic": "./src/elastic/index.ts", "./env": "./env.ts", "./config": "./config.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "drizzle-kit": "pnpm with-env drizzle-kit", "elastic": "pnpm tsx ./src/elastic/cli.ts", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "lint": "eslint", "seed": "pnpm tsx ./src/seed.ts", "test": "vitest run --passWithNoTests", "test:watch": "vitest --passWithNoTests", "tsx": "pnpm with-env tsx --conditions=react-server", "tsx:debug": "pnpm with-env tsx --conditions=react-server --inspect-brk", "typecheck": "tsc --noEmit", "with-env": "dotenvx run -f ../../.env.local -f ../../.env --overload --"}, "prettier": "@kreios/prettier-config", "dependencies": {"@castore/core": "2.3.1", "@commander-js/extra-typings": "12.1.0", "@faker-js/faker": "9.0.0", "@impactly/flags": "workspace:*", "@impactly/integration": "workspace:*", "@kreios/elasticsearch": "workspace:*", "@kreios/eventsourcing": "workspace:*", "@kreios/utils": "workspace:*", "@t3-oss/env-core": "0.11.1", "awilix": "12.0.3", "lodash-es": "4.17.21", "ulidx": "2.4.1", "zod": "3.23.8"}, "devDependencies": {"@dotenvx/dotenvx": "1.14.0", "@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/react": "18.3.5", "drizzle-kit": "0.30.4", "eslint": "9.10.0", "prettier": "3.4.2", "tsx": "4.19.0", "typescript": "5.6.2", "vitest": "2.1.9"}, "peerDependencies": {"react": "18.3.1"}}