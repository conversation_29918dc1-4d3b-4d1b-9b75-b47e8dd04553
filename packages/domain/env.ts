/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { createEnv } from "@t3-oss/env-core"
import { vercel } from "@t3-oss/env-core/presets"
import { z } from "zod"

export const env = createEnv({
  extends: [vercel()],
  server: {
    DATABASE_URL: z.string().url(),
    ELASTICSEARCH_URL: z.string().url(),
    ELASTICSEARCH_API_KEY: z.string().optional(),
    STABILITYAI_API_KEY: z.string().optional(),
    PLATFORM_RESOURCE_NAMESPACE: z.string().optional(),
  },
  runtimeEnv: process.env,
  skipValidation:
    !process.env.VERCEL &&
    (!!process.env.CI ||
      !!process.env.SKIP_ENV_VALIDATION ||
      process.env.npm_lifecycle_event === "lint" ||
      process.env.NODE_ENV === "test"),
  emptyStringAsUndefined: true,
})
