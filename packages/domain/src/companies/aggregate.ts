/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

// Enums
const CompanyStatusEnum = z.enum(["bankrupt", "delete", "entered_in_the_register", "in_liquidation"])
const CategoryChoicesEnum = z.enum(["environmental", "governance", "social", "na"])
const LevelChoicesEnum = z.enum(["high", "low", "medium", "very_high", "very_low"])
const StatusChoicesEnum = z.enum(["DONE", "ERROR", "IN_PROGRESS", "ISSUES_EVALUATED", "STARTED"])

// The money schema is reused in multiple places, so it's defined here
const moneySchema = z.object({
  amount: z.number(),
  currency: z.string(),
})

const businessActivitySchema = z.object({
  id: z.string(),
  euTaxonomyEligible: z.boolean(),
  naceCode: z.object({
    label: z.string().optional(),
    labelEt: z.string().optional(),
    code: z.string().optional(),
  }),
  naceCodeId: z.string().optional(),
  isMainActivity: z.boolean().optional(),
  revenue: moneySchema.optional(),
})

const annualReportSchema = z.object({
  financialYear: z.number(),
  averageFullTimeEmployees: z.number().optional(),
  revenue: moneySchema.optional(),
  netProfit: moneySchema.optional(),
  totalAssets: moneySchema.optional(),
})

const materialityMappingSchema = z.object({
  executionDate: z.date(),
  relevantPositives: z.array(
    z.object({
      summary: z.string().optional(),
      maturityLevel: z.string(),
    })
  ),
  relevantIssues: z.array(
    z.object({
      name: z.string(),
      category: CategoryChoicesEnum,
      description: z.string(),
      maturityLevel: LevelChoicesEnum,
      summary: z.string().optional(),
      recommendations: z.array(z.string()),
      sources: z.array(
        z.object({
          sourceType: z.string(),
          source: z.string(),
        })
      ),
    })
  ),
})

const evaluationSchema = z.object({
  id: z.string(),
  status: StatusChoicesEnum,
  completedAt: z.date().optional(),
  evaluationYear: z.number().optional(),
  overallRecommendations: z.array(z.string()),
  environmentalMaturity: LevelChoicesEnum,
  socialMaturity: LevelChoicesEnum,
  governanceMaturity: LevelChoicesEnum,
  overallSummary: z.string().optional(),
  inputs: z.object({
    dataDepth: z.object({
      baseData: z.boolean(),
      publicData: z.boolean(),
      selfAssessment: z.boolean(),
      advanced: z.boolean(),
    }),
    evaluationDepth: z.object({
      esgRiskMapping: z.boolean(),
      aiEvaluations: z.boolean(),
      esgRecommendations: z.boolean(),
      enrichedEvaluation: z.boolean(),
      fullEvaluation: z.boolean(),
    }),
    sources: z.object({
      businessRegistry: z.boolean(),
      annualReport: z.boolean(),
      website: z.boolean(),
      media: z.boolean(),
      questionnaire: z.boolean(),
      proprietaryData: z.boolean(),
    }),
  }),
})

const mediaSourceSchema = z.object({
  id: z.string(),
  url: z.string(),
  title: z.string(),
  summary: z.string().optional(),
  sentiment: z.string().optional(),
  story: z.string().optional(),
  publishedAt: z.date().optional(),
  esgCategory: z.enum(["ENVIRONMENTAL", "GOVERNANCE", "N_A", "SOCIAL"]).optional(),
})

export const companyEmissionSchema = z.object({
  id: z.string(),
  year: z.number().optional(),
  source: z.string().optional(),
  basis: z.string().optional(),
  multiplier: z.string().optional(),
  unit: z.string().optional(),
  value: z.string().optional(),
  scope: z.string().optional(),
})

// Pulling everything together
export const companyAggregateSchema = defineAggregate(
  z.object({
    // Basic information
    name: z.string(),
    about: z.string().optional(),
    aboutEn: z.string().optional(),
    aboutEt: z.string().optional(),
    registrationNumber: z.string().optional().optional(),
    status: CompanyStatusEnum,
    country: z.string(),
    employeeCount: z.number().optional(),
    address: z.object({
      street: z.string().optional(),
      houseNumber: z.string().optional(),
      postalCode: z.string().optional(),
      city: z.string().optional(),
    }),
    registrationDate: z.date().optional(),
    website: z.string().optional(),

    // Business activities
    businessActivities: z.array(businessActivitySchema),

    // Financial data
    annualReports: z.array(annualReportSchema),

    // ESG assessment (a.k.a. materiality mapping)
    latestMaterialityMapping: materialityMappingSchema.optional(),

    // Published evaluations
    publishedEvaluations: evaluationSchema.array(),

    // Media sources
    mediaSources: z.array(mediaSourceSchema).optional(),

    // Latest GHG emission
    latestGhgEmission: z.array(companyEmissionSchema).optional().nullable(),
  })
)

export type CompanyAggregate = z.infer<typeof companyAggregateSchema>
