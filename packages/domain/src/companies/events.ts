/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { CompanyItemFragment } from "@impactly/integration/backend"
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

import { graphqlZodSchema } from "../utils"

const companySchema = z.object({
  name: z.string(),
  about: z.string().optional(),
  registrationNumber: z.string().optional(),
  status: z.enum(["bankrupt", "delete", "entered_in_the_register", "in_liquidation"]),
  country: z.string(),
  employeeCount: z.number().optional(),
  address: z.object({
    street: z.string().optional(),
    houseNumber: z.string().optional(),
    postalCode: z.string().optional(),
    city: z.string().optional(),
  }),
  registrationDate: z.date().optional(),
  website: z.string().optional(),
  businessActivities: z.array(
    z.object({
      id: z.string(),
      euTaxonomyEligible: z.boolean(),
      naceCode: z.object({
        label: z.string().optional(),
        labelEt: z.string().optional(),
        code: z.string().optional(),
      }),
      isMainActivity: z.boolean().optional(),
      revenue: z
        .object({
          amount: z.number(),
          currency: z.string(),
        })
        .optional(),
    })
  ),
  annualReports: z.array(
    z.object({
      financialYear: z.number(),
      averageFullTimeEmployees: z.number().optional(),
      revenue: z
        .object({
          amount: z.number(),
          currency: z.string(),
        })
        .optional(),
      netProfit: z
        .object({
          amount: z.number(),
          currency: z.string(),
        })
        .optional(),
      totalAssets: z
        .object({
          amount: z.number(),
          currency: z.string(),
        })
        .optional(),
    })
  ),

  latestGhgEmission: z
    .array(
      z.object({
        id: z.string(),
        year: z.number(),
        source: z.string().optional(),
        basis: z.string().optional(),
        multiplier: z.string().optional(),
        unit: z.string(),
        value: z.string(),
        scope: z.string(),
      })
    )
    .optional(),
  mediaSources: z
    .array(
      z.object({
        id: z.string(),
        url: z.string(),
        sentiment: z.string().optional(),
        title: z.string(),
        summary: z.string().optional(),
        story: z.string().optional(),
        publishedAt: z.date().optional(),
        esgCategory: z.enum(["ENVIRONMENTAL", "GOVERNANCE", "N_A", "SOCIAL"]).optional(),
      })
    )
    .optional(),
  latestMaterialityMapping: z
    .object({
      executionDate: z.date(),
      relevantPositives: z.array(
        z.object({
          summary: z.string().optional(),
          maturityLevel: z.string(),
        })
      ),
      relevantIssues: z.array(
        z.object({
          name: z.string(),
          category: z.enum(["environmental", "governance", "social", "na"]),
          description: z.string(),
          maturityLevel: z.enum(["high", "low", "medium", "very_high", "very_low"]),
          summary: z.string().optional(),
          recommendations: z.array(z.string()),
          sources: z.array(
            z.object({
              sourceType: z.string(),
              source: z.string(),
            })
          ),
        })
      ),
    })
    .optional(),
  publishedEvaluations: z
    .object({
      id: z.string(),
      status: z.enum(["DONE", "ERROR", "IN_PROGRESS", "ISSUES_EVALUATED", "STARTED"]),
      completedAt: z.date().optional(),
      evaluationYear: z.number().optional(),
      overallRecommendations: z.array(z.string()),
      environmentalMaturity: z.enum(["high", "low", "medium", "very_high", "very_low"]),
      socialMaturity: z.enum(["high", "low", "medium", "very_high", "very_low"]),
      governanceMaturity: z.enum(["high", "low", "medium", "very_high", "very_low"]),
      overallSummary: z.string().optional(),
      inputs: z.object({
        dataDepth: z.object({
          baseData: z.boolean(),
          publicData: z.boolean(),
          selfAssessment: z.boolean(),
          advanced: z.boolean(),
        }),
        evaluationDepth: z.object({
          esgRiskMapping: z.boolean(),
          aiEvaluations: z.boolean(),
          esgRecommendations: z.boolean(),
          enrichedEvaluation: z.boolean(),
          fullEvaluation: z.boolean(),
        }),
        sources: z.object({
          businessRegistry: z.boolean(),
          annualReport: z.boolean(),
          website: z.boolean(),
          media: z.boolean(),
          questionnaire: z.boolean(),
          proprietaryData: z.boolean(),
        }),
      }),
    })
    .array(),
})

export const companyCreatedV1 = defineEventType({
  aggregateType: "company",
  eventType: "created",
  schemaVersion: 1,
  schema: companySchema,
})

export const companyUpdatedV1 = defineEventType({
  aggregateType: "company",
  eventType: "updated",
  schemaVersion: 1,
  schema: companySchema.partial(),
})

export const companyDeletedV1 = defineEventType({
  aggregateType: "company",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const companyChangedOnPlatformV1 = defineCDCEventType({
  aggregateType: "company",
  schemaVersion: 1,
  source: "platform",
  sourceType: "company",
  schema: graphqlZodSchema<CompanyItemFragment>().nullable(),
})

export const companyEvents = [companyCreatedV1, companyUpdatedV1, companyDeletedV1, companyChangedOnPlatformV1]
