/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { CompanyItemFragment } from "@impactly/integration/backend"

import { defineReducer } from "@kreios/eventsourcing"
import { isTruthy } from "@kreios/utils/isTruthy"
import { toUTCDate } from "@kreios/utils/to-utc-date"

import type { AggregateWithoutBase } from "../utils"
import type { CompanyAggregate } from "./aggregate"
import type { companyEvents } from "./events"

export const companyReducer = defineReducer<CompanyAggregate, typeof companyEvents>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "company:created:v1": {
      return {
        ...event.payload,
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
      }
    }
    case "company:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "company:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
    case "company:changeCaptured/platform/company:v1": {
      if (event.payload.sourceData)
        return {
          ...state!,
          ...mapToAggregate(event.payload.sourceData),
          aggregateId,
          version,
          createdAt: state?.createdAt ?? new Date(timestamp),
          updatedAt: new Date(timestamp),
        }
      else
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
    }
  }
})

function mapToAggregate(company: CompanyItemFragment): AggregateWithoutBase<CompanyAggregate> {
  return {
    aggregateId: company.id,
    name: company.name,
    about: company.about ?? undefined,
    aboutEn: company.aboutEn ?? undefined,
    aboutEt: company.aboutEt ?? undefined,
    registrationNumber: company.registrationNumber ?? undefined,
    status: company.status!,
    country: company.country!,
    employeeCount: company.employeeNumber ?? undefined,
    address: {
      street: company.street ?? undefined,
      houseNumber: company.houseNumber ?? undefined,
      postalCode: company.postalCode ?? undefined,
      city: company.city ?? undefined,
    },
    registrationDate: toUTCDate(company.registrationDate),
    website: company.urls[0]?.url ?? undefined,
    businessActivities: company.businessActivities.map((activity) => ({
      id: activity.id,
      naceCode: {
        code: activity.naceCode?.code ?? undefined,
        label: activity.naceCode?.label ?? undefined,
        labelEt: activity.naceCode?.labelEt ?? undefined,
      },
      naceCodeId: activity.naceCode!.id,
      euTaxonomyEligible: activity.euTaxonomyEligible!,
      isMainActivity: activity.latestReportItem?.isMainActivity ?? undefined,
      revenue: activity.latestReportItem?.businessActivityRevenue
        ? {
            amount: activity.latestReportItem.businessActivityRevenue.amount,
            currency: activity.latestReportItem.businessActivityRevenue.currency,
          }
        : undefined,
    })),
    latestGhgEmission: company.latestGhgEmission?.filter(isTruthy).map((source) => ({
      id: source.id,
      year: source.year ?? undefined,
      source: source.source ?? undefined,
      basis: source.basis ?? undefined,
      multiplier: source.multiplier ?? undefined,
      unit: source.unit ?? undefined,
      value: source.value ?? undefined,
      scope: source.scope ?? undefined,
    })),

    mediaSources: company.mediaSources?.filter(isTruthy).map((source) => ({
      id: source.id,
      url: source.url,
      title: source.title,
      summary: source.summary,
      sentiment: source.sentiment ?? undefined,
      story: source.story ?? undefined,
      publishedAt: toUTCDate(source.publishedAt),
      esgCategory: source.esgCategory ?? undefined,
    })),
    annualReports: company.annualReports.map((report) => ({
      financialYear: report.financialYear!,
      averageFullTimeEmployees: report.averageFullTimeEmployeesConsolidated ?? undefined,
      revenue: report.companyRevenueConsolidated
        ? {
            amount: report.companyRevenueConsolidated.amount,
            currency: report.companyRevenueConsolidated.currency,
          }
        : undefined,
      netProfit: report.netProfitConsolidated
        ? {
            amount: report.netProfitConsolidated.amount,
            currency: report.netProfitConsolidated.currency,
          }
        : undefined,
      totalAssets: report.totalAssets
        ? {
            amount: report.totalAssets.amount,
            currency: report.totalAssets.currency,
          }
        : undefined,
    })),
    latestMaterialityMapping: company.latestEsgMaterialityMapping
      ? {
          executionDate: toUTCDate(company.latestEsgMaterialityMapping.executionDate)!,
          relevantPositives: company.latestEsgMaterialityMapping.relevantPositives!.map((positive) => ({
            summary: positive!.summary ?? undefined,
            maturityLevel: positive!.maturityLevel!,
          })),
          relevantIssues: company.latestEsgMaterialityMapping.relevantIssues!.map((issue) => ({
            name: issue!.iissue!.name!,
            category: issue!.iissue!.category!,
            description: issue!.iissue!.issue!.description!,
            maturityLevel: issue!.maturityLevel!,
            summary: issue!.summary ?? undefined,
            recommendations: issue!.recommendations ?? [],
            sources:
              issue!.sources?.map((source) => ({
                sourceType: source!.sourceType!,
                source: source!.source!,
              })) ?? [],
          })),
        }
      : undefined,
    publishedEvaluations:
      company.publishedEvaluations?.map((evaluation) => ({
        id: evaluation!.id,
        status: evaluation!.status!,
        completedAt: toUTCDate(evaluation!.completedAt),
        evaluationYear: evaluation!.evaluationYear ?? undefined,
        overallRecommendations: evaluation!.overallRecommendations ?? [],
        environmentalMaturity: evaluation!.environmentalMaturity!,
        socialMaturity: evaluation!.socialMaturity!,
        governanceMaturity: evaluation!.governanceMaturity!,
        overallSummary: evaluation!.overallSummary ?? undefined,
        inputs: {
          dataDepth: {
            baseData: evaluation!.inputs!.dataDepth!.baseData!,
            publicData: evaluation!.inputs!.dataDepth!.publicData!,
            selfAssessment: evaluation!.inputs!.dataDepth!.selfAssessment!,
            advanced: evaluation!.inputs!.dataDepth!.advanced!,
          },
          evaluationDepth: {
            esgRiskMapping: evaluation!.inputs!.evaluationDepth!.esgRiskMapping!,
            aiEvaluations: evaluation!.inputs!.evaluationDepth!.aiEvaluations!,
            esgRecommendations: evaluation!.inputs!.evaluationDepth!.esgRecommendations!,
            enrichedEvaluation: evaluation!.inputs!.evaluationDepth!.enrichedEvaluation!,
            fullEvaluation: evaluation!.inputs!.evaluationDepth!.fullEvaluation!,
          },
          sources: {
            businessRegistry: evaluation!.inputs!.sources!.businessRegistry!,
            annualReport: evaluation!.inputs!.sources!.annualReport!,
            website: evaluation!.inputs!.sources!.website!,
            media: evaluation!.inputs!.sources!.media!,
            questionnaire: evaluation!.inputs!.sources!.questionnaire!,
            proprietaryData: evaluation!.inputs!.sources!.proprietaryData!,
          },
        },
      })) ?? [],
  }
}
