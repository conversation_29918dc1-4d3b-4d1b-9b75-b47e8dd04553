/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import { companyEventStore } from "./store"

export const companiesIndexConfig = createIndexConfig({
  name: "companies",
  displayName: "Companies",
  getTotalCount: () => companyEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  buildUrl: (aggregateId) => `/admin/companies/${aggregateId}`,
  fetchById: (aggregateId) => companyEventStore.getExistingAggregate(aggregateId).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await companyEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield companyEventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (company) => ({
    _id: company.aggregateId,
    aggregateId: company.aggregateId,
    labelcreatedAt: formatDateToIsoString(company.createdAt),
    updatedAt: formatDateToIsoString(company.updatedAt),
    deletedAt: company.deletedAt ? formatDateToIsoString(company.deletedAt) : null,
    label: company.name,

    name: company.name,
    about: company.about,
    registrationNumber: company.registrationNumber,
    status: company.status,
    country: company.country,
    employeeCount: company.employeeCount,
    address: company.address,
    registrationDate: formatDateToIsoString(company.registrationDate),
    website: company.website,

    businessActivities: company.businessActivities,
    annualReports: company.annualReports,
    publishedEvaluations: company.publishedEvaluations,
    latestEvaluation: company.publishedEvaluations.at(-1),
    latestMaterialityMapping: company.latestMaterialityMapping,
  }),
  properties: {
    name: { type: "text", fields: { keyword: { type: "keyword" } } },
    about: { type: "text" },
    registrationNumber: { type: "keyword" },
    status: { type: "keyword" },
    country: { type: "keyword" },
    employeeCount: { type: "integer" },
    address: {
      properties: {
        street: { type: "text" },
        houseNumber: { type: "keyword" },
        postalCode: { type: "keyword" },
        city: { type: "text", fields: { keyword: { type: "keyword" } } },
      },
    },
    registrationDate: { type: "date" },
    website: { type: "keyword" },
  },
})

export type CompanyDocument = ReturnType<typeof companiesIndexConfig.toDocument>
