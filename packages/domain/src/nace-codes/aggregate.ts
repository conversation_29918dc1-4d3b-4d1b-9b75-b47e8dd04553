/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

// Enums
const CollectionTypeEnum = z.enum(["classes", "divisions", "groups", "sections"])
const EbrdRiskLevelEnum = z.enum(["excluded", "high", "low", "medium"])

export const naceCodeAggregateSchema = defineAggregate(
  z.object({
    code: z.string(),
    label: z.string(),
    collectionType: CollectionTypeEnum.optional(),
    description: z.string().optional(),
    caseLawIfApplicable: z.string().optional(),
    excludes: z.string().optional(),
    overallEbrdRiskLevel: EbrdRiskLevelEnum.optional(),
    environmentalEbrdRiskLevel: EbrdRiskLevelEnum.optional(),
    socialEbrdRiskLevel: EbrdRiskLevelEnum.optional(),
    parentId: z.string().optional(),
  })
)

export type NaceCodeAggregate = z.infer<typeof naceCodeAggregateSchema>
