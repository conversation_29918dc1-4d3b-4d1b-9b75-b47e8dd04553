/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import type { NaceCodeAggregate } from "./aggregate"
import { naceCodeEventStore } from "./store"

export const naceCodesIndexConfig = createIndexConfig({
  name: "nacecodes",
  displayName: "NACE Codes",
  getTotalCount: () => naceCodeEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  fetchById: (id: string) => naceCodeEventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await naceCodeEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield naceCodeEventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (naceCode: NaceCodeAggregate) => ({
    _id: naceCode.aggregateId,
    aggregateId: naceCode.aggregateId,
    createdAt: formatDateToIsoString(naceCode.createdAt),
    updatedAt: formatDateToIsoString(naceCode.updatedAt),
    deletedAt: naceCode.deletedAt ? formatDateToIsoString(naceCode.deletedAt) : null,
    label: naceCode.label,

    code: naceCode.code,
    collectionType: naceCode.collectionType,
    description: naceCode.description,
    caseLawIfApplicable: naceCode.caseLawIfApplicable,
    excludes: naceCode.excludes,
    overallEbrdRiskLevel: naceCode.overallEbrdRiskLevel,
    environmentalEbrdRiskLevel: naceCode.environmentalEbrdRiskLevel,
    socialEbrdRiskLevel: naceCode.socialEbrdRiskLevel,
    parentId: naceCode.parentId,
  }),
  properties: {
    code: { type: "keyword" },
    label: { type: "text", fields: { keyword: { type: "keyword" } } },
    collectionType: { type: "keyword" },
    description: { type: "text" },
    caseLawIfApplicable: { type: "text" },
    excludes: { type: "text" },
    overallEbrdRiskLevel: { type: "keyword" },
    environmentalEbrdRiskLevel: { type: "keyword" },
    socialEbrdRiskLevel: { type: "keyword" },
    parentId: { type: "keyword" },
  },
})

export type NaceCodeDocument = ReturnType<typeof naceCodesIndexConfig.toDocument>
