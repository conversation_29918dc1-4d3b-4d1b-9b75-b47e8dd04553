/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { NaceCodeItemFragment } from "@impactly/integration/backend"
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

import { graphqlZodSchema } from "../utils"

const naceCodeSchema = z.object({
  code: z.string(),
  label: z.string(),
  collectionType: z.enum(["classes", "divisions", "groups", "sections"]).optional(),
  description: z.string().optional(),
  caseLawIfApplicable: z.string().optional(),
  excludes: z.string().optional(),
  overallEbrdRiskLevel: z.enum(["excluded", "high", "low", "medium"]).optional(),
  environmentalEbrdRiskLevel: z.enum(["excluded", "high", "low", "medium"]).optional(),
  socialEbrdRiskLevel: z.enum(["excluded", "high", "low", "medium"]).optional(),
  parentId: z.string().optional(),
})

export const naceCodeCreatedV1 = defineEventType({
  aggregateType: "naceCode",
  eventType: "created",
  schemaVersion: 1,
  schema: naceCodeSchema,
})

export const naceCodeUpdatedV1 = defineEventType({
  aggregateType: "naceCode",
  eventType: "updated",
  schemaVersion: 1,
  schema: naceCodeSchema.partial(),
})

export const naceCodeDeletedV1 = defineEventType({
  aggregateType: "naceCode",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const naceCodeChangedOnPlatformV1 = defineCDCEventType({
  aggregateType: "naceCode",
  schemaVersion: 1,
  source: "platform",
  sourceType: "naceCode",
  schema: graphqlZodSchema<NaceCodeItemFragment>().nullable(),
})

export const naceCodeEvents = [naceCodeCreatedV1, naceCodeUpdatedV1, naceCodeDeletedV1, naceCodeChangedOnPlatformV1]
