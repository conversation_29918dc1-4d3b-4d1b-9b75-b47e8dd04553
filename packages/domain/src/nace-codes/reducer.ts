/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { NaceCodeItemFragment } from "@impactly/integration/backend"

import { defineReducer } from "@kreios/eventsourcing"

import type { AggregateWithoutBase } from "../utils"
import type { NaceCodeAggregate } from "./aggregate"
import type { naceCodeEvents } from "./events"

export const naceCodeReducer = defineReducer<NaceCodeAggregate, typeof naceCodeEvents>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "naceCode:created:v1": {
      return {
        ...event.payload,
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
      }
    }
    case "naceCode:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "naceCode:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
    case "naceCode:changeCaptured/platform/naceCode:v1": {
      if (event.payload.sourceData)
        return {
          ...state!,
          ...mapToAggregate(event.payload.sourceData),
          aggregateId,
          version,
          createdAt: state?.createdAt ?? new Date(timestamp),
          updatedAt: new Date(timestamp),
        }
      else
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
    }
  }
})

function mapToAggregate(naceCode: NaceCodeItemFragment): AggregateWithoutBase<NaceCodeAggregate> {
  return {
    aggregateId: naceCode.id,
    code: naceCode.code!,
    label: naceCode.label!,
    overallEbrdRiskLevel: naceCode.overallEbrdRiskLevel!,
    environmentalEbrdRiskLevel: naceCode.environmentalEbrdRiskLevel!,
    socialEbrdRiskLevel: naceCode.socialEbrdRiskLevel!,
    parentId: naceCode.parent?.id,
  }
}
