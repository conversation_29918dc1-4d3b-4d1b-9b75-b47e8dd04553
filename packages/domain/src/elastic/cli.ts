/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { fileURLToPath } from "url"

import { parseCLI } from "@kreios/elasticsearch/cli"

await import("../bootstrap")

const { config } = await import(".")

// If this module is being run directly, parse the CLI arguments
const __filename = fileURLToPath(import.meta.url)
if (process.argv[1] === __filename) {
  parseCLI(config, process.argv)
}
