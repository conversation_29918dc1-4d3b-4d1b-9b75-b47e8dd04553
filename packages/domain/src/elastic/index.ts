/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { createGatewayConfig } from "@kreios/elasticsearch"

import { env } from "../../env"
import { companiesIndexConfig } from "../companies/elastic"
import { evaluationRequestsIndexConfig } from "../evaluation-requests/elastic" // Add this line
import { naceCodesIndexConfig } from "../nace-codes/elastic"
import { portalUsersIndexConfig } from "../portal-users/elastic"
import { surveyBatchesIndexConfig } from "../survey-batches/elastic"
import { surveysIndexConfig } from "../surveys/elastic"
import { watchlistsIndexConfig } from "../watchlists/elastic"

// The main configuration object for the Elasticsearch gateway
export const config = createGatewayConfig({
  url: env.ELASTICSEARCH_URL,
  apiKey: env.ELASTICSEARCH_API_KEY,
  indexPrefix: env.PLATFORM_RESOURCE_NAMESPACE, // Use the namespace as the index prefix
  indices: [
    companiesIndexConfig,
    portalUsersIndexConfig,
    watchlistsIndexConfig,
    naceCodesIndexConfig,
    evaluationRequestsIndexConfig,
    surveyBatchesIndexConfig,
    surveysIndexConfig,
  ],
})
