/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { Command } from "@commander-js/extra-typings"
import { faker } from "@faker-js/faker/locale/en"
import { ulid } from "ulidx"

import { createMultiBar } from "@kreios/utils/progress-bar"

const program = new Command()
  .argument("[amount]", "amount", (value) => Number(value), 100)
  .action(async (amount) => {
    await import("./bootstrap")
    const { createCompanyCommand, companyEventStore } = await import("./companies")
    const { createEvaluationRequestCommand, evaluationRequestEventStore } = await import("./evaluation-requests")
    const { createPortalUserCommand, portalUserEventStore } = await import("./portal-users")
    const { createWatchlistCommand, watchlistEventStore } = await import("./watchlists")
    const { createSurveyBatchCommand, surveyBatchEventStore } = await import("./survey-batches")
    const { createSurveyCommand, surveyEventStore } = await import("./surveys")

    console.log("Creating fake data")

    const multibar = createMultiBar()

    const companyBar = multibar.create("Companies", amount)
    const userBar = multibar.create("Portal Users", amount / 2)
    const watchlistBar = multibar.create("Watchlists", amount / 5)
    const surveyBatchBar = multibar.create("Survey Batches", amount / 4)
    const evaluationBar = multibar.create("Evaluation Requests", amount / 3)
    const surveyBar = multibar.create("Surveys", 0)

    // Seed companies
    const companyPromises = Array.from({ length: amount }, () => ({
      name: faker.company.name(),
      about: faker.company.catchPhrase(),
      registrationNumber: faker.string.alphanumeric(10),
      status: faker.helpers.arrayElement(["bankrupt", "delete", "entered_in_the_register", "in_liquidation"]),
      country: faker.location.countryCode(),
      employeeCount: faker.number.int({ min: 1, max: 10000 }),
      address: {
        street: faker.location.street(),
        houseNumber: faker.location.buildingNumber(),
        postalCode: faker.location.zipCode(),
        city: faker.location.city(),
      },
      registrationDate: faker.date.past(),
      website: faker.internet.url(),
      businessActivities: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => ({
        id: ulid(),
        euTaxonomyEligible: faker.datatype.boolean(),
        naceCode: {
          label: faker.lorem.sentence(),
          labelEt: faker.lorem.sentence(),
          code: faker.lorem.sentence(),
        },
        isMainActivity: faker.datatype.boolean(),
        revenue: {
          amount: faker.number.int({ min: 10000, max: ******** }),
          currency: "EUR",
        },
      })),
      annualReports: Array.from({ length: 3 }, (_, index) => ({
        financialYear: 2023 - index,
        averageFullTimeEmployees: faker.number.int({ min: 1, max: 1000 }),
        revenue: {
          amount: faker.number.int({ min: 100000, max: ********0 }),
          currency: "EUR",
        },
        netProfit: {
          amount: faker.number.int({ min: -1000000, max: ******** }),
          currency: "EUR",
        },
        totalAssets: {
          amount: faker.number.int({ min: 100000, max: ********00 }),
          currency: "EUR",
        },
      })),
      latestMaterialityMapping: {
        executionDate: faker.date.recent(),
        relevantPositives: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => ({
          summary: faker.lorem.sentence(),
          maturityLevel: faker.helpers.arrayElement(["high", "medium", "low"]),
        })),
        relevantIssues: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => ({
          name: faker.lorem.words(3),
          category: faker.helpers.arrayElement(["environmental", "governance", "social", "na"]),
          description: faker.lorem.paragraph(),
          maturityLevel: faker.helpers.arrayElement(["high", "low", "medium", "very_high", "very_low"]),
          summary: faker.lorem.sentence(),
          recommendations: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => faker.lorem.sentence()),
          sources: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
            sourceType: faker.helpers.arrayElement(["report", "website", "news"]),
            source: faker.internet.url(),
          })),
        })),
      },
      publishedEvaluations: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => ({
        id: ulid(),
        status: faker.helpers.arrayElement(["DONE", "ERROR", "IN_PROGRESS", "ISSUES_EVALUATED", "STARTED"]),
        completedAt: faker.date.recent(),
        evaluationYear: faker.date.past().getFullYear(),
        overallRecommendations: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
          faker.lorem.sentence()
        ),
        environmentalMaturity: faker.helpers.arrayElement(["high", "low", "medium", "very_high", "very_low"]),
        socialMaturity: faker.helpers.arrayElement(["high", "low", "medium", "very_high", "very_low"]),
        governanceMaturity: faker.helpers.arrayElement(["high", "low", "medium", "very_high", "very_low"]),
        overallSummary: faker.lorem.paragraph(),
        inputs: {
          dataDepth: {
            baseData: faker.datatype.boolean(),
            publicData: faker.datatype.boolean(),
            selfAssessment: faker.datatype.boolean(),
            advanced: faker.datatype.boolean(),
          },
          evaluationDepth: {
            esgRiskMapping: faker.datatype.boolean(),
            aiEvaluations: faker.datatype.boolean(),
            esgRecommendations: faker.datatype.boolean(),
            enrichedEvaluation: faker.datatype.boolean(),
            fullEvaluation: faker.datatype.boolean(),
          },
          sources: {
            businessRegistry: faker.datatype.boolean(),
            annualReport: faker.datatype.boolean(),
            website: faker.datatype.boolean(),
            media: faker.datatype.boolean(),
            questionnaire: faker.datatype.boolean(),
            proprietaryData: faker.datatype.boolean(),
          },
        },
      })),
    })).map(async (company) => {
      const { aggregateId } = await createCompanyCommand.handler(company, [companyEventStore], {})

      companyBar.increment()

      return aggregateId
    })

    const companies = await Promise.all(
      (await Promise.all(companyPromises)).map(
        async (aggregateId) => (await companyEventStore.getExistingAggregate(aggregateId)).aggregate
      )
    )

    // Seed portal users
    const portalUserPromises = Array.from({ length: amount / 2 }, () => ({
      email: faker.internet.email(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      isActive: faker.datatype.boolean(),
      dateJoined: faker.date.past(),
      hasGivenConsent: faker.datatype.boolean(),
      roles: faker.helpers.arrayElements(["ADMIN", "CLIENT_ADMIN", "COMPANY_USER"], {
        min: 1,
        max: 3,
      }),
      tenantId: faker.datatype.boolean() ? ulid() : null,
    })).map(async (user) => {
      const { aggregateId } = await createPortalUserCommand.handler(user, [portalUserEventStore], {})

      userBar.increment()

      return aggregateId
    })

    const portalUsers = await Promise.all(
      (await Promise.all(portalUserPromises)).map(
        async (aggregateId) => (await portalUserEventStore.getExistingAggregate(aggregateId)).aggregate
      )
    )

    // Seed watchlists
    const watchlistPromises = Array.from({ length: amount / 5 }, () => ({
      name: faker.lorem.words(3),
      tenantId: ulid(),
      portalUserId: portalUsers[faker.number.int({ min: 0, max: portalUsers.length - 1 })].aggregateId,
      entries: Array.from({ length: faker.number.int({ min: 5, max: 20 }) }, () => ({
        id: ulid(),
        companyId: companies[faker.number.int({ min: 0, max: companies.length - 1 })].aggregateId,
        contacts: Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () => ({
          id: ulid(),
          email: faker.internet.email(),
          name: faker.person.fullName(),
        })),
      })),
    })).map(async (watchlist) => {
      const { aggregateId } = await createWatchlistCommand.handler(watchlist, [watchlistEventStore], {})

      watchlistBar.increment()

      return aggregateId
    })

    // Seed survey batches
    const surveyBatchPromises = Array.from({ length: amount / 4 }, () => ({
      name: faker.lorem.words(3),
      description: faker.lorem.paragraph(),
      tenantId: faker.datatype.boolean() ? ulid() : null,
      template: "UNIFIED_QUESTIONNAIRE_V1",
      tenant: faker.datatype.boolean() ? { id: ulid() } : undefined,
    })).map(async (batch) => {
      const { aggregateId } = await createSurveyBatchCommand.handler(
        { ...batch, template: "UNIFIED_QUESTIONNAIRE_V1" },
        [surveyBatchEventStore],
        {}
      )

      surveyBatchBar.increment()

      return { aggregateId }
    })

    const surveyBatches = await Promise.all(surveyBatchPromises)

    // Seed surveys with exact status distribution
    const createSurveysForBatch = async (batch: { aggregateId: string }) => {
      const surveys = companies.slice(0, 10).map((company) => {
        const statusRoll = Math.random()
        let status: "OPEN" | "NOT_STARTED" | "IN_PROGRESS" | "COMPLETE"
        let progress: number

        // Match exact percentages: 10% opened, 18% not started, 27% in progress, 45% completed
        if (statusRoll < 0.1) {
          status = "OPEN"
          progress = 0
        } else if (statusRoll < 0.28) {
          status = "NOT_STARTED"
          progress = 0
        } else if (statusRoll < 0.55) {
          status = "IN_PROGRESS"
          progress = faker.number.int({ min: 1, max: 99 })
        } else {
          status = "COMPLETE"
          progress = 100
        }

        return {
          batchId: batch.aggregateId,
          companyId: company.aggregateId,
          template: "UNIFIED_QUESTIONNAIRE_V1",
          status,
          progress,
          readyForEdit: status === "COMPLETE",
          statusChangedAt: faker.date.recent({ days: 30 }),
          lastReminder: status !== "COMPLETE" ? faker.date.recent({ days: 14 }) : undefined,
          financialYear: new Date().getFullYear(),
          deadline: faker.date.future(),
          submittedAt: status === "COMPLETE" ? faker.date.recent() : undefined,
          documents: Array.from({ length: faker.number.int({ min: 0, max: 3 }) }, () => ({
            id: ulid(),
            file: faker.system.filePath(),
            fileName: faker.system.fileName(),
            fileSize: faker.number.int({ min: 1000, max: ******** }),
            isAttachment: faker.datatype.boolean(),
          })),
          formData: {
            responses: Array.from({ length: faker.number.int({ min: 5, max: 15 }) }, () => ({
              questionId: ulid(),
              answer: faker.helpers.arrayElement(["YES", "NO", "NOT_APPLICABLE"]),
              comment: faker.datatype.boolean() ? faker.lorem.sentence() : undefined,
            })),
          },
        } satisfies Parameters<typeof createSurveyCommand.handler>[0]
      })

      surveyBar.update({ message: "Creating surveys...", total: surveyBar.getTotal() + surveys.length })

      // Create all surveys for this batch
      const surveyPromises = surveys.map(async (survey) => {
        const { aggregateId } = await createSurveyCommand.handler(survey, [surveyEventStore], {})

        surveyBar.increment()

        return aggregateId
      })

      return Promise.all(surveyPromises)
    }

    // Create surveys for all batches
    const surveyPromises = Promise.all(surveyBatches).then(async (batch) =>
      Promise.all(batch.map((batch) => createSurveysForBatch(batch)))
    )

    // Seed evaluation requests
    const evaluationRequestPromises = Array.from({ length: amount / 3 }, () => {
      const company = companies[faker.number.int({ min: 0, max: companies.length - 1 })]
      const portalUser = portalUsers[faker.number.int({ min: 0, max: portalUsers.length - 1 })]
      return {
        companyId: company.aggregateId,
        companyName: company.name,
        portalUserId: portalUser.aggregateId,
        portalUserEmail: portalUser.email,
        portalUserName: `${portalUser.firstName} ${portalUser.lastName}`,
        infoSustainability: faker.datatype.boolean(),
        infoSupplyChain: faker.datatype.boolean(),
        infoAI: faker.datatype.boolean(),
      }
    }).map(async (request) => {
      const { aggregateId } = await createEvaluationRequestCommand.handler(request, [evaluationRequestEventStore], {})
      evaluationBar.increment()
      return aggregateId
    })

    await Promise.all([
      Promise.all(evaluationRequestPromises).then(() => evaluationBar.stop()),
      Promise.all(watchlistPromises).then(() => watchlistBar.stop()),
      Promise.all(portalUserPromises).then(() => userBar.stop()),
      Promise.all(companyPromises).then(() => companyBar.stop()),
      Promise.all(surveyBatchPromises),
      surveyPromises.then(() => {
        surveyBar.stop()
        surveyBatchBar.stop()
      }),
    ])

    // Stop the progress bars
    multibar.stop()

    console.log("Done seeding")

    await container.dispose()
  })

// Parse the command line arguments
program.parse(process.argv)
