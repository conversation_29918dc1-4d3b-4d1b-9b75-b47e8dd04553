/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { z } from "zod"

import type { AggregateBase } from "@kreios/eventsourcing"

export const graphqlZodSchema = <T>() => z.custom<T>((data): data is T => true)

export type AggregateWithoutBase<TAggregate extends AggregateBase> = Omit<
  TAggregate,
  keyof Omit<AggregateBase, "aggregateId">
>
