/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { PortalUserItemFragment } from "@impactly/integration/backend"
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

import { graphqlZodSchema } from "../utils"

const PortalUserRoleEnum = z.enum([
  "ADMIN",
  "DATA_VIEWER",
  "SURVEY_MANAGER",
  "WATCHLIST_MANAGER",
  "CLIENT_ADMIN",
  "COMPANY_USER",
  "OBSERVER",
])

const portalUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  companyId: z.string().optional(),
  hasGivenConsent: z.boolean().default(false),
  isActive: z.boolean(),
  dateJoined: z.date(),
  roles: z.array(PortalUserRoleEnum),
  tenantId: z.string().nullable(),
})

export const portalUserCreatedV1 = defineEventType({
  aggregateType: "portalUser",
  eventType: "created",
  schemaVersion: 1,
  schema: portalUserSchema,
})

export const portalUserUpdatedV1 = defineEventType({
  aggregateType: "portalUser",
  eventType: "updated",
  schemaVersion: 1,
  schema: portalUserSchema.partial(),
})

export const portalUserDeletedV1 = defineEventType({
  aggregateType: "portalUser",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const portalUserChangedOnPlatformV1 = defineCDCEventType({
  aggregateType: "portalUser",
  schemaVersion: 1,
  source: "platform",
  sourceType: "portalUser",
  schema: graphqlZodSchema<PortalUserItemFragment>().nullable(),
})

export const portalUserEvents = [
  portalUserCreatedV1,
  portalUserUpdatedV1,
  portalUserDeletedV1,
  portalUserChangedOnPlatformV1,
]
