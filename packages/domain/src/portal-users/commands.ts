/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { ulid } from "ulidx"
import { z } from "zod"

import { defineCommand } from "@kreios/eventsourcing"
import { defineCDCCommand } from "@kreios/eventsourcing/cdc"

import { portalUserChangedOnPlatformV1, portalUserCreatedV1 } from "./events"
import { portalUserEventStore } from "./store"

export const createPortalUserCommand = defineCommand({
  commandId: "createPortalUser",
  eventStores: [portalUserEventStore],
  inputSchema: portalUserCreatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid().optional(),
  }),
  outputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const aggregateId = commandInput.aggregateId ?? ulid()

    await eventStore.pushEvent({
      type: portalUserCreatedV1.type,
      aggregateId,
      version: 1,
      timestamp: new Date().toISOString(),
      payload: commandInput,
    })

    return { aggregateId }
  },
})

export const capturePortalUserChangesCommand = defineCDCCommand({
  commandId: "capturePortalUserChanges",
  eventStore: portalUserEventStore,
  eventType: portalUserChangedOnPlatformV1,
})
