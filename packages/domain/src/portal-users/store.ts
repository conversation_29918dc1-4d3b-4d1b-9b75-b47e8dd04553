/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { EventStore } from "@castore/core"

import { DrizzleEventStorageAdapter } from "@kreios/eventsourcing/drizzle"

import { portalUserEvents } from "./events"
import { portalUserReducer } from "./reducer"

const db = container.resolve("db")

export const portalUserEventStore = new EventStore({
  eventStoreId: "portalUsers",
  eventStorageAdapter: new DrizzleEventStorageAdapter(db, "portalUsers"),
  eventTypes: portalUserEvents,
  reducer: portalUserReducer,
})

export type PortalUserEventStore = typeof portalUserEventStore
