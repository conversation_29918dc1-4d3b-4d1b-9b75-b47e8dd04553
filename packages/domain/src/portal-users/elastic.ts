/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import type { PortalUserAggregate } from "./aggregate"
import { portalUserEventStore } from "./store"

export const portalUsersIndexConfig = createIndexConfig({
  name: "portalusers",
  displayName: "Portal Users",
  getTotalCount: () => portalUserEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  fetchById: (id: string) => portalUserEventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await portalUserEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield portalUserEventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (portalUser: PortalUserAggregate) => ({
    _id: portalUser.aggregateId,
    aggregateId: portalUser.aggregateId,
    createdAt: formatDateToIsoString(portalUser.createdAt),
    updatedAt: formatDateToIsoString(portalUser.updatedAt),
    deletedAt: portalUser.deletedAt ? formatDateToIsoString(portalUser.deletedAt) : null,
    label: portalUser.email,

    email: portalUser.email,
    firstName: portalUser.firstName,
    lastName: portalUser.lastName,
    isActive: portalUser.isActive,
    dateJoined: formatDateToIsoString(portalUser.dateJoined),
    roles: portalUser.roles,
    tenantId: portalUser.tenantId,
    hasGivenConsent: portalUser.hasGivenConsent,
    companyId: portalUser.companyId,
  }),
  properties: {
    email: { type: "keyword" },
    firstName: { type: "text", fields: { keyword: { type: "keyword" } } },
    lastName: { type: "text", fields: { keyword: { type: "keyword" } } },
    isActive: { type: "boolean" },
    dateJoined: { type: "date" },
    roles: { type: "keyword" },
    tenantId: { type: "keyword" },
    hasGivenConsent: { type: "boolean" },
    companyId: { type: "keyword" },
  },
})

export type PortalUserDocument = ReturnType<typeof portalUsersIndexConfig.toDocument>
