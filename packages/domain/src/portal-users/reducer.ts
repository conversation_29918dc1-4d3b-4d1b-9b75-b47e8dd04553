/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { PortalUserItemFragment } from "@impactly/integration/backend"

import { defineReducer } from "@kreios/eventsourcing"
import { toUTCDate } from "@kreios/utils/to-utc-date"

import type { AggregateWithoutBase } from "../utils"
import type { PortalUserAggregate } from "./aggregate"
import type { portalUserEvents } from "./events"

export const portalUserReducer = defineReducer<PortalUserAggregate, typeof portalUserEvents>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "portalUser:created:v1": {
      return {
        ...event.payload,
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
      }
    }
    case "portalUser:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "portalUser:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
    case "portalUser:changeCaptured/platform/portalUser:v1": {
      if (event.payload.sourceData)
        return {
          ...state!,
          ...mapToAggregate(event.payload.sourceData),
          aggregateId,
          version,
          createdAt: state?.createdAt ?? new Date(timestamp),
          updatedAt: new Date(timestamp),
        }
      else
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
    }
  }
})

/**
 * Maps a PortalUserItemFragment to a Partial<PortalUserAggregate>.
 *
 * @param user - The PortalUserItemFragment to map.
 * @returns A Partial<PortalUserAggregate> representing the portal user data.
 */
function mapToAggregate(user: PortalUserItemFragment): AggregateWithoutBase<PortalUserAggregate> {
  return {
    aggregateId: user.id,
    email: user.email,
    firstName: user.firstName ?? undefined,
    lastName: user.lastName ?? undefined,
    companyId: user.company?.id ?? undefined,
    hasGivenConsent: user.hasGivenConsent,
    isActive: user.isActive,
    dateJoined: toUTCDate(user.dateJoined)!,
    roles: user.roles.map((role) => role.role!),
    tenantId: user.tenant?.id ?? null,
  }
}
