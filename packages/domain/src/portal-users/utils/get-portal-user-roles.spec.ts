/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { describe, expect, it } from "vitest"

import type { PortalUserDocument } from "../elastic"
import { getPortalUserRoles } from "./get-portal-user-roles"

describe("getPortalUserRoles", () => {
  it("should return true for assigned roles", () => {
    const user: Pick<PortalUserDocument, "roles"> = {
      roles: ["ADMIN", "COMPANY_USER", "DATA_VIEWER"],
    }

    const roles = getPortalUserRoles(user)

    expect(roles.isAdmin).toBe(true)
    expect(roles.isCompanyUser).toBe(true)
    expect(roles.isDataViewer).toBe(true)
  })

  it("should return false for unassigned roles", () => {
    const user: Pick<PortalUserDocument, "roles"> = {
      roles: ["ADMIN"],
    }

    const roles = getPortalUserRoles(user)

    expect(roles.isCompanyUser).toBe(false)
    expect(roles.isSurveyManager).toBe(false)
    expect(roles.isWatchlistManager).toBe(false)
    expect(roles.isClientAdmin).toBe(false)
    expect(roles.isDataViewer).toBe(false)
    expect(roles.isObserver).toBe(false)
  })

  it("should return false for all roles when user has no roles", () => {
    const user: Pick<PortalUserDocument, "roles"> = {
      roles: [],
    }

    const roles = getPortalUserRoles(user)

    expect(roles.isAdmin).toBe(false)
    expect(roles.isCompanyUser).toBe(false)
    expect(roles.isSurveyManager).toBe(false)
    expect(roles.isWatchlistManager).toBe(false)
    expect(roles.isClientAdmin).toBe(false)
    expect(roles.isDataViewer).toBe(false)
    expect(roles.isObserver).toBe(false)
  })
})
