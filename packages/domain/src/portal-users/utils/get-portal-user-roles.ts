import { camelCase } from "lodash-es"

import type { PortalUserDocument } from "../elastic"

/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
type CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}`
  ? `${Lowercase<P1>}${Uppercase<P2>}${CamelCase<P3>}`
  : Lowercase<S>

/**
 * Utility function to get a boolean for each role
 * @param portalUser - The portal user
 * @returns An object with a boolean for each role
 */
export const getPortalUserRoles = (portalUser: Pick<PortalUserDocument, "roles">) => {
  type Role = PortalUserDocument["roles"][number]
  type CamelCaseRole = CamelCase<`is_${Role}`>

  // Using a proxy to
  return new Proxy(
    portalUser.roles.reduce(
      (acc, role) => {
        acc[camelCase(`is_${role}`) as CamelCaseRole] = true
        return acc
      },
      {} as Record<CamelCaseRole, boolean>
    ),
    {
      get: (target, prop) => {
        return prop in target ? target[prop as keyof typeof target] : false
      },
    }
  )
}
