/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { cache } from "react"
import { roles as rolesFlag } from "@impactly/flags"

export const getPortalUserByEmail = cache(async (email: string) => {
  const gateway = container.resolve("gateway")
  const roles = await rolesFlag()
  const portalUser = await gateway.findFirst("portalusers", {
    term: { email },
  })

  if (!portalUser) return undefined

  if (roles) return { ...portalUser, roles: [roles] }

  return portalUser
})
