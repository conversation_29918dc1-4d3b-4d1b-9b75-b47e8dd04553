/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

const PortalUserRoleEnum = z.enum([
  "ADMIN",
  "DATA_VIEWER",
  "SURVEY_MANAGER",
  "WATCHLIST_MANAGER",
  "CLIENT_ADMIN",
  "COMPANY_USER",
  "OBSERVER",
])

export const portalUserAggregateSchema = defineAggregate(
  z.object({
    email: z.string().email(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    companyId: z.string().optional(),
    hasGivenConsent: z.boolean().default(false),
    isActive: z.boolean(),
    dateJoined: z.date(),
    roles: z.array(PortalUserRoleEnum),
    tenantId: z.string().nullable(),
  })
)

export type PortalUserAggregate = z.infer<typeof portalUserAggregateSchema>
