/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { asFunction, createContainer } from "awilix"

import { ElasticsearchGateway } from "@kreios/elasticsearch"
import { connect } from "@kreios/eventsourcing/drizzle"

import type { DomainContainer } from "../config"
import { env } from "../env"

// To be used to register the dependencies when using any kind of cli tool
// for example seed or reindex

const container = createContainer<DomainContainer>()

globalThis.container = container

// Always use postgres for cli tools because we don't need to handle connection pooling
const { db, client } = await connect({
  type: "postgres",
  url: env.DATABASE_URL,
  options: {
    connect_timeout: 200,
  },
})

container.register({
  db: asFunction(() => db)
    .singleton()
    .disposer(() => client.close()),
})

const { config } = await import("@impactly/domain/elastic/index")

container.register({
  gateway: asFunction(() => new ElasticsearchGateway(config))
    .singleton()
    .disposer((gateway) => gateway.close()),
})
