/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyBatchItemFragment } from "@impactly/integration/backend"
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

import { graphqlZodSchema } from "../utils"

const surveyBatchSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  template: z.enum(["UNIFIED_QUESTIONNAIRE_V1"]).optional(),
  tenant: z
    .object({
      id: z.string(),
    })
    .optional(),
})

export const surveyBatchCreatedV1 = defineEventType({
  aggregateType: "surveyBatch",
  eventType: "created",
  schemaVersion: 1,
  schema: surveyBatchSchema,
})

export const surveyBatchUpdatedV1 = defineEventType({
  aggregateType: "surveyBatch",
  eventType: "updated",
  schemaVersion: 1,
  schema: surveyBatchSchema.partial(),
})

export const surveyBatchDeletedV1 = defineEventType({
  aggregateType: "surveyBatch",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const surveyBatchChangedOnPlatformV1 = defineCDCEventType({
  aggregateType: "surveyBatch",
  schemaVersion: 1,
  source: "platform",
  sourceType: "surveyBatch",
  schema: graphqlZodSchema<SurveyBatchItemFragment>().nullable(),
})

export const surveyBatchEvents = [
  surveyBatchCreatedV1,
  surveyBatchUpdatedV1,
  surveyBatchDeletedV1,
  surveyBatchChangedOnPlatformV1,
]
