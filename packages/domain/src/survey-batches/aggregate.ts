/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

export const surveyBatchAggregateSchema = defineAggregate(
  z.object({
    name: z.string(),
    description: z.string().optional(),
    template: z.enum(["UNIFIED_QUESTIONNAIRE_V1", "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"]).optional(),
    tenant: z
      .object({
        id: z.string(),
      })
      .optional(),
  })
)

export type SurveyBatchAggregate = z.infer<typeof surveyBatchAggregateSchema>
