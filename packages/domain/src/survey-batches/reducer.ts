/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyBatchItemFragment } from "@impactly/integration/backend"

import { defineReducer } from "@kreios/eventsourcing"
import { decodeRelayID } from "@kreios/utils/decode-relay-id"
import { isBase64 } from "@kreios/utils/is-base64"

import type { AggregateWithoutBase } from "../utils"
import type { SurveyBatchAggregate } from "./aggregate"
import type { surveyBatchEvents } from "./events"

export const surveyBatchReducer = defineReducer<SurveyBatchAggregate, typeof surveyBatchEvents>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "surveyBatch:created:v1": {
      return {
        ...event.payload,
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
      }
    }
    case "surveyBatch:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "surveyBatch:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
    case "surveyBatch:changeCaptured/platform/surveyBatch:v1": {
      if (event.payload.sourceData)
        return {
          ...state!,
          ...mapToAggregate(event.payload.sourceData),
          aggregateId,
          createdAt: state?.createdAt ?? new Date(timestamp),
          version,
          updatedAt: new Date(timestamp),
        }
      else
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
    }
  }
})

function mapToAggregate(batch: SurveyBatchItemFragment): AggregateWithoutBase<SurveyBatchAggregate> {
  const id = batch.id
  const aggregateId = isBase64(id) ? decodeRelayID(id) : id
  return {
    aggregateId,
    name: batch.name ?? "",
    description: batch.description,
    template: batch.template!,
    tenant: batch.tenant!,
  }
}
