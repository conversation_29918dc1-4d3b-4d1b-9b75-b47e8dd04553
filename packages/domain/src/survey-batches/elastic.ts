/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import { surveyBatchEventStore } from "./store"

export const surveyBatchesIndexConfig = createIndexConfig({
  name: "surveybatches",
  displayName: "Survey Batches",
  getTotalCount: () => surveyBatchEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  buildUrl: (aggregateId) => `/admin/survey-batches/${aggregateId}`,
  fetchById: (aggregateId) =>
    surveyBatchEventStore.getExistingAggregate(aggregateId).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await surveyBatchEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield surveyBatchEventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (batch) => ({
    _id: batch.aggregateId,
    aggregateId: batch.aggregateId,
    createdAt: formatDateToIsoString(batch.createdAt),
    updatedAt: formatDateToIsoString(batch.updatedAt),
    deletedAt: batch.deletedAt ? formatDateToIsoString(batch.deletedAt) : null,
    label: batch.name,
    name: batch.name,
    description: batch.description,
    template: batch.template,
    tenant: batch.tenant,
  }),
  properties: {
    aggregateId: { type: "keyword" },
    label: { type: "text", fields: { keyword: { type: "keyword" } } },
    name: { type: "text", fields: { keyword: { type: "keyword" } } },
    description: { type: "text" },
    template: { type: "keyword" },
    tenant: {
      properties: {
        id: { type: "keyword" },
      },
    },
  },
})

export type SurveyBatchDocument = ReturnType<typeof surveyBatchesIndexConfig.toDocument>
