/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { WatchlistItemFragment } from "@impactly/integration/backend"
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

import { graphqlZodSchema } from "../utils"

const watchlistSchema = z.object({
  tenantId: z.string().optional(), // TODO: Find out why this is optional in the backend
  portalUserId: z.string().optional(), // TODO: Find out why this is optional in the backend
  name: z.string(),
  entries: z.array(
    z.object({
      id: z.string(),
      companyId: z.string(),
      contacts: z.array(
        z.object({
          id: z.string(),
          email: z.string(),
          name: z.string().nullable(),
        })
      ),
    })
  ),
})

export const watchlistCreatedV1 = defineEventType({
  aggregateType: "watchlist",
  eventType: "created",
  schemaVersion: 1,
  schema: watchlistSchema,
})

export const watchlistUpdatedV1 = defineEventType({
  aggregateType: "watchlist",
  eventType: "updated",
  schemaVersion: 1,
  schema: watchlistSchema.partial(),
})

export const watchlistDeletedV1 = defineEventType({
  aggregateType: "watchlist",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const watchlistChangedOnPlatformV1 = defineCDCEventType({
  aggregateType: "watchlist",
  schemaVersion: 1,
  source: "platform",
  sourceType: "watchlist",
  schema: graphqlZodSchema<WatchlistItemFragment>().nullable(),
})

export const watchlistEvents = [
  watchlistCreatedV1,
  watchlistUpdatedV1,
  watchlistDeletedV1,
  watchlistChangedOnPlatformV1,
]
