/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import type { WatchlistAggregate } from "./aggregate"
import { watchlistEventStore } from "./store"

export const watchlistsIndexConfig = createIndexConfig({
  name: "watchlists",
  displayName: "Watchlists",
  getTotalCount: () => watchlistEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  fetchById: (id: string) => watchlistEventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await watchlistEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield watchlistEventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (watchlist: WatchlistAggregate) => ({
    _id: watchlist.aggregateId,
    aggregateId: watchlist.aggregateId,
    createdAt: formatDateToIsoString(watchlist.createdAt),
    updatedAt: formatDateToIsoString(watchlist.updatedAt),
    deletedAt: watchlist.deletedAt ? formatDateToIsoString(watchlist.deletedAt) : null,
    label: watchlist.name,

    tenantId: watchlist.tenantId,
    portalUserId: watchlist.portalUserId,
    name: watchlist.name,
    entries: watchlist.entries,
  }),
  analysis: {
    tokenizer: {
      ngram_tokenizer: {
        type: "ngram",
        min_gram: 2,
        max_gram: 3,
        token_chars: ["letter", "digit"],
      },
    },
    analyzer: {
      ngram_analyzer: {
        tokenizer: "ngram_tokenizer",
        type: "custom",
      },
    },
  },
  properties: {
    label: {
      type: "text",
      term_vector: "yes",
      analyzer: "ngram_analyzer",
    },
    name: { type: "text", fields: { keyword: { type: "keyword" } } },
    entries: {
      type: "nested",
      properties: {
        id: { type: "keyword" },
        company: {
          properties: {
            id: { type: "keyword" },
            name: { type: "text", fields: { keyword: { type: "keyword" } } },
            registrationNumber: { type: "keyword" },
            country: { type: "keyword" },
          },
        },
        contacts: {
          type: "nested",
          properties: {
            id: { type: "keyword" },
            email: { type: "keyword" },
            name: { type: "text", fields: { keyword: { type: "keyword" } } },
          },
        },
      },
    },
  },
})

export type WatchlistDocument = ReturnType<typeof watchlistsIndexConfig.toDocument>
