/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { ulid } from "ulidx"
import { z } from "zod"

import { defineCommand } from "@kreios/eventsourcing"
import { defineCDCCommand } from "@kreios/eventsourcing/cdc"

import { watchlistChangedOnPlatformV1, watchlistCreatedV1 } from "./events"
import { watchlistEventStore } from "./store"

export const createWatchlistCommand = defineCommand({
  commandId: "createWatchlist",
  eventStores: [watchlistEventStore],
  inputSchema: watchlistCreatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid().optional(),
  }),
  outputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const aggregateId = commandInput.aggregateId ?? ulid()

    await eventStore.pushEvent({
      type: watchlistCreatedV1.type,
      aggregateId,
      version: 1,
      timestamp: new Date().toISOString(),
      payload: commandInput,
    })

    return { aggregateId }
  },
})

export const captureWatchlistChangesCommand = defineCDCCommand({
  commandId: "captureWatchlistChanges",
  eventStore: watchlistEventStore,
  eventType: watchlistChangedOnPlatformV1,
})
