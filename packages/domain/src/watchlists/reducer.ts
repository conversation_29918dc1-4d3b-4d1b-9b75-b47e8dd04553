/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { WatchlistItemFragment } from "@impactly/integration/backend"

import { defineReducer } from "@kreios/eventsourcing"

import type { AggregateWithoutBase } from "../utils"
import type { WatchlistAggregate } from "./aggregate"
import type { watchlistEvents } from "./events"

export const watchlistReducer = defineReducer<WatchlistAggregate, typeof watchlistEvents>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "watchlist:created:v1": {
      return {
        ...event.payload,
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
      }
    }
    case "watchlist:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "watchlist:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
    case "watchlist:changeCaptured/platform/watchlist:v1": {
      if (event.payload.sourceData)
        return {
          ...state!,
          ...mapToAggregate(event.payload.sourceData),
          aggregateId,
          version,
          createdAt: state?.createdAt ?? new Date(timestamp),
          updatedAt: new Date(timestamp),
        }
      else
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
    }
  }
})

function mapToAggregate(watchlist: WatchlistItemFragment): AggregateWithoutBase<WatchlistAggregate> {
  return {
    aggregateId: watchlist.id,
    name: watchlist.name,
    tenantId: watchlist.tenant?.id,
    portalUserId: watchlist.portalUser?.id,
    entries: watchlist.entries.map((entry) => ({
      id: entry.id,
      companyId: entry.company.id,
      contacts: entry.contacts.map((contact) => ({
        id: contact.id,
        email: contact.email,
        name: contact.name ?? null,
      })),
    })),
  }
}
