/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

// Contact schema
const contactSchema = z.object({
  id: z.string(),
  email: z.string(),
  name: z.string().nullable(),
})

// Watchlist entry schema
const watchlistEntrySchema = z.object({
  id: z.string(),
  companyId: z.string(),
  contacts: z.array(contactSchema),
})

// Main watchlist schema
export const watchlistAggregateSchema = defineAggregate(
  z.object({
    tenantId: z.string().optional(), // TODO: Find out why this is optional in the backend
    portalUserId: z.string().optional(), // TODO: Find out why this is optional in the backend
    name: z.string(),
    entries: z.array(watchlistEntrySchema),
  })
)

export type WatchlistAggregate = z.infer<typeof watchlistAggregateSchema>
