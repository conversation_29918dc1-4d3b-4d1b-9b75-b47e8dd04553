/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { defineReducer } from "@kreios/eventsourcing"

import type { EvaluationRequestAggregate } from "./aggregate"
import type { evaluationRequestEvents } from "./events"

export const evaluationRequestReducer = defineReducer<EvaluationRequestAggregate, typeof evaluationRequestEvents>(
  (state, event) => {
    const { aggregateId, version, timestamp } = event

    switch (event.type) {
      case "evaluationRequest:created:v1": {
        return {
          ...event.payload,
          aggregateId: aggregateId,
          version,
          createdAt: new Date(timestamp),
          updatedAt: new Date(timestamp),
          deleted: false,
          deletedAt: null,
          status: "pending",
          notifications: [],
        }
      }
      case "evaluationRequest:updated:v1": {
        return {
          ...state!,
          ...event.payload,
          version,
          updatedAt: new Date(timestamp),
        }
      }
      case "evaluationRequest:deleted:v1": {
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
      }
      case "evaluationRequest:fulfilled:v1": {
        return {
          ...state!,
          version,
          status: "fulfilled",
          updatedAt: new Date(timestamp),
        }
      }
      case "evaluationRequest:notificationSent:v1": {
        return {
          ...state!,
          version,
          notifications: [...state!.notifications, event.payload],
          updatedAt: new Date(timestamp),
        }
      }
    }
  }
)
