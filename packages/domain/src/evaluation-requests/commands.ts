/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { ulid } from "ulidx"
import { z } from "zod"

import { defineCommand } from "@kreios/eventsourcing"

import {
  evaluationRequestCreatedV1,
  evaluationRequestDeletedV1,
  evaluationRequestFulfilledV1,
  evaluationRequestNotificationSentV1,
  evaluationRequestUpdatedV1,
} from "./events"
import { evaluationRequestEventStore } from "./store"

export const createEvaluationRequestCommand = defineCommand({
  commandId: "createEvaluationRequest",
  eventStores: [evaluationRequestEventStore],
  inputSchema: evaluationRequestCreatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid().optional(),
  }),
  outputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const aggregateId = commandInput.aggregateId ?? ulid()

    await eventStore.pushEvent({
      type: evaluationRequestCreatedV1.type,
      aggregateId,
      version: 1,
      timestamp: new Date().toISOString(),
      payload: commandInput,
    })

    return { aggregateId }
  },
})

export const updateEvaluationRequestCommand = defineCommand({
  commandId: "updateEvaluationRequest",
  eventStores: [evaluationRequestEventStore],
  inputSchema: evaluationRequestUpdatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId, ...payload } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Evaluation request not found`)
    }

    await eventStore.pushEvent({
      type: evaluationRequestUpdatedV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload,
    })
  },
})

export const deleteEvaluationRequestCommand = defineCommand({
  commandId: "deleteEvaluationRequest",
  eventStores: [evaluationRequestEventStore],
  inputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Evaluation request not found`)
    }

    await eventStore.pushEvent({
      type: evaluationRequestDeletedV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload: {},
    })
  },
})

export const fulfillEvaluationRequestCommand = defineCommand({
  commandId: "fulfillEvaluationRequest",
  eventStores: [evaluationRequestEventStore],
  inputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Evaluation request not found`)
    }

    if (aggregate.status === "fulfilled") {
      throw new Error(`Evaluation request is already fulfilled`)
    }

    await eventStore.pushEvent({
      type: evaluationRequestFulfilledV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload: {},
    })
  },
})

export const sendEvaluationRequestNotificationCommand = defineCommand({
  commandId: "sendEvaluationRequestNotification",
  eventStores: [evaluationRequestEventStore],
  inputSchema: z.object({
    aggregateId: z.string().ulid(),
    email: z.string(),
    type: z.enum(["request-received", "request-fulfilled"]),
    messageId: z.string(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId, ...payload } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Evaluation request not found`)
    }

    await eventStore.pushEvent({
      type: evaluationRequestNotificationSentV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload: {
        ...payload,
        sentAt: new Date(),
      },
    })
  },
})

export const recordEvaluationRequestNotificationSentCommand = defineCommand({
  commandId: "recordEvaluationRequestNotificationSent",
  eventStores: [evaluationRequestEventStore],
  inputSchema: z.object({
    aggregateId: z.string().ulid(),
    email: z.string(),
    type: z.enum(["request-received", "request-fulfilled"]),
    messageId: z.string(),
    sentAt: z.date(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId, ...payload } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Evaluation request not found`)
    }

    await eventStore.pushEvent({
      type: evaluationRequestNotificationSentV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload,
    })
  },
})
