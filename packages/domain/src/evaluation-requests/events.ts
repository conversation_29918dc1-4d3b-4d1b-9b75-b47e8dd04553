/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"

const evaluationRequestSchema = z.object({
  companyId: z.string().optional(),
  companyName: z.string(),
  portalUserId: z.string(),
  portalUserEmail: z.string(),
  portalUserName: z.string().optional(),
  infoSustainability: z.boolean(),
  infoSupplyChain: z.boolean(),
  infoAI: z.boolean(),
})

export const evaluationRequestCreatedV1 = defineEventType({
  aggregateType: "evaluationRequest",
  eventType: "created",
  schemaVersion: 1,
  schema: evaluationRequestSchema,
})

export const evaluationRequestUpdatedV1 = defineEventType({
  aggregateType: "evaluationRequest",
  eventType: "updated",
  schemaVersion: 1,
  schema: evaluationRequestSchema.partial(),
})

export const evaluationRequestDeletedV1 = defineEventType({
  aggregateType: "evaluationRequest",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const evaluationRequestFulfilledV1 = defineEventType({
  aggregateType: "evaluationRequest",
  eventType: "fulfilled",
  schemaVersion: 1,
  schema: z.object({}),
})

export const evaluationRequestNotificationSentV1 = defineEventType({
  aggregateType: "evaluationRequest",
  eventType: "notificationSent",
  schemaVersion: 1,
  schema: z.object({
    email: z.string(),
    type: z.enum(["request-received", "request-fulfilled"]),
    sentAt: z.date(),
    messageId: z.string(),
  }),
})

export const evaluationRequestEvents = [
  evaluationRequestCreatedV1,
  evaluationRequestUpdatedV1,
  evaluationRequestDeletedV1,
  evaluationRequestFulfilledV1,
  evaluationRequestNotificationSentV1,
]
