/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

export const evaluationRequestAggregateSchema = defineAggregate(
  z.object({
    companyId: z.string().optional(),
    companyName: z.string(),
    portalUserId: z.string(),
    portalUserEmail: z.string(),
    portalUserName: z.string().optional(),
    infoSustainability: z.boolean(),
    infoSupplyChain: z.boolean(),
    infoAI: z.boolean(),
    status: z.enum(["pending", "fulfilled"]),
    notifications: z.array(
      z.object({
        email: z.string(),
        type: z.enum(["request-received", "request-fulfilled"]),
        sentAt: z.date(),
        messageId: z.string(),
      })
    ),
  })
)

export type EvaluationRequestAggregate = z.infer<typeof evaluationRequestAggregateSchema>
