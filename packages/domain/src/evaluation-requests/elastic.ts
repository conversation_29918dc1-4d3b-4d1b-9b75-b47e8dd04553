/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import type { EvaluationRequestAggregate } from "./aggregate"
import { evaluationRequestEventStore } from "./store"

export const evaluationRequestsIndexConfig = createIndexConfig({
  name: "evaluationrequests",
  displayName: "Evaluation Requests",
  getTotalCount: () => evaluationRequestEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  buildUrl: (aggregateId) => `/admin/evaluation-requests/${aggregateId}`,
  fetchById: (id: string) => evaluationRequestEventStore.getExistingAggregate(id).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await evaluationRequestEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield evaluationRequestEventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (evaluationRequest: EvaluationRequestAggregate) => ({
    _id: evaluationRequest.aggregateId,
    aggregateId: evaluationRequest.aggregateId,
    createdAt: formatDateToIsoString(evaluationRequest.createdAt),
    updatedAt: formatDateToIsoString(evaluationRequest.updatedAt),
    deletedAt: evaluationRequest.deletedAt ? formatDateToIsoString(evaluationRequest.deletedAt) : null,
    label: `${evaluationRequest.companyName} - ${evaluationRequest.portalUserEmail}`,

    companyId: evaluationRequest.companyId,
    companyName: evaluationRequest.companyName,
    portalUserId: evaluationRequest.portalUserId,
    portalUserEmail: evaluationRequest.portalUserEmail,
    portalUserName: evaluationRequest.portalUserName,
    infoSustainability: evaluationRequest.infoSustainability,
    infoSupplyChain: evaluationRequest.infoSupplyChain,
    infoAI: evaluationRequest.infoAI,
    status: evaluationRequest.status,
    notifications: evaluationRequest.notifications.map((notification) => ({
      email: notification.email,
      type: notification.type,
      sentAt: formatDateToIsoString(notification.sentAt),
      messageId: notification.messageId,
    })),
  }),
  properties: {
    companyId: { type: "keyword" },
    companyName: { type: "text", fields: { keyword: { type: "keyword" } } },
    portalUserId: { type: "keyword" },
    portalUserEmail: { type: "keyword" },
    portalUserName: { type: "text", fields: { keyword: { type: "keyword" } } },
    infoSustainability: { type: "boolean" },
    infoSupplyChain: { type: "boolean" },
    infoAI: { type: "boolean" },
    status: { type: "keyword" },
    notifications: {
      type: "nested",
      properties: {
        email: { type: "keyword" },
        type: { type: "keyword" },
        sentAt: { type: "date" },
        messageId: { type: "keyword" },
      },
    },
  },
})

export type EvaluationRequestDocument = ReturnType<typeof evaluationRequestsIndexConfig.toDocument>
