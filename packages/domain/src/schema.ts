/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { eventsTable } from "@kreios/eventsourcing/drizzle"

// One table per aggregate/event store
export const companies = eventsTable("companies")
export const portalUsers = eventsTable("portalUsers")
export const watchlists = eventsTable("watchlists")
export const naceCodes = eventsTable("naceCodes")
export const evaluationRequests = eventsTable("evaluationRequests")
export const surveys = eventsTable("surveys")
export const surveyBatches = eventsTable("surveyBatches")
