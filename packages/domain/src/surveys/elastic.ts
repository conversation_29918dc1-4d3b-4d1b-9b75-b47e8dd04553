/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"

import type { SurveyAggregate } from "./aggregate"
import { surveyEventStore } from "./store"

export const surveysIndexConfig = createIndexConfig({
  name: "surveys",
  displayName: "Surveys",
  getTotalCount: () => surveyEventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  buildUrl: (aggregateId) => `/admin/surveys/${aggregateId}`,
  fetchById: (aggregateId) => surveyEventStore.getExistingAggregate(aggregateId).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await surveyEventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield surveyEventStore.getExistingAggregate(entry.aggregateId).then(({ aggregate }) => aggregate)
      }
    })()
  },
  toDocument: (survey: SurveyAggregate) => {
    return {
      _id: survey.aggregateId,
      aggregateId: survey.aggregateId,
      createdAt: formatDateToIsoString(survey.createdAt),
      updatedAt: formatDateToIsoString(survey.updatedAt),
      deletedAt: survey.deletedAt ? formatDateToIsoString(survey.deletedAt) : null,
      statusChangedAt: formatDateToIsoString(survey.statusChangedAt),
      batchId: survey.batchId,
      label: survey.batchId,
      companyId: survey.companyId,
      company: survey.company,
      tenant: survey.tenant,
      status: survey.status,
      template: survey.template,
      financialYear: survey.financialYear,
      deadline: survey.deadline ? formatDateToIsoString(survey.deadline) : null,
      submittedAt: survey.submittedAt ? formatDateToIsoString(survey.submittedAt) : null,
      readyForEdit: survey.readyForEdit,
      documents: survey.documents,
      progress: survey.progress,
      lastReminder: survey.lastReminder ? formatDateToIsoString(survey.lastReminder) : null,
      users: survey.users,
      formData: survey.formData,
      prefilledData: survey.prefilledData,
      langConsent: survey.langConsent,
    }
  },
  properties: {
    batchId: { type: "keyword" },
    companyId: { type: "keyword" },
    company: {
      properties: {
        id: { type: "keyword" },
        name: { type: "text", fields: { keyword: { type: "keyword" } } },
      },
    },
    tenant: {
      properties: {
        id: { type: "keyword" },
        name: { type: "text" },
      },
    },
    status: { type: "keyword" },
    template: { type: "keyword" },
    financialYear: { type: "integer" },
    deadline: { type: "date" },
    submittedAt: { type: "date" },
    statusChangedAt: { type: "date" },
    readyForEdit: { type: "boolean" },
    prefilledData: { type: "object", enabled: false },
    formData: { type: "object", enabled: false },
    sharedWith: {
      properties: {
        id: { type: "keyword" },
        email: { type: "keyword" },
        name: { type: "text" },
        role: { type: "keyword" },
      },
    },
    documents: {
      properties: {
        id: { type: "keyword" },
        file: { type: "keyword" },
        fileName: { type: "text" },
        fileSize: { type: "long" },
        isAttachment: { type: "boolean" },
      },
    },
    progress: { type: "integer" },
    lastReminder: { type: "date" },
    users: {
      properties: {
        id: { type: "keyword" },
        email: { type: "keyword" },
        companyId: { type: "keyword" },
      },
    },
  },
})

export type SurveyDocument = ReturnType<typeof surveysIndexConfig.toDocument>
