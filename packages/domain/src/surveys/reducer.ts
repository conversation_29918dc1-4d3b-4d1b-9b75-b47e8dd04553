/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

/* eslint-disable @typescript-eslint/no-unsafe-argument */
import type { SurveyItemFragment } from "@impactly/integration/backend"

import { defineReducer } from "@kreios/eventsourcing"
import { agnosticSuperjsonParse } from "@kreios/utils/agnostic-superjson-parse"
import { decodeRelayID } from "@kreios/utils/decode-relay-id"
import { isBase64 } from "@kreios/utils/is-base64"
import { toUTCDate } from "@kreios/utils/to-utc-date"

import type { AggregateWithoutBase } from "../utils"
import type { SurveyAggregate } from "./aggregate"
import type { surveyEvents } from "./events"

export const surveyReducer = defineReducer<SurveyAggregate, typeof surveyEvents>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "survey:created:v1": {
      return {
        ...event.payload,
        formData: readJSONStringOrObject(event.payload.formData),
        prefilledData: readJSONStringOrObject(event.payload.prefilledData),
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
        progress: typeof event.payload.progress === "number" ? event.payload.progress : 0,
      }
    }
    case "survey:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        formData: readJSONStringOrObject(event.payload.formData, state?.formData),
        prefilledData: readJSONStringOrObject(event.payload.prefilledData, state?.prefilledData),
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "survey:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
    case "survey:changeCaptured/platform/survey:v1": {
      if (event.payload.sourceData) {
        const aggregate = mapToAggregate(event.payload.sourceData)

        return {
          ...state!,
          ...aggregate,
          formData: readJSONStringOrObject(aggregate.formData, state?.formData),
          prefilledData: readJSONStringOrObject(aggregate.prefilledData, state?.prefilledData),
          aggregateId,
          version,
          createdAt: state?.createdAt ?? new Date(timestamp),
          updatedAt: new Date(timestamp),
        }
      } else
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
    }
  }
})

/**
 * Reads a JSON string or object and returns the object.
 * If the input is not a string, it is returned as is.
 * If the input is a string, it is parsed using {@link agnosticSuperjsonParse}.
 * If the input is undefined, the fallback is returned.
 * @param input - The input to read.
 * @param fallback - The fallback value.
 * @returns The parsed object or the input if it is not a string.
 */
const readJSONStringOrObject = (input?: string | Record<string, unknown>, fallback?: Record<string, unknown>) => {
  if (!input) return fallback
  if (typeof input === "string") return agnosticSuperjsonParse<Record<string, unknown>>(input)
  return input
}

/**
 * Maps a graphql representation of a survey to an aggregate without base.
 * @param survey - The survey item fragment.
 * @returns The aggregate without base.
 */
function mapToAggregate(survey: SurveyItemFragment): AggregateWithoutBase<SurveyAggregate> {
  const id = survey.batch?.id ?? ""
  const batchId = isBase64(id) ? decodeRelayID(id) : id
  return {
    aggregateId: survey.id,
    batchId: batchId,
    companyId: survey.company?.id ?? "",
    company: survey.company
      ? {
          id: survey.company.id,
          name: "name" in survey.company ? survey.company.name : "",
        }
      : undefined,
    tenant: survey.tenant ? { id: survey.tenant.id, name: survey.tenant.name ?? undefined } : undefined,
    status: survey.status ?? "NOT_STARTED",
    template: survey.template ?? "UNIFIED_QUESTIONNAIRE_V1",
    financialYear: survey.financialYear ?? undefined,
    deadline: toUTCDate(survey.deadline),
    submittedAt: toUTCDate(survey.submittedAt),
    readyForEdit: survey.readyForEdit,
    formData: survey.formData as Record<string, unknown>,
    progress: survey.progress ?? 0,
    statusChangedAt: toUTCDate(survey.statusChangedAt),
    lastReminder: toUTCDate(survey.lastReminder),
    documents: survey.inputDocuments?.filter((doc) => doc !== null) ?? [],
    prefilledData: survey.prefilledData as Record<string, unknown>,
    langConsent: survey.langConsent ?? undefined,
  }
}
