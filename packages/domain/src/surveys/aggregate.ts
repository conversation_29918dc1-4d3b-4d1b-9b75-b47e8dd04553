/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

const SurveyStatusEnum = z.enum([
  "OPEN",
  "IN_PROGRESS",
  "COMPLETE",
  "NOT_STARTED",
  "SUBMITTED",
  "SENT",
  "PENDING",
  "CANCELLED",
  "PROCESSING",
])
const SurveyTemplateEnum = z.enum(["UNIFIED_QUESTIONNAIRE_V1", "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"])

const surveyDocumentSchema = z.object({
  id: z.string(),
  file: z.string(),
  fileName: z.string(),
  fileSize: z.number(),
  isAttachment: z.boolean(),
})

const surveyUserSchema = z.object({
  id: z.string(),
  email: z.string(),
  companyId: z.string().optional(),
})

export const surveyAggregateSchema = defineAggregate(
  z.object({
    batchId: z.string(),
    companyId: z.string().optional(),
    company: z
      .object({
        id: z.string(),
        name: z.string(),
      })
      .optional(),
    tenant: z
      .object({
        id: z.string(),
        name: z.string().optional(),
      })
      .optional(),
    status: SurveyStatusEnum,
    template: SurveyTemplateEnum,
    financialYear: z.number().optional(),
    deadline: z.date().optional(),
    submittedAt: z.date().optional(),
    readyForEdit: z.boolean(),
    formData: z.record(z.any()).optional(),
    prefilledData: z.object({ formData: z.record(z.any()).optional() }).optional(),
    documents: z.array(surveyDocumentSchema).optional(),
    progress: z.number().default(0),
    statusChangedAt: z.date().optional(),
    lastReminder: z.date().optional(),
    users: z.array(surveyUserSchema).optional(),
    langConsent: z.string().optional(),
  })
)

export type SurveyAggregate = z.infer<typeof surveyAggregateSchema>
