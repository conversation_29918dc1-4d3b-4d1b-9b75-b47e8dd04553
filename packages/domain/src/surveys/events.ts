/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import type { SurveyItemFragment } from "@impactly/integration/backend"
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

import { graphqlZodSchema } from "../utils"

const surveySchema = z.object({
  batchId: z.string(),
  companyId: z.string().optional(),
  company: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .optional(),
  tenant: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .optional(),
  status: z
    .enum(["OPEN", "IN_PROGRESS", "COMPLETE", "NOT_STARTED", "SUBMITTED", "SENT", "PENDING", "CANCELLED", "PROCESSING"])
    .default("NOT_STARTED"),
  template: z.enum(["UNIFIED_QUESTIONNAIRE_V1"]).default("UNIFIED_QUESTIONNAIRE_V1"),
  financialYear: z.number().optional(),
  deadline: z.date().optional(),
  submittedAt: z.date().optional(),
  readyForEdit: z.boolean(),
  formData: z.any().optional(),
  prefilledData: z.any().optional(),
  documents: z
    .array(
      z.object({
        id: z.string(),
        file: z.string(),
        fileName: z.string(),
        fileSize: z.number(),
        isAttachment: z.boolean(),
      })
    )
    .optional(),
  progress: z.number().default(0),
  statusChangedAt: z.date().optional(),
  lastReminder: z.date().optional(),
})

export const surveyCreatedV1 = defineEventType({
  aggregateType: "survey",
  eventType: "created",
  schemaVersion: 1,
  schema: surveySchema,
})

export const surveyUpdatedV1 = defineEventType({
  aggregateType: "survey",
  eventType: "updated",
  schemaVersion: 1,
  schema: surveySchema.partial(),
})

export const surveyDeletedV1 = defineEventType({
  aggregateType: "survey",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const surveyChangedOnPlatformV1 = defineCDCEventType({
  aggregateType: "survey",
  schemaVersion: 1,
  source: "platform",
  sourceType: "survey",
  schema: graphqlZodSchema<SurveyItemFragment>().nullable(),
})

export const surveyEvents = [surveyCreatedV1, surveyUpdatedV1, surveyDeletedV1, surveyChangedOnPlatformV1]
