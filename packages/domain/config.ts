/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { ElasticsearchGateway } from "@kreios/elasticsearch"
import type { Database } from "@kreios/eventsourcing/drizzle"

import type { config } from "./src/elastic"

export interface DomainContainer {
  gateway: ElasticsearchGateway<typeof config>
  db: Database
}
