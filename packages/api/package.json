{"name": "@impactly/api", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts", "./trpc": "./src/trpc/index.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@impactly/domain": "workspace:*", "@impactly/flags": "workspace:*", "@impactly/integration": "workspace:*", "@impactly/jobs": "workspace:*", "@kreios/api": "workspace:*", "@kreios/auth": "workspace:*", "@kreios/elasticsearch": "workspace:*", "@kreios/utils": "workspace:*", "@t3-oss/env-core": "0.11.1", "@trpc/server": "11.0.0-rc.401", "awilix": "12.0.3", "next-intl": "4.0.2", "superjson": "2.2.1", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vitest": "2.1.9"}}