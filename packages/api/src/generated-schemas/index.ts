/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import * as z from "zod"

import selfAssesmentQuestionnaireEn from "./self-assesment-questionnaire-en"
import selfAssesmentQuestionnaireEt from "./self-assesment-questionnaire-et"
import universalQuestionnaireSchemaEn from "./universal-questionnaire-en"
import universalQuestionnaireSchemaEt from "./universal-questionnaire-et"

export enum Language {
  EN = "en",
  ET = "et",
}

export const getSchemas = (language: Language) => {
  const universalQuestionnaireSchema = getUniversalQuestionnaireSchema(language)

  return {
    surveyFormSchema: z.object({
      general: universalQuestionnaireSchema.shape.general._def.innerType,
      climate_impact: universalQuestionnaireSchema.shape.climate_impact._def.innerType,
      green_transition: universalQuestionnaireSchema.shape.green_transition._def.innerType,
      nature: universalQuestionnaireSchema.shape.nature._def.innerType,
      social_governance: universalQuestionnaireSchema.shape.social_governance._def.innerType,
      nfrd_reporting: universalQuestionnaireSchema.shape.nfrd_reporting._def.innerType,
    }),
  }
}

export const getSelfAssessmentFormSchema = (language: Language) => {
  const universalQuestionnaireSchema = getSelfAssessmentQuestionnaireSchema(language)

  return {
    selfAssessmentFormSchema: z.object({
      general: universalQuestionnaireSchema.shape.general._def.innerType,
      esg_management_strategy: universalQuestionnaireSchema.shape.esg_management_strategy._def.innerType,
      climate_impact_energy_use: universalQuestionnaireSchema.shape.climate_impact_energy_use._def.innerType,
      managing_environmental_resources:
        universalQuestionnaireSchema.shape.managing_environmental_resources._def.innerType,
      workforce_social_responsibility:
        universalQuestionnaireSchema.shape.workforce_social_responsibility._def.innerType,
      business_ethics_governance: universalQuestionnaireSchema.shape.business_ethics_governance._def.innerType,
    }),
  }
}
// Function to get the survey form schema based on the language
export const getSurveyFormSchema = (language: Language) => {
  const schemas = getSchemas(language)
  return schemas.surveyFormSchema
}

export const getSelfAssessmentSchema = (language: Language) => {
  const schemas = getSelfAssessmentFormSchema(language)
  return schemas.selfAssessmentFormSchema
}

/**
 * Returns the appropriate schema based on the language.
 * @param language The language to get the schema for
 * @returns The schema for the specified language
 */
export const getUniversalQuestionnaireSchema = (language: Language) => {
  return language === Language.EN ? universalQuestionnaireSchemaEn : universalQuestionnaireSchemaEt
}
export const getSelfAssessmentQuestionnaireSchema = (language: Language) => {
  return language === Language.EN ? selfAssesmentQuestionnaireEn : selfAssesmentQuestionnaireEt
}
