/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2025 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { z } from "zod"

export default z.object({
  introduction: z
    .object({
      main_text: z
        .object({})
        .describe(
          "Following regulatory requirements (i.e. the Non-Financial Reporting Directive (NFRD) and upcoming Corporate Sustainability Reporting Directive (CSRD)) as for reporting purposes, this unified questionnaire is designed by Estonian, Latvian and Lithuanian Banking Associations to collect quantitative and qualitative data to assess Client's environmental and climate-related impact, social and governance practices, as well as to evaluate Client's readiness to green transition."
        )
        .optional()
        .nullable(),
    })
    .readonly()
    .optional()
    .nullable(),
  general: z
    .object({
      survey_fin_year: z
        .number({ invalid_type_error: "See väli peab olema kehtiv number", required_error: "See väli on kohustuslik" })
        .gte(2001, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (2001)" })
        .lte(2024, { message: "See väli peab olema väiksem või võrdne maksimumväärtusega (2024)" }),
      survey_revenue_for_year: z.number({
        invalid_type_error: "See väli peab olema kehtiv number",
        required_error: "See väli on kohustuslik",
      }),
    })
    .optional()
    .nullable(),
  esg_management_strategy: z
    .object({
      practices_for_transitioning_towards_a_more_sustainable_economy: z
        .object({
          if_sust_vision: z.boolean({
            invalid_type_error: "See väli peab olema tõene või väär",
            required_error: "See väli on kohustuslik",
          }),
          sust_vision: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_sust_strategy: z.boolean({
            invalid_type_error: "See väli peab olema tõene või väär",
            required_error: "See väli on kohustuslik",
          }),
          sust_strategy: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          sust_strategy_ref: z
            .array(
              z.object({
                key: z.string({
                  invalid_type_error: "See väli peab olema kehtiv tekst",
                  required_error: "See väli on kohustuslik",
                }),
                mimeType: z.string({
                  invalid_type_error: "See väli peab olema kehtiv tekst",
                  required_error: "See väli on kohustuslik",
                }),
                name: z.string({
                  invalid_type_error: "See väli peab olema kehtiv tekst",
                  required_error: "See väli on kohustuslik",
                }),
                url: z
                  .string({
                    invalid_type_error: "See väli peab olema kehtiv tekst",
                    required_error: "See väli on kohustuslik",
                  })
                  .optional()
                  .nullable(),
              })
            )
            .describe(
              "Requests a link to or submission of the document detailing the company’s sustainability strategy."
            )
            .optional()
            .nullable(),
          if_sustainable_practices: z.boolean({
            invalid_type_error: "See väli peab olema tõene või väär",
            required_error: "See väli on kohustuslik",
          }),
          sustainable_practices: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_sust_commitments: z.boolean({
            invalid_type_error: "See väli peab olema tõene või väär",
            required_error: "See väli on kohustuslik",
          }),
          sust_commitments: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_sust_certification: z.boolean({
            invalid_type_error: "See väli peab olema tõene või väär",
            required_error: "See väli on kohustuslik",
          }),
          sust_certification: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          esg_initiatives: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      human_rights_policies: z
        .object({
          if_child_labour_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_forced_labour_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_human_trafficking: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_discrimination_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_accident_prevention: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          other_hr_policy: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      business_conduct_policies: z
        .object({
          if_bribery_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_corruption_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_fraud_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_whistleb_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_supplier_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_aml_policy: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
  climate_impact_energy_use: z
    .object({
      energy_consumption: z
        .object({
          total_energy_cons: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          renewable_electricity_cons: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nonrenewable_electricity_cons: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          renewable_fuels_cons: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nonrenewable_fuels_cons: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      ghg_emissions: z
        .object({
          ghg_scope1: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope1_biogenic: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope2_locationbased: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope2_marketbased: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope1_2_total: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_total: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_1: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_2: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_3: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_4: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_5: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_6: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_7: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_8: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_9: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_10: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_11: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_12: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_13: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_14: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_scope3_cat_15: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghg_total_emissions: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      ghg_reduction: z
        .object({
          if_ghg_reduction_goals: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          ghggoal: z
            .array(
              z.object({
                ghggoal_element: z
                  .enum(
                    [
                      "total_ghg_emissions",
                      "total_scope_1_&_scope_2",
                      "scope_1",
                      "scope_2_(location_based)",
                      "scope_2_(market_based)",
                      "scope_3",
                    ],
                    {
                      invalid_type_error: "See väli peab olema kehtiv valik",
                      required_error: "See väli on kohustuslik",
                    }
                  )
                  .optional()
                  .nullable(),
                ghggoal_base_year: z
                  .number({
                    invalid_type_error: "See väli peab olema kehtiv number",
                    required_error: "See väli on kohustuslik",
                  })
                  .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
                  .optional()
                  .nullable(),
                ghggoal_target_year_st: z
                  .number({
                    invalid_type_error: "See väli peab olema kehtiv number",
                    required_error: "See väli on kohustuslik",
                  })
                  .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
                  .optional()
                  .nullable(),
                ghggoal_goal_numeric_st: z
                  .number({
                    invalid_type_error: "See väli peab olema kehtiv number",
                    required_error: "See väli on kohustuslik",
                  })
                  .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
                  .optional()
                  .nullable(),
                ghggoal_target_year_lt: z
                  .number({
                    invalid_type_error: "See väli peab olema kehtiv number",
                    required_error: "See väli on kohustuslik",
                  })
                  .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
                  .optional()
                  .nullable(),
                ghggoal_goal_numeric_lt: z
                  .number({
                    invalid_type_error: "See väli peab olema kehtiv number",
                    required_error: "See väli on kohustuslik",
                  })
                  .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
                  .optional()
                  .nullable(),
              })
            )
            .optional()
            .nullable(),
          ghggoal_narrative: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      transition_plan: z
        .object({
          if_transition_plan_existence: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          transition_plan_description: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      physical_risks_from_climate_change: z
        .object({
          if_physical_risk_identification: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          physical_risk_identification: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          climate_change_adaptation_actions: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          insurance_coverage: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
          physical_risk_effect: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
  managing_environmental_resources: z
    .object({
      pollution: z
        .object({
          if_pollution_reporting: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_pollution_to_air: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_pollution_to_water: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_pollution_to_soil: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      waste_management: z
        .object({
          generation_of_waste: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          recycling_or_reuse: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
          use_of_materials: z
            .string({
              invalid_type_error: "See väli peab olema kehtiv tekst",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      company_water_use: z
        .object({
          water_withdrawal: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          water_withdrawal_in_area_of_high_water_stress: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          water_consumption: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      biodiversity: z
        .object({
          if_land_near_sensitive_areas: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          land_area_near_biodiversity: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          total_use_of_land: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          total_sealed_area: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          total_nature_oriented_area_on_site: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
          total_nature_oriented_area_off_site: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
  workforce_social_responsibility: z
    .object({
      workforce_general_characteristics: z
        .object({
          nr_employees: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nr_employees_gender_m: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nr_employees_gender_f: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nr_employees_gender_other: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nr_temporary_employees: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nr_permanent_employees: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          employee_turnover_rate: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .lte(100, { message: "See väli peab olema väiksem või võrdne maksimumväärtusega (100)" })
            .optional()
            .nullable(),
          nr_non_employees: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      workforce_remuneration_collective_bargaining_and_training: z
        .object({
          gender_paygap: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .lte(100, { message: "See väli peab olema väiksem või võrdne maksimumväärtusega (100)" })
            .optional()
            .nullable(),
          employees_covered_cb: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .lte(100, { message: "See väli peab olema väiksem või võrdne maksimumväärtusega (100)" })
            .optional()
            .nullable(),
          trainings_gender_m: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          trainings_gender_f: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          if_minimum_wage_pay: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      workforce_health_safety: z
        .object({
          work_accidents_employees: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
          work_fatalities_employees: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
  business_ethics_governance: z
    .object({
      incidents_related_severe_human_rights: z
        .object({
          nr_human_rights_violations: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
          nr_human_rights_stakeholders_cases: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .gte(0, { message: "See väli peab olema suurem või võrdne miinimumväärtusega (0)" })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      corruption_and_bribery: z
        .object({
          if_abc_convictions: z
            .boolean({
              invalid_type_error: "See väli peab olema tõene või väär",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          nr_abc_convictions: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          abc_fines: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      workforce_characteristics: z
        .object({
          management_board_f: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
          management_board_m: z
            .number({
              invalid_type_error: "See väli peab olema kehtiv number",
              required_error: "See väli on kohustuslik",
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
})
