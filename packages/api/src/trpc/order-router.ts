/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { PortalUserAggregate, WatchlistAggregate } from "@impactly/domain"
import {
  companyEventStore,
  evaluationRequestEventStore,
  portalUserEventStore,
  watchlistEventStore,
} from "@impactly/domain"
import { createEvaluationRequestCommand } from "@impactly/domain/evaluation-requests/commands"
import { getPortalUserByEmail } from "@impactly/domain/portal-users/utils/get-portal-user-by-email"
import { service as integrationService, PortalUserRoleEnum } from "@impactly/integration/backend"
import { dispatcher } from "@impactly/jobs"
import { CompanyCaptureChangeProvider } from "@impactly/jobs/cdc/companies"
import { PortalUserCaptureChangeProvider } from "@impactly/jobs/cdc/portal-users"
import { CaptureChangeService } from "@impactly/jobs/cdc/service"
import { WatchlistCaptureChangeProvider } from "@impactly/jobs/cdc/watchlists"
import { z } from "zod"

import { createLocalizedError, ErrorTranslationKeys } from "../utils/localized-error"
import { createTRPCRouter, publicProcedure } from "./trpc"

const gateway = container.resolve("gateway")

const orderProfileSchema = z.object({
  companyId: z.string().optional(),
  companyName: z.string(),
  name: z.string(),
  email: z.string().email(),
  infoSustainability: z.boolean(),
  infoSupplyChain: z.boolean(),
  infoAI: z.boolean(),
})

const captureChangeService = new CaptureChangeService(gateway, {
  "portal-users": new PortalUserCaptureChangeProvider(integrationService, portalUserEventStore),
  watchlists: new WatchlistCaptureChangeProvider(
    integrationService,
    watchlistEventStore,
    new CaptureChangeService(gateway, {
      companies: new CompanyCaptureChangeProvider(integrationService, companyEventStore),
    })
  ),
})

export const orderRouter = createTRPCRouter({
  submitOrder: publicProcedure.input(orderProfileSchema).mutation(async ({ input }) => {
    console.log("Received order submission:", input)
    const { email, companyId, companyName, name, infoSustainability, infoSupplyChain, infoAI } = input

    // 1. Validate if user already exists
    console.log("Checking if user exists:", email)
    const portalUserDocument = await getPortalUserByEmail(email)

    let portalUser: PortalUserAggregate | undefined
    if (portalUserDocument) {
      console.log("Existing user found:", portalUserDocument.aggregateId)
      portalUser = await portalUserEventStore
        .getAggregate(portalUserDocument.aggregateId)
        .then(({ aggregate }) => aggregate)
    }

    if (!portalUser) {
      console.log("Creating new user:", email)
      const [firstName, lastName] = name.split(" ", 2) // TODO: Align with the backend
      const createUserResult = await integrationService.createPortalUser({
        email,
        requestedBy: email,
        roles: [{ role: PortalUserRoleEnum.CompanyUser }],
        firstName,
        lastName,
      })

      if (!createUserResult.createPortalUser?.portalUser) {
        console.error("Failed to create portal user:", email)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.PORTAL_USER_UPDATE_FAILED)
      }

      console.log("New user created:", createUserResult.createPortalUser.portalUser.id)
      await captureChangeService.captureChange("portal-users", createUserResult.createPortalUser.portalUser)
      const { aggregate } = await portalUserEventStore.getExistingAggregate(
        createUserResult.createPortalUser.portalUser.id
      )
      portalUser = aggregate
    }

    // 2. Validate if watchlist for user already exists
    console.log("Checking watchlist for user:", portalUser.aggregateId)
    const watchlistDocument = await gateway.findFirst("watchlists", {
      match: { portalUserId: portalUser.aggregateId },
    })

    let watchlist: WatchlistAggregate | undefined
    if (watchlistDocument) {
      console.log("Existing watchlist found:", watchlistDocument.aggregateId)
      watchlist = await watchlistEventStore
        .getAggregate(watchlistDocument.aggregateId)
        .then(({ aggregate }) => aggregate)
    }
    if (!watchlist) {
      console.log("Creating new watchlist for user:", portalUser.aggregateId)
      const createWatchlistResult = await integrationService.createWatchlist({
        requestedBy: email,
        portalUserId: portalUser.aggregateId,
        name: `${portalUser.email}'s Watchlist`,
      })

      if (!createWatchlistResult.createWatchlist?.watchlist) {
        console.error("Failed to create watchlist for user:", portalUser.aggregateId)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.ORDER_SUBMIT_FAILED)
      }

      console.log("New watchlist created:", createWatchlistResult.createWatchlist.watchlist.id)
      await captureChangeService.captureChange("watchlists", createWatchlistResult.createWatchlist.watchlist)
      const { aggregate } = await watchlistEventStore.getExistingAggregate(
        createWatchlistResult.createWatchlist.watchlist.id
      )
      watchlist = aggregate
    }

    // 3. Validate if entry for watchlist already exists (only if companyId is provided)
    if (companyId) {
      console.log("Checking if company exists in watchlist:", companyId)
      const watchlistEntry = watchlist.entries.find((entry) => entry.companyId === companyId)

      if (!watchlistEntry) {
        console.log("Adding company to watchlist:", companyId)
        // Add entry to watchlist
        const addEntryResult = await integrationService.addEntryToWatchlist({
          requestedBy: email,
          watchlistId: watchlist.aggregateId,
          companyId,
        })

        if (!addEntryResult.addEntryToWatchlist?.entry) {
          console.error("Failed to add entry to watchlist:", companyId)
          throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.ORDER_SUBMIT_FAILED)
        }

        console.log("Company added to watchlist:", companyId)

        // Re-fetch the watchlist to get the latest state
        const watchListQuery = await integrationService.getWatchlist({ id: watchlist.aggregateId })
        await captureChangeService.captureChange("watchlists", watchListQuery)
        const { aggregate } = await watchlistEventStore.getExistingAggregate(watchListQuery.id)
        watchlist = aggregate
      }
    }

    // 4. Create a new evaluation request
    console.log("Creating evaluation request")
    const evaluationRequestInput = {
      companyId: companyId,
      companyName: companyName,
      portalUserId: portalUser.aggregateId,
      portalUserEmail: email,
      portalUserName: name,
      infoSustainability,
      infoSupplyChain,
      infoAI,
    }

    const { aggregateId: evaluationRequestId } = await createEvaluationRequestCommand.handler(evaluationRequestInput, [
      evaluationRequestEventStore,
    ])

    console.log("Evaluation request created:", evaluationRequestId)
    const { aggregate } = await evaluationRequestEventStore.getExistingAggregate(evaluationRequestId)
    await gateway.reindex("evaluationrequests", aggregate)

    // 5. Run sendNotifications job synchronously
    console.log("Running sendNotifications job synchronously")
    await dispatcher.handle("send-notifications", { evaluationRequestId })
    console.log("Notifications sent successfully")

    // We're done here, so let's go home
    console.log("Order submission completed successfully")
    return { success: true, evaluationRequestId }
  }),
})
