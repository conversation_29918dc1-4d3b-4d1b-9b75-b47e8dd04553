/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { EvaluationRequestDocument } from "@impactly/domain/evaluation-requests/elastic"
import type { TRPCRouterRecord } from "@trpc/server"
import { evaluationRequestEventStore } from "@impactly/domain/evaluation-requests/index"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { z } from "zod"

import { createLocalizedError, ErrorTranslationKeys } from "../utils/localized-error"
import { protectedProcedure } from "./trpc"

const gateway = container.resolve("gateway")

const idSchema = z.string().describe("Aggregate ID")
export const evaluationRequestsRouter = {
  history: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
    const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

    if (!(isAdmin || isClientAdmin)) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

    const { events } = await evaluationRequestEventStore.getEvents(input)
    return events
  }),

  get: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
    const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

    if (!(isAdmin || isClientAdmin)) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

    try {
      const { aggregate } = await evaluationRequestEventStore.getExistingAggregate(input)
      return aggregate
    } catch (error) {
      throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.EVALUATION_REQUEST_NOT_FOUND, error)
    }
  }),

  list: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(5).optional().default(10),
        page: z.number().min(1).optional().default(1),
        search: z.string().optional(),
      })
    )
    .query(async ({ input: { limit, page, search }, ctx: { portalUser } }) => {
      const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

      if (!(isAdmin || isClientAdmin)) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

      const { total, documents } = await gateway.search<EvaluationRequestDocument, never>({
        query: {
          bool: {
            should: search
              ? [
                  {
                    multi_match: {
                      fuzziness: 2.5,
                      query: search,
                      fields: ["companyName", "portalUserEmail", "portalUserName", "status"],
                    },
                  },
                ]
              : [],
          },
        },
        index: "evaluationrequests",
        from: (page - 1) * limit,
        size: limit,
      })

      return {
        count: total,
        data: documents,
        facets: [],
      }
    }),
} satisfies TRPCRouterRecord
