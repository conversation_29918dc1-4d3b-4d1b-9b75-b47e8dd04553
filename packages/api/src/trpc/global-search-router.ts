/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { TRPCRouterRecord } from "@trpc/server"
import { config as gatewayConfig } from "@impactly/domain/elastic"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { z } from "zod"

import type { Document, GatewayConfig, IndexConfig } from "@kreios/elasticsearch"
import { extractLogicalIndexName, findIndexConfig } from "@kreios/elasticsearch/utils"

import { protectedProcedure } from "./trpc"

const gateway = container.resolve("gateway")

type SearchDocument = Document & {
  aggregateId: string
}

/** Type utility to create the return type of SearchAggreagation for a specific index
 * @typeParam TName - The name of the index
 * @typeParam TDocument - The type of the document in the index
 */
interface BaseBucket<TName extends string, TDocument> {
  key: `${TName}_${string}`
  doc_count: number
  top_hits_per_index: {
    hits: {
      hits: [
        {
          _source: TDocument
        },
      ]
    }
  }
}

/**
 * Type utility to get the search aggreagation buckets for a gateway config
 * @typeParam TConfig - The gateway config
 */
type ExtractSearchBuckets<TConfig extends GatewayConfig> = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [Index in TConfig["indices"][number] as Index["name"]]: Index extends IndexConfig<infer TName, any, infer TDocument>
    ? BaseBucket<TName, TDocument>
    : never
}[TConfig["indices"][number]["name"]]

type GlobalSearchAggregation = {
  byIndex: {
    buckets: ExtractSearchBuckets<typeof gatewayConfig>[]
  }
}

/**
 * Type utility to extract the search result from the gateway response
 * @typeParam T - The search aggreagation buckets
 * @returns The an array of { type: string, items: SearchDocument[] } objects
 */
type ExtractSearchResult<T extends BaseBucket<string, SearchDocument>[]> = T extends (infer U)[]
  ? U extends BaseBucket<infer TName, infer TDocument>
    ? { type: TName; items: (TDocument & { href: string })[] }[]
    : never
  : never

export const globalSearchRouter = {
  searchAll: protectedProcedure.input(z.string()).query(async ({ input: search, ctx: { portalUser } }) => {
    const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

    if (!(isAdmin || isClientAdmin)) return []

    const {
      aggregations: { byIndex },
    } = await gateway.search<SearchDocument, GlobalSearchAggregation>({
      index: gatewayConfig.indices.map((index) => index.name),
      query: {
        multi_match: {
          query: search,
          fields: ["*"],
        },
      },
      size: 0,
      aggs: {
        byIndex: {
          terms: { field: "_index" },
          aggs: {
            top_hits_per_index: {
              top_hits: {
                size: 3,
              },
            },
          },
        },
      },
    })

    const searchResults = []
    for (const bucket of byIndex.buckets) {
      const indexName = extractLogicalIndexName(gatewayConfig, bucket.key)
      const index = findIndexConfig(gatewayConfig, indexName)

      if (index?.buildUrl) {
        const { buildUrl } = index

        searchResults.push({
          type: index.displayName,
          items: bucket.top_hits_per_index.hits.hits.map((hit) => {
            return {
              ...hit._source,
              href: buildUrl(hit._source.aggregateId),
            }
          }),
        })
      }
    }

    return searchResults as ExtractSearchResult<(typeof byIndex)["buckets"]>
  }),
} satisfies TRPCRouterRecord
