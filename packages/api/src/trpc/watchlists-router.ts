/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { WatchlistDocument } from "@impactly/domain/watchlists/elastic"
import type { TRPCRouterRecord } from "@trpc/server"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { watchlistEventStore } from "@impactly/domain/watchlists/index"
import { z } from "zod"

import { createIndexDataLoaderFactory } from "@kreios/api/utils"

import { createLocalizedError, ErrorTranslationKeys } from "../utils/localized-error"
import { protectedProcedure } from "./trpc"

const gateway = container.resolve("gateway")

const idSchema = z.string().describe("Aggregate ID")

const createIndexDataLoader = createIndexDataLoaderFactory(gateway)

export const watchlistsRouter = {
  history: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
    const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

    if (!(isAdmin || isClientAdmin)) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

    const { events } = await watchlistEventStore.getEvents(input)
    return events
  }),

  get: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
    const { aggregate } = await watchlistEventStore.getAggregate(input)
    if (!aggregate) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.WATCHLIST_NOT_FOUND)

    const portalUserId = portalUser.aggregateId

    if (portalUserId !== aggregate.portalUserId) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

    const companyLoader = createIndexDataLoader("companies")
    const naceCodeLoader = createIndexDataLoader("nacecodes")

    return {
      ...aggregate,
      entries: await Promise.all(
        aggregate.entries.map(async (entry) => {
          const company = await companyLoader.load(entry.companyId)

          return {
            ...entry,
            company: {
              ...company,
              businessActivities: await Promise.all(
                company.businessActivities.map(async (activity) => ({
                  ...activity,
                  naceCode: activity.naceCodeId ? await naceCodeLoader.load(activity.naceCodeId) : undefined,
                }))
              ),
            },
          }
        })
      ),
    }
  }),

  list: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).optional().default(1),
        limit: z.number().min(5).optional().default(10),
        search: z.string().optional(),
      })
    )
    .query(async ({ input: { limit, page, search }, ctx: { portalUser } }) => {
      const portalUserId = portalUser.aggregateId

      // Now search for watchlists using the found portalUserId
      const { total, documents } = await gateway.search<WatchlistDocument, never>({
        query: {
          bool: {
            must: [
              { term: { portalUserId: portalUserId } },
              ...(search ? [{ multi_match: { fuzziness: 0.5, query: search, fields: ["label", "description"] } }] : []),
            ],
          },
        },
        index: "watchlists",
        from: (page - 1) * limit,
        size: limit,
      })

      return {
        count: total,
        data: documents,
        facets: [],
      }
    }),
} satisfies TRPCRouterRecord
