/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { companiesRouter } from "./companies-router"
import { evaluationRequestsRouter } from "./evaluation-requests-router"
import { globalSearchRouter } from "./global-search-router"
import { orderRouter } from "./order-router"
import { portalUserRouter } from "./portal-user-router"
import { surveysRouter } from "./survey-router"
import { createTRPCRouter } from "./trpc"
import { watchlistsRouter } from "./watchlists-router"

export { createTRPCContext } from "./trpc"

export const appRouter = createTRPCRouter({
  globalSearch: globalSearchRouter,
  companies: companiesRouter,
  watchlists: watchlistsRouter,
  order: orderRouter,
  evaluationRequests: evaluationRequestsRouter,
  user: portalUserRouter,
  surveys: surveysRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
