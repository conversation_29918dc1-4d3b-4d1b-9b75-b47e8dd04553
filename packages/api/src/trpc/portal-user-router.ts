/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { portalUserEventStore } from "@impactly/domain"
import { service as integrationService } from "@impactly/integration/backend"
import { PortalUserCaptureChangeProvider } from "@impactly/jobs/cdc/portal-users"
import { CaptureChangeService } from "@impactly/jobs/cdc/service"
import { z } from "zod"

import { runSync } from "@kreios/utils/sync-execution-context"

import { createLocalizedError, ErrorTranslationKeys } from "../utils/localized-error"
import { createTRPCRouter, protectedProcedure } from "./trpc"

const gateway = container.resolve("gateway")

const captureChangeService = new CaptureChangeService(gateway, {
  "portal-users": new PortalUserCaptureChangeProvider(integrationService, portalUserEventStore),
})

export const portalUserRouter = createTRPCRouter({
  get: protectedProcedure.query(async ({ ctx: { portalUser } }) => {
    try {
      const { aggregate } = await portalUserEventStore.getExistingAggregate(portalUser.aggregateId)

      if (!aggregate.companyId) {
        return aggregate
      }
      const company = await gateway.findFirst("companies", {
        match: { _id: aggregate.companyId },
      })
      return company ? { ...aggregate, companyName: company.name } : aggregate
    } catch (error) {
      console.error("Failed to get portal user:", portalUser.aggregateId, error)
      throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.PORTAL_USER_NOT_FOUND)
    }
  }),
  update: protectedProcedure
    .input(
      z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        hasGivenConsent: z.boolean(),
      })
    )
    .mutation(async ({ ctx: { portalUser }, input }) => {
      const updatePayload = {
        ...input,
        requestedBy: portalUser.email,
        id: portalUser.aggregateId,
      }
      const updateUserResult = await integrationService.updatePortalUser(updatePayload)

      if (!updateUserResult.updatePortalUser?.portalUser) {
        console.error("Failed to update portal user:", portalUser.aggregateId)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.PORTAL_USER_UPDATE_FAILED)
      }

      // Run elastic reindex in sync context to avoid race condition
      // where the portal user is not updated in time before the user is redirected a different page
      await runSync(() =>
        captureChangeService.captureChange("portal-users", updateUserResult.updatePortalUser!.portalUser!)
      )

      const { aggregate } = await portalUserEventStore.getExistingAggregate(portalUser.aggregateId)

      return aggregate
    }),
})
