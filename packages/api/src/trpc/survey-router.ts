/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { SurveyBatchDocument } from "@impactly/domain/survey-batches/elastic"
import type { surveyBatchAggregateSchema } from "@impactly/domain/survey-batches/index"
import type { SurveyDocument } from "@impactly/domain/surveys/elastic"
import type { TRPCRouterRecord } from "@trpc/server"
import { portalUserEventStore, surveyEventStore } from "@impactly/domain"
import { getPortalUserRoles } from "@impactly/domain/portal-users/utils/get-portal-user-roles"
import { surveyBatchEventStore } from "@impactly/domain/survey-batches/index"
import { service as integrationService, SurveyStatusEnum, SurveyTemplateEnum } from "@impactly/integration/backend"
import { PortalUserCaptureChangeProvider } from "@impactly/jobs/cdc/portal-users"
import { CaptureChangeService } from "@impactly/jobs/cdc/service"
import { SurveyBatchCaptureChangeProvider } from "@impactly/jobs/cdc/survey-batches"
import { SurveyCaptureChangeProvider } from "@impactly/jobs/cdc/surveys"
import { getLocale, getTranslations } from "next-intl/server"
import { z } from "zod"

import type { GlobalAggregations } from "@kreios/api/utils"
import type { TermsAggregation } from "@kreios/elasticsearch"
import {
  createExclusiveFacetAggregations,
  createFacet,
  createFacets,
  createIndexDataLoaderFactory,
  transformGlobalAggregations,
} from "@kreios/api/utils"
import { createMapper } from "@kreios/utils/create-mapper"
import { entries, fromEntries } from "@kreios/utils/entries"
import { isTruthy } from "@kreios/utils/isTruthy"
import { toLowerCase } from "@kreios/utils/to-lower-case"

import type { Language } from "../generated-schemas"
import selfAssessmentSchema from "../../../../apps/app/src/survey-templates/self_assessment.json"
import surveyFormSchemaJSON from "../../../../apps/app/src/survey-templates/universal_v1.schema.json"
import { getSelfAssessmentSchema, getSurveyFormSchema } from "../generated-schemas"
import { extractEnumPaths, reverseEnumFields, translateEnumValues } from "../utils/enum-utils"
import { flattenObject, transformFlatJson } from "../utils/flat-json"
import { createLocalizedError, ErrorTranslationKeys } from "../utils/localized-error"
import { calculateProgress } from "../utils/survey-progress"
import { protectedProcedure } from "./trpc"

const gateway = container.resolve("gateway")

const createIndexDataLoader = createIndexDataLoaderFactory(gateway)

const captureChangesService = new CaptureChangeService(gateway, {
  "survey-batches": new SurveyBatchCaptureChangeProvider(integrationService, surveyBatchEventStore),
  surveys: new SurveyCaptureChangeProvider(integrationService, surveyEventStore),
  "portal-users": new PortalUserCaptureChangeProvider(integrationService, portalUserEventStore),
})
const transformZodEnumToGraphqlEnum = (
  zodEnum: z.infer<typeof surveyBatchAggregateSchema.shape.template._def.innerType>
): SurveyTemplateEnum => {
  switch (zodEnum) {
    case "UNIFIED_QUESTIONNAIRE_V1":
      return SurveyTemplateEnum.UnifiedQuestionnaireV1
    case "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1":
      return SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1
  }
}

const paginationSchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(5).optional().default(10),
  search: z.string().optional(),
  template: z.array(z.string()).optional(),
})

const calculatePercentage = (value: number | undefined, total: number) => {
  // Handling both undefined & NaN when dividing by 0
  if (!value || !total) return 0
  return Math.round((value / total) * 100)
}

const locale = (await getLocale()) as Language
const surveyFormSchema = getSurveyFormSchema(locale)
const selfAssessmentFormSchema = getSelfAssessmentSchema(locale)

const surveyEnumPaths = extractEnumPaths(surveyFormSchema)
const selfAssessmentEnumPaths = extractEnumPaths(selfAssessmentFormSchema)

type SurveyFormSections = {
  general: z.infer<typeof surveyFormSchema.shape.general>
  climate_impact: z.infer<typeof surveyFormSchema.shape.climate_impact>
  green_transition: z.infer<typeof surveyFormSchema.shape.green_transition>
  nature: z.infer<typeof surveyFormSchema.shape.nature>
  social_governance: z.infer<typeof surveyFormSchema.shape.social_governance>
  nfrd_reporting: z.infer<typeof surveyFormSchema.shape.nfrd_reporting>
}

type SelfAssessmentFormSections = {
  general: z.infer<typeof selfAssessmentFormSchema.shape.general>
  esg_management_strategy: z.infer<typeof selfAssessmentFormSchema.shape.esg_management_strategy>
  climate_impact_energy_use: z.infer<typeof selfAssessmentFormSchema.shape.climate_impact_energy_use>
  managing_environmental_resources: z.infer<typeof selfAssessmentFormSchema.shape.managing_environmental_resources>
  workforce_social_responsibility: z.infer<typeof selfAssessmentFormSchema.shape.workforce_social_responsibility>
  business_ethics_governance: z.infer<typeof selfAssessmentFormSchema.shape.business_ethics_governance>
}

export const surveysRouter = {
  // Survey Batch Routes
  list: protectedProcedure
    .input(paginationSchema)
    .query(async ({ input: { limit, page, search, template }, ctx: { portalUser } }) => {
      const { isCompanyUser, isClientAdmin } = getPortalUserRoles(portalUser)
      const mustConditions = []
      if (template && template.length > 0) {
        mustConditions.push({
          terms: { template },
        })
      }

      // Get survey batches with pagination
      const { total, documents: batches } = await gateway.search<SurveyBatchDocument, never>({
        query: {
          bool: {
            must: [...(isClientAdmin ? [{ term: { "tenant.id": portalUser.tenantId } }] : []), ...mustConditions],
            should: search
              ? [
                  {
                    multi_match: {
                      query: search,
                      fields: ["name"],
                      fuzziness: 2.5,
                    },
                  },
                ]
              : [],
            minimum_should_match: search ? 1 : 0,
          },
        },
        index: "surveybatches",
        from: (page - 1) * limit,
        size: limit,
        sort: [{ createdAt: { order: "desc", unmapped_type: "date" } }],
      })

      // Early return if no batches found
      if (!batches.length) {
        return {
          count: 0,
          data: [],
        }
      }

      // Get aggregated stats for all batches
      const { aggregations } = await gateway.search<
        SurveyDocument,
        {
          by_batch: {
            buckets: Array<{
              key: string
              doc_count: number
              status_counts: {
                buckets: Array<{
                  key: "OPEN" | "NOT_STARTED" | "IN_PROGRESS" | "COMPLETE"
                  doc_count: number
                }>
              }
            }>
          }
        }
      >({
        index: "surveys",
        size: 0,
        query: {
          bool: {
            must: [
              {
                terms: {
                  batchId: batches.map((b) => b.aggregateId),
                },
              },
              ...(isCompanyUser ? [{ term: { companyId: portalUser.companyId ?? "" } }] : []),
            ],
            should: search
              ? [
                  {
                    multi_match: {
                      fuzziness: 2.5,
                      query: search,
                      fields: ["company.name"],
                    },
                  },
                ]
              : undefined,
          },
        },
        aggs: {
          by_batch: {
            terms: { field: "batchId" },
            aggs: {
              status_counts: {
                terms: { field: "status" },
              },
            },
          },
        },
      })

      // Map batches with their stats
      const batchesWithStats = batches.map((batch) => {
        const batchStats = aggregations.by_batch.buckets.find((b) => b.key === batch.aggregateId)
        const total = batchStats?.doc_count ?? 0

        // Initialize status counts

        // Populate actual counts
        const statusCounts = batchStats?.status_counts.buckets.reduce(
          (acc, bucket) => {
            acc[bucket.key] = bucket.doc_count
            return acc
          },
          {
            OPEN: 0,
            NOT_STARTED: 0,
            IN_PROGRESS: 0,
            COMPLETE: 0,
            SUBMITTED: 0,
            SENT: 0,
            PENDING: 0,
            CANCELLED: 0,
          }
        )

        // Calculate percentages
        const statsPercentage = {
          open: calculatePercentage(statusCounts?.OPEN, total),
          notStarted: calculatePercentage(statusCounts?.NOT_STARTED, total),
          inProgress: calculatePercentage(statusCounts?.IN_PROGRESS, total),
          complete: calculatePercentage(statusCounts?.COMPLETE, total),
          submitted: calculatePercentage(statusCounts?.SUBMITTED, total),
          sent: calculatePercentage(statusCounts?.SENT, total),
          pending: calculatePercentage(statusCounts?.PENDING, total),
          cancelled: calculatePercentage(statusCounts?.CANCELLED, total),
        }

        return {
          ...batch,
          stats: statsPercentage,
          companyCount: total,
        }
      })
      return {
        count: total,
        data: batchesWithStats,
      }
    }),

  get: protectedProcedure.input(z.string()).query(async ({ input: id, ctx: { portalUser } }) => {
    const t = await getTranslations()
    const { isClientAdmin, isAdmin } = getPortalUserRoles(portalUser)

    if (!(isClientAdmin || isAdmin)) throw createLocalizedError("FORBIDDEN", t(ErrorTranslationKeys.FORBIDDEN))

    const { aggregate: batch } = await surveyBatchEventStore.getAggregate(id)
    if (!batch) throw createLocalizedError("NOT_FOUND", t(ErrorTranslationKeys.SURVEY_BATCH_NOT_FOUND))

    const { documents: surveys } = await gateway.search<SurveyDocument, never>({
      query: {
        bool: {
          must: [{ term: { batchId: id } }],
        },
      },
      index: "surveys",
      size: 10000,
    })

    const total = surveys.length
    const stats = surveys.reduce(
      (acc, survey) => {
        acc[toLowerCase(survey.status)]++
        return acc
      },
      {
        open: 0,
        not_started: 0,
        in_progress: 0,
        complete: 0,
        submitted: 0,
        sent: 0,
        pending: 0,
        cancelled: 0,
        processing: 0,
      }
    )

    return {
      ...batch,
      stats: fromEntries(entries(stats).map(([key, value]) => [key, calculatePercentage(value, total)])),
    }
  }),

  getCompanySurveys: protectedProcedure
    .input(
      z
        .object({
          filters: z
            .object({
              status: z.array(z.string()).optional(),
              template: z.array(z.string()).optional(),
              batchId: z.string().optional(),
            })
            .optional()
            .default({}),
        })
        .optional()
        .default({})
    )
    .query(async ({ ctx: { portalUser }, input }) => {
      if (!portalUser.companyId)
        return {
          stats: {
            pending: 0,
            open: 0,
            not_started: 0,
            in_progress: 0,
            complete: 0,
            submitted: 0,
            sent: 0,
            cancelled: 0,
            processing: 0,
          },
        }

      // Build the query conditions
      const mustConditions: Array<Record<string, unknown>> = [{ term: { companyId: portalUser.companyId } }]

      // Add status filter if provided
      if (input.filters.status && input.filters.status.length > 0) {
        mustConditions.push({
          terms: { status: input.filters.status },
        })
      }

      // Add template filter if provided
      if (input.filters.template && input.filters.template.length > 0) {
        mustConditions.push({
          terms: { template: input.filters.template },
        })
      }

      // Add batchId filter if provided
      if (input.filters.batchId) {
        mustConditions.push({
          term: { batchId: input.filters.batchId },
        })
      }

      const { documents: surveys } = await gateway.search<SurveyDocument, never>({
        query: {
          bool: {
            must: mustConditions,
          },
        },
        index: "surveys",
        size: 10000,
      })

      const total = surveys.length
      const stats = surveys.reduce(
        (acc, survey) => {
          acc[toLowerCase(survey.status)]++
          return acc
        },
        {
          open: 0,
          not_started: 0,
          in_progress: 0,
          complete: 0,
          submitted: 0,
          sent: 0,
          pending: 0,
          cancelled: 0,
          processing: 0,
        }
      )

      return {
        stats: fromEntries(entries(stats).map(([key, value]) => [key, calculatePercentage(value, total)])),
      }
    }),

  listSurveys: protectedProcedure
    .input(
      z.object({
        page: z.number().min(1).optional().default(1),
        limit: z.number().min(5).optional().default(10),
        search: z.string().optional(),
        filters: z
          .object({
            status: z.array(z.string()).optional(),
            batchId: z.string().optional(),
            template: z.array(z.string()).optional(), // Add template filter
            statusChangedAt: z
              .union([
                z.date().transform((date) => ({ from: date, to: date })),
                z.object({ to: z.date().optional(), from: z.date().optional() }).optional(),
              ])
              .optional(),
          })
          .optional()
          .default({}),
        sort: z.record(z.enum(["asc", "desc"])).optional(),
      })
    )

    .query(async ({ ctx: { portalUser }, input: { page, limit, search, filters, sort } }) => {
      const { isCompanyUser, isObserver } = getPortalUserRoles(portalUser)

      if ((isCompanyUser || isObserver) && !portalUser.companyId) {
        return {
          data: [],
          count: 0,
          facets: [],
        }
      }

      const baseFilters = {
        ...filters,
        ...((isCompanyUser || isObserver) && portalUser.companyId ? { companyId: portalUser.companyId } : {}),
      }
      const filterMap = createMapper(baseFilters)({
        status: (statuses) => ({ terms: { status: statuses } }),
        batchId: (batchId) => ({ term: { batchId } }),
        template: (templates) => ({ terms: { template: templates } }),
        statusChangedAt: (dates) => ({
          range: { statusChangedAt: { gte: dates.from?.toISOString(), lte: dates.to?.toISOString() } },
        }),
        companyId: (id) => ({ term: { companyId: id } }),
      })

      const activeFilters = [
        ...Object.values(filterMap).filter(isTruthy),
        ...(search
          ? [
              {
                bool: {
                  should: [
                    // Exact and fuzzy matching
                    {
                      multi_match: {
                        query: search,
                        fields: ["tenant.name", "company.name", "documents.fileName", "users.email"],
                        fuzziness: "AUTO",
                      },
                    },
                    // Prefix matching for partial matches
                    {
                      match_phrase_prefix: {
                        "company.name": {
                          query: search,
                        },
                      },
                    },
                    {
                      match_phrase_prefix: {
                        "tenant.name": {
                          query: search,
                        },
                      },
                    },
                  ],
                  minimum_should_match: 1,
                },
              },
            ]
          : []),
      ]

      const { total, documents, aggregations } = await gateway.search<
        SurveyDocument,
        GlobalAggregations<{
          status: TermsAggregation
        }>
      >({
        query: { bool: { must: activeFilters } },
        index: "surveys",
        from: (page - 1) * limit,
        size: limit,
        sort: [
          ...(sort
            ? Object.entries(sort).map(([field, order]) =>
                field === "company_name" ? { "company.name.keyword": order } : { [field]: order }
              )
            : []),
          { createdAt: { order: "desc", unmapped_type: "date" } },
        ],
        aggs: createExclusiveFacetAggregations(undefined, activeFilters, {
          status: {
            terms: { field: "status" },
          },
        }),
      })

      const normilizedAggregations = transformGlobalAggregations(aggregations)
      const companyLoader = createIndexDataLoader("companies")

      return {
        data: await Promise.all(
          documents.map(async (survey) => {
            const company = survey.companyId ? await companyLoader.load(survey.companyId) : null
            const isOverdue =
              survey.deadline && new Date(survey.deadline) < new Date() && survey.status !== SurveyStatusEnum.Submitted

            return {
              aggregateId: survey.aggregateId,
              company: company,
              status: survey.status,
              statusChangedAt: survey.statusChangedAt,
              lastReminder: survey.lastReminder,
              progress: survey.progress,
              batchId: survey.batchId,
              template: survey.template,
              deadline: survey.deadline,
              issuer: survey.tenant?.name,
              overdue: isOverdue,
              submittedAt: survey.submittedAt,
            }
          })
        ),
        count: total,
        facets: await createFacets(normilizedAggregations, {
          status: createFacet({
            column: "status",
            title: "status",
            label: (id) => id.toLowerCase(),
            load: async (id: "NOT_STARTED" | "OPEN" | "IN_PROGRESS" | "COMPLETE" | "CANCELLED") => id,
          }),
        }),
      }
    }),
  getSurvey: protectedProcedure.input(z.string()).query(async ({ input: id }) => {
    const t = await getTranslations()

    const survey = await integrationService.getSurvey({ id })

    if (!survey.id) throw createLocalizedError("NOT_FOUND", t(ErrorTranslationKeys.SURVEY_NOT_FOUND))

    console.log("----------------------------------------------------------------")
    console.log("Survey: ", { survey })
    console.log("----------------------------------------------------------------")
    // Get company details
    const company = await gateway.findFirst("companies", {
      match: { aggregateId: survey.company?.id },
    })
    if (!company) throw createLocalizedError("NOT_FOUND", t(ErrorTranslationKeys.COMPANY_NOT_FOUND))

    // Transform and process formData
    let transformedFormData
    let reversedFormData
    let formData

    try {
      transformedFormData = survey.formData ? transformFlatJson(surveyFormSchemaJSON, survey.formData) : undefined
      reversedFormData = transformedFormData ? reverseEnumFields(transformedFormData, surveyEnumPaths) : undefined
      formData = reversedFormData as SurveyFormSections
    } catch (error) {
      console.error("Error transforming survey formData:", { error: JSON.stringify(error) })
      transformedFormData = undefined
      reversedFormData = undefined
      formData = undefined
    }

    // Transform and process prefilledData
    let prefilledData
    try {
      const prefilledDataObj = survey.prefilledData as { formData?: Record<string, unknown> } | undefined
      const transformedPrefilledData = prefilledDataObj?.formData
        ? transformFlatJson(surveyFormSchemaJSON, prefilledDataObj.formData)
        : undefined
      prefilledData = transformedPrefilledData
        ? reverseEnumFields(transformedPrefilledData, surveyEnumPaths)
        : undefined
    } catch (error) {
      console.error("Error transforming survey prefilledData:", error)
      prefilledData = undefined
    }

    return {
      id: survey.id,
      status: survey.status,
      company: {
        id: company.aggregateId,
        name: company.name,
      },
      batchId: survey.batch?.id,
      progress: survey.progress,
      formData,
      createdAt: survey.createdAt,
      updatedAt: survey.updatedAt,
      statusChangedAt: survey.statusChangedAt,
      lastReminder: survey.lastReminder,
      prefilledData: prefilledData ? { formData: prefilledData as SurveyFormSections } : undefined,
    }
  }),

  getSelfAssessment: protectedProcedure.input(z.string()).query(async ({ input: id }) => {
    const t = await getTranslations()

    const selfAssessment = await integrationService.getSurvey({ id })

    if (!selfAssessment.id || selfAssessment.template !== SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1)
      throw createLocalizedError("NOT_FOUND", t(ErrorTranslationKeys.SURVEY_NOT_FOUND))
    console.log("----------------------------------------------------------------")
    console.log("Self Assessment: ", { selfAssessment })
    console.log("----------------------------------------------------------------")

    // Get company details
    const company = await gateway.findFirst("companies", {
      match: { aggregateId: selfAssessment.company?.id },
    })
    if (!company) throw createLocalizedError("NOT_FOUND", t(ErrorTranslationKeys.COMPANY_NOT_FOUND))

    // Transform and process formData
    let transformedFormData
    let reversedFormData
    let formData

    try {
      transformedFormData = selfAssessment.formData
        ? transformFlatJson(selfAssessmentSchema, selfAssessment.formData)
        : undefined
      reversedFormData = transformedFormData
        ? reverseEnumFields(transformedFormData, selfAssessmentEnumPaths)
        : undefined
      formData = reversedFormData as SelfAssessmentFormSections
    } catch (error) {
      console.error("Error transforming self-assessment formData:", error)
      transformedFormData = undefined
      reversedFormData = undefined
      formData = undefined
    }

    // Transform and process prefilledData
    let prefilledData
    try {
      const prefilledDataObj = selfAssessment.prefilledData as Record<string, unknown> | undefined
      const transformedPrefilledData = prefilledDataObj
        ? transformFlatJson(selfAssessmentSchema, prefilledDataObj)
        : undefined
      prefilledData = transformedPrefilledData
        ? reverseEnumFields(transformedPrefilledData, selfAssessmentEnumPaths)
        : undefined
    } catch (error) {
      console.error("Error transforming self-assessment prefilledData:", error)
      prefilledData = undefined
    }

    return {
      id: selfAssessment.id,
      status: selfAssessment.status,
      company: {
        id: company.aggregateId,
        name: company.name,
      },
      batchId: selfAssessment.batch?.id,
      progress: selfAssessment.progress,
      formData,
      createdAt: selfAssessment.createdAt,
      updatedAt: selfAssessment.updatedAt,
      statusChangedAt: selfAssessment.statusChangedAt,
      lastReminder: selfAssessment.lastReminder,
      prefilledData: prefilledData ? { formData: prefilledData as SelfAssessmentFormSections } : undefined,
    }
  }),

  submitSurveyForm: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        formData: surveyFormSchema,
      })
    )
    .mutation(async ({ input: { id, formData } }) => {
      const t = await getTranslations("surveyEnums")

      const survey = await gateway.findFirst("surveys", {
        match: { aggregateId: id },
      })
      if (!survey) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.SURVEY_NOT_FOUND)

      const translatedFormData = translateEnumValues(formData, t, surveyEnumPaths)

      const flatObject = flattenObject(translatedFormData)

      const updatePayload = {
        // requestedBy: portalUser.email, // The update mutation does not require 'requestedBy.' Previously, we were using the 'submitSurvey' mutation, which only had 'requestedBy' and 'surveyId,' but we need to update the survey and 'formData' as well.
        surveyId: survey.aggregateId,
        formData: flatObject,
        status: SurveyStatusEnum.Complete,
        progress: 100,
      }
      const createdSurvey = await integrationService.updateSurvey(updatePayload).catch((error) => {
        console.error("Failed to update survey:", error)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_UPDATE_FAILED)
      })

      await captureChangesService.captureChange("surveys", createdSurvey)

      const { aggregate } = await surveyEventStore.getExistingAggregate(survey.aggregateId)

      return aggregate
    }),

  submitSelfAssessmentForm: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        formData: selfAssessmentFormSchema,
      })
    )
    .mutation(async ({ input: { id, formData } }) => {
      const t = await getTranslations("surveyEnums")

      const selfAssessment = await gateway.findFirst("surveys", {
        match: { aggregateId: id },
      })
      if (!selfAssessment || selfAssessment.template !== SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1)
        throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.SURVEY_NOT_FOUND)

      const translatedFormData = translateEnumValues(formData, t, selfAssessmentEnumPaths)

      const flatObject = flattenObject(translatedFormData)

      const updatePayload = {
        surveyId: selfAssessment.aggregateId,
        formData: flatObject,
        status: SurveyStatusEnum.Complete,
        progress: 100,
      }
      const createdSurvey = await integrationService.updateSurvey(updatePayload).catch((error) => {
        console.error("Failed to update survey:", error)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_UPDATE_FAILED)
      })

      await captureChangesService.captureChange("surveys", createdSurvey)

      const { aggregate } = await surveyEventStore.getExistingAggregate(selfAssessment.aggregateId)

      return aggregate
    }),

  cancelSurvey: protectedProcedure
    .input(
      z.object({
        surveyIds: z.array(z.string()).min(1),
      })
    )
    .mutation(async ({ ctx: { portalUser }, input: { surveyIds } }) => {
      const { isClientAdmin } = getPortalUserRoles(portalUser)
      if (!isClientAdmin) throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

      const surveys = await integrationService.updateSurveyStatus({
        status: SurveyStatusEnum.Cancelled,
        surveyIds,
      })

      if (!surveys.length) {
        console.error("Failed to update surveys:", surveyIds)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_UPDATE_FAILED)
      }

      await Promise.all(surveys.map((survey) => captureChangesService.captureChange("surveys", survey)))
    }),

  updateSurveyStatus: protectedProcedure
    .input(
      z.object({
        surveyId: z.string(),
        status: z.enum([
          SurveyStatusEnum.Cancelled,
          SurveyStatusEnum.Complete,
          SurveyStatusEnum.InProgress,
          SurveyStatusEnum.NotStarted,
          SurveyStatusEnum.Open,
          SurveyStatusEnum.Pending,
          SurveyStatusEnum.Sent,
          SurveyStatusEnum.Submitted,
          SurveyStatusEnum.Processing,
        ]),
        progress: z.number().min(0).max(100).optional(),
      })
    )
    .mutation(async ({ input: { surveyId, status, progress } }) => {
      try {
        await integrationService.updateSurvey({ status, surveyId, progress })
        return { status: "Success", message: "Survey status updated successfully" }
      } catch (error) {
        console.error("Error occurred while updating status", { error: JSON.stringify(error) })
      }
    }),

  createBatch: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, "Batch name is required"),
        template: z.enum([
          SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1,
          SurveyTemplateEnum.UnifiedQuestionnaireV1,
        ]),
      })
    )
    .mutation(async ({ input, ctx: { portalUser } }) => {
      const createBatchPayload = {
        name: input.name,
        template: input.template,
        description: "",
        tenantId: portalUser.tenantId,
      }

      const surveyBatch = await integrationService.createSurveyBatch(createBatchPayload).catch((error) => {
        console.error("Failed to create survey batch:", error)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_CREATE_FAILED)
      })

      await captureChangesService.captureChange("survey-batches", surveyBatch)

      const { aggregate } = await surveyBatchEventStore.getExistingAggregate(surveyBatch.id)

      return aggregate
    }),
  sendSurvey: protectedProcedure
    .input(
      z.object({
        batchId: z.string(),
        template: z.enum(["UNIFIED_QUESTIONNAIRE_V1", "SELF_ASSESSMENT_UNIFIED_QUESTIONNAIRE_V1"]),
        companySurveys: z.array(
          z.object({
            companyId: z.string(),
            contactEmails: z.array(z.string().email()),
            deadline: z.string().datetime(),
            financialYear: z.number(),
          })
        ),
      })
    )
    .mutation(async ({ input: { batchId, template, companySurveys }, ctx: { portalUser } }) => {
      const batch = await gateway.findFirst("surveybatches", { match: { aggregateId: batchId } })
      if (!batch) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.SURVEY_BATCH_NOT_FOUND)

      const sendSurveyPayload = {
        requestedBy: portalUser.email,
        template: transformZodEnumToGraphqlEnum(template),
        batchId,
        companySurveys,
      }

      try {
        const surveys = await integrationService.createSurvey(sendSurveyPayload)
        return surveys
      } catch (error) {
        console.error("Failed to create surveys:", { error })
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_CREATE_FAILED)
      }
    }),
  autoSaveSurveyForm: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        progress: z.number().min(0).max(100).optional(),
        status: z
          .enum([
            SurveyStatusEnum.Cancelled,
            SurveyStatusEnum.Complete,
            SurveyStatusEnum.InProgress,
            SurveyStatusEnum.NotStarted,
            SurveyStatusEnum.Open,
            SurveyStatusEnum.Pending,
            SurveyStatusEnum.Sent,
            SurveyStatusEnum.Submitted,
            SurveyStatusEnum.Processing,
          ])
          .optional(),
        // Use a more permissive schema for auto-save
        // This allows for any form data structure during auto-save for both survey types
        formData: z.union([
          // Regular survey schema
          z
            .object({
              general: z.record(z.string(), z.any()).optional(),
              climate_impact: z.record(z.string(), z.any()).optional(),
              green_transition: z.record(z.string(), z.any()).optional(),
              nature: z.record(z.string(), z.any()).optional(),
              social_governance: z.record(z.string(), z.any()).optional(),
              nfrd_reporting: z.record(z.string(), z.any()).optional(),
            })
            .partial(),
          // Self-assessment schema
          z
            .object({
              general: z.record(z.string(), z.any()).optional(),
              esg_management_strategy: z.record(z.string(), z.any()).optional(),
              climate_impact_energy_use: z.record(z.string(), z.any()).optional(),
              managing_environmental_resources: z.record(z.string(), z.any()).optional(),
              workforce_social_responsibility: z.record(z.string(), z.any()).optional(),
              business_ethics_governance: z.record(z.string(), z.any()).optional(),
            })
            .partial(),
        ]),
      })
    )
    .mutation(async ({ input: { id, formData, status } }) => {
      const t = await getTranslations("surveyEnums")

      const survey = await gateway.findFirst("surveys", {
        match: { aggregateId: id },
      })
      if (!survey) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.SURVEY_NOT_FOUND)

      // Determine which schema and enum paths to use based on the survey template
      const isSelfAssessment = survey.template === SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1
      const schemaToUse = isSelfAssessment ? selfAssessmentSchema : surveyFormSchemaJSON
      const enumPathsToUse = isSelfAssessment ? selfAssessmentEnumPaths : surveyEnumPaths

      // Translate enum values to match the format expected by the backend
      const translatedFormData = translateEnumValues(formData, t, enumPathsToUse)

      // Use the same flattenObject function as in submitSurveyForm
      const flatObject = flattenObject(translatedFormData)

      // Calculate progress based on required fields, with debug logging
      console.log(`Using ${isSelfAssessment ? "self-assessment" : "survey"} schema for progress calculation`)
      const progress = calculateProgress(formData, schemaToUse, true)

      // If the user is actively working on the form, set status to InProgress
      const newStatus = status ?? (progress > 0 && progress < 100 ? SurveyStatusEnum.InProgress : undefined)

      const updatePayload = {
        surveyId: survey.aggregateId,
        formData: flatObject,
        progress,
        status: newStatus,
      }

      console.log("--------------------------------------------------------------")
      console.log("Progress: ", { progress })
      console.log("--------------------------------------------------------------")

      await integrationService.updateSurvey(updatePayload).catch((error) => {
        console.error("Failed to auto-save survey:", error)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_UPDATE_FAILED)
      })

      // await captureChangesService.captureChange("surveys", updatedSurvey)

      const { aggregate } = await surveyEventStore.getExistingAggregate(survey.aggregateId)

      return aggregate
    }),

  autoSaveSelfAssessmentForm: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        progress: z.number().min(0).max(100).optional(),
        status: z
          .enum([
            SurveyStatusEnum.Cancelled,
            SurveyStatusEnum.Complete,
            SurveyStatusEnum.InProgress,
            SurveyStatusEnum.NotStarted,
            SurveyStatusEnum.Open,
            SurveyStatusEnum.Pending,
            SurveyStatusEnum.Sent,
            SurveyStatusEnum.Submitted,
            SurveyStatusEnum.Processing,
          ])
          .optional(),
        // Use a permissive schema specifically for self-assessment auto-save
        formData: z
          .object({
            general: z.record(z.string(), z.any()).optional(),
            esg_management_strategy: z.record(z.string(), z.any()).optional(),
            climate_impact_energy_use: z.record(z.string(), z.any()).optional(),
            managing_environmental_resources: z.record(z.string(), z.any()).optional(),
            workforce_social_responsibility: z.record(z.string(), z.any()).optional(),
            business_ethics_governance: z.record(z.string(), z.any()).optional(),
          })
          .partial(),
      })
    )
    .mutation(async ({ input: { id, formData, status } }) => {
      const t = await getTranslations("surveyEnums")

      const survey = await gateway.findFirst("surveys", {
        match: { aggregateId: id },
      })
      if (!survey) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.SURVEY_NOT_FOUND)

      // Verify this is a self-assessment survey
      if (survey.template !== SurveyTemplateEnum.SelfAssessmentUnifiedQuestionnaireV1) {
        throw createLocalizedError(
          "BAD_REQUEST",
          "This endpoint is only for self-assessment surveys. Use autoSaveSurveyForm for regular surveys."
        )
      }

      try {
        // Translate enum values to match the format expected by the backend
        const translatedFormData = translateEnumValues(formData, t, selfAssessmentEnumPaths)

        // Use the same flattenObject function as in submitSurveyForm
        const flatObject = flattenObject(translatedFormData)

        // Calculate progress based on required fields
        const progress = calculateProgress(formData, selfAssessmentSchema, true)

        // If the user is actively working on the form, set status to InProgress
        const newStatus = status ?? (progress > 0 && progress < 100 ? SurveyStatusEnum.InProgress : undefined)

        const updatePayload = {
          surveyId: survey.aggregateId,
          formData: flatObject,
          progress,
          status: newStatus,
        }

        console.log("--------------------------------------------------------------")
        console.log("Self-assessment Progress: ", { progress })
        console.log("--------------------------------------------------------------")

        await integrationService.updateSurvey(updatePayload).catch((error) => {
          console.error("Failed to auto-save self-assessment:", error)
          throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_UPDATE_FAILED)
        })

        const { aggregate } = await surveyEventStore.getExistingAggregate(survey.aggregateId)

        return aggregate
      } catch (error) {
        console.error("Error in autoSaveSelfAssessmentForm:", error)
        throw createLocalizedError("INTERNAL_SERVER_ERROR", ErrorTranslationKeys.SURVEY_UPDATE_FAILED)
      }
    }),
} satisfies TRPCRouterRecord
