/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

export const dummyPrefilledData = {
  survey_fin_year: 12,
  survey_revenue_for_year: 12,
  if_sust_vision: true,
  sust_vision: "Sustainability vision description",
  sust_strategy: "Sustainability strategy description",
  if_sustainable_practices: true,
  sustainable_practices: "Sustainability strategy description",
  if_sust_commitments: true,
  sust_commitments: "Sustainability strategy description",
  if_sust_certification: true,
  sust_certification: "Sustainability strategy description",
  if_child_labour_policy: true,
  if_forced_labour_policy: true,
  if_human_trafficking: true,
  if_discrimination_policy: true,
  if_accident_prevention: true,
  other_hr_policy: "Other Human Rights",
  if_bribery_policy: true,
  if_corruption_policy: true,
  if_fraud_policy: true,
  if_whistleb_policy: true,
  if_supplier_policy: true,
  if_aml_policy: true,
  esg_initiatives: "Sustainability Initiatives ",
  total_energy_cons: 1,
  renewable_electricity_cons: 2,
  nonrenewable_electricity_cons: 2,
  renewable_fuels_cons: 1,
  nonrenewable_fuels_cons: 1,
  ghg_scope1: 1,
  ghg_scope1_biogenic: 1,
  ghg_scope2_locationbased: 1,
  ghg_scope2_marketbased: 1,
  ghg_scope1_2_total: 1,
  ghg_total_emissions: 1,
  ghg_scope3_total: 11,
  ghg_scope3_cat_1: 1,
  ghg_scope3_cat_2: 1,
  ghg_scope3_cat_3: 1,
  ghg_scope3_cat_4: 1,
  ghg_scope3_cat_5: 1,
  ghg_scope3_cat_6: 1,
  ghg_scope3_cat_7: 1,
  ghg_scope3_cat_8: 1,
  ghg_scope3_cat_9: 1,
  ghg_scope3_cat_10: 1,
  ghg_scope3_cat_11: 1,
  ghg_scope3_cat_12: 1,
  ghg_scope3_cat_13: 1,
  ghg_scope3_cat_14: 1,
  ghg_scope3_cat_15: 1,
  if_ghg_reduction_goals: true,
  ghggoal: [
    {
      ghggoal_element: "Mõjuala 2 (turupõhine)",
      ghggoal_base_year: 11,
      ghggoal_target_year_st: 11,
      ghggoal_goal_numeric_st: 11,
      ghggoal_target_year_lt: 11,
      ghggoal_goal_numeric_lt: 11,
    },
  ],
  ghggoal_narrative: "oTHER gHHG goals",
  if_transition_plan_existence: true,
  transition_plan_description: "Transition Plan Description ",
  if_physical_risk_identification: true,
  physical_risk_identification: "Physical Risk Identification Description ",
  climate_change_adaptation_actions: "Climate Change",
  insurance_coverage: 1,
  physical_risk_effect: "Physical Climate Risk",
  if_pollution_reporting: true,
  if_pollution_to_air: true,
  if_pollution_to_water: true,
  if_pollution_to_soil: true,
  generation_of_waste: 11,
  recycling_or_reuse: 111,
  use_of_materials: "Use of materials",
  water_withdrawal: 0,
  water_withdrawal_in_area_of_high_water_stress: 10,
  water_consumption: 12,
  if_land_near_sensitive_areas: true,
  land_area_near_biodiversity: 1,
  total_use_of_land: 1,
  total_sealed_area: 1,
  total_nature_oriented_area_on_site: 1,
  total_nature_oriented_area_off_site: 1,
  nr_employees: 1,
  nr_employees_gender_m: 1,
  nr_employees_gender_f: 1,
  nr_employees_gender_other: 1,
  nr_temporary_employees: 1,
  nr_permanent_employees: 0,
  employee_turnover_rate: 1,
  nr_non_employees: 1,
  gender_paygap: 1,
  employees_covered_cb: 1,
  trainings_gender_m: 1,
  trainings_gender_f: 1,
  work_accidents_employees: 22,
  work_fatalities_employees: 21,
  if_minimum_wage_pay: true,
  nr_human_rights_violations: 11,
  nr_human_rights_stakeholders_cases: 11,
  if_abc_convictions: true,
  nr_abc_convictions: 11,
  abc_fines: 11,
  management_board_f: 111,
  management_board_m: 11,
  if_revenue_sensitive_sectors: true,
  rev_controversial_weapons: 11,
  rev_tobacco: 11,
  rev_fossil_fuel: 11,
  rev_chemicals: 11,
  if_benchmark_exclusion: true,
  if_mining_1: true,
  if_oil_10: true,
  if_gas_50: true,
  if_electricity_highghg_50: true,
}
