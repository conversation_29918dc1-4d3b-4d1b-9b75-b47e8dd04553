/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { TRPC_ERROR_CODE_KEY } from "@trpc/server/rpc"
import { TRPCError } from "@trpc/server"

/**
 * Creates a localized error with a translation key instead of a hardcoded message.
 * The frontend will use this key to look up the appropriate translation.
 *
 * @param code The TRPC error code
 * @param translationKey The key in the translation files for this error
 * @param cause Optional cause of the error
 * @returns A TRPCError with the translation key as the message
 */
export function createLocalizedError(code: TRPC_ERROR_CODE_KEY, translationKey: string, cause?: unknown): TRPCError {
  return new TRPCError({
    code,
    message: translationKey,
    cause,
  })
}

/**
 * Common error translation keys
 */
export const ErrorTranslationKeys = {
  // Common errors
  UNAUTHORIZED: "errors.common.unauthorized",
  FORBIDDEN: "errors.common.forbidden",
  NOT_FOUND: "errors.common.notFound",
  BAD_REQUEST: "errors.common.badRequest",
  INTERNAL_SERVER_ERROR: "errors.common.serverError",
  TIMEOUT: "errors.common.timeout",

  // Auth errors
  INVALID_CREDENTIALS: "errors.auth.invalidCredentials",
  SESSION_EXPIRED: "errors.auth.sessionExpired",
  EMAIL_NOT_VERIFIED: "errors.auth.emailNotVerified",
  USER_NOT_FOUND: "errors.auth.userNotFound",

  // Survey errors
  SURVEY_CREATE_FAILED: "errors.surveys.createFailed",
  SURVEY_UPDATE_FAILED: "errors.surveys.updateFailed",
  SURVEY_DELETE_FAILED: "errors.surveys.deleteFailed",
  SURVEY_SEND_FAILED: "errors.surveys.sendFailed",
  SURVEY_NOT_COMPLETE: "errors.surveys.notComplete",
  SURVEY_ALREADY_SUBMITTED: "errors.surveys.alreadySubmitted",
  SURVEY_PAST_DEADLINE: "errors.surveys.pastDeadline",
  SURVEY_NOT_FOUND: "errors.surveys.notFound",
  SURVEY_BATCH_NOT_FOUND: "errors.surveys.batchNotFound",

  // Company errors
  COMPANY_CREATE_FAILED: "errors.companies.createFailed",
  COMPANY_UPDATE_FAILED: "errors.companies.updateFailed",
  COMPANY_DELETE_FAILED: "errors.companies.deleteFailed",
  COMPANY_NOT_FOUND: "errors.companies.notFound",

  // Order errors
  ORDER_SUBMIT_FAILED: "errors.orders.submitFailed",
  ORDER_INVALID_DATA: "errors.orders.invalidData",

  // Portal user errors
  PORTAL_USER_UPDATE_FAILED: "errors.portalUsers.updateFailed",
  PORTAL_USER_NOT_FOUND: "errors.portalUsers.notFound",

  // File errors
  FILE_UPLOAD_FAILED: "errors.files.uploadFailed",
  FILE_DOWNLOAD_FAILED: "errors.files.downloadFailed",
  FILE_INVALID_TYPE: "errors.files.invalidType",

  // Watchlist errors
  WATCHLIST_NOT_FOUND: "errors.watchlists.notFound",
  WATCHLIST_CREATE_FAILED: "errors.watchlists.createFailed",
  WATCHLIST_UPDATE_FAILED: "errors.watchlists.updateFailed",
  WATCHLIST_DELETE_FAILED: "errors.watchlists.deleteFailed",

  // Evaluation request errors
  EVALUATION_REQUEST_NOT_FOUND: "errors.evaluationRequests.notFound",
  EVALUATION_REQUEST_CREATE_FAILED: "errors.evaluationRequests.createFailed",
  EVALUATION_REQUEST_UPDATE_FAILED: "errors.evaluationRequests.updateFailed",
  EVALUATION_REQUEST_DELETE_FAILED: "errors.evaluationRequests.deleteFailed",

  FILE_TOO_LARGE: "errors.files.tooLarge",
}
