/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import type { ZodType } from "zod"
import { ZodArray, ZodDefault, ZodEnum, ZodNullable, ZodObject, ZodOptional } from "zod"

import en from "../../../../apps/app/src/messages/en.json"
import et from "../../../../apps/app/src/messages/et.json"

// Type definitions
// Define a type for valid enum field types
type EnumFieldType = string | number | boolean | null | undefined | Date | Record<string, unknown> | EnumFieldType[]

type Translated<T> = {
  [K in keyof T]: T[K] extends string ? string : T[K] extends object ? Translated<T[K]> : T[K]
}

type EnumPath = string

// Translation data setup
const enEnums = en.surveyEnums as Record<string, string>
const etEnums = et.surveyEnums as Record<string, string>

const reverseEnumMap: Record<string, string> = {
  ...Object.entries(enEnums).reduce(
    (acc, [key, value]) => {
      acc[value] = key
      return acc
    },
    {} as Record<string, string>
  ),
  ...Object.entries(etEnums).reduce(
    (acc, [key, value]) => {
      acc[value] = key
      return acc
    },
    {} as Record<string, string>
  ),
}

// Helper functions
// Helper to build full paths like "general.category"
function buildPath(base: string, key: string): string {
  return base ? `${base}.${key}` : key
}

const reverseEnum = (value: string): string => {
  return reverseEnumMap[value] ?? value
}

// Main exported functions
// Use ZodType<unknown> for stricter typing
export function extractEnumPaths(schema: ZodType<unknown>, basePath = ""): string[] {
  const paths: string[] = []

  // Handle ZodEnum directly
  if (schema instanceof ZodEnum) {
    paths.push(basePath)
    return paths
  }

  // Handle ZodOptional
  if (schema instanceof ZodOptional) {
    return extractEnumPaths(schema.unwrap() as ZodType<unknown>, basePath)
  }

  // Handle ZodNullable
  if (schema instanceof ZodNullable) {
    return extractEnumPaths(schema.unwrap() as ZodType<unknown>, basePath)
  }

  // Handle ZodDefault
  if (schema instanceof ZodDefault) {
    return extractEnumPaths(schema._def.innerType as ZodType<unknown>, basePath)
  }

  // Handle ZodObject
  if (schema instanceof ZodObject) {
    const shape = schema.shape as Record<string, ZodType<unknown>>
    for (const key in shape) {
      const newPath = buildPath(basePath, key)
      paths.push(...extractEnumPaths(shape[key], newPath))
    }
  }

  // Handle ZodArray
  if (schema instanceof ZodArray) {
    const elementSchema = schema.element as ZodType<unknown>
    // For arrays of objects, traverse properties
    if (elementSchema instanceof ZodObject) {
      const shape = elementSchema.shape as Record<string, ZodType<unknown>>
      for (const key in shape) {
        const newPath = buildPath(basePath, key)
        paths.push(...extractEnumPaths(shape[key], newPath))
      }
    }
    // For arrays of enums
    else if (elementSchema instanceof ZodEnum) {
      paths.push(basePath)
    }
    // Recursively handle other array element types (e.g., arrays of arrays)
    else {
      paths.push(...extractEnumPaths(elementSchema, basePath))
    }
  }

  return paths
}

export const reverseEnumFields = <T extends EnumFieldType>(data: T, enumPaths: string[], currentPath = ""): T => {
  // Handle arrays (e.g., general.economic_activities)
  if (Array.isArray(data)) {
    const typedData = data as EnumFieldType[]
    return typedData.map((item, index) => reverseEnumFields(item, enumPaths, `${currentPath}[${index}]`)) as T
  }

  // Handle objects
  if (typeof data === "object" && data !== null && !(data instanceof Date)) {
    const result: Record<string, unknown> = {}
    for (const key in data) {
      const value = data[key]
      const fullPath = buildPath(currentPath, key)
      // Normalize path for array indices (e.g., "general.economic_activities[0].activity" -> "general.economic_activities.activity")
      const normalizedPath = fullPath.replace(/\[\d+\]/g, "")

      // Handle single string values
      if (typeof value === "string") {
        if (enumPaths.includes(normalizedPath)) {
          // Apply reverseEnum for enum fields
          result[key] = reverseEnum(value)
        } else {
          // Preserve non-enum strings as is
          result[key] = value
        }
      }
      // Handle arrays of strings (e.g., social_governance.workforce_policies)
      else if (Array.isArray(value) && value.every((v) => typeof v === "string")) {
        if (enumPaths.includes(fullPath)) {
          // Apply reverseEnum for enum arrays
          result[key] = value.map(reverseEnum)
        } else {
          // Preserve non-enum arrays as is
          result[key] = value
        }
      }
      // Handle nested objects or arrays
      else if (typeof value === "object" && value !== null && !(value instanceof Date)) {
        result[key] = reverseEnumFields(value as EnumFieldType, enumPaths, fullPath)
      }
      // Preserve all other values (numbers, booleans, null, etc.)
      else {
        result[key] = value
      }
    }
    return result as T
  }

  // Return non-object values unchanged (primitives, Date objects)
  return data
}

export function translateEnumValues<T>(
  obj: T,
  t: (key: string) => string,
  enumPaths: EnumPath[],
  path = ""
): Translated<T> {
  if (Array.isArray(obj)) {
    return obj.map((item) => translateEnumValues(item, t, enumPaths, path)) as Translated<T>
  }

  if (typeof obj === "object" && obj !== null) {
    const result: Record<string, unknown> = {}
    for (const key in obj) {
      const value = (obj as Record<string, unknown>)[key]
      const currentPath = buildPath(path, key)

      if (typeof value === "string" && enumPaths.includes(currentPath)) {
        result[key] = t(value)
      } else if (typeof value === "object" && value !== null) {
        result[key] = translateEnumValues(value, t, enumPaths, currentPath)
      } else {
        result[key] = value
      }
    }
    return result as Translated<T>
  }

  return obj as Translated<T>
}
