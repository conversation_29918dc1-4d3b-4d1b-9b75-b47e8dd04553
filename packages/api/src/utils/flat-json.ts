/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import { z } from "zod"

// Interface for SchemaProperty to avoid circular type inference
interface SchemaPropertyType {
  type: string
  properties?: Record<string, SchemaPropertyType>
  items?: SchemaPropertyType
  "x-display"?: string
  enum?: string[]
}

// Zod schema for a single schema property
const SchemaProperty: z.ZodType<SchemaPropertyType> = z.object({
  type: z.string(),
  properties: z
    .record(
      z.string(),
      z.lazy(() => SchemaProperty)
    )
    .optional(),
  items: z.lazy(() => SchemaProperty).optional(),
  "x-display": z.string().optional(),
  enum: z.array(z.string()).optional(),
})

// Zod schema for the JSON schema
const JsonSchema = z.object({
  $schema: z.string().optional(),
  type: z.string(),
  properties: z.record(z.string(), SchemaProperty).optional(),
})

// Type for flat JSON (key-value pairs with primitive or array values)
export type FlatJson = Record<string, string | number | boolean | null | undefined | Array<unknown>>

// Type for the key-path mapping
type KeyPathMap = Record<
  string,
  {
    path: string[]
    type: string
    items?: SchemaPropertyType
    xDisplay?: string
  }
>

// Interface for nested JSON to handle recursive references
interface NestedJson {
  [key: string]: string | number | boolean | null | undefined | NestedJson | Array<unknown>
}

export function transformFlatJson(schema: unknown, flatJson: unknown): NestedJson {
  // Validate inputs with Zod
  const parsedSchema = JsonSchema.parse(schema)
  const parsedFlatJson = z
    .record(
      z.string(),
      z.union([
        z.string(),
        z.number(),
        z.boolean(),
        z.null(),
        z.undefined(),
        z.array(z.any()), // Allow arrays of any type
      ])
    )
    .parse(flatJson)

  // Helper function to build a mapping of schema keys to their nested paths
  function buildKeyPathMap(
    schema: SchemaPropertyType | z.infer<typeof JsonSchema>,
    prefix = "",
    pathMap: KeyPathMap = {}
  ): KeyPathMap {
    if (!schema.properties) return pathMap

    for (const [key, value] of Object.entries(schema.properties)) {
      const currentPath = prefix ? `${prefix}.${key}` : key

      pathMap[key] = {
        path: currentPath.split("."),
        type: value.type,
        items: value.items,
        xDisplay: value["x-display"],
      }

      if (value.type === "object" && value.properties) {
        buildKeyPathMap(value, currentPath, pathMap)
      }
    }

    return pathMap
  }

  // Helper function to coerce value based on schema type
  function coerceValue(value: FlatJson[string], type: string): NestedJson[string] {
    if (type === "string") {
      return value != null ? String(value) : ""
    }
    if (type === "number") {
      if (typeof value === "string" && value.trim() !== "") {
        const parsed = parseFloat(value)
        return !isNaN(parsed) ? parsed : null
      }
      return typeof value === "number" ? value : null
    }
    return value
  }

  // Helper function to set a value in a nested object by path
  function setNestedValue(obj: NestedJson, path: string[], value: NestedJson[string]): void {
    let current: NestedJson = obj
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      current[key] = current[key] ?? {}
      current = current[key] as NestedJson
    }
    current[path[path.length - 1]] = value
  }

  // Helper function to transform values for array fields
  function transformArrayValue(
    value: FlatJson[string],
    itemsSchema: SchemaPropertyType | undefined,
    xDisplay: string | undefined
  ): NestedJson[string] {
    if (xDisplay === "file-upload") {
      if (Array.isArray(value)) {
        return value.map((v) =>
          typeof v === "string"
            ? {
                key: `doc_${Date.now()}`,
                mimeType: "",
                name: v.split("/").pop() ?? "",
                url: v,
              }
            : v
        )
      }
      return null
    }

    if (itemsSchema?.type === "object" && value && typeof value === "object" && !Array.isArray(value)) {
      const coercedObject: NestedJson = {}
      for (const [propKey, propValue] of Object.entries(value as Record<string, FlatJson[string]>)) {
        const propSchema = itemsSchema.properties?.[propKey]
        coercedObject[propKey] = propSchema ? coerceValue(propValue, propSchema.type) : propValue
      }
      return [coercedObject]
    }

    return Array.isArray(value) ? value : null
  }

  const keyPathMap: KeyPathMap = buildKeyPathMap(parsedSchema)
  const result: NestedJson = {}

  for (const key of Object.keys(keyPathMap)) {
    if (!(key in parsedFlatJson)) continue

    const value = parsedFlatJson[key]
    const { path, type, items, xDisplay } = keyPathMap[key]

    if (type === "array") {
      const transformedValue = transformArrayValue(value, items, xDisplay)
      setNestedValue(result, path, transformedValue)
    } else {
      const coercedValue = coerceValue(value, type)
      setNestedValue(result, path, coercedValue)
    }
  }

  return result
}

export function flattenObject(obj: NestedJson): FlatJson {
  const result: FlatJson = {}

  function processObject(currentObj: NestedJson, prefix = ""): void {
    for (const key in currentObj) {
      if (Object.prototype.hasOwnProperty.call(currentObj, key)) {
        const value = currentObj[key]

        if (typeof value === "object" && value !== null && !Array.isArray(value)) {
          processObject(value, prefix)
        } else {
          result[key] = value
        }
      }
    }
  }

  processObject(obj)
  return result
}
