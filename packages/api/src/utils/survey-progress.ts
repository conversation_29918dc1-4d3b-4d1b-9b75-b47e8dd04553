/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

/**
 * Utility functions for calculating survey progress based on required fields
 */

type JSONSchema = {
  type: string
  properties?: Record<string, JSONSchema>
  items?: JSONSchema
  required?: string[]
  dependency?: string[]
  [key: string]: unknown
}

/**
 * Checks if a dependency is satisfied in the form data
 * @param formData The form data to check
 * @param dependency The dependency path to check
 * @returns True if the dependency is satisfied, false otherwise
 */
function isDependencySatisfied(formData: Record<string, unknown> | undefined, dependency: string): boolean {
  if (!formData) return false

  const parts = dependency.split(".")
  let current: unknown = formData

  // Navigate through the object following the path
  for (const part of parts) {
    if (current === null || current === undefined || typeof current !== "object") {
      return false
    }

    current = (current as Record<string, unknown>)[part]
  }

  // For boolean dependencies, the value must be true
  if (typeof current === "boolean") {
    return current === true
  }

  // For other types, the value must exist and not be empty
  return current !== null && current !== undefined && current !== ""
}

/**
 * Extracts all required field paths from a JSON schema, considering dependencies
 * @param schema The JSON schema to extract required fields from
 * @param formData The form data to check dependencies against
 * @param path Current path in the schema (used for recursion)
 * @returns Array of paths to required fields
 */
export function extractRequiredFieldPaths(schema: JSONSchema, formData?: Record<string, unknown>, path = ""): string[] {
  const requiredPaths: string[] = []

  // Check if this schema has dependencies and if they are satisfied
  if (schema.dependency && formData) {
    // If any dependency is not satisfied, this field and its children are not required
    const allDependenciesSatisfied = schema.dependency.every((dep) => isDependencySatisfied(formData, dep))

    if (!allDependenciesSatisfied) {
      return []
    }
  }

  // If this is an object with properties
  if (schema.type === "object" && schema.properties) {
    // Get required fields at this level
    const requiredFields = schema.required ?? []

    // Add required fields at this level to the result
    for (const field of requiredFields) {
      const fieldPath = path ? `${path}.${field}` : field
      requiredPaths.push(fieldPath)
    }

    // Recursively process all properties (both required and optional)
    for (const [key, value] of Object.entries(schema.properties)) {
      const fieldPath = path ? `${path}.${key}` : key
      requiredPaths.push(...extractRequiredFieldPaths(value, formData, fieldPath))
    }
  }
  // If this is an array with items
  else if (schema.type === "array" && schema.items) {
    // Arrays are handled differently - we don't know how many items there will be
    // So we just process the item schema to get the structure
    requiredPaths.push(...extractRequiredFieldPaths(schema.items, formData, path))
  }

  return requiredPaths
}

/**
 * Checks if a field has a value in the form data
 * @param formData The form data to check
 * @param path Path to the field
 * @returns True if the field has a value, false otherwise
 */
export function hasFieldValue(formData: Record<string, unknown>, path: string): boolean {
  const parts = path.split(".")
  let current: unknown = formData

  // Navigate through the object following the path
  for (const part of parts) {
    if (current === null || current === undefined || typeof current !== "object") {
      return false
    }

    current = (current as Record<string, unknown>)[part]

    // Check if we've reached a value
    if (current === undefined) {
      return false
    }
  }

  // Check if the final value is empty
  if (current === null || current === undefined || current === "") {
    return false
  }

  // For arrays, check if they're empty
  if (Array.isArray(current) && current.length === 0) {
    return false
  }

  return true
}

/**
 * Calculates the progress percentage based on completed required fields
 * @param formData The form data to check
 * @param schema The JSON schema defining required fields
 * @param debug Whether to log debug information
 * @returns Progress percentage (0-100)
 */
export function calculateProgress(formData: Record<string, unknown>, schema: JSONSchema, debug = false): number {
  // Extract all required field paths, considering dependencies
  const requiredPaths = extractRequiredFieldPaths(schema, formData)

  if (requiredPaths.length === 0) {
    if (debug) console.log("No required fields found, returning 100%")
    return 100 // If no required fields, progress is 100%
  }

  // Count how many required fields have values
  let completedFields = 0
  const missingFields: string[] = []

  for (const path of requiredPaths) {
    if (hasFieldValue(formData, path)) {
      completedFields++
    } else {
      missingFields.push(path)
    }
  }

  // Calculate percentage
  const progress = Math.round((completedFields / requiredPaths.length) * 100)

  if (debug) {
    console.log("Progress calculation:")
    console.log(`Total required fields: ${requiredPaths.length}`)
    console.log(`Completed fields: ${completedFields}`)
    console.log(`Missing fields: ${missingFields.length}`)
    console.log(`Progress: ${progress}%`)

    if (missingFields.length > 0) {
      console.log("Missing fields:", missingFields)
    }
  }

  return Math.min(100, Math.max(0, progress)) // Ensure progress is between 0-100
}
