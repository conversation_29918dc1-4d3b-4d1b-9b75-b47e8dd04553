/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ.. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ..
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

import "server-only"

import { unstable_flag as flag } from "@vercel/flags/next"

import { enableCopilot } from "@kreios/copilot/flags"
import { emailOverride } from "@kreios/mail-sender/flags"

export { enableCopilot } from "@kreios/copilot/flags"

export { emailOverride } from "@kreios/mail-sender/flags"

export const roles = flag<
  "ADMIN" | "DATA_VIEWER" | "SURVEY_MANAGER" | "WATCHLIST_MANAGER" | "CLIENT_ADMIN" | "COMPANY_USER" | "OBSERVER" | null
>({
  key: "roles",
  description: "Portaluser roles",
  options: [
    { label: "Admin", value: "ADMIN" },
    { label: "Data Viewer", value: "DATA_VIEWER" },
    { label: "Survey Manager", value: "SURVEY_MANAGER" },
    { label: "Watchlist Manager", value: "WATCHLIST_MANAGER" },
    { label: "Client Admin", value: "CLIENT_ADMIN" },
    { label: "Company User", value: "COMPANY_USER" },
    { label: "Observer", value: "OBSERVER" },
  ],
  defaultValue: null,
  decide: async () => {
    return null
  },
})

export const precomputeFlags = [enableCopilot, emailOverride, roles] as const
