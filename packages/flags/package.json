{"name": "@impactly/flags", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path ../../.prettierignore", "lint": "eslint", "test": "vitest run --passWithNoTests", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@kreios/copilot": "workspace:*", "@kreios/mail-sender": "workspace:*", "@t3-oss/env-core": "0.11.1", "@vercel/flags": "2.6.1", "server-only": "0.0.1", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vitest": "2.1.9"}}