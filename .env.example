# Make sure the app doesn't think it's running on Vercel
VERCEL=

# This is the URL where the app is running
# You can also use e.g. ngrok to get a public URL which e.g. QStash (as a cloud service) can invoke
#    $ ngrok http 3000
VERCEL_URL=localhost:3000

# Redis
KV_REST_API_TOKEN="example_token"
KV_REST_API_URL="http://localhost:8079"
KV_URL="redis://localhost:6379"

# Elasticsearch
ELASTICSEARCH_URL="http://localhost:9200"

# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/postgres"

# =============================================================================
# CDC & PERFORMANCE CONFIGURATION
# =============================================================================

# CDC Processing Configuration
# Batch size for processing companies/entities from external API
# Development: 200 (safe, lower memory usage)
# Migration: 500 (faster processing for initial data load)
# Production: 200 (balanced performance and stability)
CDC_BATCH_SIZE=200

# Page size for external API requests (companies per API call)
# Development: 400 (default)
# Migration: 400 (keep default to avoid API rate limits)
# Production: 400 (balanced)
CDC_API_PAGE_SIZE=400

# Elasticsearch Configuration
# Batch size for Elasticsearch bulk operations
# Development: 10000 (default)
# Migration: 20000 (faster initial indexing)
# Production: 10000 (balanced)
ES_BATCH_SIZE=10000

# Maximum batch size for Elasticsearch index configurations
# Development: 10000 (default)
# Migration: 20000 (faster initial indexing)
# Production: 10000 (balanced)
ES_MAX_BATCH_SIZE=10000

# Database Batch Configuration
# Batch size for database operations (optimized for PostgreSQL limits)
# Development: 10922 (default PostgreSQL limit)
# Migration: 15000 (higher for faster writes)
# Production: 10922 (safe default)
DB_BATCH_SIZE=10922

# Production Optimization Mode
# development: Safe defaults, lower performance
# migration: High performance settings for initial data load
# production: Balanced performance and stability
PRODUCTION_OPTIMIZATION_MODE=development

# CDC Behavior Flags
# Skip change detection and treat all items as new/updated
# Use for fresh data loads or when you know all data has changed
# true: Skip change detection (faster, treats everything as new)
# false: Normal change detection (compares with existing data)
CDC_FORCE_FRESH_MODE=false

# CDC Filtering Options
# Filter companies by country code (ISO 2-letter codes)
# Examples: "EE" for Estonia, "LT" for Lithuania
# Leave empty or comment out to sync all countries
CDC_COUNTRY_FILTER=EE,

# Request Access Configuration
# Comma-separated list of email addresses to send request access notifications to
REQUEST_ACCESS_CONTACT_LIST=""

# Miscellaneous
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
OPENAI_API_KEY=
GOOGLE_MAPS_API_KEY=
RESEND_API_KEY=
RESEND_FROM_EMAIL=
AUTH_EMAIL_REGEX=".*"
UPLOADTHING_APP_ID=
UPLOADTHING_SECRET=
NEXT_PUBLIC_SENTRY_DSN=
NEXTAUTH_SECRET=
SECRET_KEY=
NEXT_APP_URL=