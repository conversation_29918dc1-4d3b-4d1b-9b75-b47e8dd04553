output "env_id" {
  description = "The ID of the Azure Container App Environment"
  value       = azurerm_container_app_environment.app_env.id
}

output "env_name" {
  description = "The name of the Azure Container App Environment"
  value       = azurerm_container_app_environment.app_env.name
}

output "ip_domain_record" {
  description = "The CNAME domain for the Azure App Service"
  value       = azurerm_container_app_environment.app_env.static_ip_address
  sensitive   = false
}

output "environment_domain" {
  description = "The domain name for the Azure Contaner App Environment"
  value       = azurerm_container_app_environment.app_env.default_domain
  sensitive   = false
}

output "environment_verification_id" {
  description = "The verification ID for the Azure Contaner App Environment"
  value       = azurerm_container_app_environment.app_env.custom_domain_verification_id
  sensitive   = false
}
