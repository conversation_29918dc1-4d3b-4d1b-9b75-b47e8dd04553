variable "resource_group_name" {
  description = "The name of the resource group where the Azure App Service will be deployed"
  type        = string
}

variable "location" {
  description = "The Azure region where the resources will be created"
  type        = string
}

variable "container_env_name" {
  description = "The name of the Azure Container App Environment"
  type        = string
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
  }
}

variable "log_analytics_workspace_id" {
  description = "The logs workspace id"
  type        = string
}
