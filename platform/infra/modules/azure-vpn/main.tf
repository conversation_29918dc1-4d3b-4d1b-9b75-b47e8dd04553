/**
 * This module sets up an Azure VPN Gateway with OpenVPN support and Azure AD authentication.
 * It creates:
 * - A dedicated Virtual Network with a gateway subnet
 * - A VPN Gateway with point-to-site (P2S) configuration
 * - Azure AD integration for user authentication
 */

# Get current Azure client configuration for AAD integration
data "azurerm_client_config" "current" {}

# Virtual Network for VPN Gateway
resource "azurerm_virtual_network" "vpn_vnet" {
  name                = "${var.name}-vnet"
  location            = var.location
  resource_group_name = var.resource_group_name
  address_space       = [var.vnet_address_space]
  tags                = var.tags
}

# Gateway Subnet - Azure requires this specific subnet name
resource "azurerm_subnet" "gateway_subnet" {
  name                 = "GatewaySubnet"
  resource_group_name  = var.resource_group_name
  virtual_network_name = azurerm_virtual_network.vpn_vnet.name
  address_prefixes     = [var.gateway_subnet_prefix]
}

# Static Public IP for VPN Gateway
resource "azurerm_public_ip" "vpn_public_ip" {
  name                = "${var.name}-ip"
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                 = "Standard"
  tags                = var.tags
}

# VPN Gateway with Azure AD Authentication
resource "azurerm_virtual_network_gateway" "vpn_gateway" {
  name                = "${var.name}-gateway"
  location            = var.location
  resource_group_name = var.resource_group_name
  type                = "Vpn"
  vpn_type            = "RouteBased"
  sku                 = var.gateway_sku
  enable_bgp          = false

  ip_configuration {
    name                          = "vnetGatewayConfig"
    public_ip_address_id          = azurerm_public_ip.vpn_public_ip.id
    private_ip_address_allocation = "Dynamic"
    subnet_id                     = azurerm_subnet.gateway_subnet.id
  }

  vpn_client_configuration {
    address_space = [var.vpn_client_address_space]

    vpn_client_protocols = ["OpenVPN"] # OpenVPN protocol for better client compatibility
    vpn_auth_types       = ["AAD"]     # Azure AD authentication

    # Azure AD configuration
    aad_tenant   = "https://login.microsoftonline.com/${data.azurerm_client_config.current.tenant_id}"
    aad_audience = "41b23e61-6c1e-4545-b367-cd054e0ed4b4" # Azure VPN's well-known client ID (see Azure documentation)
    aad_issuer   = "https://sts.windows.net/${data.azurerm_client_config.current.tenant_id}/"
  }

  tags = var.tags
}
