output "gateway_id" {
  description = "The ID of the VPN Gateway"
  value       = azurerm_virtual_network_gateway.vpn_gateway.id
}

output "public_ip" {
  description = "The public IP address of the VPN Gateway"
  value       = azurerm_public_ip.vpn_public_ip.ip_address
}

output "vnet_id" {
  description = "The ID of the VPN Virtual Network"
  value       = azurerm_virtual_network.vpn_vnet.id
}

output "vnet_name" {
  description = "The name of the VPN Virtual Network"
  value       = azurerm_virtual_network.vpn_vnet.name
}

output "client_address_space" {
  description = "The address space assigned to VPN clients"
  value       = var.vpn_client_address_space
}

output "tenant_id" {
  description = "The Azure AD tenant ID used for VPN authentication"
  value       = data.azurerm_client_config.current.tenant_id
} 