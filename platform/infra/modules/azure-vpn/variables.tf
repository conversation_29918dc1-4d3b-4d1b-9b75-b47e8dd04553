variable "name" {
  description = "The name prefix for all VPN-related resources"
  type        = string
}

variable "location" {
  description = "The Azure region where resources will be created"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "vnet_address_space" {
  description = "The address space for the VPN virtual network (CIDR notation)"
  type        = string
  default     = "********/16"
}

variable "gateway_subnet_prefix" {
  description = "The subnet prefix for the gateway subnet (CIDR notation). Must be part of vnet_address_space."
  type        = string
  default     = "**********/27"
}

variable "vpn_client_address_space" {
  description = "The address space for VPN clients (CIDR notation)"
  type        = string
  default     = "************/24"
}

variable "gateway_sku" {
  description = "The SKU (size) of the VPN gateway"
  type        = string
  default     = "VpnGw1"
}

variable "tags" {
  description = "Tags to be applied to all resources"
  type        = map(string)
  default     = {}
} 