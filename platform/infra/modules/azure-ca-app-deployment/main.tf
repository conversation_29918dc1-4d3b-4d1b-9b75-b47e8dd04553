data "azurerm_key_vault_secret" "app_secrets" {
  for_each     = toset(var.secrets)
  name         = replace(lower(each.value), "_", "-")
  key_vault_id = var.key_vault_id
}

locals {
  # Combine all environment variables including both static and dynamic ones
  app_env = concat(
    [
      for k, v in var.environment_variables : {
        name  = k
        value = v
      }
    ],
    [
      {
        name  = "KV_REST_API_TOKEN"
        value = random_password.srh_token.result
      },
      {
        name  = "KV_REST_API_URL"
        value = "http://${local.serverless_redis_http_name}:8080"
      },
      {
        name  = "NEXTAUTH_URL"
        value = (var.app_domain != "" && var.app_domain != null) ? "https://${var.app_domain}" : "https://${var.container_app_name}-app.${var.container_app_env_default_domain}"
      }
    ],
    [
      for secret in data.azurerm_key_vault_secret.app_secrets : {
        name  = replace(upper(secret.name), "-", "_")
        value = secret.value
      }
    ]
  )

  serverless_redis_http_name = "${var.container_app_name}-srh"
}

data "azurerm_client_config" "current" {}

resource "azurerm_user_assigned_identity" "container_app" {
  location            = var.location
  resource_group_name = var.resource_group_name
  name                = "${var.container_app_name}-app-principal"
  tags                = var.tags
}

resource "azurerm_role_assignment" "acr_pull" {
  principal_id                     = azurerm_user_assigned_identity.container_app.principal_id
  role_definition_name             = "AcrPull"
  scope                            = var.acr_id
  skip_service_principal_aad_check = true
  depends_on                       = [azurerm_user_assigned_identity.container_app]
}

resource "azurerm_container_app" "serverless_redis_http" {
  name                         = local.serverless_redis_http_name
  resource_group_name          = var.resource_group_name
  container_app_environment_id = var.container_app_env_id
  revision_mode                = "Single"
  tags                         = var.tags

  ingress {
    exposed_port     = 8080
    target_port      = 80
    transport        = "tcp"
    external_enabled = false

    traffic_weight {
      latest_revision = true
      percentage      = 100
    }
  }

  template {
    container {
      name   = local.serverless_redis_http_name
      image  = "hiett/serverless-redis-http:0.0.10"
      cpu    = var.cpu
      memory = var.memory

      env {
        name  = "SRH_MODE"
        value = "env"
      }
      env {
        name  = "SRH_TOKEN"
        value = random_password.srh_token.result
      }
      env {
        name  = "SRH_CONNECTION_STRING"
        value = var.redis_connection_string
      }
    }
  }
}

resource "azurerm_container_app" "app" {
  name                         = "${var.container_app_name}-app"
  resource_group_name          = var.resource_group_name
  container_app_environment_id = var.container_app_env_id
  revision_mode                = "Single"
  tags                         = var.tags
  depends_on                   = [azurerm_role_assignment.acr_pull]


  ingress {
    allow_insecure_connections = true
    external_enabled           = true
    target_port                = 3000
    transport                  = "http"

    dynamic "ip_security_restriction" {
      for_each = var.app_ip_whitelist
      content {
        action           = "Allow"
        ip_address_range = ip_security_restriction.value
        name             = "whitelist-${replace(ip_security_restriction.value, "/", "-")}"
      }
    }

    traffic_weight {
      latest_revision = true
      percentage      = 100
    }
  }

  registry {
    server   = var.acr_fqdn
    identity = azurerm_user_assigned_identity.container_app.id
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app.id]
  }

  template {
    container {
      name   = var.container_app_name
      image  = var.image
      cpu    = var.cpu
      memory = var.memory

      dynamic "env" {
        for_each = local.app_env
        content {
          name  = env.value.name
          value = env.value.value
        }
      }
    }
  }
}

resource "azurerm_container_app_custom_domain" "domain" {
  count            = (var.app_domain != "" && var.app_domain != null) ? 1 : 0
  name             = var.app_domain
  container_app_id = azurerm_container_app.app.id

  lifecycle {
    // When using an Azure created Managed Certificate these values must be added to ignore_changes to prevent resource recreation.
    ignore_changes = [certificate_binding_type, container_app_environment_certificate_id]
  }
}

resource "random_password" "srh_token" {
  length  = 32
  special = false
}
