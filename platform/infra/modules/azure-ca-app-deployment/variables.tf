variable "resource_group_name" {
  description = "The name of the resource group where the Azure App Service will be deployed"
  type        = string
}

variable "location" {
  description = "The Azure region where the resources will be created"
  type        = string
}

variable "container_app_name" {
  description = "The name of the Azure Container App"
  type        = string
}

variable "container_app_env_id" {
  description = "The ID of the Azure Container App Environment"
  type        = string
}

variable "container_app_env_default_domain" {
  description = "The default domain of the Azure Container App Environment"
  type        = string
}

variable "image" {
  description = "The Docker image to use for the Azure Container App, in the format 'registry/image:tag'"
  type        = string
}

variable "acr_id" {
  description = "The ID of the Azure Container Registry"
  type        = string
}

variable "acr_fqdn" {
  description = "The fully qualified domain name of the Azure Container Registry"
  type        = string
}

variable "cpu" {
  description = "The CPU limit for the container"
  type        = number
  default     = 1
}

variable "memory" {
  description = "The memory limit for the container"
  type        = string
  default     = "2Gi"
}

variable "environment_variables" {
  description = "A map of environment variables to inject into the application"
  type        = map(string)
  default     = {}
}

variable "secrets" {
  description = "A map of environment variables to inject into the application from key vault secrets"
  type        = list(string)
  default     = []
}

variable "redis_connection_string" {
  description = "Redis connection string"
  type        = string
}

variable "app_ip_whitelist" {
  description = "List of IP ranges to whitelist for the Application"
  type        = list(string)
  default = [
    "0.0.0.0/0" # Allow all incoming traffic
  ]
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
  }
}

variable "app_domain" {
  description = "The domain for the application"
  type        = string
  default     = null
}

variable "log_analytics_workspace_id" {
  description = "The logs workspace id"
  type        = string
}

variable "key_vault_id" {
  description = "The id of the key vault"
  type        = string
}

variable "key_vault_uri" {
  description = "The key vault URI"
  type        = string
}
