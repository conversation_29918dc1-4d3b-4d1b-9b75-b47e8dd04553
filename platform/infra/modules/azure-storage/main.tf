resource "azurerm_storage_account" "storage_account" {
  name                            = var.storage_account_name
  resource_group_name             = var.resource_group_name
  location                        = var.location
  account_tier                    = var.account_tier
  account_replication_type        = var.account_replication_type
  is_hns_enabled                  = var.is_hns_enabled
  allow_nested_items_to_be_public = var.allow_anonymous_access

  tags = var.tags
}

resource "azurerm_storage_container" "default_container" {
  name                  = "default"
  storage_account_id    = azurerm_storage_account.storage_account.id
  container_access_type = "private"
}

resource "azurerm_storage_container" "landing_zone_container" {
  name = "ingest"
  # Terraform will just recreate those containers where we originally used the name and not the id to reference the storage account
  # To prevent this, please checkout this issue: https://github.com/hashicorp/terraform-provider-azurerm/issues/27942
  storage_account_id    = azurerm_storage_account.storage_account.id
  container_access_type = "private"
}

resource "azurerm_storage_container" "landing_zone_dlq" {
  name                  = "ingest-dlq"
  storage_account_id    = azurerm_storage_account.storage_account.id
  container_access_type = "private"
}

output "storage_account_name" {
  value = azurerm_storage_account.storage_account.name
}

output "storage_account_key" {
  value     = azurerm_storage_account.storage_account.primary_access_key
  sensitive = true
}

output "blob_container_name" {
  value = azurerm_storage_container.default_container.name
}

output "storage_account_url" {
  value = azurerm_storage_account.storage_account.primary_blob_endpoint
}

output "connection_string" {
  value     = azurerm_storage_account.storage_account.primary_connection_string
  sensitive = true
}
