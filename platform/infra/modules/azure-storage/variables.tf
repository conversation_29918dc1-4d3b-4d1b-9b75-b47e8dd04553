variable "storage_account_name" {
  description = "The name of the Storage Account"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The Azure location for the Storage Account"
  type        = string
}

variable "is_hns_enabled" {
  description = "Whether to enable Hierarchical Namespace Support (a.k.a. Data Lake Storage Gen2) for the storage account"
  type        = bool
  default     = true
}

variable "account_tier" {
  description = "The performance tier of the storage account (Standard or Premium)"
  type        = string
  default     = "Standard"
}

variable "account_replication_type" {
  description = "The replication strategy for the storage account (LRS, GRS, RA-GRS, ZRS)"
  type        = string
  default     = "LRS"
}

variable "tags" {
  description = "Tags to associate with the storage account and container"
  type        = map(string)
}

variable "allow_anonymous_access" {
  description = "Whether to allow anonymous access to the storage account"
  type        = bool
  default     = false
}
