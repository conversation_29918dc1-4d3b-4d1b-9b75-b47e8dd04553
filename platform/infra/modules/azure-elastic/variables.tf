variable "name" {
  description = "The name of the Elasticsearch instance"
  type        = string
}

variable "location" {
  description = "The Azure location"
  type        = string
}

variable "resource_group_name" {
  description = "The resource group name for the Elasticsearch service"
  type        = string
}

variable "sku_name" {
  description = "The SKU for the Elasticsearch service"
  type        = string
  default     = "ess-consumption-2024_Monthly"
}

variable "elastic_cloud_email_address" {
  description = "The email address for Elastic Cloud"
  type        = string
}

variable "tags" {
  description = "Tags for the Elasticsearch service"
  type = map(string)
}
