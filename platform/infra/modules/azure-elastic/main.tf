resource "azurerm_elastic_cloud_elasticsearch" "elastic" {
  name                        = var.name
  location                    = var.location
  resource_group_name         = var.resource_group_name
  sku_name                    = var.sku_name
  elastic_cloud_email_address = var.elastic_cloud_email_address

  tags = var.tags
}

output "elastic_cloud_deployment_id" {
  value = azurerm_elastic_cloud_elasticsearch.elastic.elastic_cloud_deployment_id
}

output "elastic_cloud_sso_default_url" {
  value = azurerm_elastic_cloud_elasticsearch.elastic.elastic_cloud_sso_default_url
}

output "elasticsearch_service_url" {
  value = azurerm_elastic_cloud_elasticsearch.elastic.elasticsearch_service_url
}

output "kibana_service_url" {
  value = azurerm_elastic_cloud_elasticsearch.elastic.kibana_service_url
}

output "kibana_sso_uri" {
  value = azurerm_elastic_cloud_elasticsearch.elastic.kibana_sso_uri
}
