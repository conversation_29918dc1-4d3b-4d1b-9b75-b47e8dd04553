variable "communication_service_name" {
  description = "The name of the Azure Communication Service instance"
  type        = string
  default     = "data-platform-prototype-communication"
}

variable "email_service_name" {
  description = "The name of the Email Communication Service"
  type        = string
  default     = "data-platform-prototype-email"
}

variable "communication_service_data_location" {
  description = "Data storage location for the Azure Communication Service"
  type        = string
  default     = "Europe"
}

variable "resource_group_name" {
  description = "The resource group name where the service will be deployed"
  type        = string
}

variable "tags" {
  description = "Tags to assign to the resources"
  type        = map(string)
  default = {
    Project     = "data-platform"
    Environment = "dev"
  }
}
