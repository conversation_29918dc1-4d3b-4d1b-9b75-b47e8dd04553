# Azure Communication Service
resource "azurerm_communication_service" "acs" {
  name                = var.communication_service_name
  resource_group_name = var.resource_group_name
  data_location       = var.communication_service_data_location

  tags = var.tags
}

# Email Communication Service
resource "azurerm_email_communication_service" "email_service" {
  name                = var.email_service_name
  resource_group_name = var.resource_group_name
  data_location       = var.communication_service_data_location

  tags = var.tags
}

# Automatically managed domain (AzureManaged)
resource "azurerm_email_communication_service_domain" "email_domain" {
  name = "AzureManagedDomain"  # Required for Azure-managed domains
  email_service_id = azurerm_email_communication_service.email_service.id
  domain_management = "AzureManaged"  # Automatically assigned domain by Azure

  tags = var.tags
}

# Associate the Azure Managed Domain with the Communication Service
resource "azurerm_communication_service_email_domain_association" "email_domain_association" {
  communication_service_id = azurerm_communication_service.acs.id
  email_service_domain_id  = azurerm_email_communication_service_domain.email_domain.id
}

# Outputs for ACS (Azure Communication Service)
output "acs_primary_key" {
  value     = azurerm_communication_service.acs.primary_key
  sensitive = true
}

output "acs_endpoint" {
  value = azurerm_communication_service.acs.primary_connection_string
}

# Outputs for Email Service
output "email_service_id" {
  value = azurerm_email_communication_service.email_service.id
}

output "email_domain_name" {
  value = azurerm_email_communication_service_domain.email_domain.from_sender_domain
}

output "email_domain_id" {
  value = azurerm_email_communication_service_domain.email_domain.id
}

output "communication_service_domain_association_id" {
  value = azurerm_communication_service_email_domain_association.email_domain_association.id
}
