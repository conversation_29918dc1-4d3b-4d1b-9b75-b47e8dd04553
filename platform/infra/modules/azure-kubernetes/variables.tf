variable "location" {
  description = "The Azure location"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "cluster_name" {
  description = "The name of the Kubernetes cluster"
  type        = string
}

variable "dns_prefix" {
  description = "DNS prefix for the cluster"
  type        = string
}

variable "sku_tier" {
  description = "SKU tier for the cluster"
  type        = string
  default     = "Free"
}

variable "node_size" {
  description = "VM size for the default node pool"
  type        = string
  default     = "Standard_D4as_v4"
}

variable "node_count_min" {
  description = "Minimum number of nodes in the node pool"
  type        = number
  default     = 1
}

variable "node_count_max" {
  description = "Maximum number of nodes in the node pool"
  type        = number
  default     = 5
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
  }
}

variable "agw_dns_name_label" {
  description = "DNS name label for the Application Gateway public IP"
  type        = string
}

variable "allowed_container_registries" {
  description = "Allowed custom container registries. Must be a regex expression."
  type        = string
  default     = "^(docker\\.io|mcr\\.microsoft\\.com)(\\/.*)?"
}

variable "log_analytics_workspace_id" {
  description = "The logs workspace id"
  type        = string
}

variable "acr_id" {
  description = "The ID of the Azure Container Registry"
  type        = string
}
