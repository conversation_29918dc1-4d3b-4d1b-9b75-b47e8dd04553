resource "azurerm_kubernetes_cluster" "aks" {
  name                 = var.cluster_name
  location             = var.location
  resource_group_name  = var.resource_group_name
  node_resource_group  = "MC_${var.resource_group_name}_${var.location}"
  sku_tier             = var.sku_tier
  dns_prefix           = var.dns_prefix
  azure_policy_enabled = true

  default_node_pool {
    name                        = "default"
    vm_size                     = var.node_size
    vnet_subnet_id              = azurerm_subnet.aks.id
    auto_scaling_enabled        = true
    node_count                  = var.node_count_min
    min_count                   = var.node_count_min
    max_count                   = var.node_count_max
    temporary_name_for_rotation = "defaulttemp"
  }

  identity {
    type = "SystemAssigned"
  }

  network_profile {
    network_plugin = "azure"
    network_policy = "azure"
  }

  ingress_application_gateway {
    gateway_id = azurerm_application_gateway.agw.id
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      default_node_pool[0].node_count,
      default_node_pool[0].upgrade_settings
    ]
  }
}

# Assign the AcrPull role to the Kubernetes cluster's managed identity (if provided)
resource "azurerm_role_assignment" "acr_pull" {
  principal_id                     = azurerm_kubernetes_cluster.aks.identity[0].principal_id
  role_definition_name             = "AcrPull"
  scope                            = var.acr_id
  skip_service_principal_aad_check = true
}

resource "azurerm_role_assignment" "agic_contributor" {
  scope                = azurerm_application_gateway.agw.id
  role_definition_name = "Contributor"
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
}

resource "azurerm_role_assignment" "agic_reader" {
  scope                = data.azurerm_resource_group.rg.id
  role_definition_name = "Reader"
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
}

resource "azurerm_role_assignment" "agic_network_contributor" {
  scope                = azurerm_subnet.agw.id
  role_definition_name = "Network Contributor"
  principal_id         = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
}

output "principal_id" {
  value = azurerm_kubernetes_cluster.aks.identity[0].principal_id
}

output "kubelet_identity_client_id" {
  value = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id
}

output "client_certificate" {
  value     = azurerm_kubernetes_cluster.aks.kube_config[0].client_certificate
  sensitive = true
}

output "kube_config" {
  value     = azurerm_kubernetes_cluster.aks.kube_config_raw
  sensitive = true
}

output "host" {
  value = azurerm_kubernetes_cluster.aks.kube_config[0].host
}

output "client_key" {
  value     = azurerm_kubernetes_cluster.aks.kube_config[0].client_key
  sensitive = true
}

resource "azurerm_resource_policy_assignment" "policy_only_trusted_registries" {
  name                 = "allow-trusted-container-registries"
  location             = var.location
  resource_id          = azurerm_kubernetes_cluster.aks.id
  display_name         = "Only allow trusted container registries"
  description          = "Restrict Kubernetes cluster to use container images only from trusted registries."
  policy_definition_id = "/providers/microsoft.authorization/policydefinitions/febd0533-8e55-448f-b837-bd0e06f16469"

  parameters = jsonencode({
    allowedContainerImagesRegex = {
      value = var.allowed_container_registries
    }
    excludedContainers = {
      value = ["serverless-redis-http", "cert-manager-controller", "cert-manager-cainjector", "cert-manager-webhook", "cert-manager-startupapicheck"]
    }
  })

  identity {
    type = "SystemAssigned"
  }
}

resource "azurerm_resource_policy_assignment" "aks_run_as_non_root" {
  name                 = "force_run_as_non_root"
  location             = var.location
  resource_id          = azurerm_kubernetes_cluster.aks.id
  display_name         = "Kubernetes cluster pods and containers should only run with approved user and group IDs"
  description          = "Restrict Kubernetes cluster to use container images only from trusted registries."
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/f06ddb64-5fa3-4b77-b166-acb36f7f6042"

  parameters = jsonencode({
    excludedImages = {
      value = ["hiett/serverless-redis-http:0.0.10"]
    }
  })

  identity {
    type = "SystemAssigned"
  }
}

resource "azurerm_resource_policy_assignment" "aks_immutable" {
  name                 = "force_immutable"
  location             = var.location
  resource_id          = azurerm_kubernetes_cluster.aks.id
  display_name         = "Kubernetes cluster pods and containers must not be allowed to make changes to the file system"
  description          = "Restrict Kubernetes cluster pods and containers from making changes to the file system."
  policy_definition_id = "/providers/Microsoft.Authorization/policyDefinitions/df49d893-a74c-421d-bc95-c663042e5b80"

  identity {
    type = "SystemAssigned"
  }
}

resource "azurerm_monitor_diagnostic_setting" "ai_diagnostic" {
  name               = "${var.cluster_name}-diagnostic-setting"
  target_resource_id = azurerm_kubernetes_cluster.aks.id

  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category = "kube-audit"
  }

  metric {
    category = "AllMetrics"
  }
}

output "cluster_ca_certificate" {
  value     = azurerm_kubernetes_cluster.aks.kube_config[0].cluster_ca_certificate
  sensitive = true
}

output "agic_identity_object_id" {
  value = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].object_id
}

output "agic_identity_client_id" {
  value = azurerm_kubernetes_cluster.aks.ingress_application_gateway[0].ingress_application_gateway_identity[0].client_id
}

data "azurerm_resource_group" "rg" {
  name = var.resource_group_name
}
