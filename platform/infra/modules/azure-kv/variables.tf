variable "resource_group_name" {
  description = "The name of the resource group in which to create the Key Vault."
  type        = string
}

variable "location" {
  description = "The location where the Key Vault will be created."
  type        = string
}

variable "key_vault_name" {
  description = "The name of the Key Vault."
  type        = string
}

variable "sku_name" {
  description = "The SKU name of the Key Vault."
  type        = string
  default     = "standard"
}

variable "secrets" {
  description = "A map of environment variables to inject into the application from key vault secrets"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
  }
}
