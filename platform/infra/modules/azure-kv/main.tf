data "azurerm_client_config" "current" {}

resource "azurerm_key_vault" "kv" {
  name                = var.key_vault_name
  location            = var.location
  resource_group_name = var.resource_group_name
  sku_name            = var.sku_name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  tags                = var.tags
}

resource "azurerm_key_vault_access_policy" "terraform_user_policy" {
  key_vault_id = azurerm_key_vault.kv.id
  tenant_id    = data.azurerm_client_config.current.tenant_id
  object_id    = data.azurerm_client_config.current.object_id

  secret_permissions = ["Get", "Set"]
}

resource "azurerm_key_vault_secret" "secrets" {
  depends_on = [azurerm_key_vault_access_policy.terraform_user_policy]
  for_each   = toset(var.secrets)

  name         = replace(lower(each.value), "_", "-")
  value        = ""
  key_vault_id = azurerm_key_vault.kv.id
  tags         = var.tags

  lifecycle {
    ignore_changes = [
      value,
    ]
  }
}
