variable "name" {
  description = "The name of the Log Analytics Workspace"
  type        = string
}

variable "location" {
  description = "The location where the Log Analytics Workspace will be created"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group in which to create the Log Analytics Workspace"
  type        = string
}

variable "retention_in_days" {
  description = "The retention period for the Log Analytics Workspace"
  type        = number
  default     = 30
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
  }
}
