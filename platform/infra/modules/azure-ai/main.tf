resource "azurerm_ai_services" "ai" {
  name                = var.name
  location            = var.location
  resource_group_name = var.resource_group_name
  sku_name            = var.sku_name

  tags = var.tags
}

resource "azurerm_cognitive_deployment" "openai_deployment" {
  name = "gpt-4o"
  # It's a bit confusing, but it seems "Azure AI" is actually just a Cognitive Services account under the hood
  cognitive_account_id = azurerm_ai_services.ai.id

  model {
    format  = "OpenAI"
    name    = "gpt-4o"
    version = "2024-08-06"
  }

  sku {
    name = "GlobalStandard"
  }

  lifecycle {
    ignore_changes = [
      model[0].version
    ]
  }
}

resource "azurerm_monitor_diagnostic_setting" "ai_diagnostic" {
  name               = "${var.name}-diagnostic-setting"
  target_resource_id = azurerm_ai_services.ai.id

  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category = "Audit"
  }

  metric {
    category = "AllMetrics"
  }
}


output "ai_endpoint" {
  value = azurerm_ai_services.ai.endpoint
}

output "ai_primary_access_key" {
  value     = azurerm_ai_services.ai.primary_access_key
  sensitive = true
}

