variable "cert_manager_version" {
  description = "Version of cert-manager to install"
  type        = string
  default     = "v1.11.0"
}

variable "cluster_issuer_name" {
  description = "Name of the ClusterIssuer"
  type        = string
  default     = "letsencrypt"
}

variable "acme_server" {
  description = "ACME server URL"
  type        = string
  default     = "https://acme-v02.api.letsencrypt.org/directory"
}

variable "acme_email" {
  description = "Email address for ACME registration"
  type        = string
}

variable "ingress_class" {
  description = "Ingress class to use for HTTP01 challenges"
  type        = string
  default     = "azure/application-gateway"
}
