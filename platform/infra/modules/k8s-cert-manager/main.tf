resource "kubernetes_namespace" "cert_manager" {
  metadata {
    name = "cert-manager"
  }
}

resource "helm_release" "cert_manager" {
  name       = "cert-manager"
  repository = "https://charts.jetstack.io"
  chart      = "cert-manager"
  namespace  = "cert-manager"
  version    = var.cert_manager_version
  depends_on = [kubernetes_namespace.cert_manager]

  set {
    name  = "installCRDs"
    value = "true"
  }

  set {
    name  = "securityContext.enabled"
    value = "true"
  }

  set {
    name  = "securityContext.readOnlyRootFilesystem"
    value = "true"
  }
}

resource "helm_release" "cluster_issuer" {
  name  = "cluster-issuer"
  chart = "${path.module}/cluster-issuer-chart"

  set {
    name  = "name"
    value = var.cluster_issuer_name
  }

  set {
    name  = "server"
    value = var.acme_server
  }

  set {
    name  = "email"
    value = var.acme_email
  }

  set {
    name  = "privateKeySecretName"
    value = "${var.cluster_issuer_name}-account-key"
  }

  set {
    name  = "solverIngressClass"
    value = var.ingress_class
  }
}
