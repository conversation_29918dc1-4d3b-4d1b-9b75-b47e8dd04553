variable "zone_name" {
  description = "The name of the DNS zone"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "default_ttl" {
  description = "The default TTL for DNS records in seconds"
  type        = number
  default     = 3600
}

variable "a_records" {
  description = "Map of A records to create. Key is record name (use @ for apex), value is list of IP addresses"
  type        = map(list(string))
  default     = {}
}

variable "cname_records" {
  description = "Map of CNAME records to create. Key is record name, value is target hostname"
  type        = map(string)
  default     = {}
}

variable "txt_records" {
  description = "Map of TXT records to create. Key is record name, value is the text record"
  type        = map(string)
  default     = {}
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
} 