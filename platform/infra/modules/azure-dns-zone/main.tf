resource "azurerm_dns_zone" "zone" {
  name                = var.zone_name
  resource_group_name = var.resource_group_name
  tags                = var.tags
}

# A records
resource "azurerm_dns_a_record" "records" {
  for_each           = var.a_records
  name               = each.key
  zone_name          = azurerm_dns_zone.zone.name
  resource_group_name = var.resource_group_name
  ttl                = var.default_ttl
  records            = each.value
  tags               = var.tags
}

# CNAME records
resource "azurerm_dns_cname_record" "records" {
  for_each           = var.cname_records
  name               = each.key
  zone_name          = azurerm_dns_zone.zone.name
  resource_group_name = var.resource_group_name
  ttl                = var.default_ttl
  record             = each.value
  tags               = var.tags
}

# TXT records
resource "azurerm_dns_txt_record" "records" {
  for_each           = var.txt_records
  name               = each.key
  zone_name          = azurerm_dns_zone.zone.name
  resource_group_name = var.resource_group_name
  ttl                = var.default_ttl
  tags               = var.tags

  record {
    value = each.value
  }
} 