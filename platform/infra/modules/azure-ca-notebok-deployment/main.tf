terraform {
  required_providers {
    azapi = {
      source  = "Azure/azapi"
      version = ">= 1.5.0"
    }
  }
}

data "azurerm_storage_account" "notebook_sa" {
  name                = var.storage_account_name
  resource_group_name = var.resource_group_name
}

resource "azurerm_storage_share" "notebook_share" {
  name               = "notebook"
  storage_account_id = data.azurerm_storage_account.notebook_sa.id
  quota              = 10
}

resource "azurerm_container_app_environment_storage" "notebook_share" {
  name                         = "notebook-share"
  container_app_environment_id = var.container_app_env_id
  account_name                 = var.storage_account_name
  share_name                   = "notebook"
  access_key                   = data.azurerm_storage_account.notebook_sa.primary_access_key
  access_mode                  = "ReadWrite"
}

resource "azurerm_storage_share" "notebook_rules" {
  name               = "notebook-rules"
  storage_account_id = data.azurerm_storage_account.notebook_sa.id
  quota              = 10
}

resource "azurerm_container_app_environment_storage" "notebook_rules" {
  name                         = "notebook-rules"
  container_app_environment_id = var.container_app_env_id
  account_name                 = var.storage_account_name
  share_name                   = "notebook-rules"
  access_key                   = data.azurerm_storage_account.notebook_sa.primary_access_key
  access_mode                  = "ReadWrite"
}

resource "azurerm_user_assigned_identity" "container_app" {
  location            = var.location
  resource_group_name = var.resource_group_name
  name                = "${var.container_app_name}-notebook-principal"
  tags                = var.tags
}

resource "azurerm_role_assignment" "acr_pull" {
  principal_id                     = azurerm_user_assigned_identity.container_app.principal_id
  role_definition_name             = "AcrPull"
  scope                            = var.acr_id
  skip_service_principal_aad_check = true
  depends_on                       = [azurerm_user_assigned_identity.container_app]
}

resource "azurerm_container_app" "app" {
  name                         = "${var.container_app_name}-not"
  resource_group_name          = var.resource_group_name
  container_app_environment_id = var.container_app_env_id
  revision_mode                = "Single"
  tags                         = var.tags
  depends_on                   = [azurerm_role_assignment.acr_pull]


  ingress {
    allow_insecure_connections = false
    external_enabled           = true
    target_port                = 8888
    transport                  = "auto"

    dynamic "ip_security_restriction" {
      for_each = var.app_ip_whitelist
      content {
        action           = "Allow"
        ip_address_range = ip_security_restriction.value
        name             = "whitelist-${replace(ip_security_restriction.value, "/", "-")}"
      }
    }

    traffic_weight {
      latest_revision = true
      percentage      = 100
    }
  }

  registry {
    server   = var.acr_fqdn
    identity = azurerm_user_assigned_identity.container_app.id
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app.id]
  }

  template {
    max_replicas = 1
    min_replicas = 1

    http_scale_rule {
      concurrent_requests = 1000
      name                = "http-scaler"
    }
    container {
      name   = "${var.container_app_name}-notebook"
      image  = var.image
      cpu    = var.cpu
      memory = var.memory

      volume_mounts {
        name = "notebook-share"
        path = "/home/<USER>"
      }

      env {
        name  = "JUPYTER_TOKEN"
        value = random_password.notebook_app_secret.result
      }
    }

    volume {
      name         = "notebook-share"
      storage_name = "notebook-share"
      storage_type = "AzureFile"
    }
  }
}

resource "azapi_update_resource" "update_mount_options" {
  type        = "Microsoft.App/containerApps@2024-03-01"
  resource_id = azurerm_container_app.app.id
  body = {
    properties = {
      template = {
        volumes = [
          {
            "name" : "notebook-share",
            "storageType" : "AzureFile",
            "storageName" : "notebook-share",
            "mountOptions" : "dir_mode=0700,file_mode=0600,uid=1000,gid=1000,mfsymlinks,nobrl,cache=none"
          }
        ]
      }
    }
  }
}

resource "azurerm_container_app" "api" {
  name                         = "${var.container_app_name}-api"
  resource_group_name          = var.resource_group_name
  container_app_environment_id = var.container_app_env_id
  revision_mode                = "Single"
  tags                         = var.tags
  depends_on                   = [azurerm_role_assignment.acr_pull]


  ingress {
    exposed_port     = 8000
    target_port      = 8000
    transport        = "tcp"
    external_enabled = false

    traffic_weight {
      latest_revision = true
      percentage      = 100
    }
  }

  registry {
    server   = var.acr_fqdn
    identity = azurerm_user_assigned_identity.container_app.id
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.container_app.id]
  }

  template {
    container {
      name   = "${var.container_app_name}-api"
      image  = var.image_api
      cpu    = var.cpu
      memory = var.memory

      volume_mounts {
        name = "notebook-rules"
        path = "/rules"
      }

      env {
        name  = "RULE_DATA_PATH"
        value = "/rules"
      }

      env {
        name  = "NOTEBOOK_API_KEY"
        value = random_password.notebook_api_secret.result
      }
    }

    volume {
      name         = "notebook-rules"
      storage_name = "notebook-rules"
      storage_type = "AzureFile"
    }
  }
}

resource "azapi_update_resource" "update_api_mount_options" {
  type        = "Microsoft.App/containerApps@2024-03-01"
  resource_id = azurerm_container_app.api.id
  body = {
    properties = {
      template = {
        volumes = [
          {
            "name" : "notebook-rules",
            "storageType" : "AzureFile",
            "storageName" : "notebook-rules",
            "mountOptions" : "dir_mode=0700,file_mode=0600,uid=1000,gid=1000,mfsymlinks,nobrl,cache=none"
          }
        ]
      }
    }
  }
}

resource "azurerm_container_app_custom_domain" "notebook" {
  count            = (var.notebook_domain != "" && var.notebook_domain != null) ? 1 : 0
  name             = var.notebook_domain
  container_app_id = azurerm_container_app.app.id

  lifecycle {
    // When using an Azure created Managed Certificate these values must be added to ignore_changes to prevent resource recreation.
    ignore_changes = [certificate_binding_type, container_app_environment_certificate_id]
  }
}

resource "random_password" "notebook_api_secret" {
  length  = 32
  special = true
}

resource "random_password" "notebook_app_secret" {
  length  = 32
  special = true
}
