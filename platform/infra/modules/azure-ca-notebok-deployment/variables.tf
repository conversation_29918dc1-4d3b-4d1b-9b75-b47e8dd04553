variable "resource_group_name" {
  description = "The name of the resource group where the Azure App Service will be deployed"
  type        = string
}

variable "location" {
  description = "The Azure region where the resources will be created"
  type        = string
}

variable "storage_account_name" {
  description = "The name of the storage account for the file sharing"
  type        = string
}

variable "container_app_name" {
  description = "The name of the Azure Container App"
  type        = string
}

variable "container_app_env_id" {
  description = "The ID of the Azure Container App Environment"
  type        = string
}

variable "image" {
  description = "The Docker image to use for the Azure Container App, in the format 'registry/image:tag'"
  type        = string
}

variable "image_api" {
  description = "The Docker image to use for the Azure Container App Notebook API, in the format 'registry/image:tag'"
  type        = string
}

variable "acr_id" {
  description = "The ID of the Azure Container Registry"
  type        = string
}

variable "acr_fqdn" {
  description = "The fully qualified domain name of the Azure Container Registry"
  type        = string
}

variable "cpu" {
  description = "The CPU limit for the container"
  type        = number
  default     = 2
}

variable "memory" {
  description = "The memory limit for the container"
  type        = string
  default     = "4Gi"
}

variable "app_ip_whitelist" {
  description = "List of IP ranges to whitelist for the Application"
  type        = list(string)
  default = [
    "0.0.0.0/0" # Allow all incoming traffic
  ]
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
  }
}

variable "notebook_domain" {
  description = "The name of the Azure Container App"
  type        = string
}

variable "log_analytics_workspace_id" {
  description = "The logs workspace id"
  type        = string
}
