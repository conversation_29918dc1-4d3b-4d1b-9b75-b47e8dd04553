output "cname_notebook_domain" {
  description = "The URL for the Azure Container App Notebook"
  value       = "${azurerm_container_app.app.name}.${data.azurerm_container_app_environment.app_env.default_domain}"
  sensitive   = true
}

output "notebook_verification_id" {
  description = "The domain verification id for the Notebook App Service"
  value       = azurerm_container_app.app.custom_domain_verification_id
  sensitive   = true
}

output "cname_api_domain" {
  description = "The URL for the Azure Container Notebook API"
  value       = "${azurerm_container_app.api.name}.${data.azurerm_container_app_environment.app_env.default_domain}"
  sensitive   = true
}

output "api_verification_id" {
  description = "The domain verification id for the Notebook API App Service"
  value       = azurerm_container_app.api.custom_domain_verification_id
  sensitive   = true
}

output "api_secret" {
  description = "The secret for the Azure Container Notebook API"
  value       = random_password.notebook_api_secret.result
  sensitive   = true
}

output "app_secret" {
  description = "The secret for the Azure Container Notebook APP"
  value       = random_password.notebook_app_secret.result
  sensitive   = true
}

output "internal_notebook_url" {
  value = "http://${azurerm_container_app.app.name}:8888"
}

output "internal_api_url" {
  value = "http://${azurerm_container_app.api.name}:8000"
}
