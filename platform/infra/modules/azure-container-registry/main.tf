resource "azurerm_container_registry" "acr" {
  name                = var.name
  location            = var.location
  resource_group_name = var.resource_group_name
  sku                 = var.sku
  admin_enabled       = var.admin_enabled

  # Georeplication, only for Premium SKU
  dynamic "georeplications" {
    for_each = var.georeplication_locations
    content {
      location                = georeplications.value.location
      zone_redundancy_enabled = georeplications.value.zone_redundancy_enabled
      tags                    = var.tags
    }
  }

  tags = var.tags
}

resource "azurerm_monitor_diagnostic_setting" "acr_diagnostic" {
  name               = "${var.name}-diagnostic-setting"
  target_resource_id = azurerm_container_registry.acr.id

  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category = "ContainerRegistryRepositoryEvents"
  }

  enabled_log {
    category = "ContainerRegistryLoginEvents"
  }

  metric {
    category = "AllMetrics"
  }
}

resource "azurerm_security_center_subscription_pricing" "defender_for_acr" {
  tier          = "Standard"
  resource_type = "ContainerRegistry"
  depends_on    = [azurerm_container_registry.acr]
}

output "acr_login_server" {
  value = azurerm_container_registry.acr.login_server
}

output "acr_id" {
  value = azurerm_container_registry.acr.id
}

output "acr_admin_username" {
  value = azurerm_container_registry.acr.admin_username
}

output "acr_admin_password" {
  value     = azurerm_container_registry.acr.admin_password
  sensitive = true
}
