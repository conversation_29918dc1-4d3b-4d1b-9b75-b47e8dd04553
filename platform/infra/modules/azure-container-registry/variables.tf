variable "name" {
  description = "The name of the Azure Container Registry instance"
  type        = string
}

variable "location" {
  description = "The Azure location for the Container Registry"
  type        = string
}

variable "resource_group_name" {
  description = "The resource group for the Container Registry"
  type        = string
}

variable "sku" {
  description = "The SKU for the Container Registry (Basic, Standard, Premium)"
  type        = string
  default     = "Basic"
}

variable "admin_enabled" {
  description = "Enable admin user for the Container Registry"
  type        = bool
  default     = false
}

variable "georeplication_locations" {
  description = "A list of locations where the container registry should be geo-replicated. Only supported with Premium SKU."
  type = list(object({
    location                = string
    zone_redundancy_enabled = bool
  }))
  default = []
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
}

variable "log_analytics_workspace_id" {
  description = "The logs workspace id"
  type        = string
}
