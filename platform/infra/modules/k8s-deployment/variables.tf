variable "namespace_name" {
  description = "The Kubernetes namespace for the application"
  type        = string
}

variable "app_name" {
  description = "The name of the application"
  type        = string
  default     = "app"
}

variable "app_image" {
  description = "The Docker image for the application"
  type        = string
}

variable "location" {
  description = "The location where the resources will be created"
  type        = string
  nullable    = false
}

variable "resource_group_name" {
  description = "The name of the resource group in which to create resources"
  type        = string
  nullable    = false
}

variable "replica_count" {
  description = "The number of replicas for the application deployment"
  type        = number
  default     = 1
}

variable "environment_variables" {
  description = "A map of environment variables to inject into the application"
  type        = map(string)
  default     = {}
}

variable "redis_connection_string" {
  description = "Redis connection string"
  type        = string
}

variable "database_url" {
  description = "The URL for the database connection"
  type        = string
}

variable "elasticsearch_url" {
  description = "The URL for the Elasticsearch connection"
  type        = string
}

variable "elasticsearch_api_key" {
  description = "The API key for Elasticsearch"
  type        = string
}

variable "openai_api_base_url" {
  description = "The base URL for Azure OpenAI API"
  type        = string
}

variable "openai_api_key" {
  description = "The API key for Azure OpenAI API"
  type        = string
}


variable "app_domain" {
  description = "The domain for the application"
  type        = string
}

variable "cert_manager_issuer" {
  description = "The issuer for the certificate manager"
  type        = string
  default     = "letsencrypt"
}

variable "secret_key" {
  description = "The secret key for the application"
  type        = string
  sensitive   = true
}

variable "platform_resource_namespace" {
  description = "The namespace for the platform resources (useful for colocating multiple apps in the same environment)"
  type        = string
  default     = "app"
}

variable "graphql_api_keys" {
  description = "The API keys for the GraphQL API"
  type        = list(string)
  sensitive   = false
  default     = []
}

variable "app_ip_whitelist" {
  description = "List of IP ranges to whitelist for the Application"
  type        = list(string)
  default = [
    "0.0.0.0/0" # Allow all incoming traffic
  ]
}


variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
}
