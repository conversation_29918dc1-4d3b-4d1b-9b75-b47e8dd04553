resource "kubernetes_namespace_v1" "app_namespace" {
  metadata {
    name = var.namespace_name
  }
}

locals {
  # Combine all environment variables including both static and dynamic ones
  app_env = concat(
    [
      {
        name  = "PLATFORM_RESOURCE_NAMESPACE"
        value = var.platform_resource_namespace
      },
      {
        name  = "KV_REST_API_TOKEN"
        value = random_password.srh_token.result
      },
      {
        name  = "KV_REST_API_URL"
        value = "http://${kubernetes_service_v1.serverless_redis_http_service.metadata.0.name}.${kubernetes_service_v1.serverless_redis_http_service.metadata.0.namespace}.svc.cluster.local"
      },
      {
        name  = "KV_URL"
        value = var.redis_connection_string
      },
      {
        name  = "DATABASE_URL"
        value = var.database_url
      },
      {
        name  = "ELASTICSEARCH_URL"
        value = var.elasticsearch_url
      },
      {
        name  = "ELASTICSEARCH_API_KEY"
        value = var.elasticsearch_api_key
      },
      {
        name  = "OPENAI_API_KEY"
        value = var.openai_api_key
      },
      {
        name  = "OPENAI_API_BASE_URL"
        value = var.openai_api_base_url
      },
      {
        name  = "NEXTAUTH_URL"
        value = "https://${var.app_domain}"
      },
      {
        name  = "NEXTAUTH_SECRET"
        value = random_password.nextauth_secret.result
      },
      {
        name  = "SECRET_KEY"
        value = var.secret_key
      },
      {
        name  = "GRAPHQL_API_KEYS"
        value = join(",", var.graphql_api_keys)
      }
    ],
    [
      for k, v in var.environment_variables : {
        name  = k
        value = v
      }
    ]
  )
}

resource "kubernetes_deployment_v1" "app" {
  depends_on = [kubernetes_namespace_v1.app_namespace]

  metadata {
    name      = var.app_name
    namespace = var.namespace_name
  }

  spec {
    replicas = var.replica_count

    selector {
      match_labels = {
        app = var.app_name
      }
    }

    template {
      metadata {
        labels = {
          app = var.app_name
        }
      }
      spec {
        container {
          name  = var.app_name
          image = var.app_image
          port {
            container_port = 3000
          }

          // Add liveness probe
          liveness_probe {
            http_get {
              path   = "/auth/login"
              port   = 3000
              scheme = "HTTP"
            }
            initial_delay_seconds = 60
            timeout_seconds       = 1
            period_seconds        = 15
            success_threshold     = 1
            failure_threshold     = 3
          }

          // Add readiness probe
          readiness_probe {
            http_get {
              path   = "/auth/login"
              port   = 3000
              scheme = "HTTP"
            }
            initial_delay_seconds = 3
            timeout_seconds       = 20
            period_seconds        = 15
            success_threshold     = 1
            failure_threshold     = 10
          }

          security_context {
            read_only_root_filesystem  = true
            allow_privilege_escalation = false
            run_as_non_root            = true
            run_as_user                = 1000
          }

          dynamic "env" {
            for_each = local.app_env
            content {
              name  = env.value.name
              value = env.value.value
            }
          }
        }
      }
    }
  }
}

resource "random_password" "srh_token" {
  length  = 32
  special = false
}

resource "random_password" "nextauth_secret" {
  length  = 32
  special = true
}

resource "kubernetes_deployment_v1" "serverless_redis_http" {
  metadata {
    name      = "serverless-redis-http"
    namespace = var.namespace_name
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        app = "serverless-redis-http"
      }
    }

    template {
      metadata {
        labels = {
          app = "serverless-redis-http"
        }
      }
      spec {
        container {
          name  = "serverless-redis-http"
          image = "hiett/serverless-redis-http:0.0.10"
          port {
            container_port = 80
          }
          security_context {
            allow_privilege_escalation = false
            read_only_root_filesystem  = true
          }
          env {
            name  = "SRH_MODE"
            value = "env"
          }
          env {
            name  = "SRH_TOKEN"
            value = random_password.srh_token.result
          }
          env {
            name  = "SRH_CONNECTION_STRING"
            value = var.redis_connection_string
          }
        }
      }
    }
  }
}
resource "kubernetes_service_v1" "app_service" {
  depends_on = [kubernetes_namespace_v1.app_namespace] # Ensure namespace exists

  metadata {
    name      = var.app_name
    namespace = var.namespace_name
  }

  spec {
    selector = {
      app = var.app_name
    }
    port {
      port        = 80
      target_port = split(":", var.app_image).1 == "placeholder" ? 80 : 3000 // TODO: Remove when repacing the placholder image
    }
  }
}

resource "kubernetes_service_v1" "serverless_redis_http_service" {
  metadata {
    name      = "serverless-redis-http"
    namespace = var.namespace_name
  }

  spec {
    selector = {
      app = "serverless-redis-http"
    }
    port {
      port        = 80
      target_port = 80
    }
    type = "ClusterIP"
  }
}

resource "azurerm_web_application_firewall_policy" "app_firewall_rule" {
  name                = "${var.namespace_name}-waf-policy"
  resource_group_name = var.resource_group_name
  location            = var.location
  tags                = var.tags

  policy_settings {
    enabled = true
    mode    = "Prevention"
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
      rule_group_override {
        rule_group_name = "REQUEST-942-APPLICATION-ATTACK-SQLI"
        rule {
          action  = "Log"
          enabled = true
          id      = 942110
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942200
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942260
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942330
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942340
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942370
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942430
        }
        rule {
          action  = "Log"
          enabled = true
          id      = 942440
        }
      }
    }
  }

  custom_rules {
    name      = "AllowAllowDomainChallenge"
    priority  = 1
    rule_type = "MatchRule"
    action    = "Allow"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator     = "Contains"
      match_values = ["/.well-known/acme-challenge/"]
    }
  }

  custom_rules {
    name      = "AllowSpecificIPs"
    priority  = 2
    rule_type = "MatchRule"
    action    = "Block"
    enabled   = true

    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }

      operator           = "IPMatch"
      negation_condition = true
      match_values       = var.app_ip_whitelist
    }
  }
}

resource "kubernetes_ingress_v1" "app_ingress" {
  depends_on = [kubernetes_namespace_v1.app_namespace] # Ensure namespace exists

  metadata {
    name      = var.app_name
    namespace = var.namespace_name
    annotations = {
      "cert-manager.io/cluster-issuer"                          = var.cert_manager_issuer
      "kubernetes.io/ingress.class"                             = "azure/application-gateway"
      "appgw.ingress.kubernetes.io/ssl-redirect"                = "true"
      "appgw.ingress.kubernetes.io/request-timeout"             = "300"
      "appgw.ingress.kubernetes.io/connection-draining"         = "true"
      "appgw.ingress.kubernetes.io/connection-draining-timeout" = "30"
      "appgw.ingress.kubernetes.io/waf-policy-for-path"         = azurerm_web_application_firewall_policy.app_firewall_rule.id
    }
  }

  spec {
    tls {
      hosts       = [var.app_domain]
      secret_name = "${split(".", var.app_domain).0}-tls"
    }

    rule {
      host = var.app_domain
      http {
        path {
          path      = "/"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service_v1.app_service.metadata[0].name
              port {
                number = 80
              }
            }
          }
        }
      }
    }
  }
}

output "environment_variables" {
  description = "All environment variables used in the deployment"
  value       = local.app_env
  sensitive   = true
}

output "environment_variable_names" {
  description = "Names of all environment variables used in the deployment"
  value       = [for env in local.app_env : env.name]
}

output "srh_token" {
  description = "The token used to authenticate with the serverless-redis-http service"
  value       = random_password.srh_token.result
}

output "srh_url" {
  description = "The URL of the serverless-redis-http service"
  value       = "http://${kubernetes_service_v1.serverless_redis_http_service.metadata.0.name}.${kubernetes_service_v1.serverless_redis_http_service.metadata.0.namespace}.svc.cluster.local"
}

output "nextauth_secret" {
  description = "The secret used by NextAuth"
  value       = random_password.nextauth_secret.result
}
