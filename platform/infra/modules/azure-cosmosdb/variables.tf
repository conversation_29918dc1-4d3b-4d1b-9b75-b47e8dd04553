variable "name" {
  description = "The name of the Cosmos DB PostgreSQL instance"
  type        = string
}

variable "location" {
  description = "The Azure location for the Cosmos DB instance"
  type        = string
}

variable "resource_group_name" {
  description = "The resource group for the Cosmos DB instance"
  type        = string
}

variable "coordinator_storage_quota_in_mb" {
  description = "The storage quota for the coordinator in MB"
  type        = number
  default     = 131072 # Example value for coordinator storage
}

variable "coordinator_vcore_count" {
  description = "The vCore count for the coordinator"
  type        = number
  default     = 2 # Example value for coordinator vCore count
}

variable "node_count" {
  description = "The number of worker nodes in the PostgreSQL cluster"
  type        = number
  default     = 0 # Set to 0 for a single node (no workers)
}

variable "node_vcores" {
  description = "The vCores for each worker node in the PostgreSQL cluster"
  type        = number
  default     = 2 # Example for worker node vCore count
}

variable "node_storage_quota_in_mb" {
  description = "The storage quota for each worker node in MB"
  type        = number
  default     = 524288 # See https://github.com/hashicorp/terraform-provider-azurerm/issues/23924
}

variable "ha_enabled" {
  description = "Enable high availability for the Cosmos DB PostgreSQL cluster"
  type        = bool
  default     = false
}

variable "coordinator_public_ip_access_enabled" {
  description = "Enable public IP access for the coordinator"
  type        = bool
  default     = true
}

variable "node_public_ip_access_enabled" {
  description = "Enable public IP access for the worker nodes"
  type        = bool
  default     = false
}

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
}
