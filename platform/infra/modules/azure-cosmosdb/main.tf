# Generate admin password (auto-generated)
resource "random_password" "cosmosdb_admin_password" {
  length  = 16
  special = true
}

# Cosmos DB for PostgreSQL API
resource "azurerm_cosmosdb_postgresql_cluster" "cosmosdb" {
  name                = var.name
  location            = var.location
  resource_group_name = var.resource_group_name

  # Configure the DB admin login details
  administrator_login_password = random_password.cosmosdb_admin_password.result

  # Configure the coordinator node (only used for multi-node clusters)
  coordinator_storage_quota_in_mb = var.coordinator_storage_quota_in_mb
  coordinator_vcore_count         = var.coordinator_vcore_count

  # Configure the worker nodes
  node_count               = var.node_count
  node_vcores              = var.node_vcores
  node_storage_quota_in_mb = var.node_storage_quota_in_mb

  # Optional settings for public IP and high availability
  ha_enabled                           = var.ha_enabled
  coordinator_public_ip_access_enabled = var.coordinator_public_ip_access_enabled
  node_public_ip_access_enabled        = var.node_public_ip_access_enabled

  tags = var.tags
}

output "db_admin_password" {
  value     = random_password.cosmosdb_admin_password.result
  sensitive = true
}

output "db_url" {
  value     = "postgresql://citus:${urlencode(azurerm_cosmosdb_postgresql_cluster.cosmosdb.administrator_login_password)}@${azurerm_cosmosdb_postgresql_cluster.cosmosdb.servers[0].fqdn}:5432/citus?sslmode=require"
  sensitive = true
}
