variable "name" {
  description = "The name of the Redis instance"
  type        = string
}

variable "location" {
  description = "The Azure location"
  type        = string
}

variable "resource_group_name" {
  description = "The resource group name for the Redis cache"
  type        = string
}

variable "capacity" {
  description = "The capacity of the Redis cache"
  type        = number
  default     = 1
}

variable "family" {
  description = "The Redis family type (C for standard, P for premium)"
  type        = string
  default     = "C"
}

variable "sku_name" {
  description = "The SKU name for the Redis cache"
  type        = string
  default     = "Standard"
}

variable "tags" {
  description = "Tags for the Redis cache"
  type        = map(string)
}
