#==============================================================================
# Project and Environment
#==============================================================================

variable "project" {
  description = "The project name"
  type        = string
  default     = "core-infra"
}

variable "project_abbreviation" {
  description = "The abbreviated project name (used for constructing (often globally unique) 24-character resource names without special characters)"
  type        = string
  default     = "kreios"
}

variable "environment" {
  description = "The environment name"
  type        = string
  default     = "staging"
}

#==============================================================================
# Azure Subscription and Region
#==============================================================================

variable "subscription_id" {
  description = "The Azure subscription ID"
  type        = string
  default     = "44705706-f918-4504-aec7-3cbc0e9d11c9"
}

variable "location" {
  description = "The Azure location in which to create the resources"
  type        = string
  default     = "westeurope"
}

variable "resource_group_name" {
  description = "The resource group in which to create the resources"
  type        = string
  default     = "staging-core-infra"
}

#==============================================================================
# Kubernetes Configuration
#==============================================================================

variable "k8s_dns_prefix" {
  description = "The DNS prefix for the Kubernetes cluster"
  type        = string
  default     = "kreios-staging"
}

variable "k8s_sku_tier" {
  description = "SKU tier for the Kubernetes cluster"
  type        = string
  default     = "Standard"
}

variable "k8s_node_size" {
  description = "The VM size for the Kubernetes cluster nodes"
  type        = string
  default     = "Standard_D4as_v4"
}

variable "k8s_node_count_min" {
  description = "The minimum number of nodes in the Kubernetes cluster"
  type        = number
  default     = 1
}

variable "k8s_node_count_max" {
  description = "The maximum number of nodes in the Kubernetes cluster"
  type        = number
  default     = 5
}

variable "dns_name_label" {
  description = "The label to use for generating the Azure-managed DNS name for the Application Gateway (<label>.<location>.cloudapp.azure.com)"
  type        = string
  default     = "kreios-staging"
}

#==============================================================================
# Elastic Cloud Configuration
#==============================================================================

variable "elastic_cloud_email_address" {
  description = "The email address associated with the underlying Elastic Cloud account. Changing this forces a new Elasticsearch to be created."
  type        = string
  default     = "<EMAIL>"
}

#==============================================================================
# Authentication and Security
#==============================================================================

variable "acme_email" {
  description = "Email address for Let's Encrypt registration and notifications"
  type        = string
  default     = "<EMAIL>"
}

#==============================================================================
# Resource Tags
#==============================================================================

variable "tags" {
  description = "Tags for the resources"
  type        = map(string)
  default = {
    CostCenter  = "kreios-core-infra-staging"
    Environment = "staging"
    ManagedBy   = "Terraform"
    Owner       = "Christian Hillerkus"
    Project     = "kreios-core-infra"
    Purpose     = "Core staging infrastructure to be shared across all projects at Kreios"
  }
}
