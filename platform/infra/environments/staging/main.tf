terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = ">= 4.13.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.33.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = ">= 2.16.1"
    }
  }
  backend "azurerm" {
    resource_group_name  = "staging-core-infra"
    storage_account_name = "kreiosstagingsa"
    container_name       = "tfstate"
    key                  = "staging-core-infra.tfstate"
  }
}

provider "azurerm" {
  features {}
  subscription_id                 = var.subscription_id
  resource_provider_registrations = "core"
  resource_providers_to_register = [
    "Microsoft.Cache",
    "Microsoft.CognitiveServices",
    "Microsoft.Communication",
    "Microsoft.ContainerRegistry",
    "Microsoft.ContainerService",
    "Microsoft.DataLakeStore",
    "Microsoft.DBforPostgreSQL",
    "Microsoft.Elastic",
    "Microsoft.EventGrid",
    "Microsoft.KeyVault",
    "Microsoft.OperationalInsights",
    "Microsoft.PolicyInsights",
    "Microsoft.Insights"
  ]
}

data "azurerm_resource_group" "rg" {
  name = var.resource_group_name
}

data "azurerm_storage_account" "sa" {
  name                = "${var.project_abbreviation}${var.environment}sa"
  resource_group_name = data.azurerm_resource_group.rg.name
}

module "monitoring" {
  source              = "../../modules/azure-monitoring"
  location            = data.azurerm_resource_group.rg.location
  name                = "${var.project}-${var.environment}"
  resource_group_name = data.azurerm_resource_group.rg.name
  retention_in_days   = 366
  tags                = var.tags
}

module "acr" {
  source                     = "../../modules/azure-container-registry"
  location                   = data.azurerm_resource_group.rg.location
  resource_group_name        = data.azurerm_resource_group.rg.name
  name                       = "${var.project_abbreviation}${var.environment}acr"
  tags                       = var.tags
  log_analytics_workspace_id = module.monitoring.log_analytics_workspace_id
}

module "aks-cluster" {
  source                       = "../../modules/azure-kubernetes"
  location                     = data.azurerm_resource_group.rg.location
  resource_group_name          = data.azurerm_resource_group.rg.name
  cluster_name                 = "${var.project}-${var.environment}-k8s"
  dns_prefix                   = var.k8s_dns_prefix
  sku_tier                     = var.k8s_sku_tier
  node_size                    = var.k8s_node_size
  node_count_min               = var.k8s_node_count_min
  node_count_max               = var.k8s_node_count_max
  agw_dns_name_label           = var.dns_name_label
  acr_id                       = module.acr.acr_id
  allowed_container_registries = "^(docker\\.io|mcr\\.microsoft\\.com|${var.project_abbreviation}${var.environment}acr\\.azurecr\\.io)(\\/.*)?"
  tags                         = var.tags
  log_analytics_workspace_id   = module.monitoring.log_analytics_workspace_id
}

provider "kubernetes" {
  host                   = module.aks-cluster.host
  client_certificate     = base64decode(module.aks-cluster.client_certificate)
  client_key             = base64decode(module.aks-cluster.client_key)
  cluster_ca_certificate = base64decode(module.aks-cluster.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = module.aks-cluster.host
    client_certificate     = base64decode(module.aks-cluster.client_certificate)
    client_key             = base64decode(module.aks-cluster.client_key)
    cluster_ca_certificate = base64decode(module.aks-cluster.cluster_ca_certificate)
  }
}

module "k8s_cert_manager" {
  source     = "../../modules/k8s-cert-manager"
  acme_email = var.acme_email
}

module "elastic" {
  source                      = "../../modules/azure-elastic"
  location                    = data.azurerm_resource_group.rg.location
  resource_group_name         = data.azurerm_resource_group.rg.name
  name                        = "${var.project}-${var.environment}-elastic"
  elastic_cloud_email_address = var.elastic_cloud_email_address
  tags                        = var.tags
}

module "vpn" {
  source              = "../../modules/azure-vpn"
  name                = "${var.project}-${var.environment}-vpn"
  location            = data.azurerm_resource_group.rg.location
  resource_group_name = data.azurerm_resource_group.rg.name
  tags                = var.tags
}
