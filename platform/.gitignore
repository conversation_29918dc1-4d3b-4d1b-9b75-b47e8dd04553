# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
.pnpm-store

# random local files that don't need to be tracked
/local

# testing
coverage

# next.js
.next/
out/
next-env.d.ts

# nitro
.nitro/
.output/

# expo
.expo/
expo-env.d.ts
apps/expo/.gitignore
apps/expo/ios
apps/expo/android

# production
build

# misc
.DS_Store
.direnv
*.pem
.idea/*

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
dist/

# turbo
.turbo

# Sentry Config File
.sentryclirc
