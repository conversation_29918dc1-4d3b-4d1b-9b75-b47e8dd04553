# Kreios Liquiddata Core - Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-12-10

Initial release of the Kreios Liquiddata Core platform, created from splitting the legacy Liquiddata monorepo into project-specific repositories and this core platform repository. This repository contains all the shared components, utilities, and infrastructure that were previously in the `platform` directory of the monorepo.

### Features

- Core UI component library based on shadcn/ui and Radix primitives
- Data table implementation using Tanstack Table
- Admin layout components for consistent application structure
- Authentication and authorization system
- Command menu for quick navigation
- Copilot integration

### Infrastructure

- Terraform modules for standardized client-side deployments
- Environment configurations for different deployment scenarios
- Reusable infrastructure components for hosting Liquiddata apps

### Developer Experience

- Monorepo setup with Turborepo
- TypeScript configuration and best practices
- ESLint and Prettier configurations
- Platform management CLI commands
