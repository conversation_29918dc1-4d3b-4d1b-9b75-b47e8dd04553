{"name": "@kreios/cli", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "format:fix": "prettier --write . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "platform": "tsx src/cli.ts", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@commander-js/extra-typings": "12.1.0", "@t3-oss/env-core": "0.11.1"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/node": "20.14.10", "eslint": "9.10.0", "prettier": "3.4.2", "tsx": "4.19.0", "typescript": "5.6.2"}}