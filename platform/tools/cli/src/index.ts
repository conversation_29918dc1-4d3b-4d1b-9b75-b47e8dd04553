#!/usr/bin/env node
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Command } from "@commander-js/extra-typings"

import { registerContributeCommand } from "./commands/contribute"
import { registerConvertEnvFileCommand } from "./commands/convert-env-file"
import { registerUpgradeCommand } from "./commands/upgrade"
import { registerVersionCommand } from "./commands/version"

export const createCLI = () => {
  const program = new Command().name("platform").description("CLI tool for the Kreios platform")
  registerVersionCommand(program)
  registerUpgradeCommand(program)
  registerContributeCommand(program)
  registerConvertEnvFileCommand(program)
  return program
}
