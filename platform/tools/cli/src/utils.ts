/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { existsSync } from "node:fs"
import { dirname, resolve } from "node:path"

/**
 * Finds the root directory of the repository by looking for the pnpm-workspace.yaml file
 * starting from the current working directory and moving up the directory tree until either
 * finding the workspace file or reaching the filesystem root.
 */
export const findRepoRoot = (): string => {
  let currentDir = process.cwd()

  while (true) {
    if (existsSync(resolve(currentDir, "pnpm-workspace.yaml"))) {
      console.log("Found repository root at:", currentDir)
      return currentDir
    }

    const parentDir = dirname(currentDir)
    if (parentDir === currentDir) {
      // We've reached the filesystem root without finding the workspace file
      throw new Error("Could not find repository root. Are you inside a project repository?")
    }
    currentDir = parentDir
  }
}
