/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { execSync } from "node:child_process"
import type { Command } from "@commander-js/extra-typings"

import { findRepoRoot } from "../utils"

/**
 * Normalizes a changeset name by:
 * - Stripping "feature/" prefix if present
 * - Converting to lowercase
 * - Replacing any non-word characters (except -, . and _) with underscores
 * - Replacing whitespace with underscores
 */
const normalizeChangeset = (changeset: string): string => {
  return (
    changeset
      .replace(/^feature\//, "") // Strip feature/ prefix if present
      .toLowerCase()
      .replace(/\s+/g, "_") // Replace whitespace with underscore
      // eslint-disable-next-line no-useless-escape
      .replace(/[^\w\-\.]/g, "_")
  ) // Replace any special chars except -, . and _ with underscore
}

/**
 * Contributes changes back to the Liquiddata platform core using git subtree
 */
const contributeChanges = (changeset: string) => {
  const normalizedChangeset = normalizeChangeset(changeset)
  try {
    const repoRoot = findRepoRoot()
    execSync(
      `git subtree push --prefix=platform **************:Kreios-S-A-R-L/liquiddata-core.git feature/${normalizedChangeset}`,
      { stdio: "inherit", cwd: repoRoot }
    )
  } catch (error) {
    console.error("Failed to contribute changes:", error)
    process.exit(1)
  }
}

/**
 * Add the contribute command to the CLI program
 */
export const registerContributeCommand = (program: Command) => {
  program
    .command("contribute")
    .description("Contribute changes back to the Liquiddata platform core")
    .argument(
      "<changeset>",
      "Name for the changeset/branch (will be normalized to contain only lowercase letters, numbers, -, . and _)"
    )
    .action(contributeChanges)
}
