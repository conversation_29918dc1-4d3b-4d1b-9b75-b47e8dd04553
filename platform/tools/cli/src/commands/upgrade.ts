/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { execSync } from "node:child_process"
import type { Command } from "@commander-js/extra-typings"

import { findRepoRoot } from "../utils"

/**
 * Resolves version aliases to their actual git refs
 */
const resolveVersion = (version: string): string => {
  if (version === "latest") {
    return "main"
  }
  return version
}

/**
 * Upgrades the platform to a specific version using git subtree
 */
const upgradePlatform = (version: string) => {
  const resolvedVersion = resolveVersion(version)
  try {
    const repoRoot = findRepoRoot()
    const command = `git subtree pull --prefix=platform --squash **************:Kreios-S-A-R-L/liquiddata-core.git ${resolvedVersion}`
    console.log("Running command:", command)
    execSync(command, { stdio: "inherit", cwd: repoRoot })
  } catch (error) {
    if (error instanceof Error && "status" in error) {
      console.error("Failed to upgrade platform. Exit code:", error.status)
    } else {
      console.error("Failed to upgrade platform. Unknown error.")
    }
    process.exit(1)
  }
}

/**
 * Add the upgrade command to the CLI program
 */
export const registerUpgradeCommand = (program: Command) => {
  program
    .command("upgrade")
    .description("Upgrade to a specific platform version")
    .argument("<version>", 'Version to upgrade to (e.g., "v1.1.0", "latest" (aliases to "main"), "develop")')
    .action(upgradePlatform)
}
