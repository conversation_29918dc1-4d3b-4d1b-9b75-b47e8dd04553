/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import * as fs from "fs"
import type { Command } from "@commander-js/extra-typings"

const convertEnvFile = (inputFile: string, outputFile: string) => {
  // Read file contents
  const content = fs.readFileSync(inputFile, "utf8")
  // Parse it as JSON
  const data = JSON.parse(content) as Array<{ name: string; value: string }>
  // Build .env-format output
  let envOutput
  if (Array.isArray(data)) {
    envOutput = data.map((item) => `${item.name}=${item.value}`).join("\n")
  } else {
    envOutput = Object.entries(data)
      .map(([key, value]) => `${key}=${value as string}`)
      .join("\n")
  }

  // Write to output file
  fs.writeFileSync(outputFile, envOutput, "utf8")
  console.log(`Wrote ${data.length} entries to ${outputFile}`)
}

export const registerConvertEnvFileCommand = (program: Command) => {
  program
    .command("convert-env-file")
    .description('Convert JSON-ish env data (with "name" = "X" and "value" = "Y") into a standard .env format')
    .argument("<inputFile>", "Path to the input file")
    .argument("<outputFile>", "Path to the output file")
    .action(convertEnvFile)
}
