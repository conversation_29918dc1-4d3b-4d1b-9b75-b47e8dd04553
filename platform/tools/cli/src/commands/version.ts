/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { readFileSync } from "node:fs"
import { dirname, join } from "node:path"
import { fileURLToPath } from "node:url"
import type { Command } from "@commander-js/extra-typings"

const __dirname = dirname(fileURLToPath(import.meta.url))

/**
 * Get the version from the package.json file
 */
const getVersion = () => {
  type PackageJson = { version: string }
  const packageJsonPath = join(__dirname, "..", "..", "package.json")
  const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf-8")) as PackageJson
  return packageJson.version
}

/**
 * Add the version command to the CLI program
 */
export const registerVersionCommand = (program: Command) => {
  program
    .command("version")
    .description("Display the CLI version")
    .action(() => {
      console.log(getVersion())
    })

  // Also set the version for the --version flag
  program.version(getVersion())
}
