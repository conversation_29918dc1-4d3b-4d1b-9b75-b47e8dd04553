{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "ES2022", "lib": ["ES2022"], "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "noErrorTruncation": true, "incremental": true, "disableSourceOfProjectReferenceRedirect": true, "strict": true, "noUncheckedIndexedAccess": false, "checkJs": true, "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true}, "exclude": ["node_modules", "build", "dist", ".next", ".expo"]}