#!/usr/bin/env tsx

/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { execSync } from "node:child_process"
import fs from "node:fs"
import path from "node:path"

/**
 * Configuration type for the pre-commit hook
 * @property {string} commitAuthorEmailRegex - Regular expression pattern to validate commit author email addresses
 */
type Config = {
  commitAuthorEmailRegex: string
}

/**
 * Structure of the package.json file, focusing on commit-related configuration
 * @property {Object} [commitConfig] - Optional configuration for commit-related settings
 * @property {string} [commitConfig.authorEmailRegex] - Optional regex pattern for validating commit author emails
 */
type PackageJson = {
  commitConfig?: {
    authorEmailRegex?: string
  }
}

/**
 * Retrieves the configuration from the root package.json file
 * @returns {Config} The configuration object containing commit author email regex
 * @throws Will exit process if package.json cannot be read or parsed
 */
export const getConfig = (): Config => {
  try {
    const rootPackageJson = JSON.parse(
      fs.readFileSync(path.resolve(process.cwd(), "..", "..", "..", "package.json"), "utf-8")
    ) as PackageJson
    return {
      commitAuthorEmailRegex: rootPackageJson.commitConfig?.authorEmailRegex ?? ".*@kreios\\.lu$",
    }
  } catch (error) {
    console.error("Error reading package.json:", error instanceof Error ? error.message : String(error))
    process.exit(1)
  }
}

/**
 * Retrieves the email of the git commit author
 * @returns {string} The email address of the git user
 * @throws Will exit process if git config cannot be read
 */
export const getCommitAuthorEmail = (): string => {
  try {
    return execSync("git config user.email").toString().trim()
  } catch (error) {
    console.error("Error getting git user email:", error instanceof Error ? error.message : String(error))
    process.exit(1)
  }
}

/**
 * Retrieves a list of all staged files in the git repository
 * @returns {string[]} Array of staged file paths
 * @throws Will exit process if git command fails
 */
export const getStagedFiles = (): string[] => {
  try {
    const output = execSync("git diff --cached --name-only --diff-filter=ACMR").toString()
    return output.split("\n").filter(Boolean)
  } catch (error) {
    console.error("Error getting staged files:", error instanceof Error ? error.message : String(error))
    process.exit(1)
  }
}

/**
 * Validates if the provided email matches the required regex pattern
 * @param {string} email - The email address to validate
 * @param {string} regex - The regex pattern to match against
 * @returns {boolean} True if email matches the pattern, false otherwise
 */
export const validateAuthorEmail = (email: string, regex: string): boolean => {
  const emailRegex = new RegExp(regex)
  return emailRegex.test(email)
}

/**
 * Validates that staged files are either all from platform/ directory or none are
 * @param {string[]} files - Array of file paths to validate
 * @returns {boolean} True if the file paths are valid according to the rules
 */
export const validateFilePaths = (files: string[]): boolean => {
  const platformFiles = files.filter((file) => file.startsWith("platform/"))
  const nonPlatformFiles = files.filter((file) => !file.startsWith("platform/"))

  // If there are no files, that's valid
  if (files.length === 0) return true

  // Either all files should be from platform/ or none should be
  return platformFiles.length === 0 || nonPlatformFiles.length === 0
}

/**
 * Main function that runs all pre-commit checks
 * Validates commit author email and ensures consistent file paths
 */
const main = (): void => {
  const config = getConfig()
  const authorEmail = getCommitAuthorEmail()
  const stagedFiles = getStagedFiles()

  // Validate author email
  if (!validateAuthorEmail(authorEmail, config.commitAuthorEmailRegex)) {
    console.error(
      `❌ Commit author email "${authorEmail}" does not match the required pattern: ${config.commitAuthorEmailRegex}`
    )
    process.exit(1)
  }

  // Validate file paths
  if (!validateFilePaths(stagedFiles)) {
    console.error("❌ Commits must contain either only platform/ files or only non-platform/ files")
    console.error("Staged files:")
    stagedFiles.forEach((file) => console.error(`  ${file}`))
    process.exit(1)
  }

  console.log("✅ Pre-commit checks passed")
  process.exit(0)
}

// Only run main when this file is being executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
