/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { execSync } from "child_process"
import fs from "fs"
import { resolve } from "path"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

// Import the functions after mocking
import { getCommitAuthorEmail, getConfig, getStagedFiles, validateAuthorEmail, validateFilePaths } from "./pre-commit"

// Mock the child_process and fs modules
vi.mock("child_process")
vi.mock("fs")
vi.mock("path")

describe("pre-commit hook", () => {
  beforeEach(() => {
    vi.resetAllMocks()
    // Mock resolve to return a fixed path
    vi.mocked(resolve).mockReturnValue("/mock/path/package.json")
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("getConfig", () => {
    it("should return default email regex when package.json doesn't have commitConfig", () => {
      vi.mocked(fs.readFileSync).mockReturnValue(JSON.stringify({}))
      const config = getConfig()
      expect(config.commitAuthorEmailRegex).toBe(".*@kreios\\.lu$")
    })

    it("should return configured email regex from package.json", () => {
      vi.mocked(fs.readFileSync).mockReturnValue(
        JSON.stringify({
          commitConfig: {
            authorEmailRegex: ".*@custom\\.com$",
          },
        })
      )
      const config = getConfig()
      expect(config.commitAuthorEmailRegex).toBe(".*@custom\\.com$")
    })

    it("should exit with error when package.json cannot be read", () => {
      const mockExit = vi.spyOn(process, "exit").mockImplementation(() => undefined as never)
      vi.mocked(fs.readFileSync).mockImplementation(() => {
        throw new Error("File not found")
      })

      getConfig()
      expect(mockExit).toHaveBeenCalledWith(1)
    })
  })

  describe("getCommitAuthorEmail", () => {
    it("should return git user email", () => {
      vi.mocked(execSync).mockReturnValue(Buffer.from("<EMAIL>\n"))
      const email = getCommitAuthorEmail()
      expect(email).toBe("<EMAIL>")
    })

    it("should exit with error when git command fails", () => {
      const mockExit = vi.spyOn(process, "exit").mockImplementation(() => undefined as never)
      vi.mocked(execSync).mockImplementation(() => {
        throw new Error("Git command failed")
      })

      getCommitAuthorEmail()
      expect(mockExit).toHaveBeenCalledWith(1)
    })
  })

  describe("getStagedFiles", () => {
    it("should return list of staged files", () => {
      vi.mocked(execSync).mockReturnValue(Buffer.from("file1.ts\nfile2.ts\n"))
      const files = getStagedFiles()
      expect(files).toEqual(["file1.ts", "file2.ts"])
    })

    it("should filter out empty lines", () => {
      vi.mocked(execSync).mockReturnValue(Buffer.from("file1.ts\n\nfile2.ts\n\n"))
      const files = getStagedFiles()
      expect(files).toEqual(["file1.ts", "file2.ts"])
    })

    it("should exit with error when git command fails", () => {
      const mockExit = vi.spyOn(process, "exit").mockImplementation(() => undefined as never)
      vi.mocked(execSync).mockImplementation(() => {
        throw new Error("Git command failed")
      })

      getStagedFiles()
      expect(mockExit).toHaveBeenCalledWith(1)
    })
  })

  describe("validateAuthorEmail", () => {
    it("should validate email matching regex", () => {
      expect(validateAuthorEmail("<EMAIL>", ".*@kreios\\.lu$")).toBe(true)
      expect(validateAuthorEmail("<EMAIL>", ".*@kreios\\.lu$")).toBe(false)
    })

    it("should handle custom regex patterns", () => {
      expect(validateAuthorEmail("<EMAIL>", ".*@custom\\.com$")).toBe(true)
      expect(validateAuthorEmail("<EMAIL>", ".*@custom\\.com$")).toBe(false)
    })
  })

  describe("validateFilePaths", () => {
    it("should allow empty file list", () => {
      expect(validateFilePaths([])).toBe(true)
    })

    it("should allow only platform files", () => {
      expect(validateFilePaths(["platform/file1.ts", "platform/file2.ts"])).toBe(true)
    })

    it("should allow only non-platform files", () => {
      expect(validateFilePaths(["apps/file1.ts", "packages/file2.ts"])).toBe(true)
    })

    it("should reject mixed platform and non-platform files", () => {
      expect(validateFilePaths(["platform/file1.ts", "apps/file2.ts"])).toBe(false)
    })
  })
})
