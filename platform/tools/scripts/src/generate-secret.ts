/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import * as crypto from "crypto"
import { program } from "@commander-js/extra-typings"

// Define the program
program.name("generate-secret").description("CLI tool to generate a JWK-formatted AES-CBC key").version("0.1.0")

// Define the 'generate-secret' command
program.description("Generate a JWK-formatted AES-CBC key").action(async () => {
  try {
    const key = await crypto.subtle.generateKey(
      {
        name: "AES-CBC",
        length: 256,
      },
      true,
      ["encrypt", "decrypt"]
    )
    const jwk = JSON.stringify(await crypto.subtle.exportKey("jwk", key))

    console.log(`SECRET_KEY=${jwk}`)
  } catch (error) {
    console.error(error)
  }
})

// Parse the command line arguments
program.parse(process.argv)
