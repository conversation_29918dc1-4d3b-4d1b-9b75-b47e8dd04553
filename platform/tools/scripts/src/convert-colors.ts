/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Command } from "@commander-js/extra-typings"
import convert from "color-convert"
import { z } from "zod"

// Schema for color palette
const ColorPaletteSchema = z.record(z.record(z.string()))

/**
 * This script is made for converting the export output from uicolors.app from hex to hsl
 * and then format it in a way that is easy to copy/paste in the globals.css file.
 */
const program = new Command()
  .name("convert-colors")
  .description("Convert hex colors to HSL format")
  .argument("<colors>", "JSON string of color palette")
  .action((colorsJson: string) => {
    try {
      // Parse and validate input JSON
      const colors = ColorPaletteSchema.parse(JSON.parse(colorsJson))

      // Convert and format each palette
      Object.entries(colors).forEach(([paletteName, hexColors]) => {
        Object.entries(hexColors).forEach(([shade, hexColor]) => {
          // Remove # from hex color if present
          const hex = hexColor.replace("#", "")
          // Convert to HSL
          const [h, s, l] = convert.hex.hsl(hex)
          console.log(`    --${paletteName}-${shade}: ${h} ${s}% ${l}%;`)
        })
        console.log()
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error("Invalid input format:", error.errors)
      } else if (error instanceof SyntaxError) {
        console.error("Invalid JSON:", error.message)
      } else {
        console.error("An error occurred:", error)
      }
      process.exit(1)
    }
  })

program.parse()
