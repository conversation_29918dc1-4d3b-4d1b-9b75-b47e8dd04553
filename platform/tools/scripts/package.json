{"name": "@kreios/scripts", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "convert-colors": "tsx ./src/convert-colors.ts", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "generate-secret": "tsx ./src/generate-secret.ts", "lint": "eslint", "pre-commit": "tsx ./src/git-hooks/pre-commit.ts", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@commander-js/extra-typings": "12.1.0", "color-convert": "2.0.1"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/color-convert": "2.0.4", "@types/node": "20.14.10", "@vitest/coverage-v8": "2.1.1", "eslint": "9.10.0", "prettier": "3.4.2", "tsx": "4.19.0", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}