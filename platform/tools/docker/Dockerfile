FROM node:20-alpine AS alpine
ENV DO_NOT_TRACK=1

# setup pnpm on the alpine base
FROM alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable && npm install -g corepack@latest
RUN pnpm install turbo --global

RUN apk add --no-cache libc6-compat python3 make g++
RUN apk update
RUN apk upgrade openssl

FROM base AS builder

# Set working directory
WORKDIR /app
COPY . .

ARG TURBO_SCOPE
RUN turbo prune ${TURBO_SCOPE} --docker

# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer

WORKDIR /app

# First install the dependencies (as they change less often)
COPY .gitignore .gitignore
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm*.yaml .
COPY --from=builder /app/out/full/platform/patches ./platform/patches
RUN pnpm install --ignore-scripts

# Build the project
COPY --from=builder /app/out/full/ .
COPY turbo.json turbo.json

# Uncomment and use build args to enable remote caching
# ARG TURBO_TEAM
# ENV TURBO_TEAM=$TURBO_TEAM

# ARG TURBO_TOKEN
# ENV TURBO_TOKEN=$TURBO_TOKEN
ENV SKIP_ENV_VALIDATION=true
ENV NEXT_OUTPUT=standalone
ARG TURBO_SCOPE
RUN turbo run build --filter=${TURBO_SCOPE}

# use alpine as the thinest image
FROM alpine AS runner
WORKDIR /app

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

ARG APP_PATH

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/${APP_PATH}/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/${APP_PATH}/.next/static ./${APP_PATH}/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/${APP_PATH}/public ./${APP_PATH}/public

WORKDIR /app/${APP_PATH}

CMD ["node","server.js"]
