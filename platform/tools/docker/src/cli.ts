/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { execSync, exec as nodeExec } from "node:child_process"
import { existsSync, readFileSync } from "node:fs"
import * as os from "node:os"
import { dirname, join } from "node:path"
import { fileURLToPath } from "node:url"
import { promisify } from "node:util"
import { program } from "@commander-js/extra-typings"
import { input, select } from "@inquirer/prompts"
import { globSync } from "glob"

/**
 * Main entry point for the Docker build CLI.
 * Parses options, prompts for missing values, and executes the build.
 */
const main = async () => {
  // Find workspace root for all path resolutions
  const root = findRoot(dirname(fileURLToPath(import.meta.url)))

  // Set up CLI options
  program
    .option("-a, --app <app>", "App to build")
    .option("-r, --repository <repository>", "Repository name")
    .option("-t, --tag <tag>", "Tag")
    .option("-p, --platform <platform>", "Docker platform", getCurrentDockerPlatform())

  program.parse(process.argv)

  const options: Options = program.opts()

  try {
    const buildConfig = await promptForBuildConfig(options, root)
    await buildDockerImage(buildConfig, root)
  } catch (error) {
    console.error("Error building Docker image:", error)
    process.exit(1)
  }
}

// Helper Functions

/**
 * Recursively searches up the directory tree for the workspace root,
 * identified by the presence of pnpm-workspace.yaml
 */
const findRoot = (dir: string): string => {
  try {
    const workspaceFile = join(dir, "pnpm-workspace.yaml")
    readFileSync(workspaceFile)
    return dir
  } catch {
    const parentDir = dirname(dir)
    if (parentDir === dir) {
      throw new Error("Could not find pnpm-workspace.yaml in any parent directory")
    }
    return findRoot(parentDir)
  }
}

/**
 * Prompts the user for all required build configuration,
 * using provided options as defaults where available.
 */
const promptForBuildConfig = async (options: Options, root: string): Promise<DockerBuildConfig> => {
  // Get list of available apps from the workspace
  const apps = getApps(root)

  // Let user select which app to build, or use the one specified in options
  const app =
    options.app ??
    (await select({
      message: "Which app do you want to build?",
      choices: apps.map(({ path, packageName }) => ({ value: packageName, name: `${path} (${packageName})` })),
    }))

  const selectedApp = apps.find((a) => a.packageName === app)
  if (!selectedApp) throw new Error("Selected app not found")

  // Determine Docker repository name
  // Default to Azure Container Registry with app name as repository
  const defaultRepo = `kreiosstagingacr.azurecr.io/${selectedApp.packageName.split("@")[1]}`
  const repository =
    options.repository ??
    (await input({
      message: "Enter the repository name:",
      default: defaultRepo,
    }))

  // Set Docker image tag
  // Default to current git commit SHA for reproducibility
  const tag =
    options.tag ??
    (await input({
      message: "Enter the tag:",
      default: await getGitSha(),
    }))

  // Select target platform for the Docker image
  // Default to current machine's architecture
  const platform =
    options.platform ??
    (await select({
      message: "Select the Docker platform:",
      choices: [
        { value: "linux/amd64", name: "linux/amd64" },
        { value: "linux/arm64", name: "linux/arm64" },
      ],
      default: getCurrentDockerPlatform(),
    }))

  // Locate appropriate Dockerfile and determine build strategy
  const { path: dockerfilePath, useTurboBuild } = locateDockerfile(selectedApp.path, root)
  console.log(`Using Dockerfile: ${dockerfilePath} (uses turbo build: ${useTurboBuild})`)

  // Return complete build configuration
  return {
    appPath: selectedApp.path,
    packageName: selectedApp.packageName,
    repository,
    tag,
    platform,
    dockerfilePath,
    useTurboBuild,
  }
}

/**
 * Retrieves a list of available apps in the workspace by scanning for package.json files
 * in the apps directory.
 */
const getApps = (root: string) => {
  const appPaths = globSync("apps/*/package.json", { cwd: root })

  return appPaths.map((appPath) => {
    const fullPath = join(root, appPath)
    const packageJson = JSON.parse(readFileSync(fullPath, "utf-8")) as { name: string }
    return {
      path: dirname(appPath),
      packageName: packageJson.name,
    }
  })
}

/**
 * Retrieves the current Git SHA for use as the default tag.
 */
const getGitSha = async () => {
  // Convert callback-based exec to Promise-based for cleaner async/await usage
  const exec = promisify(nodeExec)

  const { stdout, stderr } = await exec("git rev-parse HEAD")
  if (stderr) throw new Error(stderr)
  return stdout.trim()
}

/**
 * Returns the appropriate Docker platform based on the host architecture.
 */
const getCurrentDockerPlatform = () => {
  const arch = os.arch()
  return arch.includes("arm") ? "linux/arm64" : "linux/amd64"
}

/**
 * Locates the appropriate Dockerfile for an app and determines if it needs turbo build.
 * Checks in the following order:
 * 1. Dockerfile in the app directory (no turbo build)
 * 2. Custom Dockerfile path specified in package.json (uses turbo build)
 * 3. Falls back to the default platform Dockerfile (uses turbo build)
 */
const locateDockerfile = (appPath: string, root: string): { path: string; useTurboBuild: boolean } => {
  const appDockerfile = join(root, appPath, "Dockerfile")
  if (existsSync(appDockerfile)) {
    return { path: appDockerfile, useTurboBuild: false }
  }

  const packageJsonPath = join(root, appPath, "package.json")
  const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf-8")) as {
    docker?: { dockerfile?: string }
  }

  if (packageJson.docker?.dockerfile) {
    const customPath = join(root, appPath, packageJson.docker.dockerfile)
    if (existsSync(customPath)) {
      return { path: customPath, useTurboBuild: true }
    }
    throw new Error(`Custom Dockerfile specified in package.json not found: ${customPath}`)
  }

  return {
    path: join(root, "platform/tools/docker/Dockerfile"),
    useTurboBuild: true,
  }
}

/**
 * Builds a Docker image using the provided configuration.
 */
const buildDockerImage = async (config: DockerBuildConfig, root: string) => {
  const DOCKER_BUILD_OPTS = `--platform ${config.platform}`

  if (config.useTurboBuild) {
    // Turbo build from repository root
    const command = `docker build ${DOCKER_BUILD_OPTS} -f ${config.dockerfilePath} --build-arg TURBO_SCOPE=${config.packageName} --build-arg APP_PATH=${config.appPath} . -t ${config.repository}:${config.tag}`
    console.log(`Executing command: ${command}`)
    execSync(command, { stdio: "inherit", cwd: root })
  } else {
    // Simple docker build from app directory
    const command = `docker build ${DOCKER_BUILD_OPTS} . -t ${config.repository}:${config.tag}`
    console.log(`Executing command: ${command}`)
    execSync(command, { stdio: "inherit", cwd: join(root, config.appPath) })
  }
}

// Types

interface Options {
  app?: string
  repository?: string
  tag?: string
  platform?: string
}

type DockerBuildConfig = {
  appPath: string
  packageName: string
  repository: string
  tag: string
  platform: string
  dockerfilePath: string
  useTurboBuild: boolean
}

// Execute
await main()
