{"name": "@kreios/docker", "version": "0.1.0", "private": true, "type": "module", "exports": {"./cli": "./cli.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "docker:build": "tsx src/cli.ts", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@commander-js/extra-typings": "12.1.0", "@inquirer/prompts": "3.0.0", "glob": "11.0.0"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/node": "20.14.10", "eslint": "9.10.0", "prettier": "3.4.2", "tsx": "4.19.0", "typescript": "5.6.2"}}