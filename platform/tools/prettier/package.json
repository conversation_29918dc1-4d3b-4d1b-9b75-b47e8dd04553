{"name": "@kreios/prettier-config", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./index.js"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@ianvs/prettier-plugin-sort-imports": "4.3.1", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "0.6.6"}, "devDependencies": {"@kreios/tsconfig": "workspace:*", "@types/node": "20.14.10", "typescript": "5.6.2"}}