import baseConfig, { {{#if (eq scope "kreios")}}platformLicense{{else}}createClientLicenseConfig{{/if}}{{#if (eq type "app")}}, restrictEnvAccess{{/if}} } from "@kreios/eslint-config/base"
{{#if (or (eq type "react") (eq type "app"))}}
import reactConfig from "@kreios/eslint-config/react"
{{/if}}
{{#if (eq type "app")}}
import nextjsConfig from "@kreios/eslint-config/nextjs"
{{/if}}

/** @type {import('typescript-eslint').Config} */
export default [
  {
    ignores: [{{#if (eq type "app")}}".next/**"{{/if}}],
  },
  ...baseConfig,
  {{#if (or (eq type "react") (eq type "app"))}}
  ...reactConfig,
  {{/if}}
  {{#if (eq type "app")}}
  ...nextjsConfig,
    ...restrictEnvAccess,
  {{/if}}
  {{#if (eq scope "kreios")}}
  ...platformLicense,
  {{else}}
  ...createClientLicenseConfig("{{clientName}}", "{{projectName}}")
  {{/if}}
]
