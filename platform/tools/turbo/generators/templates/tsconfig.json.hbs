{
  {{#if (or (eq type "react") (eq type "flags"))}}
  "extends": "@kreios/tsconfig/internal-package.json",
  {{else}}
  "extends": "@kreios/tsconfig/base.json",
  {{/if}}
  "compilerOptions": {
    {{#if (or (eq type "react") (eq type "api") (eq type "flags"))}}
    "lib": ["dom", "dom.iterable", "ES2022"],
    {{/if}}
    "jsx": "preserve",
    {{#if (eq type "app")}}
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "module": "esnext",
     "plugins": [
      {
        "name": "next"
      }
    ],
    {{/if}}
    "tsBuildInfoFile": "node_modules/.cache/tsbuildinfo.json"
  },
  {{#if (eq type "app")}}
  "include": [".", "src/app/.well-known/vercel/flags/route.ts", ".next/types/**/*.ts"],
  {{else}}
  "include": ["*.ts", "src"],
  {{/if}}
  "exclude": ["node_modules", {{#if (eq type "app")}}"./public/docs"{{/if}}]
}
