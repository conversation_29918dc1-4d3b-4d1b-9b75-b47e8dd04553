{
  "name": "@{{scope}}/{{name}}",
  "private": true,
  "version": "0.1.0",
  "type": "module",
  {{#unless (eq type "app")}}
  "exports": {
    {{#if (eq type "domain")}}
    "./elastic": "./src/elastic/index.ts",
    "./config": "./config.ts",
    {{/if}}
    {{#if (eq type "api")}}
    "./trpc": "./src/trpc/index.ts",
    {{/if}}
    ".": "./src/index.ts{{#if (eq type "react")}}x{{/if}}",
    "./env": "./env.ts",
    "./*": "./src/*.ts{{#if (eq type "react")}}x{{/if}}"
  },
  {{/unless}}
  "license": "LicenseRef-LICENSE",
  "scripts": {
    {{#if (eq type "app")}}
    "build": "cross-env-shell pnpm with-env next build $([ \"$NEXT_OUTPUT\" = \"standalone\" ] && echo \"--experimental-build-mode compile\")",
    "dev": "pnpm with-env next dev --turbo",
    "start": "pnpm with-env next start",
    {{/if}}
    {{#if (eq type "graphql")}}
    "generate:schema": "pnpm with-env generate-schema ./src/schema/index.ts",
    {{/if}}
    {{#if (eq type "jobs")}}
    "cli": "pnpm with-env tsx src/cli.ts",
    {{/if}}
    {{#if (eq type "domain")}}
    "drizzle-kit": "pnpm with-env drizzle-kit",
    "elastic": "pnpm tsx ./src/elastic/cli.ts",
    "seed": "pnpm tsx ./src/seed.ts",
    {{/if}}
    "tsx": "pnpm with-env tsx --conditions=react-server",
    "tsx:debug": "pnpm with-env tsx --conditions=react-server --inspect-brk",
    "with-env": "dotenvx run -q -f {{relativePathToRoot}}.env.local -f {{relativePathToRoot}}.env --overload --",
    "clean": "{{#if (eq type "app")}}git clean -xdf .next{{else}}rm -rf{{/if}} .turbo node_modules",
    "format": "prettier --check . --ignore-path {{relativePathToRoot}}.gitignore --ignore-path {{relativePathToRoot}}.prettierignore",
    "lint": "eslint",
    "test": "vitest run{{#unless (eq type "domain")}} --passWithNoTests{{/unless}}",
    "test:watch": "vitest{{#unless (eq type "domain")}} --passWithNoTests{{/unless}}",
    "typecheck": "tsc --noEmit{{#if (eq type "react")}} --emitDeclarationOnly false{{/if}}"
  },
  "prettier": "@kreios/prettier-config",
  "dependencies": {
    "@kreios/utils": "workspace:*",
    "@t3-oss/env-core": "0.11.1",
    "awilix": "12.0.3",
    {{#if (or (eq type "app") (eq type "api") (eq type "graphql") (eq type "jobs"))}}
    "@{{scope}}/domain": "workspace:*",
    {{/if}}
    {{#if (eq type "app")}}
    "@{{scope}}/api": "workspace:*",
    "@{{scope}}/flags": "workspace:*",
    "@{{scope}}/graphql": "workspace:*",
    "@{{scope}}/jobs": "workspace:*",
    "@kreios/admin-layout": "workspace:*",
    "@kreios/auth": "workspace:*",
    "@kreios/command-menu": "workspace:*",
    "@kreios/copilot": "workspace:*",
    "@kreios/datatable": "workspace:*",
    "@kreios/elasticsearch": "workspace:*",
    "@kreios/eventsourcing": "workspace:*",
    "@kreios/flags": "workspace:*",
    "@kreios/ui": "workspace:*",
    "@sentry/nextjs": "8.30.0",
    "@t3-oss/env-nextjs": "0.11.1",
    "@tanstack/react-query": "5.55.4",
    "@tanstack/react-query-devtools": "5.55.4",
    "@tanstack/react-query-next-experimental": "5.55.4",
    "@trpc/client": "11.0.0-rc.401",
    "@trpc/react-query": "11.0.0-rc.401",
    "@trpc/server": "11.0.0-rc.401",
    "@vercel/flags": "2.6.1",
    "@vercel/toolbar": "0.1.18",
    "constate": "3.3.2",
    "date-fns": "4.1.0",
    "glob": "11.0.0",
    "graphql-yoga": "5.7.0",
    "lucide-react": "0.457.0",
    "next": "14.2.13",
    "next-themes": "0.3.0",
    "nextjs-toploader": "3.7.15",
    "react": "18.3.1",
    "react-dom": "18.3.1",
    "react-hook-form": "7.53.0",
    "sonner": "1.5.0",
    "ssr-only-secrets": "0.1.2",
    "superjson": "2.2.1",
    {{/if}}
    {{#if (eq type "flags")}}
    "@kreios/auth": "workspace:*",
    "@kreios/copilot": "workspace:*",
    "@vercel/flags": "2.6.1",
    "server-only": "0.0.1",
    {{/if}}
    {{#if (eq type "api")}}
    "@{{scope}}/jobs": "workspace:*",
    "@kreios/api": "workspace:*",
    "@kreios/auth": "workspace:*",
    "@kreios/elasticsearch": "workspace:*",
    "@trpc/server": "11.0.0-rc.401",
    "superjson": "2.2.1",
    {{/if}}
    {{#if (eq type "domain")}}
    "@castore/command-zod": "2.3.1",
    "@castore/core": "2.3.1",
    "@castore/event-storage-adapter-in-memory": "2.3.1",
    "@castore/event-type-zod": "2.3.1",
    "@commander-js/extra-typings": "12.1.0",
    "@faker-js/faker": "9.0.0",
    "@kreios/elasticsearch": "workspace:*",
    "@kreios/eventsourcing": "workspace:*",
    "lodash-es": "4.17.21",
    {{/if}}
    {{#if (eq type "jobs")}}
    "@kreios/jobs": "workspace:*",
    {{/if}}
    {{#if (eq type "graphql")}}
    "@kreios/graphql": "workspace:*",
    "@pothos/core": "4.1.0",
    "@pothos/plugin-dataloader": "4.1.0",
    "@pothos/plugin-relay": "4.2.0",
    "@pothos/plugin-zod": "4.1.0",
    "graphql": "16.9.0",
    "graphql-scalars": "1.23.0",
    {{/if}}
    "ulidx": "2.4.1",
    "zod": "3.23.8"
  },
  "devDependencies": {
    "@kreios/eslint-config": "workspace:*",
    "@kreios/prettier-config": "workspace:*",
    "@kreios/tsconfig": "workspace:*",
    "@dotenvx/dotenvx": "1.14.0",
    "tsx": "4.19.0",
    "vite-tsconfig-paths": "5.0.1",
    {{#if (eq type "app")}}
    "@kreios/tailwind-config": "workspace:*",
    "@types/node": "20.14.10",
    "@types/react": "18.3.5",
    "@types/react-dom": "18.3.0",
    "autoprefixer": "10.4.20",
    "copyfiles": "2.4.1",
    "cross-env": "7.0.3",
    "jiti": "2.0.0-rc.1",
    "postcss": "8.4.45",
    "tailwindcss": "3.4.10",
    {{/if}}
    {{#if (eq type "domain")}}
    "@castore/lib-test-tools": "2.3.1",
    "@types/lodash-es": "4.17.12",
    "drizzle-kit": "0.30.4",
    {{/if}}
    {{#if (eq type "job")}}
    "@types/node": "20.14.10",
    {{/if}}
    "eslint": "9.10.0",
    "prettier": "3.4.2",
    "typescript": "5.6.2",
    "vitest": "2.1.9"
  }
  {{#if (eq type "react")}}
  ,
  "peerDependencies": {
    "react": "18.3.1"
  }
  {{/if}}
}
