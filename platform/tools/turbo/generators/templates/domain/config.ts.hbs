import type { AwilixContainer } from "awilix"

import type { ElasticsearchGateway } from "@kreios/elasticsearch"
import type { Database } from "@kreios/eventsourcing/drizzle"

import type { config } from "./src/elastic"

export interface DomainContainer {
  gateway: ElasticsearchGateway<typeof config>
  db: Database
}

declare global {
  // eslint-disable-next-line no-var
  var container: AwilixContainer<DomainContainer>
}
