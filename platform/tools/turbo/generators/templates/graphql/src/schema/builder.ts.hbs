import SchemaBuilder from "@pothos/core"
import DataloaderPlugin from "@pothos/plugin-dataloader"
import RelayPlugin from "@pothos/plugin-relay"
import ZodPlugin from "@pothos/plugin-zod"
import { DateTimeResolver } from "graphql-scalars"

import ZodTransformPlugin from "@kreios/graphql/plugins/zodTransformPlugin/index"

export const builder = new SchemaBuilder<{
  DefaultFieldNullability: false
  DefaultInputFieldRequiredness: true
  Scalars: {
    Date: {
      Input: Date
      Output: Date
    }
  }
}>({
  plugins: [ZodTransformPlugin, RelayPlugin, ZodPlugin, DataloaderPlugin],
  defaultFieldNullability: false,
  defaultInputFieldRequiredness: true,
  zod: {
    validationError: (zodError) => zodError,
  },
})

builder.addScalarType("Date", DateTimeResolver, {})
