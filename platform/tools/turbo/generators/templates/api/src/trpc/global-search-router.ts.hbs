
import type { TRP<PERSON>outerR<PERSON>ord } from "@trpc/server"
import { config as gatewayConfig } from "@{{scope}}/domain/elastic"
import { z } from "zod"

import type { Document, GatewayConfig, IndexConfig } from "@kreios/elasticsearch"
import { split } from "@kreios/utils/split"

import { protectedProcedure } from "./trpc"

/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

const gateway = container.resolve("gateway")

type SearchDocument = Document & {
  aggregateId: string
}

/** Type utility to create the return type of SearchAggreagation for a specific index
 * @typeParam TName - The name of the index
 * @typeParam TDocument - The type of the document in the index
 */
interface BaseBucket<TName extends string, TDocument> {
  key: `${TName}_${string}`
  doc_count: number
  top_hits_per_index: {
    hits: {
      hits: [
        {
          _source: TDocument
        },
      ]
    }
  }
}

/**
 * Type utility to get the search aggreagation buckets for a gateway config
 * @typeParam TConfig - The gateway config
 */
type ExtractSearchBuckets<TConfig extends GatewayConfig> = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [Index in TConfig["indices"][number] as Index["name"]]: Index extends IndexConfig<infer TName, any, infer TDocument>
    ? BaseBucket<TName, TDocument>
    : never
}[TConfig["indices"][number]["name"]]

type GlobalSearchAggregation = {
  byIndex: {
    buckets: ExtractSearchBuckets<typeof gatewayConfig>[]
  }
}

/**
 * Type utility to extract the search result from the gateway response
 * @typeParam T - The search aggreagation buckets
 * @returns The an array of { type: string, items: SearchDocument[] } objects
 */
type ExtractSearchResult<T extends BaseBucket<string, SearchDocument>[]> = T extends (infer U)[]
  ? U extends BaseBucket<infer TName, infer TDocument>
    ? { type: TName; items: (TDocument & { href: string })[] }[]
    : never
  : never

const indexToPathMapper = {
} satisfies Record<(typeof gatewayConfig)["indices"][number]["name"], string>
export const globalSearchRouter = {
  searchAll: protectedProcedure.input(z.string()).query(async ({ input: search }) => {
    const {
      aggregations: { byIndex },
    } = await gateway.search<SearchDocument, GlobalSearchAggregation>({

      // @ts-expect-error TODO: When adding models
      index: gatewayConfig.indices.map((index) => index.name),
      query: {
        multi_match: {
          query: search,
          fields: ["*"],
        },
      },
      size: 0,
      aggs: {
        byIndex: {
          terms: { field: "_index" },
          aggs: {
            top_hits_per_index: {
              top_hits: {
                size: 3,
              },
            },
          },
        },
      },
    })

    const searchResults = []
    for (const bucket of byIndex.buckets) {
      const indexName = extractLogicalIndexName(gatewayConfig, bucket.key)
      const index = findIndexConfig(gatewayConfig, indexName)

      if (index?.buildUrl) {
        const { buildUrl } = index

        searchResults.push({
          type: index.displayName,
          items: bucket.top_hits_per_index.hits.hits.map((hit) => {
            return {
              ...hit._source,
              href: buildUrl(hit._source.aggregateId),
            }
          }),
        })
      }
    }

    return searchResults as ExtractSearchResult<(typeof byIndex)["buckets"]>
  }),
} satisfies TRPCRouterRecord
