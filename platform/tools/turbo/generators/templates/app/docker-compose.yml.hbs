version: "3"
name: {{scope}}
services:
  redis:
    image: redis:latest
    restart: always
    ports:
      - 6379:6379

  serverless-redis-http:
    ports:
      - "8079:80"
    image: hiett/serverless-redis-http:latest
    environment:
      SRH_MODE: env
      SRH_TOKEN: example_token
      SRH_CONNECTION_STRING: "redis://redis:6379" # Using `redis` hostname since they're in the same Docker network.

  database:
    image: imresamu/postgis:15-3.4
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - 5432:5432
    volumes:
      - database:/var/lib/postgresql/data

  pg_proxy:
    image: ghcr.io/neondatabase/wsproxy:latest
    environment:
      APPEND_PORT: "database:5432"
      ALLOW_ADDR_REGEX: ".*"
      LOG_TRAFFIC: "true"
    ports:
      - "5433:80"
    depends_on:
      - database

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      <PERSON><PERSON>IN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: schnellreich
    ports:
      - 5050:80

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.13.3
    restart: always
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - cluster.routing.allocation.disk.watermark.low=90%
      - cluster.routing.allocation.disk.watermark.high=95%
      - cluster.routing.allocation.disk.watermark.flood_stage=98%
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - 9200:9200
    volumes:
      - elasticsearch:/usr/share/elasticsearch/data

  kibana:
    image: docker.elastic.co/kibana/kibana:8.13.3
    restart: always
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - 5601:5601
    volumes:
      - kibana:/usr/share/kibana/data
    depends_on:
      - elasticsearch

volumes:
  database:
    driver: local
  elasticsearch:
    driver: local
  kibana:
    driver: local
