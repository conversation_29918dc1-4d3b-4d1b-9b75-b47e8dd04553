import { asFunction } from "awilix"

import { ElasticsearchGateway } from "@kreios/elasticsearch"
import { connect } from "@kreios/eventsourcing/drizzle"

import { env } from "./env"

const { db, client } = await connect({
  type: env.VERCEL ? "vercel" : env.DATABASE_URL.includes("localhost") ? "neon" : "postgres",
  url: env.DATABASE_URL,
})

container.register({
  db: asFunction(() => db)
    .singleton()
    .disposer(() => client.close()),
})

const { config } = await import("@{{scope}}/domain/elastic/index")

container.register({
  gateway: asFunction(() => new ElasticsearchGateway(config))
    .singleton()
    .disposer((gateway) => gateway.close()),
})
