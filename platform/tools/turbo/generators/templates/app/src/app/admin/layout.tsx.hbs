import type { ReactNode, ComponentProps } from "react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { FeatureFlagsProvider } from "@/components/feature-flag-provider"
import { Logo } from "@/components/logo"
import { SearchCommands } from "@/components/search/search"
import { TRPCReactProvider } from "@/lib/trpc-provider"
import { enableCopilot } from "@{{scope}}/flags"
import NextTopLoader from "nextjs-toploader"
// import { cloakSSROnlySecret } from "ssr-only-secrets"

import { AdminLayout } from "@kreios/admin-layout"
import { SentryFeedbackWidget } from "@kreios/admin-layout/components/sentry-user-feedback-widget"
import { ThemeProvider } from "@kreios/admin-layout/components/theme-provider"
import { auth } from "@kreios/auth"
import { AdminCopilotDrawer } from "@kreios/copilot"
import { Toaster } from "@kreios/ui/sonner"
import { TooltipProvider } from "@kreios/ui/tooltip"
import { promiseObjectAll } from "@kreios/utils/promise-object-all"

const routes = [] satisfies ComponentProps<typeof AdminLayout>["routes"]

export default async function RootLayout({
  children,
  modal,
}: Readonly<{
  children: ReactNode
  modal: ReactNode
}>) {
  const session = await auth()

  if (!session?.user) redirect("/auth/login?callbackUrl=/admin")

  // Get the cookies directly
  const cookieStore = cookies()

  return (
    <FeatureFlagsProvider
      flags={await promiseObjectAll({ enableCopilot: enableCopilot() })}
    >
      <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
        <Toaster />
        <SentryFeedbackWidget enabled={true} />
        <TooltipProvider>
          <TRPCReactProvider cookies={cookieStore}>
            <NextTopLoader />
            <AdminCopilotDrawer modal={modal}>
              <AdminLayout
                logoBackground
                logo={<Logo />}
                routes={routes}
                globalSearch={<SearchCommands />}
                displayRootBreadcrumb={false}
              >
                {children}
              </AdminLayout>
            </AdminCopilotDrawer>
          </TRPCReactProvider>
        </TooltipProvider>
      </ThemeProvider>
    </FeatureFlagsProvider>
  )
}
