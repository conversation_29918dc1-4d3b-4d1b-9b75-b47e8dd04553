import type { ApiData } from "@vercel/flags"
import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
import * as flags from "@{{scope}}/flags"
import { verifyAccess } from "@vercel/flags"
import { unstable_getProviderData as getProviderData } from "@vercel/flags/next"

export const runtime = "edge"
export const dynamic = "force-dynamic" // defaults to auto

/**
 * This route is used by the vercel toolbar to access the feature flags
 */
export async function GET(request: NextRequest) {
  const access = await verifyAccess(request.headers.get("Authorization"))
  if (!access) return NextResponse.json(null, { status: 401 })

  return NextResponse.json<ApiData>(getProviderData(flags))
}
