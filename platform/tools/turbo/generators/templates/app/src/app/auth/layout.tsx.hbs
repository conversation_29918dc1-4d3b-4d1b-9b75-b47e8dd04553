import type { ReactNode } from "react"
import NextTopLoader from "nextjs-toploader"

import { ThemeProvider } from "@kreios/admin-layout/components/theme-provider"
import { Toaster } from "@kreios/ui/sonner"
import { TooltipProvider } from "@kreios/ui/tooltip"

export default async function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
      <Toaster />
      <TooltipProvider>
        <NextTopLoader />
        {children}
      </TooltipProvider>
    </ThemeProvider>
  )
}
