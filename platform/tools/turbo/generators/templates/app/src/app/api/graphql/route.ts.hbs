
import { NextResponse } from "next/server"
import { env } from "@/env"
import { createYoga } from "graphql-yoga"

import { auth } from "@kreios/auth"

const getHandler = async () => {
  const { schema } = await import("@{{scope}}/graphql/schema/index")

  const { handleRequest } = createYoga({
    schema,
    context: ({ request }) => {
      // Securing all requests to the GraphQL API using an API key
      const apiKey = request.headers.get("authorization")
      if (!apiKey || !env.GRAPHQL_API_KEYS.includes(apiKey)) throw new Error("Unauthorized")

      return {}
    },
    graphiql: {
      defaultQuery: `
      query {
        technicalSolutions {
          id
        }
      }
    `,
      additionalHeaders: {
        authorization: env.GRAPHQL_API_KEYS.at(0)!,
      },
    },
    graphqlEndpoint: "/api/graphql",
    fetchAPI: { Response },
  })

  return handleRequest
}

// Securing the GraphiQL interface using the auth middleware
export const GET = auth(async (req) => {
  if (!req.auth) return NextResponse.redirect(new URL("/auth/login?callbackUrl=/api/graphql", req.url))

  const handleRequest = await getHandler()

  return handleRequest(req, {})
})

const handleRequest = async (req: Request) => {
  const handleRequest = await getHandler()

  return handleRequest(req, {})
}

export { handleRequest as POST, handleRequest as OPTIONS }
