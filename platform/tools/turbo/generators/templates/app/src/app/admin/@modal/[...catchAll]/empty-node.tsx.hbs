"use client"

import type { FC, ReactNode } from "react"
import { createContext } from "react"

/**
 * A context providers which main purpose is to be a empty node that react will use as boundary to reinitialize the component tree.
 */
const EmptyStateContext = createContext<string | null>(null)
export const EmptyStateProvider: FC<{ value: string | null; children: ReactNode }> = ({ value, children }) => (
  <EmptyStateContext.Provider value={value}>{children}</EmptyStateContext.Provider>
)
