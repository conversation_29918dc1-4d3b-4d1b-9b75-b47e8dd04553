import { cookies } from "next/headers"
import { EmptyStateProvider } from "@/app/admin/@modal/[...catchAll]/empty-node"

import { auth } from "@kreios/auth"
import { AI } from "@kreios/copilot"
import { Chat } from "@kreios/copilot/chat"
import { getUserChat } from "@kreios/copilot/persistance"
import { nanoid } from "@kreios/utils/nanoid"

export default async function Page() {
  const session = await auth()

  if (!session?.user) {
    return null
  }

  const chatId = cookies().get("copilotchatid")?.value

  if (!chatId) {
    const id = nanoid()

    return (
      <EmptyStateProvider value={id} key={id}>
        <AI initialAIState=\{{ chatId: id, messages: [] }}>
          <Chat id={id} session={session} />
        </AI>
      </EmptyStateProvider>
    )
  }

  const chat = await getUserChat(chatId)

  if (!chat || "error" in chat) return null

  return (
    <EmptyStateProvider value={chat.id} key={chat.id}>
      <AI initialAIState=\{{ chatId: chat.id, messages: chat.messages }}>
        <Chat id={chat.id} session={session} initialMessages={chat.messages} />
      </AI>
    </EmptyStateProvider>
  )
}
