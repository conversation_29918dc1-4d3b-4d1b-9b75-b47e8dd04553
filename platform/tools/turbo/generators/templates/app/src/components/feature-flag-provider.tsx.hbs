"use client"

import type { enableCopilot } from "@{{scope}}/flags"

import { createFeatureFlagsProvider } from "@kreios/flags/feature-flag-provider-factory"

// A Scoped Feature Flag Provider for the {{projectName}}
// Using the useFeatureFlags hook, you can access the feature flags for the {{projectName}} inside a client component
export const [FeatureFlagsProvider, useFeatureFlags] = createFeatureFlagsProvider<{
  enableCopilot: typeof enableCopilot
}>()
