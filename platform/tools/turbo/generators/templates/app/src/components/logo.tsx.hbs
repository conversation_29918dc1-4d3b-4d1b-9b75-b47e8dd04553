import type { ComponentProps, FC } from "react"

import { cn } from "@kreios/ui"


export const LogoLarge: FC<ComponentProps<"svg">> = ({ className, ...props }) => {
  return null
}

const LogoSmall: FC<ComponentProps<"svg">> = ({ className, ...props }) => null

export const Logo = () => (
  <>
    <LogoLarge className="w-full text-[#e74f3d] group-[[data-collapsed=true]]:hidden" />
    <LogoSmall className="hidden size-4 md:group-[[data-collapsed=true]]:block md:group-[[data-collapsed=false]]:hidden" />
  </>
)
