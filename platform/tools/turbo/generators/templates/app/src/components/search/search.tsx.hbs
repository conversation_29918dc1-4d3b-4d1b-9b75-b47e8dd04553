"use client"

import type { RouterOutput } from "@/lib/trpc-provider"
import { api } from "@/lib/trpc-provider"

import type { ComponentDictionary } from "@kreios/command-menu/commands/search-commands"
import { SearchCommands as CommonSearchCommands } from "@kreios/command-menu/commands/search-commands"

type GlobalSearchOutput = RouterOutput["globalSearch"]["searchAll"]

const componentDictionary: ComponentDictionary<GlobalSearchOutput[number]> = {}

export const SearchCommands: React.FC = () => {
  // @ts-expect-error TODO: When adding models
  return <CommonSearchCommands componentDict={componentDictionary} procedure={api.globalSearch.searchAll} />
}
