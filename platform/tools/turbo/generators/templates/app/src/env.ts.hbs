

import { env as apiEnv } from "@{{scope}}/api/env"
import { env as dbEnv } from "@{{scope}}/domain/env"
import { env as graphqlEnv } from "@{{scope}}/graphql/env"
import { createEnv } from "@t3-oss/env-nextjs"
import { vercel } from "@t3-oss/env-nextjs/presets"
import { z } from "zod"

import { env as authEnv } from "@kreios/auth/env"
import { env as copilotEnv } from "@kreios/copilot/env"

/* eslint-disable no-restricted-properties */

export const env = createEnv({
  extends: [authEnv, dbEnv, apiEnv, copilotEnv, graphqlEnv, vercel()],

  shared: {
    NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  },
  /**
   * Serverside Environment variables, not available on the client.
   *
   * 💡 Will throw if you access these variables on the client.
   */
  server: {
    GRAPHQL_API_KEYS: z.string().transform((val) => val.split(",")),
  },

  /**
   * Environment variables available on the client (and server).
   *
   * 💡 You'll get type errors if these are not prefixed with NEXT_PUBLIC_.
   */
  client: {
  },

  /**
   * Due to how Next.js bundles environment variables on Edge and Client,
   * we need to manually destructure them to make sure all are included in bundle.
   *
   * 💡 You'll get type errors if not all variables from `server` & `client` are included here.
   */
  runtimeEnv: {
    GRAPHQL_API_KEYS: process.env.GRAPHQL_API_KEYS,
    NODE_ENV: process.env.NODE_ENV,
  },

  /**
   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially
   * useful for Docker builds.
   */
  skipValidation:
       !process.env.VERCEL && (
      !!process.env.CI ||
      !!process.env.SKIP_ENV_VALIDATION ||
      process.env.npm_lifecycle_event === "lint" ||
      process.env.NODE_ENV === "test"
    ),

  /**
   * Makes it so that empty strings are treated as undefined.
   * `SOME_VAR: z.string()` and `SOME_VAR=''` will throw an error.
   */
  emptyStringAsUndefined: true,
})
