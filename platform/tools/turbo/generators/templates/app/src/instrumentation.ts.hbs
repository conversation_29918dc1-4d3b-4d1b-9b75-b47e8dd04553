export async function register() {
/* eslint-disable no-restricted-properties */
/* eslint-disable turbo/no-undeclared-env-vars */
  const TURBOPACK_ENABLED = Boolean(process.env.TURBOPACK)

  if (process.env.NEXT_RUNTIME === "nodejs") {
    await Promise.all([
      !TURBOPACK_ENABLED ? import("../sentry.server.config") : Promise.resolve(),
      import("./container").then(() => import("./config")),
    ])
  }

  if (process.env.NEXT_RUNTIME === "edge") {
    if (!TURBOPACK_ENABLED) await import("../sentry.edge.config")
  }
}
