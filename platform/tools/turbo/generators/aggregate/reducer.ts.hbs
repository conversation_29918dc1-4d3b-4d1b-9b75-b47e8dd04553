import { defineReducer } from "@kreios/eventsourcing"

import type { {{capitalize name}}Aggregate } from "./aggregate"
import type { {{lowercase name}}Events } from "./events"

export const {{lowercase name}}Reducer = defineReducer<{{capitalize name}}Aggregate, typeof {{lowercase name}}Events>((state, event) => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "{{lowercase name}}:created:v1": {
      return {
        ...event.payload,
        aggregateId: aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deleted: false,
        deletedAt: null,
      }
    }
    case "{{lowercase name}}:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "{{lowercase name}}:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
      }
    }
  }
})
