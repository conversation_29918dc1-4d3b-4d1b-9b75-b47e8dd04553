import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"

const {{lowercase name}}Schema = z.object({

})

export const {{lowercase name}}CreatedV1 = defineEventType({
  aggregateType: "{{lowercase name}}",
  eventType: "created",
  schemaVersion: 1,
  schema: {{lowercase name}}Schema,
})

export const {{lowercase name}}UpdatedV1 = defineEventType({
  aggregateType: "{{lowercase name}}",
  eventType: "updated",
  schemaVersion: 1,
  schema: {{lowercase name}}Schema.partial(),
})

export const {{lowercase name}}DeletedV1 = defineEventType({
  aggregateType: "{{lowercase name}}",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

export const {{lowercase name}}Events = [{{lowercase name}}CreatedV1, {{lowercase name}}UpdatedV1, {{lowercase name}}DeletedV1]
