import { EventStore } from "@castore/core"

import { DrizzleEventStorageAdapter } from "@kreios/eventsourcing/drizzle"

import { {{lowercase name}}Events } from "./events"
import { {{lowercase name}}Reducer } from "./reducer"

const db = container.resolve("db")

export const {{lowercase name}}EventStore = new EventStore({
  eventStoreId: "{{lowercase pluralName}}",
  eventStorageAdapter: new DrizzleEventStorageAdapter(db, "{{lowercase pluralName}}"),
  eventTypes: {{lowercase name}}Events,
  reducer: {{lowercase name}}Reducer,
})

export type {{capitalize name}}EventStore = typeof {{lowercase name}}EventStore
