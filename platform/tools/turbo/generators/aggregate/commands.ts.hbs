import { ulid } from "ulidx"
import { z } from "zod"

import { defineCommand } from "@kreios/eventsourcing"

import { {{lowercase name}}CreatedV1, {{lowercase name}}DeletedV1, {{lowercase name}}UpdatedV1 } from "./events"
import { {{lowercase name}}EventStore } from "./store"

export const create{{capitalize name}}Command = defineCommand({
  commandId: "create{{capitalize name}}",
  eventStores: [{{lowercase name}}EventStore],
  inputSchema: {{lowercase name}}CreatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid().optional(),
  }),
  outputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const aggregateId = commandInput.aggregateId ?? ulid()

    await eventStore.pushEvent({
      type: {{lowercase name}}CreatedV1.type,
      aggregateId,
      version: 1,
      timestamp: new Date().toISOString(),
      payload: commandInput,
    })

    return { aggregateId }
  },
})

export const update{{capitalize name}}Command = defineCommand({
  commandId: "update{{capitalize name}}",
  eventStores: [{{lowercase name}}EventStore],
  inputSchema: {{lowercase name}}UpdatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId, ...payload } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`{{capitalize name}} not found`)
    }

    await eventStore.pushEvent({
      type: {{lowercase name}}UpdatedV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload,
    })
  },
})

export const delete{{capitalize name}}Command = defineCommand({
  commandId: "delete{{capitalize name}}",
  eventStores: [{{lowercase name}}EventStore],
  inputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`{{capitalize name}} not found`)
    }

    await eventStore.pushEvent({
      type: {{lowercase name}}DeletedV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload: {},
    })
  },
})
