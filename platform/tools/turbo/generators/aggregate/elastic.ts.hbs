import { createIndexConfig } from "@kreios/elasticsearch"
import { formatDateToIsoString } from "@kreios/utils/date-to-iso-string"
import { {{lowercase name}}EventStore } from "./store"

export const {{lowercase pluralName}}IndexConfig = createIndexConfig({
  name: "{{lowercase pluralName}}",
  getTotalCount: () => {{lowercase name}}EventStore.listAggregateIds().then(({ aggregateIds }) => aggregateIds.length),
  fetchById: (aggregateId) => {{lowercase name}}EventStore.getExistingAggregate(aggregateId).then(({ aggregate }) => aggregate),
  fetchAll: async function () {
    const { aggregateIds } = await {{lowercase name}}EventStore.listAggregateIds()
    return (function* () {
      for (const entry of aggregateIds) {
        yield {{lowercase name}}EventStore
          .getExistingAggregate(entry.aggregateId)
          .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
      }
    })()
  },
  toDocument: (aggregate) => ({
    _id: aggregate.aggregateId,
    aggregateId: aggregate.aggregateId,
    createdAt: formatDateToIsoString(aggregate.createdAt),
    updatedAt: formatDateToIsoString(aggregate.updatedAt),
    deletedAt: aggregate.deletedAt ? formatDateToIsoString(aggregate.deletedAt) : null,
    label: aggregate.aggregateId,
  }),
    analysis: {
    analyzer: {
      autocomplete: {
        type: "custom",
        tokenizer: "ngram",
        filter: ["lowercase", "asciifolding"],
        char_filter: ["html_strip"],
      },
    },
    tokenizer: {
      ngram: {
        token_chars: ["letter", "digit"],
        type: "ngram",
        min_gram: 3,
        max_gram: 4,
      },
    },
  },
  properties: {},
})

export type {{capitalize name}}Document = ReturnType<typeof {{lowercase pluralName}}IndexConfig.toDocument>
