import { execSync } from "node:child_process"
import { lstatSync, readdirSync, readFileSync } from "node:fs"
import { join } from "node:path"
import type { PlopTypes } from "@turbo/gen"
import { globSync } from "glob"
import inquirerSearchCheckbox from "inquirer-search-checkbox"
import { kebabCase } from "lodash-es"
import { z } from "zod"

interface PackageJson {
  name: string
  scripts: Record<string, string>
  dependencies: Record<string, string>
  devDependencies: Record<string, string>
  peerDependencies: Record<string, string>
}

// Global object to store scope to path mappings
const scopeToPathMap: Record<string, string> = {
  kreios: "platform",
}

const customCommandConfigSchema = z.object({
  data: z.object({
    command: z.string(),
    message: z.string().optional(),
    cwd: z.string().optional(),
  }),
})

// Function to get known client prefixes and update the scopeToPathMap
const getKnownClientPrefixes = (): string[] => {
  const projectsPath = join(process.cwd(), "projects")

  return readdirSync(projectsPath, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => {
      const domainPackageJsonPath = join(projectsPath, dirent.name, "domain", "package.json")
      let scope: string
      try {
        const packageJson = JSON.parse(readFileSync(domainPackageJsonPath, "utf-8"))
        scope = packageJson.name.split("/")[0].replace("@", "")
      } catch (error) {
        scope = dirent.name
      }

      scopeToPathMap[scope] = join("projects", dirent.name)
      return scope
    })
    .filter((prefix): prefix is string => prefix !== null)
}

const LICENSE_REGEX =
  /This software, (?<projectName>.+), is .+\n\nCopyright \(c\) \d+ (?<clientName>.+) All rights reserved./

// Function to extract client and project name from LICENSE file
const extractLicenseInfo = (scope: string): { clientName: string; projectName: string } => {
  if (scope === "kreios") {
    return { clientName: "KREIOS S.A.R.L", projectName: "Kreios Data Platform" }
  }

  const basePath = scopeToPathMap[scope]
  if (!basePath) {
    throw new Error(`Unknown scope: ${scope}`)
  }

  const licensePath = join(process.cwd(), basePath, "LICENSE")
  try {
    const licenseContent = readFileSync(licensePath, "utf-8")
    const res = LICENSE_REGEX.exec(licenseContent)

    if (!res || !res.groups || !res.groups.clientName || !res.groups.projectName) {
      throw new Error("Unable to extract client or project name from LICENSE file")
    }

    const { clientName, projectName } = res.groups
    return { clientName, projectName }
  } catch (error) {
    console.error(`Error reading LICENSE file: ${error}`)
    return { clientName: "UNKNOWN CLIENT", projectName: "UNKNOWN PROJECT" }
  }
}

// Function to determine the folder path based on the package name and scope
const buildFolderPath = (scope: string, name: string): string => {
  const basePath = scopeToPathMap[scope]
  if (!basePath) {
    throw new Error(`Unknown scope: ${scope}`)
  }
  return join(basePath, name)
}

// Function to read all package.json files and extract dependencies
const getAllDependencies = () => {
  const packageJsonFiles = globSync("**/package.json", {
    ignore: ["**/node_modules/**", "**/dist/**", "**/build/**", "**/test/**", "**/out/**", "**/platform/**"],
  })
  const allDeps = new Map<
    string,
    {
      name: string
      version: string
      type: "dependencies" | "devDependencies" | "peerDependencies"
    }
  >()

  packageJsonFiles.forEach((file) => {
    const content = JSON.parse(readFileSync(file, "utf-8")) as PackageJson
    Object.entries(content.dependencies || {}).forEach(([name, version]) =>
      allDeps.set(name, { name: name, version, type: "dependencies" })
    )
    Object.entries(content.devDependencies || {}).forEach(([name, version]) =>
      allDeps.set(name, { name: name, version, type: "devDependencies" })
    )
    Object.entries(content.peerDependencies || {}).forEach(([name, version]) =>
      allDeps.set(name, { name: name, version, type: "peerDependencies" })
    )
  })

  return allDeps
}

const createPackage = (
  scope: string,
  name: string,
  type: string,
  allDeps: ReturnType<typeof getAllDependencies>,
  deps: string[],
  licenseInfo?: { clientName: string; projectName: string }
): PlopTypes.ActionType[] => {
  const folderPath = buildFolderPath(scope, name)
  const { clientName, projectName } = licenseInfo ?? extractLicenseInfo(scope)

  const actions: PlopTypes.ActionType[] = [
    {
      type: "add",
      path: `${folderPath}/package.json`,
      data: {
        relativePathToRoot: scope === "kreios" ? "../../" : "../../../",
        scope,
        name,
        type,
        clientName,
        projectName,
      },
      templateFile: "templates/package.json.hbs",
    },
    {
      type: "add",
      path: `${folderPath}/tsconfig.json`,
      data: { scope, name, type, clientName, projectName },
      templateFile: "templates/tsconfig.json.hbs",
    },
    {
      type: "add",
      path: `${folderPath}/eslint.config.js`,
      data: { scope, name, type, clientName, projectName },
      templateFile: "templates/eslint.config.js.hbs",
    },
  ]

  const templates = join(process.cwd(), "turbo/generators/templates", type)

  globSync("**/*", { cwd: templates, dot: true, ignore: [".DS_Store"] }).forEach((file) => {
    if (lstatSync(join(templates, file)).isDirectory()) return
    actions.push({
      type: "add",
      path: join(folderPath, file).replace(".hbs", ""),
      data: { scope, name, type, clientName, projectName },
      templateFile: join("templates", type, file),
    })
  })

  actions.push({
    type: "modify",
    path: `${folderPath}/package.json`,
    data: { scope, name, type, clientName, projectName },
    transform: (content) => {
      const packageJson = JSON.parse(content)
      const selectedDeps = deps

      for (const name of selectedDeps) {
        const typesPackage = `@types/${name}`
        if (allDeps.has(typesPackage)) selectedDeps.push(typesPackage)

        const { version, type } = allDeps.get(name)!
        if (!packageJson[type]) packageJson[type] = {}
        packageJson[type][name] = version
      }

      return JSON.stringify(packageJson, null, 2)
    },
  })

  return actions
}

export default function generator(plop: PlopTypes.NodePlopAPI): void {
  const knownClientPrefixes = getKnownClientPrefixes()
  const allDeps = getAllDependencies()

  plop.setPrompt("checkbox", inquirerSearchCheckbox)

  const commonPackages = [
    {
      name: "nextjs app",
      value: "app",
    },
    {
      name: "domain (eventsourcing)",
      value: "domain",
    },
    {
      name: "flags (feature flags)",
      value: "flags",
    },
    {
      name: "api (trpc)",
      value: "api",
    },
    {
      name: "job (event driven queue)",
      value: "jobs",
    },
    {
      name: "graphql (pothos schema)",
      value: "graphql",
    },
  ]

  plop.setGenerator("project", {
    description: "Generate a new app for the Kreios Data Platform Prototype Monorepo",
    prompts: [
      {
        type: "input",
        name: "folder",
        message: "What should be the folder name of the app?",
      },
      {
        type: "input",
        name: "scope",
        message: "What should be the package prefix of the app?",
      },
      {
        type: "input",
        name: "projectName",
        message: "What should be the name of the project mentioned in the license?",
      },
      {
        type: "input",
        name: "clientName",
        message: "What should be the name of the client mentioned in the license?",
      },
      {
        type: "checkbox",
        name: "packages",
        message: "Select packages & apps to add:",
        default: commonPackages.map(({ value }) => value),
        choices: commonPackages,
        pageSize: 20,
      },
    ],
    actions: (answers) => {
      const { folder, projectName, clientName, packages, scope } = answers as {
        folder: string
        projectName: string
        clientName: string
        packages: string[]
        scope: string
      }

      const rootPath = join("projects", folder)

      scopeToPathMap[scope] = rootPath

      const actions: PlopTypes.ActionType[] = [
        {
          type: "add",
          path: join(rootPath, "LICENSE"),
          templateFile: "LICENSE.hbs",
          data: { projectName, clientName, year: new Date().getFullYear() },
        },
        ...packages.flatMap((pkg) => createPackage(scope, pkg, pkg, allDeps, [], { clientName, projectName })),
      ]

      const run = (command: string, message?: string, cwd?: string) =>
        actions.push({
          type: "runCommand",
          data: { command, cwd, message },
        })

      const turboFilter = `--ui stream --filter=@${scope}/*`

      run("pnpm install", "Installing...")
      run(`pnpm turbo run format ${turboFilter} -- --write`, "Formatting package")
      run(`pnpm turbo run lint ${turboFilter} -- --fix`, "Linting package")
      run(`pnpm turbo run typecheck ${turboFilter}`, "Typechecking package")

      return actions
    },
  })

  const domains = globSync("projects/*/domain", {
    cwd: process.cwd(),
    ignore: ["**/node_modules/**", "**/dist/**", "**/build/**", "**/test/**", "**/out/**"],
  })

  plop.setGenerator("aggregate", {
    description: "Generate a new aggregate for the Kreios Data Platform Prototype Monorepo",
    prompts: [
      {
        type: "list",
        name: "path",
        message: "Select the domain for the aggregate:",
        choices: domains.map((domain) => {
          const projectName = domain.split("/").at(-2)

          const domainPackageJsonPath = join(domain, "package.json")

          return {
            name: `${projectName} (${JSON.parse(readFileSync(domainPackageJsonPath, "utf-8")).name})`,
            value: domain,
          }
        }),
      },
      {
        type: "input",
        name: "name",
        message: "What is the name of the aggregate?",
      },
      {
        type: "input",
        name: "pluralName",

        message: "What is the plural name of the aggregate?",
        default: (answers) => `${answers.name}s`,
      },
    ],

    actions: (answers) => {
      const { path, name, pluralName } = answers as {
        path: string
        name: string
        pluralName: string
      }

      const files = globSync("*.hbs", { cwd: join(process.cwd(), "turbo/generators/aggregate") })

      const actions: PlopTypes.ActionType[] = files.map((file) => ({
        type: "add",
        path: join(path, "src", kebabCase(name).toLowerCase(), file.replace(".hbs", "")),
        templateFile: `aggregate/${file}`,
      }))

      const run = (command: string, message?: string, cwd?: string) =>
        actions.push({
          type: "runCommand",
          data: { command, cwd, message },
        })

      console.log(path)

      const scope = JSON.parse(readFileSync(join(path, "package.json"), "utf-8")).name

      const turboFilter = `--ui stream --filter=${scope}`

      run(`pnpm turbo run format ${turboFilter} -- --write`, "Formatting package")
      run(`pnpm turbo run lint ${turboFilter} -- --fix`, "Linting package")
      run(`pnpm turbo run typecheck ${turboFilter}`, "Typechecking package")

      return actions
    },
  })

  plop.setGenerator("package", {
    description: "Generate a new package for the Kreios Data Platform Prototype Monorepo",
    prompts: [
      {
        type: "list",
        name: "scope",
        message: "Select the scope for the package:",
        choices: [
          { name: "kreios (platform)", value: "kreios" },
          ...knownClientPrefixes.map((prefix) => ({
            name: `${prefix} (${scopeToPathMap[prefix].split("/").at(-1)})`,
            value: prefix,
          })),
        ],
      },
      {
        type: "list",
        name: "type",
        message: "What type of package is this?",
        choices: (answers) => {
          const baseChoices = [
            {
              name: "base (typescript only)",
              value: "base",
            },
            {
              name: "react (react with tailwind)",
              value: "react",
            },
          ]
          return answers.scope === "kreios" ? baseChoices : [...baseChoices, ...commonPackages]
        },
      },
      {
        type: "input",
        name: "name",
        message: "What is the name of the package?",
        default: (answers) => (commonPackages.some(({ value }) => answers.type === value) ? answers.type : undefined),
      },
      {
        type: "checkbox",
        name: "dependencies",
        message: "Select dependencies to add:",
        choices: (answers) => {
          return [...allDeps.values()]
            .sort((a, b) => a.name.localeCompare(b.name))
            .filter(({ version, name }) => {
              if (name.startsWith("@types/")) return false
              if (version !== "workspace:*") return true
              return name.startsWith(`@${answers.scope}`) || name.startsWith("@kreios")
            })
            .map(({ name }) => ({
              value: name,
              name: name,
            }))
        },
        pageSize: 20,
      },
    ],
    actions: (answers) => {
      const { scope, name, type, dependencies } = answers as {
        scope: string
        name: string
        type: string
        dependencies: string[]
      }
      const actions = createPackage(scope, name, type, allDeps, dependencies)

      const run = (command: string, message?: string, cwd?: string) =>
        actions.push({
          type: "runCommand",
          data: { command, cwd, message },
        })

      const turboFilter = `--ui stream --filter=@${scope}/*`

      run("pnpm install", "Installing...")
      run(`pnpm turbo run format ${turboFilter} -- --write`, "Formatting package")
      run(`pnpm turbo run lint ${turboFilter} -- --fix`, "Linting package")
      run(`pnpm turbo run typecheck ${turboFilter}`, "Typechecking package")

      return actions
    },
  })

  plop.setHelper("eq", (a, b) => a === b)
  plop.setHelper("or", (a, b) => a || b)
  plop.setHelper("lowercase", (str) => str.toLowerCase())
  plop.setHelper("capitalize", (str) => str.charAt(0).toUpperCase() + str.slice(1).toLocaleLowerCase())

  // Add a custom action to run commands
  plop.setActionType("runCommand", (answers, config) => {
    const safeConfig = customCommandConfigSchema.safeParse(config)
    if (!safeConfig.success) {
      throw (new Error("Invalid config for runCommand action").stack = safeConfig.error.stack)
    }
    const { command, cwd, message } = safeConfig.data.data

    try {
      execSync(command, { stdio: "inherit", cwd: cwd })
      return message || `Command executed successfully: ${command}`
    } catch (error) {
      console.error(error)
      throw error
    }
  })
}
