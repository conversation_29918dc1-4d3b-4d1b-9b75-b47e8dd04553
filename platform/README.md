# Kreios Liquiddata Core

This repository serves as the core foundation of the Kreios Liquiddata Project, providing reusable, open-source building blocks that can be shared and utilized across various Kreios projects.

## Overview

The Liquiddata Core repository (formerly known as data-platform) is designed to be a central source of shared components, utilities, and infrastructure that can be integrated into other Kreios projects. It emphasizes code reusability, maintainability, and standardization across the Kreios ecosystem.

## Integration Method

This repository is not intended for standalone use or development. Instead, it should be integrated into other repositories using `git subtree`. This approach allows for:

- Maintaining a single source of truth for shared components
- Easy version management of the platform across different projects
- Flexibility in accepting upstream changes and contributing back

### Integration Commands

Here are the key commands for working with the platform in your project:

```bash
# Check current platform version
git log -1 --oneline platform
# or
pnpm platform status

# Update platform to specific version
git subtree pull --prefix=platform https://github.com/liquiddata-core.git <ref>
# or
pnpm platform upgrade <ref>

# Push local platform changes upstream
git subtree push --prefix=platform https://github.com/liquiddata-core.git <branch>
# or
pnpm platform push <branch>
```

## Technology Stack

The repository is built using modern web technologies and tools:

- **Core Language**: TypeScript
- **Framework**: Next.js 14
- **UI Layer**:
  - React 18
  - Radix UI primitives
  - shadcn/ui components
  - TailwindCSS for styling
  - Tanstack Table for data grids
  - Recharts for data visualization
- **Build System**: Turborepo for monorepo management
- **Package Management**: pnpm
- **Infrastructure**: Terraform for core infrastructure management
- **Development Tools**: 
  - ESLint for code linting
  - Prettier for code formatting
  - Custom CLI tools for development workflows

## Repository Structure

The repository is organized into these top-level directories:

- `/core/` - Core platform components and utilities (UI, hooks, data tables, auth, etc.)
- `/infra/` - Shared infrastructure for hosting Liquiddata apps (`environments` for client-side deployments, `modules` for reusable Terraform modules)
- `/tools/` - Development tools and utilities (Docker, CLI tools, build configs)
- `/docs/` - Platform documentation
- `/patches/` - Package patches and overrides

## Versioning and Changelog

All changes are tracked in `CHANGELOG.md`, which includes:
- Detailed descriptions of changes in each version
- Documentation of breaking changes
- Migration guides when necessary

## Contributing

When contributing to this repository, please note that changes here will affect all projects that integrate this platform. Therefore:

1. Ensure backwards compatibility when possible
2. Document breaking changes thoroughly
3. Update the changelog with all significant changes
4. Follow the established coding standards and patterns

Projects using this platform are encouraged to contribute improvements back to the core codebase. If you develop functionality in your project that could be useful for other projects:

1. Ensure your changes follow the platform's coding standards and patterns
2. Document your changes thoroughly, including:
   - The purpose and benefits of the changes
   - How to use new features or APIs
   - If breaking changes are necessary, provide clear upgrade instructions
3. Create a pull request with the platform team
4. Add an entry to the CHANGELOG.md describing your changes
5. If your changes are not backward compatible, include step-by-step upgrade instructions for existing projects

## License

See the [LICENSE](LICENSE) file for details. 