# Deployment Guide

## Overview

HSP uses Terraform to automate infrastructure setup and management. Each environment has its own infrastructure configuration in the `infra/` directory.

## Setting up a new environment

This section is relevant when you need to deploy HSP to a new environment, such as setting up a new development instance or creating a new production deployment. It covers the initial infrastructure setup steps.

### Creating the Terraform Config

1. Copy an existing environment folder from `infra/`:

```bash
cp -r infra/existing_env infra/new_env
```

2. Adapt the configuration files to your needs

### Configure the Terraform Backend

Before deploying a new environment, you need to set up storage for the Terraform state:

- For Azure deployments: Set up an Azure Storage Account
- Consult the [Terraform backend documentation](https://www.terraform.io/docs/language/settings/backends/index.html) for other options

## Deployment Steps

This section describes the process of deploying new versions of HSP to an existing environment. You'll need these steps whenever you want to update an environment with new code or configuration changes.

### 1. Build Docker Image

Build a Docker image from the current repository state:

```bash
pnpm docker:build
```

Note: Ensure the repository name matches the one declared in your environment's Terraform configuration.

### 2. Push Image

Push the built image to your container registry:

```bash
pnpm push <image>
```

### 3. Update Configuration

Update the image reference in your environment's variables:

```bash
# Edit infra/<env>/variables.tf
```

### 4. Apply Infrastructure Changes

Navigate to your environment's directory and apply the Terraform configuration:

```bash
cd infra/<env>/

# Only needed if you've updated the platform since your last deployment
terraform init --upgrade

terraform apply
```

## Continuous Integration

This section covers the automated build and deployment setup included in the repository. The repository includes CI/CD pipeline templates for:
- GitHub Actions
- Azure DevOps

These templates automate:
- Building and testing the application
- Creating and pushing Docker images
- Deploying to different environments

You can find the pipeline definitions in:
- `.github/workflows/` for GitHub Actions
- `.azure-pipelines/` for Azure DevOps

Adapt these templates to match your specific deployment needs and infrastructure setup.
