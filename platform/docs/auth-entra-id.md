# Azure Entra ID Authentication Setup Guide

This guide walks through setting up Azure Entra ID (formerly Azure AD) authentication for applications built on the Kreios platform.

## Prerequisites

- Access to an Azure subscription
- Access to Azure Entra ID (formerly Azure AD) tenant
- Administrative access to register applications in Azure Entra ID
- Your application's URL (e.g., `https://acme.platform.softwarefactory.lu`)

## 1. Create App Registration in Azure Portal

1. Sign in to the [Azure Portal](https://portal.azure.com)
2. Search for and select "Microsoft Entra ID" (formerly Azure Active Directory)
3. In the left menu, under "Manage", select "App registrations"
4. Click "New registration"
5. Configure the application:
   - **Name**: Enter the name of your application, which will help identify it in the Azure portal.
   - **Supported account types**: Accounts in this organizational directory only (Single tenant)
   - **Redirect URI**:
     - Platform: Web
     - URL: `https://<your-application-domain>/api/auth/callback/microsoft-entra-id`
     - For local development: `http://localhost:3000/api/auth/callback/microsoft-entra-id`

## 2. Configure Authentication

1. In your app registration, go to "Authentication" in the left menu
2. Under "Platform configurations", ensure your Web platform is configured
3. Under "Advanced settings":
   - Enable "Access tokens"
   - Enable "ID tokens"

## 3. Create Client Secret

1. Go to "Certificates & secrets" in the left menu
2. Under "Client secrets", click "New client secret"
3. Add a description (e.g., "ACME Platform Auth")
4. Choose an expiration (recommended: 12 months)
5. **Important**: Copy the generated secret value immediately - you won't be able to see it again

## 4. Configure Environment Variables to deploy

The following environment variables need to be configured for Azure Entra ID authentication:

AZURE_ENTRA_ID_CLIENT_ID
AZURE_ENTRA_ID_CLIENT_SECRET
AZURE_ENTRA_ID_TENANT_ID

These values must be added to the AKS (Azure Kubernetes Service) application as environment variables to ensure proper authentication setup. To achieve this, you need to update the appropriate Terraform file, based on the environment in which this application will be used.

If you are running the application locally, add these values to your .env file.
