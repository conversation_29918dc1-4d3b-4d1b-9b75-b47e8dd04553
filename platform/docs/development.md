# Development Guide

This guide helps you set up and work with the HSP development environment. Whether you're setting up a new development environment, making changes to existing features, or contributing to the platform, you'll find the relevant information here.

## Development Environment

Before you begin development, you'll need to set up your development environment. While we aim to support various development environments, some configurations have proven more efficient than others.

We recommend Linux (either native or via Windows Subsystem for Linux) or macOS with Visual Studio Code or Cursor AI for development, but you can successfully develop on:

- Any Linux distribution
- macOS
- Windows
- Using other IDEs like IntelliJ

## System Requirements

To ensure a smooth development experience, you'll need several system-level dependencies. These are documented in `flake.nix` and can be installed in different ways depending on your preferences.

You have two options for setting up dependencies:

### Using Nix Package Manager

```bash
nix develop
```

### Manual Installation

Install the following using your preferred package manager:

- Homebrew (macOS)
- apt (Ubuntu/Debian)
- pacman/yay (Arch Linux)
- Others

Required dependencies:

- Node.js 20
- pnpm 9
- Docker and Docker Compose
- Kubernetes tools (kube<PERSON>l, helm)
- Terraform CLI
- Azure CLI
- PostgreSQL
- Additional tools listed in `flake.nix`

## Environment Setup

You can either use our official development environments or set up your own local environment. Each option has different tradeoffs in terms of setup time and resource usage.

### Development Environment Options

#### Option 1: Using Official Development Environments

1. Obtain an `env.local` file for one of the official development environments
2. Place it at the root of your repository
3. For resource isolation, override `PLATFORM_RESOURCE_NAMESPACE` in your `.env` file
   - This isolates your messages, Elasticsearch indices, database tables, and other resources
   - Prevents interference with shared resources and other developers
   - Recommended for fresh starts or when you don't want to share infrastructure

#### Option 2: Self-Sufficient Local Environment

1. Use the `docker-compose.yaml` file at the repository root
2. Copy the `env.docker` file to `.env` at your repository root
3. This configuration will use resources hosted by Docker Compose

### Resource Setup

1. Install package dependencies:

```bash
pnpm install
```

2. Initialize domain resources:

```bash
cd packages/domain

# If using a relational database as event store
pnpm drizzle-kit migrate

# Seed the database with initial data
pnpm seed

# Build and populate the search indices
pnpm reindex
```

## Launching Applications

The project consists of multiple applications. You can run them individually or all at once, depending on your development needs.

### Starting Individual Apps

Navigate to the specific app directory and start it:

```bash
cd apps/app
pnpm dev
```

### Starting All Apps

From the repository root:

```bash
pnpm dev
```

## Database Migrations

The project uses database migrations to track and apply schema changes. This section covers how to generate and apply migrations when working with relational database event stores.

### After Changes or New Releases

To update resources after changes or new releases:

```bash
pnpm drizzle-kit migrate
pnpm reindex
```

### When Modifying Domain Model

When modifying the domain model (e.g., adding a new aggregate with table-per-aggregate strategy):

```bash
pnpm drizzle-kit generate  # Generate migration files
pnpm drizzle-kit migrate   # Apply migrations
```

## Branching Patterns and Git Workflow

We use Git Flow for version control. This section describes our branch structure and how to work with different types of changes.

### Main Branches

- `main` - Production releases only, always stable
- `develop` - Main development branch, integration point for features

### Supporting Branches

- `feature/*` - New features and non-emergency bug fixes

  - Branch from: `develop`
  - Merge back into: `develop`
  - Naming: `feature/LD-123_add_user_authentication`

- `hotfix/*` - Emergency production fixes

  - Branch from: `main`
  - Merge back into: `main` AND `develop`
  - Naming: `hotfix/LD-456_fix_login_issue`

- `release/*` - Release preparation

  - Branch from: `develop`
  - Merge back into: `main` AND `develop`
  - Naming: `release/1.2.0`

- `bugfix/*` - Bug fixes for unreleased features
  - Branch from: `develop`
  - Merge back into: `develop`
  - Naming: `bugfix/LD-789_fix_validation`

### Branch Naming Convention

- Always use the appropriate prefix based on branch type
- Include ticket ID when applicable (e.g., `LD-123`)
- Use lowercase and underscores for readability
- Be concise but descriptive
- Examples:
  - `feature/LD-123_add_user_authentication`
  - `hotfix/LD-456_fix_login_issue`
  - `release/1.2.0`
  - `bugfix/LD-789_fix_validation`

### Best Practices

- Keep branches focused on a single feature or fix
- Regularly sync with the parent branch
- Delete branches after merging
- Never commit directly to `main` or `develop`
- Separate application changes from platform changes
- Keep changes focused and atomic
- One logical change per commit

## Contributing Platform Changes

This section explains how to properly submit changes to the Liquiddata platform core when working on HSP features.

### External Contributors

If you're not a member of the Kreios Liquiddata core team, you have two options:

1. Create and submit a patch using `git format-patch`, or
2. Request a core team member to submit the changes on your behalf

### Core Team Members

Ensure commits separate platform changes from project code.

Use the Platform CLI to contribute back changes like so:

```bash
pnpm platform contribute <changeset_name>
```

_Notes:_

- This command pushes any new commits in the `platform/` folder to a feature branch in the platform repository.
- `changeset_name` will be converted to lowercase with underscores and used as the feature branch name
- Examples: `"1234_my_change"` or `"A_nice_change"`

### Platform Version Management

To upgrade to a specific platform version, again using the Platform CLI:

```bash
pnpm platform upgrade <version>
```

_Notes:_

- `version` is the concrete version ref (Git ref, really) to upgrade to, which is either:
  - A specific release (e.g., `"v1.1.0"`)
  - `"latest"` for the latest stable release
  - `"develop"` for the latest (unstable and potentially untested) development version
