# Binding a Custom Domain to Your Azure Application Using Terraform

This guide explains how to bind a custom domain to your Azure application using Terraform and DNS records. Follow these steps to ensure the process is completed successfully.

## Prerequisites

1. **Access to your Terraform environment**: Ensure you have Terraform installed and configured on your system.
2. **Domain Management Access**: You need access to manage DNS records for your domain.
3. **Application Deployment**: Your application should already be deployed and running on Azure.

## Steps

### 1. Authenticate with Azure

Before initializing Terraform, authenticate to the Azure subscription where your resources are hosted:

```bash
az login
```

Once authenticated, Azure will display a list of subscriptions. You can:

1. Type the number corresponding to the desired subscription directly in the terminal. After selecting, Azure will set the chosen subscription as active and display a confirmation message.
2. Alternatively, use the following command to explicitly set the subscription:

```bash
az account set --subscription "<your-subscription-id-or-name>"
```

### 2. Initialize and Apply Terraform

Start by initializing and applying your Terraform configuration to set up the necessary infrastructure.

```bash
terraform init
terraform apply
```

Review the proposed changes and confirm to apply them.

### 3. Retrieve Domain Verification ID and Create DNS Records

Run the following command to retrieve the domain verification ID required for TXT record creation:

```bash
terraform output domain_verification_id
```

This will output a value similar to:

```
"CBADF43A4D2C4F53632BA4C3585082650D0DB4F06267B556F28C8E80F36DBF4"
```

Use this `domain_verification_id` value to create a TXT record in your DNS provider:

- **Record Type**: TXT
- **Host/Name**: asuid
- **Value**: The `domain_verification_id` output from Terraform.

Next, retrieve the IP address for your application using:

```bash
terraform output ip_domain_record
```

Then, create an A record in your DNS provider:

- **Record Type**: A
- **Host/Name**: The subdomain or root domain you want to bind.
- **Value**: The `ip_domain_record` output from Terraform.

> **Note:** If you prefer to use a CNAME instead of an A record, you can retrieve the CNAME value using:
>
> ```bash
> terraform output cname_domain_record
> ```
>
> Then, create a CNAME record instead of an A record:
>
> - **Record Type**: CNAME
> - **Host/Name**: The subdomain you want to bind.
> - **Value**: The `cname_domain_record` output from Terraform.

### 4. Update Terraform Configuration

Once the DNS records have been set up, update your Terraform configuration to bind the custom domain. Set the `app_domain` variable in your Terraform script to the custom domain value:

```hcl
variable "app_domain" {
  default = "your-custom-domain.com"
}
```

### 5. Reapply Terraform

Reapply your Terraform configuration to complete the binding process:

```bash
terraform plan
terraform apply
```

Alternatively, you can pass the value during the Terraform apply command without modifying your Terraform scripts. This approach is useful for temporary or ad hoc changes:

```bash
terraform apply -var="app_domain=your-custom-domain.com"
```

> **Warning:** If you are using a CI/CD script to apply the Terraform changes, avoid this method and ensure the value is defined within the configuration to maintain consistency and reproducibility.

## Verification

Once the process is complete:

1. Verify the DNS records have propagated using tools like [DNS Checker](https://dnschecker.org).
2. In the Azure Portal, navigate to your application’s _Custom Domains_ settings and confirm that the domain has been successfully bound.
3. Test accessing your application via the custom domain.

If there are any issues, revisit the DNS records and ensure they are correctly configured. You can use tools like `nslookup` or `dig` to debug DNS resolution issues.
