/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { SchemaTypes } from "@pothos/core"
import SchemaBuilder, { BasePlugin } from "@pothos/core"

import type { ZodEnumType } from "./types"

import "./global-types"

const pluginName = "zodTransform"

export default pluginName

export class ZodTransformPlugin<Types extends SchemaTypes> extends BasePlugin<Types> {}

SchemaBuilder.registerPlugin(pluginName, ZodTransformPlugin)

const proto: PothosSchemaTypes.SchemaBuilder<SchemaTypes> =
  SchemaBuilder.prototype as PothosSchemaTypes.SchemaBuilder<SchemaTypes>

proto.zodEnum = function <T extends ZodEnumType>(zodEnum: T, name: string) {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return this.enumType(name, {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    values: zodEnum.options,
    description: zodEnum.description,
  })
}
