/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-namespace */
import type { SchemaTypes } from "@pothos/core"
import type { z } from "zod"

import type { Prettify } from "@kreios/utils/types/prettify"

import type { ZodTransformPlugin } from "./index"

type ValueOf<T> = T extends Array<infer U> ? U : T[keyof T]

declare global {
  export namespace PothosSchemaTypes {
    export interface Plugins<Types extends SchemaTypes> {
      zodTransform: ZodTransformPlugin<Types>
    }

    export interface SchemaBuilder<Types extends SchemaTypes> {
      zodEnum: <T extends [string, ...string[]]>(
        zodEnum: z.Zod<PERSON>num<T>,
        name: string
      ) => PothosSchemaTypes.EnumRef<Types, Prettify<ValueOf<T>>>
    }
  }
}
