#!/usr/bin/env tsx
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { writeFile } from "node:fs/promises"
import { join } from "node:path"
import type { GraphQLSchema } from "graphql"
import { program } from "@commander-js/extra-typings"
import { lexicographicSortSchema, printSchema } from "graphql"
import { createJiti } from "jiti"

const jiti = createJiti(process.cwd())

program
  .version("0.1.0")
  .argument("<schema-path>", "Path to the schema file")
  .argument("[output-path]", "Path to output the generated schema.graphql file", "schema.graphql")
  .action(async (schemaPath, outputPath) => {
    try {
      console.log(process.cwd())
      const { schema } = (await jiti.import(schemaPath)) as { schema?: GraphQLSchema }

      if (!schema) {
        throw new Error("No schema exported from the provided file")
      }

      console.log("Generating the graphql schema")
      await writeFile(join(process.cwd(), outputPath), printSchema(lexicographicSortSchema(schema)))
      console.log(`Generated the graphql schema at ${outputPath}`)
    } catch (error) {
      console.error("Error generating schema:", error)
      process.exit(1)
    }
  })
  .parse(process.argv)
