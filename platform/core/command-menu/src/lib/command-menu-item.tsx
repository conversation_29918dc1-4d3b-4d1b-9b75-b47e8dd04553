/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import { forwardRef } from "react"
import { useRouter } from "nextjs-toploader/app"

import { cn } from "@kreios/ui"
import { CommandItem } from "@kreios/ui/command"

import { useCommandMenu } from "./command-menu-context"

/** @public */
export const CommandMenuItem = forwardRef<
  React.ElementRef<typeof CommandItem>,
  React.ComponentPropsWithoutRef<typeof CommandItem>
>(({ className, onSelect, ...props }, ref) => {
  const [, setOpen] = useCommandMenu()
  return (
    <CommandItem
      className={className}
      ref={ref}
      onSelect={
        onSelect
          ? (...args) => {
              onSelect(...args)
              setOpen(false)
            }
          : undefined
      }
      {...props}
    />
  )
})

export const CommandMenuLinkItem = forwardRef<
  React.ElementRef<typeof CommandMenuItem>,
  React.ComponentPropsWithoutRef<typeof CommandMenuItem> & { href: string }
>(({ className, href, ...props }, ref) => {
  const router = useRouter()
  return (
    <CommandMenuItem
      className={cn("cursor-pointer", className)}
      ref={ref}
      onSelect={() => {
        console.log("href", href)
        router.push(href)
      }}
      {...props}
    />
  )
})
