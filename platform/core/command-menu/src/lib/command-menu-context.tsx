/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { Dispatch, FC, ReactNode, SetStateAction } from "react"
import { createContext, useContext, useMemo, useState } from "react"

import { useKeyboardEvent } from "@kreios/hooks/use-keyboard-event"

type CommandMenuContextType = [boolean, Dispatch<SetStateAction<boolean>>]

// eslint-disable-next-line @typescript-eslint/no-empty-function
const CommandMenuContext = createContext<CommandMenuContextType>([true, () => {}])

export const useCommandMenu = () => useContext(CommandMenuContext)

/** @public */
export const CommandMenuConsumer = CommandMenuContext.Consumer

export const CommandMenuProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [open, setIsOpen] = useState(false)

  useKeyboardEvent(["cmd+k", "/"], () => setIsOpen((cur) => !cur))

  return (
    <CommandMenuContext.Provider
      value={useMemo(() => [open, setIsOpen] satisfies CommandMenuContextType, [open, setIsOpen])}
    >
      {children}
    </CommandMenuContext.Provider>
  )
}
