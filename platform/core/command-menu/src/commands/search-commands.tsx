/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { QueryLike } from "@trpc/react-query/shared"
import type { TRPCQueryProcedure } from "@trpc/server"
import type { TRPC_ERROR_CODE_NUMBER } from "@trpc/server/unstable-core-do-not-import"
import type { z } from "zod"
import { FileIcon } from "@radix-ui/react-icons"
import { useDebounce } from "use-debounce"

import type { Session } from "@kreios/auth"
import { CommandEmpty, CommandGroup, CommandLoading, useCommandState } from "@kreios/ui/command"
import { Skeleton } from "@kreios/ui/skeleton"
import { capitalize } from "@kreios/utils/capitilize"

import { CommandMenuLinkItem } from "../lib/command-menu-item"

export interface GlobalSearchResultItem {
  aggregateId: string
  label: string
  href: string
}

interface GlobalSearchResult {
  type: string
  items: GlobalSearchResultItem[]
}

export type ComponentDictionary<T extends GlobalSearchResult> = {
  [K in T["type"]]?: React.FC<{ item: Extract<T, { type: K }>["items"][number] }>
}

/**
 * Our Root types for all apps in the platform
 * TODO: Make this common type
 */
type RootTypes = {
  ctx: {
    session: Session | null
  }
  meta: object
  errorShape: {
    data: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      zodError: z.typeToFlattenedError<any, string> | null
      code:
        | "PARSE_ERROR"
        | "BAD_REQUEST"
        | "INTERNAL_SERVER_ERROR"
        | "NOT_IMPLEMENTED"
        | "UNAUTHORIZED"
        | "FORBIDDEN"
        | "NOT_FOUND"
        | "METHOD_NOT_SUPPORTED"
        | "TIMEOUT"
        | "CONFLICT"
        | "PRECONDITION_FAILED"
        | "UNSUPPORTED_MEDIA_TYPE"
        | "PAYLOAD_TOO_LARGE"
        | "UNPROCESSABLE_CONTENT"
        | "TOO_MANY_REQUESTS"
        | "CLIENT_CLOSED_REQUEST"
      httpStatus: number
      path?: string | undefined
      stack?: string | undefined
    }
    message: string
    code: TRPC_ERROR_CODE_NUMBER
  }
  transformer: true
}

/**
 * The TRPC procedure for searching
 * @typeParam T - The app specific search result type
 */
type SearchProcedure<T extends GlobalSearchResult> = QueryLike<
  RootTypes,
  TRPCQueryProcedure<{
    input: string
    output: T[]
  }>
>

export const SearchCommands = <T extends GlobalSearchResult>({
  componentDict,
  procedure,
  defaultSearchComponent,
}: {
  componentDict?: ComponentDictionary<T>
  defaultSearchComponent?: React.FC<{ item: GlobalSearchResultItem }>
  procedure: SearchProcedure<T>
}) => {
  const search = useCommandState((state) => state.search)
  const [debouncedSearch] = useDebounce(search, 500)

  // eslint-disable-next-line react-compiler/react-compiler
  const { data: searchResults, isFetching } = procedure.useQuery(debouncedSearch, { enabled: !!debouncedSearch })

  if (isFetching) {
    return (
      <>
        {Array.from({ length: 3 }, (_) => (
          <CommandLoading>
            <div className="flex items-center space-x-4 px-2 py-2">
              <Skeleton className="mr-2 h-5 w-5 flex-shrink-0 rounded-full" />
              <div className="w-full space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </div>
          </CommandLoading>
        ))}
      </>
    )
  }

  if (searchResults?.length === 0) {
    return <CommandEmpty>No results found.</CommandEmpty>
  }

  return (
    <>
      {searchResults?.map((result) => (
        <CommandGroup key={result.type} heading={capitalize(result.type)}>
          {result.items.map((item) => {
            const Component =
              componentDict?.[result.type as keyof typeof componentDict] ?? defaultSearchComponent ?? searchComponent
            return <Component key={item.aggregateId} item={item} />
          })}
        </CommandGroup>
      ))}
    </>
  )
}

const searchComponent: React.FC<{ item: GlobalSearchResultItem }> = ({ item: item }) => (
  <CommandMenuLinkItem key={item.aggregateId} href={item.href}>
    <FileIcon className="mr-2 h-4 w-4" />
    <div>
      <span className="font-medium text-gray-900">{item.label}</span>
    </div>
  </CommandMenuLinkItem>
)
