/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import { LaptopIcon, MoonIcon, SunIcon } from "lucide-react"
import { useTheme } from "next-themes"

import { CommandGroup } from "@kreios/ui/command"

import { CommandMenuItem } from "../lib/command-menu-item"

export const ThemeCommands = () => {
  const { setTheme } = useTheme()

  return (
    <CommandGroup heading="Theme" key="Theme">
      <CommandMenuItem onSelect={() => setTheme("light")} key="Light">
        <SunIcon className="mr-2 h-4 w-4" />
        Light
      </CommandMenuItem>
      <CommandMenuItem onSelect={() => setTheme("dark")} key="Dark">
        <MoonIcon className="mr-2 h-4 w-4" />
        Dark
      </CommandMenuItem>
      <CommandMenuItem onSelect={() => setTheme("system")} key="System">
        <LaptopIcon className="mr-2 h-4 w-4" />
        System
      </CommandMenuItem>
    </CommandGroup>
  )
}
