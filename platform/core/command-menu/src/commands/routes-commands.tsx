/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { ComponentType, FC } from "react"

import { CommandGroup } from "@kreios/ui/command"

import { CommandMenuLinkItem } from "../lib/command-menu-item"

export const RouteCommands: FC<{
  routes: { href: string; icon: ComponentType<{ className?: string }>; label: string }[]
}> = ({ routes }) => (
  <CommandGroup heading="Links" key="Links">
    {routes.map((route) => (
      <CommandMenuLinkItem href={route.href} key={route.href} value={route.label}>
        <route.icon className="mr-2 h-4 w-4" />
        {route.label}
      </CommandMenuLinkItem>
    ))}
  </CommandGroup>
)
