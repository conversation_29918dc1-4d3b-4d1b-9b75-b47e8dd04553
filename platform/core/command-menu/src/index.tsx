/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC, ReactElement, ReactPortal } from "react"
import { forwardRef, Fragment } from "react"
import { Primitive } from "@radix-ui/react-primitive"

import { CommandDialog, CommandInput, CommandList, CommandSeparator } from "@kreios/ui/command"

import { CommandMenuProvider, useCommandMenu } from "./lib/command-menu-context"

export const CommandMenuTrigger = forwardRef<
  React.ElementRef<typeof Primitive.button>,
  Omit<React.ComponentPropsWithoutRef<typeof Primitive.button>, "onClick">
>((props, forwardedRef) => {
  const [open, setOpen] = useCommandMenu()
  return (
    <Primitive.button
      type="button"
      aria-expanded={open}
      data-state={open ? "open" : "closed"}
      ref={forwardedRef}
      onClick={() => setOpen((cur) => !cur)}
      {...props}
    />
  )
})

type CommandMenuChildren = ReactElement | Array<CommandMenuChildren> | ReactPortal | boolean | null | undefined

export { CommandMenuProvider as CommandMenu, useCommandMenu }

export const CommandMenuContent: FC<{ children: CommandMenuChildren; shouldFilter?: boolean }> = ({
  shouldFilter,
  ...props
}) => {
  const [open, setOpen] = useCommandMenu()

  const children = Array.isArray(props.children) ? props.children : [props.children]

  return (
    <CommandDialog open={open} onOpenChange={setOpen} shouldFilter={shouldFilter}>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        {/* <CommandEmpty>No results found.</CommandEmpty> */}
        {children.map((child, index, array) =>
          index !== array.length - 1 ? (
            <Fragment key={child && typeof child === "object" && "key" in child ? (child.key ?? index) : index}>
              {child}
              <CommandSeparator />
            </Fragment>
          ) : (
            child
          )
        )}
      </CommandList>
    </CommandDialog>
  )
}
