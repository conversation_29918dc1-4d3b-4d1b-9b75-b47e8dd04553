{"name": "@kreios/command-menu", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.tsx", "./*": "./src/*.tsx"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@kreios/prettier-config", "dependencies": {"@kreios/auth": "workspace:*", "@kreios/hooks": "workspace:*", "@kreios/ui": "workspace:*", "@kreios/utils": "workspace:*", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-primitive": "2.0.0", "@trpc/client": "11.0.0-rc.401", "@trpc/react-query": "11.0.0-rc.401", "@trpc/server": "11.0.0-rc.401", "lucide-react": "0.457.0", "next-themes": "0.3.0", "nextjs-toploader": "3.7.15", "use-debounce": "10.0.3", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}, "peerDependencies": {"next": "14.2.13", "react": "18.3.1", "react-dom": "18.3.1"}}