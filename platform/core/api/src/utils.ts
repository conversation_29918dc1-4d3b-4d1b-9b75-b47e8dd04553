/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  AggregationsAggregationContainer,
  QueryDslQueryContainer,
  ScriptSort,
} from "@elastic/elasticsearch/lib/api/types"
import type { z } from "zod"

import type { Document, ElasticsearchGateway, GatewayConfig, IndexName, TermsAggregation } from "@kreios/elasticsearch"
import type { DocumentType, TermsAggregationWithLabels } from "@kreios/elasticsearch/gateway"
import type { Prettify } from "@kreios/utils/types/prettify"
import { DataLoader } from "@kreios/utils/dataloader"
import { entries } from "@kreios/utils/entries"
import { In } from "@kreios/utils/in"

type Aggregations = Record<string, TermsAggregation | TermsAggregationWithLabels<any>>

export type Config<T = any, L = any> = {
  column: string
  title: string
  load: (id: any) => Promise<T>
} & ("label" extends keyof NoInfer<T>
  ? { label?: (obj: T, label: L) => string }
  : { label: (obj: T, label: L) => string })

export type FacetConfig<T extends Aggregations> = {
  [K in keyof T]: Config
}

/**
 * Type helper function to safly create a facet config
 * Prevents problems with infering return type of load function
 */
export const createFacet = <T>(config: Config<T>): Prettify<Config<T>> => config

/**
 * Creates faceted search options from elasticsearch term aggregations.
 * Supports both direct object loading and sub-aggregation based labeling.
 *
 * @example Simple object loading mode:
 * ```ts
 * const facets = await createFacets(aggregations, {
 *   status: createFacet({
 *     column: "status",
 *     title: "Status",
 *     label: (obj) => obj.name,
 *     load: (id) => statusLoader.load(id)
 *   })
 * })
 * ```
 *
 * @example Sub-aggregation mode using elasticsearch inner_hits:
 * ```ts
 * const facets = await createFacets(aggregations, {
 *   salesManager: createFacet({
 *     column: "salesManager",
 *     title: "Sales Manager",
 *     label: (
 *       _: string,
 *       { salesManager: { name } }: { salesManager: Pick<CustomerProjectDocument["salesManager"], "name"> }
 *     ) => name,
 *     load: async (id: string) => id // Pass through ID when using sub-aggregations
 *   })
 * })
 * ```
 *
 * @param aggregations - Elasticsearch term aggregations response
 * @param config - Configuration object defining how to load and label each facet
 * @returns Array of facet options with values, counts and labels
 *
 * @throws Error if label generation fails and no fallback is available
 */
export async function createFacets<T extends Aggregations, TConfig extends FacetConfig<T>>(
  aggregations: T,
  config: TConfig
) {
  const facets = await Promise.all(
    entries(aggregations).map(async ([key, aggregation]) => {
      const {
        label = (obj: unknown) => {
          if (typeof obj === "object" && obj && "label" in obj && typeof obj.label === "string") return obj.label
          // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
          throw new Error(`No label function provided for ${obj}`)
        },
        column,
        title,
        load,
      } = config[key]

      const options = await Promise.all(
        aggregation.buckets.map(async (bucket) => {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          const obj = await load(bucket.key)

          // Get the label context from sub-aggregations if present
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          const labelContext = "labels" in bucket ? bucket.labels.hits.hits[0]._source : undefined

          // Extract label in steps for easier debugging
          let labelText: string
          if (obj === undefined) {
            console.log(`Unable to load object for facet "${key as string}" with value "${bucket.key}"`)
            labelText = `[${bucket.key}]`
          } else {
            labelText = label(obj, labelContext)
          }

          return {
            value: bucket.key,
            count: bucket.doc_count,
            label: labelText,
          }
        })
      )

      return {
        column: column,
        title: title,
        options,
      }
    })
  )
  return facets
}

export const createIndexDataLoaderFactory =
  <TConfig extends GatewayConfig>(gateway: ElasticsearchGateway<TConfig>) =>
  <TIndex extends IndexName<TConfig>, TDocument extends Document = DocumentType<TConfig, TIndex>>(
    index: TIndex,
    options?: {
      field?: string
    }
  ) =>
    new DataLoader(async (ids: readonly string[]) => {
      const field = options?.field
      // If a field is provided, search for the documents instead of getting them by their IDs
      if (field) {
        const { documents } = await gateway.search<TDocument, never>({
          index,
          size: ids.length,
          query: {
            bool: {
              must: [{ terms: { [field]: ids as string[] } }],
            },
          },
        })

        // Create a map of the documents by their IDs for easier access
        const map = new Map<string, TDocument>(
          documents.map((doc) => {
            if (In(field, doc)) return [doc[field] as string, doc]
            throw new Error(`Field ${field} not found in document`)
          })
        )

        return ids.map((id) => map.get(id)!)
      }

      const result = await gateway.get<TDocument>(index, ids as string[])
      return ids.map((id) => result.get(id)!)
    })

/**
 * Type helper to construct the return type of the `search` method when using global aggregations
 * and filtered aggregations
 */
export type GlobalAggregations<
  Aggregations extends Record<string, TermsAggregation | TermsAggregationWithLabels<unknown>>,
> = {
  global: { doc_count: number } & {
    [K in keyof Aggregations]: {
      doc_count: number
      filtered: Aggregations[K]
    }
  }
}

/**
 * Create an Elasticsearch script sort for an enum field
 * @param field - The field to sort by
 * @param enumValue - The zod enum to infer the sort order from
 * @param order - The order to sort by
 * @returns The sort object to be used in the search query
 */
export const createEnumOrderSort = <Field extends string>(
  field: Field,
  enumValue: z.ZodEnum<[string, ...string[]]>,
  order: "asc" | "desc"
) => ({
  _script: {
    type: "number",
    script: {
      lang: "painless",
      source: `
                def order = [${enumValue._def.values.map((value, i) => `'${value}': ${i}`).join(",")}];
                return order.get(doc['${field}'].value);
              `,
    },
    order,
  } satisfies ScriptSort,
})

/**
 * Creates Elasticsearch aggregations for faceted search with exclusive filtering.
 * Each aggregation excludes its own filter from the filter context to prevent
 * the facet from filtering itself out, while still being affected by other active filters.
 *
 * @param should - Optional search query conditions (e.g., text search)
 * @param activeFilters - Currently applied filter conditions across all facets
 * @param baseAggs - Base aggregation configurations for each facet
 * @returns Elasticsearch aggregations configuration with exclusive filtering applied
 *
 */
export const createExclusiveFacetAggregations = (
  should: QueryDslQueryContainer[] | undefined,
  activeFilters: QueryDslQueryContainer[],
  baseAggs: Record<
    string,
    Omit<AggregationsAggregationContainer, "terms"> & Required<Pick<AggregationsAggregationContainer, "terms">>
  >
) => ({
  global: {
    global: {},
    aggs: Object.fromEntries(
      Object.entries(baseAggs).map(([key, agg]) => [
        key,
        {
          filter: {
            bool: {
              // minimum_should_match: 1,
              should,
              must: activeFilters.filter(
                (filter) => !("terms" in filter && filter.terms && agg.terms.field && agg.terms.field in filter.terms)
              ),
            },
          },
          aggs: {
            filtered: agg,
          },
        },
      ])
    ),
  },
})

/**
 * Transforms global aggregations into normilized aggregations by extracting the filtered results
 * @param aggregations The global aggregations from elasticsearch
 * @returns The transformed aggregations
 */
export const transformGlobalAggregations = <
  T extends Record<string, TermsAggregation | TermsAggregationWithLabels<unknown>>,
  K extends keyof T & string,
>(
  aggregations: GlobalAggregations<T>
): { [Key in K]: T[Key] } => {
  return Object.fromEntries(
    entries(aggregations.global)
      .filter(([key]) => key !== "doc_count")
      .map(([key, value]) => [key, value.filtered])
  ) as { [Key in K]: T[Key] }
}
