{"name": "@kreios/api", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@elastic/elasticsearch": "8.15.0", "@kreios/elasticsearch": "workspace:*", "@kreios/utils": "workspace:*"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vitest": "2.1.9"}}