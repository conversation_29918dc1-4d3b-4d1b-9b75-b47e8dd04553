{"name": "@kreios/hooks", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {"./trpc/*": "./src/trpc/*.ts", "./*": "./src/*.tsx"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@kreios/prettier-config", "dependencies": {"@tanstack/react-query": "5.55.4", "react-use-event-hook": "0.9.6"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@trpc/react-query": "11.0.0-rc.401", "@trpc/server": "11.0.0-rc.401", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}}