/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { Mutation } from "@tanstack/react-query"
import type { MutationLike } from "@trpc/react-query/shared"
import type { AnyTRPCMutationProcedure } from "@trpc/server"
import type { AnyRootTypes } from "@trpc/server/unstable-core-do-not-import"
import { useQueryClient } from "@tanstack/react-query"

/**
 * Equivalent to trpc getQueryKey but for mutations
 * Returns the cache key for a given mutation procedure used by tanstack/query
 */
export function getMutationKey<TMutationProcedure extends MutationLike<AnyRootTypes, AnyTRPCMutationProcedure>>(
  mutationProcedure: TMutationProcedure
) {
  // @ts-expect-error - we don't expose _def on the type layer
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
  return [mutationProcedure._def().path as string[]]
}

/**
 * Utility hook that retuns a function to check if a mutation is in progress, allows for filtering multiple mutations using predicate
 *
 * @example
 * const isMutating = useIsMutationContext()
 *
 * // show loading... if mutation for specific id is ongoing
 * return (
 *      isMutating(
 *        api.example.mutation,
 *        (mutation) => mutation.state.variables?.id === id
 *      ) ? "Loading..." : null
 */
export const useIsMutationContext = () => {
  const queryclient = useQueryClient()

  return <TMutationProcedure extends MutationLike<AnyRootTypes, AnyTRPCMutationProcedure>>(
    mutationProcedure: TMutationProcedure,
    predicate: (
      mutation: Mutation<
        ReturnType<TMutationProcedure["useMutation"]>["data"],
        ReturnType<TMutationProcedure["useMutation"]>["error"],
        ReturnType<TMutationProcedure["useMutation"]>["variables"]
      >
    ) => boolean
  ) => {
    return queryclient.isMutating({
      mutationKey: getMutationKey(mutationProcedure),
      predicate,
    })
  }
}
