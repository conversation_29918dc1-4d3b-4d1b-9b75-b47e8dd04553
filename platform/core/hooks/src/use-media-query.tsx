/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import { useCallback, useMemo, useSyncExternalStore } from "react"

const supportMatchMedia = typeof window !== "undefined" && typeof window.matchMedia !== "undefined"

/**
 * A hook to listen for changes in media queries.
 *
 * @param {string} query - The media query to listen for.
 * @param {typeof window.matchMedia | null} [matchMedia=supportMatchMedia ? window.matchMedia : null] - The matchMedia function to use. Defaults to window.matchMedia if available.
 * @param {((query: string) => { matches: boolean }) | null} [ssrMatchMedia=null] - A server-side matchMedia function. Used for server-side rendering.
 * @param {boolean} [defaultMatches=false] - The default value for matches. Used before the first render or if matchMedia is not available.
 * @param {boolean} [noSsr=false] - If true, the hook will not use server-side rendering.
 *
 * @returns {boolean} - Whether the media query matches the current viewport.
 *
 * @example
 * const isDesktop = useMediaQuery('(min-width: 1024px)');
 */
export function useMediaQuery(
  query: string,
  matchMedia: typeof window.matchMedia | null = supportMatchMedia ? window.matchMedia : null,
  ssrMatchMedia: ((query: string) => { matches: boolean }) | null = null,
  defaultMatches = false,
  noSsr = false
): boolean {
  const getDefaultSnapshot = useCallback(() => defaultMatches, [defaultMatches])
  const getServerSnapshot = useMemo(() => {
    if (noSsr && matchMedia) {
      return () => matchMedia(query).matches
    }

    if (ssrMatchMedia !== null) {
      const { matches } = ssrMatchMedia(query)
      return () => matches
    }
    return getDefaultSnapshot
  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia])
  const [getSnapshot, subscribe] = useMemo(() => {
    if (matchMedia === null) {
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      return [getDefaultSnapshot, () => () => {}]
    }

    const mediaQueryList = matchMedia(query)

    return [
      () => mediaQueryList.matches,
      (notify: () => void) => {
        mediaQueryList.addEventListener("change", notify)
        return () => mediaQueryList.removeEventListener("change", notify)
      },
    ]
  }, [getDefaultSnapshot, matchMedia, query])
  const match = useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot)

  return match
}

export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
} as const

export type Breakpoints = keyof typeof BREAKPOINTS

/**
 * A hook to retrieve a boolean value indicating whether the viewport matches a given breakpoint.
 * @param {Breakpoints} breakpoint - The breakpoint to listen for.
 * @returns {boolean} - Whether the breakpoint matches the current viewport.
 * @example
 * const isDesktop = useBreakpoint('lg');
 */
export const useBreakpoint = (breakpoint: Breakpoints) => useMediaQuery(`(min-width: ${BREAKPOINTS[breakpoint]}px)`)
