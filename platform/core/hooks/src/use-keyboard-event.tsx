/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useEffect } from "react"
import { useEvent } from "react-use-event-hook"

type KeyType = `cmd+${string}` | `${string & {}}`

export const useKeyboardEvent = (key: KeyType | KeyType[], callback: () => void) => {
  const toggle = useEvent(callback)

  const { meta, normal } = (Array.isArray(key) ? key : [key]).reduce(
    (acc, key) => {
      if (key.startsWith("cmd+")) acc.meta.push(key.replace("cmd+", "").toLocaleLowerCase())
      else acc.normal.push(key.toLocaleLowerCase())
      return acc
    },
    { meta: new Array<string>(), normal: new Array<string>() }
  )

  useEffect(() => {
    function onKeyDown(e: KeyboardEvent) {
      try {
        if (!e.key) return

        const keyLower = e.key.toLowerCase()

        if ((meta.includes(keyLower) && (e.metaKey || e.ctrlKey)) || normal.includes(keyLower)) {
          if (
            (e.target instanceof HTMLElement && e.target.isContentEditable) ||
            e.target instanceof HTMLInputElement ||
            e.target instanceof HTMLTextAreaElement ||
            e.target instanceof HTMLSelectElement
          ) {
            return
          }
          toggle()
        }
      } catch (error) {
        console.error("Error in keyboard event handler:", error)
      }
    }

    window.addEventListener("keydown", onKeyDown)

    return () => window.removeEventListener("keydown", onKeyDown)
  }, [key, meta, normal, toggle])
}
