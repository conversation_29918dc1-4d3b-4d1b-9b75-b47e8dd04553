/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"
import { getTranslations } from "next-intl/server"

import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from "@kreios/mail-sender/utils"

export const MagicLinkEmail: FC<{ url: string; name: string; logo?: string; primaryColor?: string }> = async ({
  url,
  name,
  logo: logoUrl,
  primaryColor = "#18181B",
}) => {
  const t = await getTranslations("emailContent.authenticationEmail")

  const main = {
    backgroundColor: "#ffffff",
    fontFamily:
      '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
  }

  const container = {
    margin: "0 auto",
    padding: "20px 0 48px",
    maxWidth: "580px",
  }

  const logoContainer = {
    display: "flex",
    alignItems: "center",
    marginBottom: "24px",
  }

  const logo = {
    marginRight: "12px",
  }

  const heading = {
    fontSize: "32px",
    fontWeight: "bold",
    color: primaryColor,
  }

  const paragraph = {
    fontSize: "16px",
    lineHeight: "26px",
  }

  const button = {
    backgroundColor: primaryColor,
    borderRadius: "4px",
    color: "#fff",
    fontWeight: "bold",
    textDecoration: "none",
    textAlign: "center" as const,
    display: "block",
    width: "100%",
    padding: "12px 20px",
    fontSize: "16px",
    boxShadow: "0 4px 6px rgba(34, 197, 94, 0.25)",
    transition: "all 0.2s ease-in-out",
  }

  const hr = {
    borderColor: "#cccccc",
    margin: "20px 0",
  }

  const footer = {
    color: "#8898aa",
    fontSize: "12px",
  }

  return (
    <Html>
      <Head />
      <Preview>{t("preview", { name })}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            {!!logoUrl && <Img src={logoUrl} width="auto" height="80" alt="Logo" style={logo} />}
            <Heading style={heading}>{name}</Heading>
          </Section>
          <Text style={paragraph}>{t("greeting")}</Text>
          <Text style={paragraph}>{t("welcome", { name })}</Text>
          <Text style={paragraph}>{t("disclaimer")}</Text>
          <Button style={button} href={url}>
            {t("button", { name })}
          </Button>
          <Hr style={hr} />
          <Text style={footer}>{t("footer")}</Text>
        </Container>
      </Body>
    </Html>
  )
}
