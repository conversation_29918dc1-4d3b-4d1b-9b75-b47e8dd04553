/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { EmailConfig } from "@auth/core/providers"

import { MailSender } from "@kreios/mail-sender"
import { AzureProvider, ResendProvider } from "@kreios/mail-sender/providers"
import { render } from "@kreios/mail-sender/utils"
import { capitalize } from "@kreios/utils/capitilize"

import { env } from "../../env"
import { MagicLinkEmail } from "../templates/MagicLinkEmail"

/**
 * Helper function to extract the app name from a URL's hostname.
 * The most left subdomain is used as the name.
 *
 * @param url - The URL to extract the app name from
 * @returns The formatted app name
 */
function extractAppNameFromUrl(url: string): string {
  const { hostname } = new URL(url)
  return hostname.split(".").at(0)!.split("-").map(capitalize).join(" ")
}

/**
 * Helper function to initialize the MailSender with the appropriate provider (Resend or Azure).
 *
 * @returns MailSender - An instance of the MailSender class
 * @throws Error if neither Resend nor Azure Communication Services credentials are provided.
 */
function initializeMailSender(): MailSender {
  if (env.RESEND_API_KEY && env.RESEND_FROM_EMAIL) {
    const resendProvider = new ResendProvider(env.RESEND_API_KEY)
    console.info("Resend provider selected for password-less authentication via email in NextAuth.")
    return new MailSender(resendProvider, {
      name: env.AUTH_EMAIL_NAME ?? "Data Platform",
      email: env.RESEND_FROM_EMAIL,
    })
  } else if (env.AZURE_COMMUNICATION_CONNECTION_STRING && env.AZURE_COMMUNICATION_FROM_EMAIL) {
    const azureProvider = new AzureProvider(env.AZURE_COMMUNICATION_CONNECTION_STRING)
    console.info("Azure Communication Services selected for password-less authentication via email in NextAuth.")
    return new MailSender(azureProvider, {
      name: env.AUTH_EMAIL_NAME ?? "Data Platform",
      email: env.AZURE_COMMUNICATION_FROM_EMAIL,
    })
  } else {
    throw new Error(
      "Neither Resend nor Azure Communication Services credentials were provided. Please set the necessary environment variables."
    )
  }
}

/**
 * EmailProvider dynamically selects between Resend and Azure Communication Services
 * based on environment variables and returns an EmailConfig for NextAuth.
 *
 * @returns EmailConfig - The email provider configuration for NextAuth.
 */
export const EmailProvider = (): EmailConfig => {
  const mailSender = initializeMailSender()

  return {
    id: "email",
    type: "email",
    name: "Email",
    from: mailSender.senderAddress.email,
    maxAge: 24 * 60 * 60, // Validity of the verification token in seconds (24 hours).
    options: {},
    async sendVerificationRequest({ identifier, url }) {
      const name = env.AUTH_EMAIL_NAME ?? extractAppNameFromUrl(url)

      const logo = env.AUTH_EMAIL_LOGO
        ? env.AUTH_EMAIL_LOGO.startsWith("http")
          ? // if logo is just a path attach it to the sign in hostname
            env.AUTH_EMAIL_LOGO
          : new URL(env.AUTH_EMAIL_LOGO, new URL(url).origin).toString()
        : undefined

      try {
        const email = (await MagicLinkEmail({
          url,
          name,
          logo: logo?.includes("localhost") ? undefined : logo,
          primaryColor: env.AUTH_EMAIL_PRIMARY_COLOR,
        })) as React.ReactElement

        // Render the email into both plain text and HTML versions
        const [text, html] = await Promise.all([render(email, { plainText: true }), render(email)])

        await mailSender.send({
          to: [{ name: identifier, email: identifier }],
          subject: `Log in to ${name}`,
          text,
          html,
        })
      } catch (error) {
        console.error("Error during email sending process:", error)
        throw new Error("Failed to send the verification email.")
      }
    },
  }
}
