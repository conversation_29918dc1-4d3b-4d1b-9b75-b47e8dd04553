/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import "server-only"

import type { DefaultSession, NextAuthConfig } from "next-auth"
import { UpstashRedisAdapter } from "@auth/upstash-redis-adapter"
import { Redis } from "@upstash/redis"
import GoogleProvider from "next-auth/providers/google"
import MicrosoftEntraIDProvider from "next-auth/providers/microsoft-entra-id"

import { isTruthy } from "@kreios/utils/isTruthy"

import { env } from "../env"
import { EmailVerificationError } from "./errors"
import { EmailProvider } from "./providers"

/**
 * Module augmentation for `next-auth` types.
 * Extends the `Session` type to include a custom `user.id` property.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string
    } & DefaultSession["user"]
  }
}

/**
 * NextAuth.js configuration object.
 * It sets up pages, callbacks, Redis adapter, and authentication providers.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  // Custom pages for handling errors, sign-in, and email verification.
  pages: {
    error: "/auth/error",
    signIn: "/auth/login",
    verifyRequest: "/auth/verify-request",
  },

  /**
   * Upstash Redis adapter for session storage and persistence.
   * This enables session persistence in the Redis database using Upstash.
   *
   * @see https://upstash.com/
   */
  adapter: UpstashRedisAdapter(
    new Redis({
      url: env.KV_REST_API_URL,
      token: env.KV_REST_API_TOKEN,
      cache: "default", // Recommended by the Vercel KV package.
    })
  ),

  // Trust the host when running in production mode.
  trustHost: true,

  /**
   * Configured authentication providers.
   * Includes Google authentication and custom email provider using `EmailProvider`.
   */
  providers: [
    // Google OAuth provider configuration.
    env.AUTH_GOOGLE_ENABLED &&
      env.GOOGLE_CLIENT_ID &&
      env.GOOGLE_CLIENT_SECRET &&
      GoogleProvider({
        clientId: env.GOOGLE_CLIENT_ID,
        clientSecret: env.GOOGLE_CLIENT_SECRET,
        allowDangerousEmailAccountLinking: true, // Allows account linking across providers
      }),

    // Microsoft Entra ID provider configuration
    env.AUTH_MICROSOFT_ENABLED &&
      env.AZURE_ENTRA_ID_CLIENT_ID &&
      env.AZURE_ENTRA_ID_CLIENT_SECRET &&
      env.AZURE_ENTRA_ID_TENANT_ID &&
      MicrosoftEntraIDProvider({
        clientId: env.AZURE_ENTRA_ID_CLIENT_ID,
        clientSecret: env.AZURE_ENTRA_ID_CLIENT_SECRET,
        tenantId: env.AZURE_ENTRA_ID_TENANT_ID,
        profile: (profile) => {
          // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
          profile.email = profile.email ?? profile.preferred_username
          return {
            id: profile.sub,
            name: profile.nickname,
            email: profile.email,
            image: profile.picture,
          }
        },
        authorization: {
          params: {
            prompt: "select_account",
          },
        },
        allowDangerousEmailAccountLinking: true,
      }),

    // Custom email authentication provider.
    env.AUTH_EMAIL_ENABLED && EmailProvider(),
  ].filter(isTruthy), // Filter out falsy providers to avoid undefined entries.

  // Callback functions that run during the authentication lifecycle.
  callbacks: {
    /**
     * The `signIn` callback verifies email attributes.
     * Ensures that an email is provided and valid, and checks if the email is verified.
     *
     * @param user - The user object returned by the OAuth provider.
     * @param account - The account object returned by the OAuth provider.
     * @param profile - The profile object returned by the OAuth provider.
     * @returns `true` if sign-in is successful; otherwise, throws an error.
     */
    async signIn({ user, profile }) {
      const email = user.email
      if (!email) throw new EmailVerificationError("No email provided")
      if (profile?.email_verified === false) throw new EmailVerificationError("Email not verified")
      if (!env.AUTH_EMAIL_REGEX.test(email)) throw new EmailVerificationError("Email is not allowed")
      return true
    },
  },
} satisfies NextAuthConfig
