/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { OAuthProviderButtonStyles } from "@auth/core/providers/oauth"
import type { FC, ReactNode } from "react"
import { Loader2Icon } from "lucide-react"
import { useFormStatus } from "react-dom"

import { Button } from "@kreios/ui/button"

export const ProviderButton: FC<{
  styles?: OAuthProviderButtonStyles
  name: string
  id: string
  icon?: ReactNode
}> = ({ styles, name, id, icon }) => {
  const { pending } = useFormStatus()

  return (
    <Button name="provider" value={id} variant="outline" type="submit" disabled={pending}>
      {pending ? (
        <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        (icon ?? (!!styles && <img src={`https://authjs.dev/img/providers/${id}.svg`} className="mr-2 h-4 w-4" />))
      )}
      {name}
    </Button>
  )
}
