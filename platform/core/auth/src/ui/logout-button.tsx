/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ComponentProps, FC } from "react"
import { signOut } from "next-auth/react"

import { Button } from "@kreios/ui/button"
import { toast } from "@kreios/ui/sonner"

export const LogoutButton: FC<ComponentProps<typeof Button>> = ({ children = "Logout", ...props }) => {
  return (
    <Button
      onClick={() =>
        toast.promise(signOut(), {
          success: "Logged out",
          loading: "Logging out...",
          error: "Failed to log out",
        })
      }
      {...props}
    >
      {children}
    </Button>
  )
}
