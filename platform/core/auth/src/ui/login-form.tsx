/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC, ReactNode } from "react"
import { forwardRef } from "react"
import { useFormState } from "react-dom"

import { cn } from "@kreios/ui"

const ErrorMessage = forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, children, ...props }, ref) => {
    if (!children) return null

    return (
      <p ref={ref} className={cn("text-sm font-medium text-destructive", className)} {...props}>
        {children}
      </p>
    )
  }
)
ErrorMessage.displayName = "ErrorMessage"

type State = {
  error?: {
    message?: string
    cause?: Error
    provider?: string
  }
}

export const UserLoginForm: FC<{
  action: (state: State, formData: FormData) => Promise<State>
  children: ReactNode[]
}> = ({ action, children }) => {
  const [state, formAction] = useFormState(action, {})

  return (
    <form className="grid gap-4" action={formAction}>
      <ErrorMessage className="text-center text-base">{state.error?.message}</ErrorMessage>

      {children}
    </form>
  )
}
