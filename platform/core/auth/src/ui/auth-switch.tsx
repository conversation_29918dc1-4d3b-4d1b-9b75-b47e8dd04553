/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use server"

import type { FC, ReactNode } from "react"
import { Suspense } from "react"

import { auth } from ".."

/**
 * This component checks if the user is signed in and displays the appropriate content.
 * It uses Suspense to wait for the authentication check to complete.
 *
 * @component
 * @param {ReactNode} children - The content to display if the user is signed in.
 * @param {ReactNode} fallback - The content to display while waiting for the authentication check to complete.
 * @param {ReactNode} guestContent - The content to display if the user is not signed in.
 *
 * @example
 * ```tsx
 * <AuthSwitch
 *   fallback={<div>Loading...</div>}
 *   guestContent={<div>Please sign in.</div>}
 * >
 *   <div>Welcome back!</div>
 * </AuthSwitch>
 * ```
 */
export const AuthSwitch: FC<{
  children?: ReactNode
  fallback?: ReactNode
  guestContent?: ReactNode
}> = ({ children, fallback, guestContent }) => {
  return (
    <Suspense fallback={fallback}>
      <AuthSwitchInner guestContent={guestContent}>{children}</AuthSwitchInner>
    </Suspense>
  )
}

const AuthSwitchInner: FC<{
  children: ReactNode
  guestContent?: ReactNode
}> = async ({ children, guestContent }) => {
  const session = await auth()

  return <>{session ? children : guestContent}</>
}
