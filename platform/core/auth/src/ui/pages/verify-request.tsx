/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"

import { ButtonLink } from "@kreios/ui/button-link"

export interface VerifyRequestPageContent {
  title: string
  description: string
  returnToLogin: string
}

export interface VerifyRequestPageProps {
  content: VerifyRequestPageContent
  headerActions?: React.ReactNode
}

export const VerifyRequestPage: FC<VerifyRequestPageProps> = ({ content, headerActions }) => {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 px-4 py-12 dark:bg-gray-900 sm:px-6 lg:px-8">
      {headerActions && (
        <div className="absolute left-4 right-4 top-4 flex justify-end md:left-8 md:right-8 md:top-8">
          {headerActions}
        </div>
      )}
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100">{content.title}</h2>
          <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">{content.description}</p>
        </div>
        <div className="mt-5">
          <ButtonLink variant="default" className="w-full" href="/auth/login">
            {content.returnToLogin}
          </ButtonLink>
        </div>
      </div>
    </div>
  )
}
