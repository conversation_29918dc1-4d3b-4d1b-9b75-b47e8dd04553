/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { AccessDenied, AuthError } from "@auth/core/errors"
import type { ComponentProps, FC, ReactNode } from "react"
import { isNextRouterError } from "next/dist/client/components/is-next-router-error"
import Link from "next/link"
import { without } from "lodash-es"
import { MailIcon } from "lucide-react"
import { z } from "zod"

import { cn } from "@kreios/ui"
import { buttonVariants } from "@kreios/ui/button"
import { Input } from "@kreios/ui/input"
import { Label } from "@kreios/ui/label"

import { authConfig } from "../../config"
import { EmailVerificationError } from "../../errors"
import { signIn } from "../../index"
import { AuthSwitch } from "../auth-switch"
import { UserLoginForm } from "../login-form"
import { ProviderButton } from "../provider-button"

const oauthProviders = without(
  authConfig.providers.map((provider) => provider.id),
  "email"
)

const hasEmailProvider = oauthProviders.length < authConfig.providers.length
const hasOAuthProvider = oauthProviders.length > 0

const emailProviderSchema = z.object({
  email: z.string().email(),
  provider: z.literal("email"),
})

const providersSchema = z.object({
  provider: z.enum<string, ["google"]>(
    // @ts-expect-error - zod expects a typesafe string array
    oauthProviders
  ),
})

const schema =
  hasOAuthProvider && hasEmailProvider
    ? z.discriminatedUnion("provider", [emailProviderSchema, providersSchema])
    : hasEmailProvider
      ? emailProviderSchema
      : providersSchema

const isAuthError = (error: unknown): error is AuthError =>
  error instanceof Error && "type" in error && typeof error.type === "string"

const isAccessDeniedError = (error: unknown): error is AccessDenied =>
  isAuthError(error) && error.type === "AccessDenied"

const DashboardLink: FC<{ toDashboardText: string }> = ({ toDashboardText }) => (
  <AuthSwitch>
    <Link
      href="/admin"
      className={cn(buttonVariants({ variant: "ghost" }), "absolute right-4 top-4 md:right-8 md:top-8")}
    >
      {toDashboardText}
    </Link>
  </AuthSwitch>
)

const EmailProviderFormSection = ({
  content,
}: {
  content: { emailLabel: string; emailPlaceholder: string; signInWithEmail: string }
}) => (
  <div className="grid gap-2">
    <div className="grid gap-1">
      <Label htmlFor="email">{content.emailLabel}</Label>
      <Input
        id="email"
        name="email"
        placeholder={content.emailPlaceholder}
        type="email"
        autoCapitalize="none"
        autoComplete="email"
        autoCorrect="off"
      />
    </div>
    <ProviderButton id="email" name={content.signInWithEmail} icon={<MailIcon className="mr-2 h-4 w-4" />} />
  </div>
)

const LoginFormSeparator = ({ className, ...props }: Omit<ComponentProps<"div">, "children">) => (
  <div className={cn("relative", className)} {...props}>
    <div className="absolute inset-0 flex items-center">
      <span className="w-full border-t" />
    </div>
    <div className="relative flex justify-center text-xs uppercase">
      <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
    </div>
  </div>
)

const AuthenticationForm: FC<{
  className?: string
  callbackUrl?: string
  branding?: ReactNode
  content: {
    title: string
    email: string
    oauth: string
    oauthMultiple: string
    emailLabel: string
    emailPlaceholder: string
    signInWithEmail: string
  }
}> = ({ callbackUrl, className, branding, content }) => {
  return (
    <div className={cn("mx-auto grid w-[22.5rem] gap-6", className)}>
      <div className="mb-10 flex items-center justify-center text-primary lg:hidden">{branding}</div>
      <div className="grid gap-2 text-center">
        <h1 className="text-3xl font-bold">{content.title}</h1>
        <p className="text-balance text-muted-foreground">
          {hasEmailProvider ? content.email : oauthProviders.length === 1 ? content.oauth : content.oauthMultiple}
        </p>
      </div>
      <UserLoginForm
        action={async (prev, formData: FormData) => {
          "use server"

          const validatedFields = schema.safeParse(Object.fromEntries(formData.entries()))

          if (!validatedFields.success) {
            const fieldErrors = (
              validatedFields as z.SafeParseError<
                | {
                    email: string
                  }
                | { provider: string }
              >
            ).error.flatten().fieldErrors

            console.error(validatedFields.error)

            if ("email" in fieldErrors) {
              return {
                error: {
                  provider: "email",
                  message: fieldErrors.email?.join(", "),
                },
              }
            }

            return {
              error: {
                message: fieldErrors.provider?.join(", "),
              },
            }
          }

          const { data } = validatedFields

          try {
            await signIn(data.provider, {
              ...(data.provider === "email"
                ? {
                    email: data.email,
                  }
                : {}),
              redirectTo: callbackUrl ?? "/admin",
            })

            return {}
          } catch (error) {
            console.error(error)
            if (isNextRouterError(error)) throw error // Re-throw if an expected internal Next.js router error occurs

            // next-auth signIn error handling
            // email verification inside singIn callback has failed
            if (isAccessDeniedError(error) && error.cause?.err instanceof EmailVerificationError) {
              return {
                error: {
                  provider: data.provider,
                  message: error.cause.err.message,
                },
              }
            }

            return {
              error: {
                provider: data.provider,
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-expect-error
                message: error.message as string,
              },
            }
          }
        }}
      >
        {authConfig.providers
          .slice()
          .sort(({ type: a }, { type: b }) => a.localeCompare(b))
          .map((provider, index, array) =>
            provider.type === "email" ? (
              <>
                <EmailProviderFormSection
                  content={{
                    emailLabel: content.emailLabel,
                    emailPlaceholder: content.emailPlaceholder,
                    signInWithEmail: content.signInWithEmail,
                  }}
                />
                {index === 0 && array.length > 1 && <LoginFormSeparator />}
              </>
            ) : (
              <ProviderButton
                id={provider.id}
                key={provider.name}
                name={provider.name}
                styles={"style" in provider ? provider.style : undefined}
              />
            )
          )}
      </UserLoginForm>
    </div>
  )
}

const LoginFormContainer: FC<ComponentProps<"div">> = ({ children, className, ...props }) => (
  <div className={cn("relative flex h-full items-center justify-center bg-background py-12", className)} {...props}>
    {children}
  </div>
)

const ImageContainer: FC<
  ComponentProps<"div"> & {
    branding: ReactNode
  }
> = ({ branding, children, className, ...props }) => (
  <div
    className={cn("hidden items-center justify-center bg-primary text-primary-foreground lg:flex", className)}
    {...props}
  >
    {branding}
    {children}
  </div>
)

export interface LoginLayoutProps {
  children: ReactNode
}

export interface LoginFormContainerProps {
  children: ReactNode
}

export interface LoginImageContainerProps {
  className?: string
  branding: ReactNode
  children?: ReactNode
}

export interface LoginHeaderActionsProps {
  className?: string
  children?: ReactNode
}

const HeaderActions: FC<LoginHeaderActionsProps> = ({ className, children }) => (
  <div className={cn("absolute left-4 right-4 top-4 flex justify-between md:left-8 md:right-8 md:top-8", className)}>
    {children}
  </div>
)

export function LoginLayout({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <div className={cn("h-screen w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]", className)}>
      {children}
    </div>
  )
}

export interface LoginPageContent {
  title: string
  email: string
  oauth: string
  oauthMultiple: string
  emailLabel: string
  emailPlaceholder: string
  signInWithEmail: string
  toDashboard: string
}

export interface LoginPageProps {
  searchParams: { callbackUrl?: string }
  headerActions?: ReactNode
  headerLeftActions?: ReactNode
  branding?: ReactNode
  content: LoginPageContent
}

export const LoginPage: FC<LoginPageProps> = ({
  branding,
  searchParams,
  headerActions,
  headerLeftActions,
  content,
}) => {
  return (
    <LoginLayout>
      <LoginLayout.FormContainer>
        {content.toDashboard && <LoginLayout.DashboardLink toDashboardText={content.toDashboard} />}
        {(headerActions ?? headerLeftActions) && (
          <HeaderActions>
            <div>{headerLeftActions}</div>
            <div>{headerActions}</div>
          </HeaderActions>
        )}
        <AuthenticationForm callbackUrl={searchParams.callbackUrl} branding={branding} content={content} />
      </LoginLayout.FormContainer>
      <LoginLayout.ImageContainer branding={branding} />
    </LoginLayout>
  )
}

LoginLayout.FormContainer = LoginFormContainer
LoginLayout.ImageContainer = ImageContainer
LoginLayout.DashboardLink = DashboardLink
LoginLayout.AuthenticationForm = AuthenticationForm
LoginLayout.HeaderActions = HeaderActions
