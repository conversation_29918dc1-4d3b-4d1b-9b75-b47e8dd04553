{"name": "@kreios/auth", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": {"react-server": "./src/index.rsc.ts", "default": "./src/index.ts"}, "./ui/*": "./src/ui/*.tsx", "./env": "./env.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@auth/core": "0.34.2", "@auth/upstash-redis-adapter": "2.4.2", "@kreios/mail-sender": "workspace:*", "@kreios/ui": "workspace:*", "@kreios/utils": "workspace:*", "@t3-oss/env-core": "0.11.1", "@upstash/redis": "1.34.0", "lodash-es": "4.17.21", "lucide-react": "0.457.0", "next-auth": "5.0.0-beta.18", "next-intl": "4.0.2", "server-only": "0.0.1", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/lodash-es": "4.17.12", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}, "peerDependencies": {"next": "14.2.13", "react": "18.3.1", "react-dom": "18.3.1"}}