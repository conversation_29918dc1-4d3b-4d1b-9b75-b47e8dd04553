/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable no-restricted-properties */
import { createEnv } from "@t3-oss/env-core"
import { z } from "zod"

import { env as mailSenderEnv } from "@kreios/mail-sender/env"

/**
 * Creates a Zod schema that validates string representations of boolean values.
 * Accepts "true"/"false"/"1"/"0" and transforms to boolean.
 *
 * @returns Base Zod schema that validates string booleans and transforms to boolean
 */
const booleanString = () =>
  z.enum(["true", "false", "1", "0"]).transform((val) => val.toLowerCase() === "true" || val === "1")

export const env = createEnv({
  extends: [mailSenderEnv],
  server: {
    GOOGLE_CLIENT_ID: z.string().optional(),
    GOOGLE_CLIENT_SECRET: z.string().optional(),
    AUTH_EMAIL_REGEX: z
      .string()
      .transform((val) => new RegExp(val))
      .describe("Regex for all emails which are allowed to register/login"),

    AUTH_EMAIL_LOGO: z
      .union([z.string(), z.string().url()])
      .optional()
      .describe("URL to the logo to be used in the email"),
    AUTH_EMAIL_NAME: z.string().optional().describe("Name to be used in the email"),
    AUTH_EMAIL_PRIMARY_COLOR: z.string().optional().describe("Primary color to be used in the email"),
    RESEND_API_KEY: z
      .string()
      .optional()
      .describe("API key for the resend API see https://resend.com/docs/send-with-nextjs"),
    RESEND_FROM_EMAIL: z.string().optional().describe("Resend sender email (needs to use a validated domain)"),
    AZURE_COMMUNICATION_CONNECTION_STRING: z.string().optional().describe("Azure Communication connection string"),
    AZURE_COMMUNICATION_FROM_EMAIL: z
      .string()
      .optional()
      .describe("Azure Communication sender email (needs to use a validated domain)"),
    KV_REST_API_TOKEN: z.string().describe("API token for the KV REST API use `vercel env pull` to get the token"),
    KV_REST_API_URL: z.string().url().describe("URL for the KV REST API use `vercel env pull` to get the URL"),
    NEXTAUTH_SECRET: process.env.NODE_ENV === "production" ? z.string().min(1) : z.string().min(1).optional(),

    // Microsoft Entra ID configuration
    AZURE_ENTRA_ID_CLIENT_ID: z.string().optional().describe("Microsoft Entra ID Application (client) ID"),
    AZURE_ENTRA_ID_CLIENT_SECRET: z.string().optional().describe("Microsoft Entra ID client secret"),
    AZURE_ENTRA_ID_TENANT_ID: z.string().optional().describe("Microsoft Entra ID tenant ID"),

    // Flags to enable/disable authentication providers
    AUTH_EMAIL_ENABLED: booleanString()
      .default("true")
      .describe("Enable/disable email authentication provider (true/false/1/0)"),
    AUTH_GOOGLE_ENABLED: booleanString()
      .default("true")
      .describe("Enable/disable Google authentication provider (true/false/1/0)"),
    AUTH_MICROSOFT_ENABLED: booleanString()
      .default("true")
      .describe("Enable/disable Microsoft authentication provider (true/false/1/0)"),
  },
  runtimeEnv: process.env,
  skipValidation:
    !process.env.VERCEL &&
    (!!process.env.CI ||
      !!process.env.SKIP_ENV_VALIDATION ||
      process.env.npm_lifecycle_event === "lint" ||
      process.env.NODE_ENV === "test"),
  emptyStringAsUndefined: true,
})
