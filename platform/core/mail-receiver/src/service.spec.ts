/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable turbo/no-undeclared-env-vars */
import { beforeEach, describe, expect, it } from "vitest"

import type { IMAPConnectionDetails } from "./service"
import { INBOX, MailReceiver } from "./service"

// Get the environment variables
const imapHost = process.env.IMAP_HOST
const imapPort = process.env.IMAP_PORT ? parseInt(process.env.IMAP_PORT) : undefined
const imapSecure = process.env.IMAP_SECURE === "true"
const imapUser = process.env.IMAP_USER
const imapPassword = process.env.IMAP_PASSWORD

// We need to disable the following rule because of the
// eslint-disable @typescript-eslint/no-unsafe-assignment

// Skip tests if the required environment variables are not present
describe.runIf(imapHost && imapPort && imapUser && imapPassword)("MailReceiver", () => {
  let mailReceiver: MailReceiver
  let connectionDetails: IMAPConnectionDetails

  beforeEach(() => {
    mailReceiver = new MailReceiver()
    connectionDetails = {
      host: imapHost!,
      port: imapPort!,
      secure: imapSecure,
      user: imapUser!,
      password: imapPassword!,
    }
  })

  it("should list available mailboxes", async () => {
    const mailboxes = await mailReceiver.listMailboxes(connectionDetails)

    expect(mailboxes.length).toBeGreaterThan(0)
    expect(mailboxes).toContainEqual(expect.objectContaining({ name: INBOX }))
  })

  it("should receive mails from the inbox", async () => {
    // Given
    const mails = await asArray(mailReceiver.receive(connectionDetails))

    // Then
    expect(mails.length).toBeGreaterThan(0)
    mails.forEach((mail) => {
      expect(mail.to).toContainEqual(expect.objectContaining({ email: "<EMAIL>" }))
    })
  })

  it("should have access to all attachments of a mail", async () => {
    // Given
    const [firstMail] = await take(mailReceiver.receive(connectionDetails), 1)

    // Then
    expect(firstMail.attachments.length).toBeGreaterThan(0)
    firstMail.attachments.forEach((attachment) => {
      expect(attachment.filename).toBeDefined()
      expect(attachment.mimeType).toBeDefined()
      expect(attachment.size).toBeGreaterThan(0)
      expect(attachment.content).toBeDefined()
    })
    expect(firstMail.attachments).toContainEqual(expect.objectContaining({ inline: false }))
    expect(firstMail.attachments).toContainEqual(expect.objectContaining({ inline: true }))
  })

  it("supports fetching mails starting from a specific mail received in the past", async () => {
    // Given
    const [firstMail] = await take(mailReceiver.receive(connectionDetails, INBOX), 1)

    // When
    const mails = await asArray(mailReceiver.receive(connectionDetails, INBOX, firstMail.uid))

    // Then
    expect(mails.length).toBeGreaterThan(0)
    expect(mails).not.toContainEqual(expect.objectContaining({ uid: firstMail.uid }))
    expect(mails).not.toContainEqual(expect.objectContaining({ messageId: firstMail.messageId }))
  })
})

// HELPER FUNCTIONS

/**
 * Takes the first n elements from the given async generator.
 *
 * @param gen - The async generator to take elements from.
 * @param n - The number of elements to take.
 * @returns {Promise<T[]>} A promise that resolves to an array of the first n elements.
 * @template T - The type of the elements in the generator.
 */
async function take<T>(gen: AsyncGenerator<T>, n: number): Promise<T[]> {
  const result: T[] = []
  for (let i = 0; i < n; i++) {
    const next = await gen.next()
    if (next.done) {
      break
    }
    result.push(next.value)
  }
  return result
}

/**
 * Converts the given async generator into an array.
 *
 * @param gen - The async generator to convert.
 * @returns {Promise<T[]>} A promise that resolves to an array of the elements.
 * @template T - The type of the elements in the generator.
 */
async function asArray<T>(gen: AsyncGenerator<T>): Promise<T[]> {
  const result: T[] = []
  for await (const value of gen) {
    result.push(value)
  }
  return result
}
