/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { ListTreeResponse } from "imapflow"
import type { Email } from "postal-mime"
import { ImapFlow } from "imapflow"
import PostalMime from "postal-mime"

/**
 * A class that handles receiving and processing emails from an IMAP server.
 */
export class MailReceiver {
  /**
   * Connects to the IMAP server using the provided connection details.
   *
   * @param {IMAPConnectionDetails} connectionDetails - The details required to connect to the IMAP server.
   * @private
   * @returns {Promise<ImapFlow>} A promise that resolves to an ImapFlow instance.
   */
  private async connect(connectionDetails: IMAPConnectionDetails): Promise<ImapFlow> {
    const client = new ImapFlow({
      host: connectionDetails.host,
      port: connectionDetails.port,
      secure: connectionDetails.secure,
      auth: {
        user: connectionDetails.user,
        pass: connectionDetails.password,
      },
    })

    await client.connect()
    console.log(`[${connectionDetails.user}] Connected to IMAP server`)
    return client
  }

  /**
   * Connects to the IMAP server and retrieves the list of available mailboxes.
   * @param {IMAPConnectionDetails} connectionDetails - The details required to connect to the IMAP server.
   * @returns {Promise<Mailbox[]>} A promise that resolves to a list of available mailboxes, which can be nested.
   */
  async listMailboxes(connectionDetails: IMAPConnectionDetails): Promise<Mailbox[]> {
    // Wait until client connects and authorizes
    const client = await this.connect(connectionDetails)

    // Get the list of mailboxes from the server and parse them into the required format
    const tree = await client.listTree()
    const parseBoxes = (node: ListTreeResponse): Mailbox[] => {
      // @types/imapflow declares `folders` as mandatory, but in reality it can be undefined
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      if (!node.folders || node.folders.length === 0) {
        return []
      }

      return node.folders.map((child) => ({
        name: child.name,
        path: child.path,
        children: parseBoxes(child),
      }))
    }
    const mailboxes = parseBoxes(tree)
    console.log(`[${connectionDetails.user}] Found ${mailboxes.length} mailboxes`)

    // Even if everything goes well, we should still log out from the server
    await client.logout()
    console.log(`[${connectionDetails.user}] Logged out from IMAP server`)

    return mailboxes
  }

  /**
   * Parses attachments from the message.
   * @param {Object} message - The message object.
   * @returns {Promise<MailAttachment[]>} A promise that resolves to an array of parsed MailAttachment objects.
   * @private
   */
  private parseAttachments(message: Email): MailAttachment[] {
    return message.attachments
      .filter((part) => part.filename)
      .map((part) => {
        const content = new Uint8Array(part.content)
        return {
          filename: part.filename!,
          size: content.byteLength,
          inline: part.disposition === "inline",
          mimeType: part.mimeType,
          content: Buffer.from(content),
        }
      })
  }

  /**
   * Helper function to map an address object to a MailAddress object.
   * @param {Object} address - The address object.
   * @returns {MailAddress} The mapped MailAddress object.
   * @private
   */
  private mapAddress(address?: { name?: string; address?: string }): MailAddress | undefined {
    return address
      ? {
          name: address.name,
          email: address.address!,
        }
      : undefined
  }

  /**
   * Helper function to map an array of address objects to an array of MailAddress objects.
   * @param {Array} addresses - The array of address objects.
   * @returns {MailAddress[]} The array of mapped MailAddress objects.
   * @private
   */
  private mapAddresses(addresses?: { name?: string; address?: string }[]): MailAddress[] {
    return addresses?.map((address) => this.mapAddress(address)!) ?? []
  }

  /**
   * Connects to the IMAP server and retrieves mails from the specified mailbox.
   * @param {IMAPConnectionDetails} connectionDetails - The details required to connect to the IMAP server.
   * @param {string} [mailbox] - The full path of the mailbox to retrieve mails from (default is "INBOX").
   * @param {number} [sinceUid] - The UID of the last processed email to fetch new emails from.
   * @returns {AsyncGenerator<Mail>} An async generator that yields mails in the specified format.
   */
  async *receive(
    connectionDetails: IMAPConnectionDetails,
    mailbox: string = INBOX,
    sinceUid?: number
  ): AsyncGenerator<Mail> {
    // Wait until client connects and authorizes
    const client = await this.connect(connectionDetails)

    // Select and lock a mailbox. Throws if mailbox does not exist
    const lock = await client.getMailboxLock(mailbox)
    console.info(`[${connectionDetails.user}] Selected and acquired lock for mailbox: ${mailbox}`)

    try {
      // If sinceUid is provided, then fetch emails from that UID onwards. Otherwise, fetch all emails.
      const range = sinceUid ? `${sinceUid}:*` : "1:*"

      // Fetch only the UID and source of the email, since we can get everything else by parsing the source.
      const parts = { uid: true, source: true }

      // If sinceUid is provided, then we'll be using it instead of the sequence number in the range expression above.
      const options = sinceUid ? { uid: true } : {}

      // Fetch emails from the mailbox using an async generator, which will stream (yield) emails as they are received.
      for await (const message of client.fetch(range, parts, options)) {
        // Didn't find a way to stop ImapFlow from returning the message referenced by sinceUid itself
        if (sinceUid && message.uid === sinceUid) {
          continue
        }

        // Parse the source of the email, since the ImapFlow message object doesn't contain all the required fields.
        const parsed = await new PostalMime().parse(message.source)
        const mail: Mail = {
          uid: message.uid,
          source: message.source,
          messageId: parsed.messageId,
          mailbox: mailbox,
          date: new Date(parsed.date!),
          to: this.mapAddresses(parsed.to),
          from: this.mapAddress(parsed.from)!,
          cc: this.mapAddresses(parsed.cc),
          replyTo: this.mapAddresses(parsed.replyTo),
          subject: parsed.subject ?? "",
          htmlBody: parsed.html ?? "",
          attachments: this.parseAttachments(parsed), // Parse attachments
        }
        yield mail
      }
    } catch (error) {
      console.error(`[${connectionDetails.user}] Failed to receive emails:`, error)
      throw error
    } finally {
      // Make sure lock is released, otherwise next `getMailboxLock()` never returns
      lock.release()
    }

    // Even if everything goes well, we should still log out from the server
    await client.logout()
    console.log(`[${connectionDetails.user}] Logged out from IMAP server`)
  }
}

export type MailAddress = {
  /** The name associated with the email address. */
  name?: string

  /** The email address. */
  email: string
}

export type MailAttachment = {
  /** The name of the file. */
  filename: string

  /** The size of the file in bytes. */
  size: number

  /** The MIME type of the file. */
  mimeType: string

  /** Whether the attachment is embedded in the email body (e.g., an image). */
  inline: boolean

  /** The content of the attachment. */
  content: Buffer
}

export type Mail = {
  /** The unique identifier of the email message as far as the IMAP server is concerned. */
  uid: number

  /** The unique identifier of the email message. */
  messageId: string

  /** The source of the email message. */
  source: Buffer

  /** The mailbox the email is stored in. */
  mailbox: string

  /** The date the email was received. */
  date: Date

  /** The list of recipients of the email. */
  to: MailAddress[]

  /** The sender of the email. */
  from: MailAddress

  /** The list of CC recipients of the email. */
  cc: MailAddress[]

  /** The reply-to addresses of the email. */
  replyTo: MailAddress[]

  /** The subject of the email. */
  subject: string

  /** The HTML body of the email. */
  htmlBody: string

  /** The list of attachments in the email. */
  attachments: MailAttachment[]
}

export type IMAPConnectionDetails = {
  /** The hostname of the IMAP server. */
  host: string

  /** The port number to connect to. */
  port: number

  /** Whether to use a secure connection. */
  secure: boolean

  /** The username for authentication. */
  user: string

  /** The password for authentication. */
  password: string
}

export type Mailbox = {
  /** The name of the mailbox. */
  name: string

  /** The full path of the mailbox. */
  path: string

  /** The list of nested mailboxes. */
  children?: Mailbox[]
}

/** Constants to represent some default mailbox names. */
export const INBOX = "INBOX"
export const OUTBOX = "Outbox"
