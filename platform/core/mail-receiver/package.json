{"name": "@kreios/mail-receiver", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"imapflow": "1.0.164", "postal-mime": "2.2.7"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/imapflow": "1.0.19", "@types/node": "20.14.10", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}