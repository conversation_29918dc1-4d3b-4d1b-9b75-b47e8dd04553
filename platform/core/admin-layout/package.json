{"name": "@kreios/admin-layout", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.tsx", "./*": "./src/*.tsx"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@kreios/prettier-config", "dependencies": {"@castore/core": "2.3.1", "@kreios/auth": "workspace:*", "@kreios/command-menu": "workspace:*", "@kreios/copilot": "workspace:*", "@kreios/datatable": "workspace:*", "@kreios/ui": "workspace:*", "@kreios/utils": "workspace:*", "@sentry/nextjs": "8.49.0", "@trpc/react-query": "11.0.0-rc.401", "@trpc/server": "11.0.0-rc.401", "constate": "3.3.2", "lucide-react": "0.457.0", "next-auth": "5.0.0-beta.18", "next-intl": "4.0.2", "next-themes": "0.3.0", "nextjs-toploader": "3.7.15", "path-to-regexp": "8.2.0", "react-hook-form": "7.53.0", "react-innertext": "1.1.5", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}, "peerDependencies": {"next": "14.2.13", "react": "18.3.1", "react-dom": "18.3.1"}}