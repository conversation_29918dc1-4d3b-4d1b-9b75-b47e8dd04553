/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { ReactNode } from "react"
import { Bot } from "lucide-react"

import { CopilotTrigger } from "@kreios/copilot"
import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { Separator } from "@kreios/ui/separator"
import { SidebarTrigger } from "@kreios/ui/sidebar"

const CopilotButton = () => (
  <Button
    size="sm"
    aria-label="AI Copilot"
    className={cn(
      "relative overflow-hidden rounded-lg bg-white text-sm font-medium text-gray-800 transition-all duration-300 ease-in-out sm:rounded-full",
      "before:absolute before:inset-0 before:rounded-lg before:bg-gradient-to-r before:from-blue-400 before:to-purple-400 before:p-0.5 before:content-[''] dark:before:from-purple-500 dark:before:to-blue-500 sm:before:rounded-full",
      "after:absolute after:inset-0.5 after:rounded-lg after:bg-white after:content-[''] hover:-translate-y-0.5 hover:scale-105 focus-visible:ring-2 focus-visible:ring-blue-300 dark:bg-gray-900 dark:text-white dark:after:bg-gray-900 sm:after:rounded-full",
      "max-sm:size-8 max-sm:p-0"
    )}
  >
    <span className="relative z-10 flex items-center justify-center gap-2">
      <Bot className="h-5 w-5" />
      <span className="max-sm:sr-only">AI Copilot</span>
    </span>
    <span className="glance-animation absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-gray-200/50 to-transparent dark:via-white/25 sm:rounded-full" />
  </Button>
)

export const Header = ({ children, className }: { className?: string; children: ReactNode }) => (
  <header
    className={cn(
      // "transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",
      "flex h-auto items-center gap-4 border-b bg-background px-4 py-2 sm:py-4 md:h-14 md:h-auto md:border-0 md:bg-muted/40 md:px-6 md:backdrop-blur-[6px]",
      className
    )}
  >
    <SidebarTrigger className="lg:hidden" />
    <Separator orientation="vertical" className="mr-2 h-4 lg:hidden" />
    {children}

    <CopilotTrigger asChild>
      <CopilotButton />
    </CopilotTrigger>
  </header>
)
