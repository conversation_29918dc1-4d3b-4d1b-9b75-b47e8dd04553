/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { ComponentType } from "react"
import { Suspense } from "react"

export const withSkeleton = <P extends object>(
  FallbackComponent: ComponentType<P>,
  SuspenseComponent: ComponentType<P>
): ComponentType<P> => {
  const Page = (props: P) => (
    <Suspense fallback={<FallbackComponent {...props} />}>
      <SuspenseComponent {...props} />
    </Suspense>
  )

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  Page.displayName = `withSkeleton(${SuspenseComponent.displayName ?? SuspenseComponent.name ?? "NextPage"})`
  // return FallbackComponent
  return Page
}
