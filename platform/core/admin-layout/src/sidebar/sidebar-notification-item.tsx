/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { FC } from "react"
import { Bell } from "lucide-react"

import { Badge } from "@kreios/ui/badge"
import { SidebarMenuBadge, SidebarMenuButton, SidebarMenuItem } from "@kreios/ui/sidebar"

/**
 * A Sidebar menu item that opens the notification popover
 */
export const SidebarNotificationItem: FC<{ count?: number; disabled?: boolean }> = ({ count = 0, disabled }) => {
  return (
    <SidebarMenuItem aria-disabled={disabled}>
      <SidebarMenuButton disabled={disabled} tooltip="Notifications">
        <Bell />
        <span>Notifications</span>
      </SidebarMenuButton>
      {count > 0 && (
        <SidebarMenuBadge>
          <Badge
            variant="destructive"
            className="items-center justify-center rounded-full group-aria-disabled/menu-item:opacity-50 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:size-5"
          >
            {count}
          </Badge>
        </SidebarMenuBadge>
      )}
    </SidebarMenuItem>
  )
}
