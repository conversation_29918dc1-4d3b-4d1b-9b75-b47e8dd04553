/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC } from "react"
import { forwardRef } from "react"
import { LogOut, MoonIcon, SunIcon } from "lucide-react"
import { signOut } from "next-auth/react"
import { useTranslations } from "next-intl"
import { useTheme } from "next-themes"

import type { Session } from "@kreios/auth"
import { cn } from "@kreios/ui"
import { Avatar, AvatarFallback, AvatarImage } from "@kreios/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@kreios/ui/dropdown-menu"
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@kreios/ui/sidebar"
import { toast } from "@kreios/ui/sonner"

/**
 * Get initials from a name.
 */
const getAvatarString = (name: string) =>
  name
    .split(/ |\./)
    .slice(0, 2)
    .map((n) => n.at(0)?.toLocaleUpperCase())
    .join("")

const UserAvatar = forwardRef<
  React.ElementRef<typeof Avatar>,
  React.ComponentPropsWithoutRef<typeof Avatar> & { user: NonNullable<Session["user"]> }
>(({ className, user, ...props }, ref) => (
  <Avatar className={cn("h-8 w-8 rounded-lg", className)} {...props} ref={ref}>
    <AvatarImage referrerPolicy="no-referrer" src={user.image ?? undefined} alt={user.name ?? undefined} />
    <AvatarFallback className="rounded-lg">{getAvatarString(user.name ?? user.email ?? "U")}</AvatarFallback>
  </Avatar>
))

const UserLabel = ({ user }: { user: NonNullable<Session["user"]> }) => (
  <div className="grid flex-1 text-left text-sm leading-tight">
    <span className="truncate font-semibold">{user.name}</span>
    <span className="truncate text-xs">{user.email}</span>
  </div>
)

export const SidebarUserMenu: FC<{ user: NonNullable<Session["user"]> }> = ({ user }) => {
  const { isMobile, open } = useSidebar()

  const { theme, setTheme } = useTheme()
  const t = useTranslations("sidebar")

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <UserAvatar user={user} />
              <UserLabel user={user} />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile || open ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <UserAvatar user={user} />
                <UserLabel user={user} />
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onSelect={() => setTheme(theme === "dark" ? "light" : "dark")}>
                <SunIcon className="hidden h-[1.2rem] w-[1.2rem] dark:block" />
                <MoonIcon className="h-[1.2rem] w-[1.2rem] dark:hidden" />
                <span>{t("theme")}</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />

            <DropdownMenuItem
              onSelect={() => {
                toast.promise(signOut(), {
                  success: t("loggedOut"),
                  loading: t("loggingOut"),
                  error: t("logoutError"),
                })
              }}
            >
              <LogOut />
              {t("logOut")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
