/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { FC } from "react"
import { Search } from "lucide-react"

import { CommandMenuTrigger } from "@kreios/command-menu"
import { SidebarMenuButton, SidebarMenuItem } from "@kreios/ui/sidebar"

/**
 * A Sidebar menu item that opens the global search command menu
 */
export const SidebarGlobalSearchItem: FC<{ disabled?: boolean }> = ({ disabled = false }) => {
  return (
    <SidebarMenuItem aria-disabled={disabled}>
      <CommandMenuTrigger asChild>
        <SidebarMenuButton disabled={disabled} tooltip="Search">
          <Search />
          <span>Search</span>
        </SidebarMenuButton>
      </CommandMenuTrigger>
    </SidebarMenuItem>
  )
}
