/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC, ReactNode } from "react"
import { ChevronRight } from "lucide-react"

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@kreios/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@kreios/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@kreios/ui/sidebar"

type SidebarCollapsibleProps = {
  /**
   * The label of the navigation item.
   */
  label: string
  /**
   * The href of the navigation item.
   */
  href?: string
  /**
   * The icon of the navigation item.
   */
  icon?: ReactNode
  /**
   * The sub-items of the navigation item.
   */
  items?: SidebarCollapsibleProps[]
  /**
   * Whether the navigation item is active.
   */
  isActive?: boolean
  /**
   * Whether the navigation item is disabled.
   */
  disabled?: boolean
}

/**
 * A collapsible sidebar menu item
 */
export const SidebarCollapsible: FC<SidebarCollapsibleProps> = (item) => {
  const { open, isMobile } = useSidebar()

  if (!open)
    return (
      <SidebarMenu>
        <SidebarMenuItem aria-disabled={item.disabled}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                disabled={item.disabled}
                tooltip={item.label}
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                {item.icon}
                {item.label}
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              side={isMobile ? "bottom" : "right"}
              align={isMobile ? "end" : "start"}
              sideOffset={4}
            >
              <DropdownMenuLabel className="">{item.label}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                {item.items?.map((subItem) => (
                  <DropdownMenuItem key={subItem.label}>
                    {subItem.icon}
                    <span>{subItem.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    )

  return (
    <SidebarMenu>
      <Collapsible key={item.label} asChild defaultOpen={item.isActive} className="group/collapsible">
        <SidebarMenuItem aria-disabled={item.disabled}>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton tooltip={item.label} disabled={item.disabled}>
              {item.icon}
              <span>{item.label}</span>
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items?.map((subItem) => (
                <SidebarMenuSubItem key={subItem.label}>
                  <SidebarMenuSubButton asChild>
                    <a href={subItem.href}>
                      <span>{subItem.label}</span>
                    </a>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    </SidebarMenu>
  )
}
