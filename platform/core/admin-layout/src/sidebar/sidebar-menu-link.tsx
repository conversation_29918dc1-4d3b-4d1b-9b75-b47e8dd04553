/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

import type { ComponentPropsWithoutRef, FC } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { match as createPathMatch } from "path-to-regexp"

import { SidebarMenuButton } from "@kreios/ui/sidebar"

/**
 * A Sidebar menu item for links
 */
export const SidebarMenuLink: FC<
  {
    href: string
    label: string
    icon: React.ReactNode
    /**
     * The path to match for isActive on.
     * @example "/admin/portfolios/:id"
     * @see https://github.com/pillarjs/path-to-regexp
     */
    match?: string
  } & ComponentPropsWithoutRef<typeof SidebarMenuButton>
> = ({ label: name, href, icon, disabled, match, ...props }) => {
  const pathname = usePathname()
  const transformedPathname = pathname.replace(/\/$/, "")
  const active =
    transformedPathname === href.replace(/\/$/, "") || (match ? !!createPathMatch(match)(transformedPathname) : false)

  return (
    <SidebarMenuButton asChild isActive={active} tooltip={name} aria-disabled={disabled} {...props}>
      <Link className="text-nowrap" href={href} aria-disabled={disabled}>
        {icon}
        {name}
      </Link>
    </SidebarMenuButton>
  )
}
