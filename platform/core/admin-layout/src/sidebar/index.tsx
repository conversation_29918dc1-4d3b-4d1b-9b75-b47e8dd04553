/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC, ReactNode } from "react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"

import { auth } from "@kreios/auth"
import { cn } from "@kreios/ui"
import {
  Sidebar,
  SidebarAutoCollapse,
  SidebarFloatingTrigger,
  SidebarInset,
  SidebarProvider,
  SidebarRail,
} from "@kreios/ui/sidebar"

/**
 * The root sidebar of the
 */
export const AdminSidebar: FC<{ children: ReactNode; sidebar: ReactNode }> = async ({ children, sidebar }) => {
  const session = await auth()

  if (!session?.user) return redirect("/auth/login")

  // The Sidebar component is persisting the state inside a cookie
  // To prevent SSR mismatch, we need to read the cookie value here
  // we need to use the cookie name instead of importing since you can't import values from a client component
  const sidebarState = (cookies().get("sidebar:state")?.value ?? "true") == "true"
  return (
    //
    <SidebarProvider defaultOpen={sidebarState}>
      <SidebarAutoCollapse />
      <Sidebar className="z-50" collapsible="icon">
        <SidebarFloatingTrigger className="hidden lg:inline-flex" />
        {sidebar}
        <SidebarRail />
      </Sidebar>
      <SidebarInset
        className={cn(
          "bg-muted/40 transition-[width] duration-200 ease-linear", // animate the width of the main element
          // The set width of the main element to the remaining width deducting the sidebar width depending on the collapsible mode
          "w-[calc(100vw-var(--sidebar-width))]", // sidebar not collapsed
          "peer-data-[collapsible=icon]:w-[calc(100vw-var(--sidebar-width-icon))]", // sidebar collapsed with icon
          "!peer-data-[mobile=true]:w-screen" // sidebar is not visible on mobile
        )}
      >
        {children}
      </SidebarInset>
    </SidebarProvider>
  )
}
