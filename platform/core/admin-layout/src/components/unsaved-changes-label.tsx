/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"
import { TriangleAlertIcon } from "lucide-react"
import { useFormContext } from "react-hook-form"

import { cn } from "@kreios/ui"

export const UnSavedChangesLabel: FC<{ className?: string }> = ({ className }) => {
  const {
    formState: { isDirty },
  } = useFormContext()

  if (!isDirty) return null

  return (
    <div className={cn("flex items-center text-muted-foreground", className)}>
      <TriangleAlertIcon className="mr-2 size-4" />
      <span className="text-sm">You have unsaved changes</span>
    </div>
  )
}
