/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ComponentProps, ElementRef, FC } from "react"
import { forwardRef, useState } from "react"
import constate from "constate"

import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetOverlay,
  SheetPortal,
  SheetTitle,
  SheetTrigger,
} from "@kreios/ui/sheet"

const [DetailDrawerProvider, useDetailDrawer] = constate(() => useState<string | null>(null))

type DetailDrawerProps = Omit<ComponentProps<typeof Sheet>, "open" | "onOpenChange">
const DetailDrawerInner: FC<DetailDrawerProps> = ({ children, ...props }) => {
  const [id, setID] = useDetailDrawer()

  return (
    <Sheet
      open={id !== null}
      onOpenChange={(open) => {
        if (!open) setID(null)
      }}
      {...props}
    >
      {children}
    </Sheet>
  )
}

const DetailDrawer: FC<DetailDrawerProps> = ({ children, ...props }) => {
  return (
    <DetailDrawerProvider>
      <DetailDrawerInner {...props}>{children}</DetailDrawerInner>
    </DetailDrawerProvider>
  )
}

const DetailDrawerTrigger = forwardRef<
  ElementRef<typeof SheetTrigger>,
  { id: string } & ComponentProps<typeof SheetTrigger>
>(({ id, children, ...props }, ref) => {
  const [, setID] = useDetailDrawer()
  return (
    <SheetTrigger ref={ref} onClick={() => setID(id)} {...props}>
      {children}
    </SheetTrigger>
  )
})

export {
  useDetailDrawer,
  DetailDrawer,
  DetailDrawerTrigger,
  /** @public */
  SheetClose as DetailDrawerClose,
  /** @public */
  SheetContent as DetailDrawerContent,
  SheetDescription as DetailDrawerDescription,
  /** @public */
  SheetFooter as DetailDrawerFooter,
  /** @public */
  SheetHeader as DetailDrawerHeader,
  /** @public */
  SheetOverlay as DetailDrawerOverlay,
  /** @public */
  SheetPortal as DetailDrawerPortal,
  SheetTitle as DetailDrawerTitle,
}
