/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { EventDetail } from "@castore/core"
import type { QueryLike } from "@trpc/react-query/shared"
import type { TRPCQueryProcedure } from "@trpc/server"
import type { AnyRootTypes } from "@trpc/server/unstable-core-do-not-import"

import { Badge } from "@kreios/ui/badge"
import { Button } from "@kreios/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@kreios/ui/card"
import { Skeleton } from "@kreios/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"
import { capitalize } from "@kreios/utils/capitilize"
import { formatRelativeDate } from "@kreios/utils/format-relative-date"

import { withSkeleton } from "../utils/with-skeleton"

function HistoryCardSkeleton() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-6 w-20" />
        </CardTitle>
        <CardDescription>
          <Skeleton className="h-4 w-64" />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Skeleton className="h-4 w-16" />
              </TableHead>
              <TableHead>
                <Skeleton className="h-4 w-16" />
              </TableHead>
              <TableHead>
                <Skeleton className="h-4 w-24" />
              </TableHead>
              <TableHead>
                <Skeleton className="h-4 w-16" />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index} className="*:align-top">
                <TableCell>
                  <Skeleton className="h-4 w-32" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-32" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

type HistoryProcedure = TRPCQueryProcedure<{
  input: string
  output: EventDetail[]
}>

export const HistoryCard = withSkeleton(
  HistoryCardSkeleton,
  ({ id, procedure }: { id: string; procedure: QueryLike<AnyRootTypes, HistoryProcedure> }) => {
    const [events] = procedure.useSuspenseQuery(id)

    return (
      <Card>
        <CardHeader>
          <CardTitle>History</CardTitle>
          <CardDescription>
            Choose a date from the list below to revert to a previous version of this object.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date/time</TableHead>
                {/* <TableHead>User</TableHead> */}
                <TableHead>Comment</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {events.map((event) => (
                <TableRow key={event.version} className="*:align-top">
                  <TableCell>
                    <span>{formatRelativeDate(new Date(event.timestamp))}</span>
                  </TableCell>
                  {/* <TableCell>
                    <div className="flex items-center space-x-2">
                      <CircleUserRound className="h-4 w-4" />
                      <span>Max Musterman</span>
                    </div>
                  </TableCell> */}
                  <TableCell>
                    <Badge className="rounded-md">{event.type}</Badge>
                    {event.type.endsWith("Updated") && event.payload ? (
                      <>
                        {" "}
                        Updated{" "}
                        {Object.keys(event.payload)
                          .map((key) => capitalize(key))
                          .join(", ")}
                      </>
                    ) : null}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button disabled variant="secondary">
                        View
                      </Button>
                      <Button disabled variant="secondary">
                        Revert
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    )
  }
)
