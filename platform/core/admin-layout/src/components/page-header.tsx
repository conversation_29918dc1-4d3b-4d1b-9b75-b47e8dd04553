/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import type { DecoratedProcedureUtilsRecord, RouterLikeInner } from "@trpc/react-query/shared"
import type {
  MutationProcedure,
  QueryProcedure,
  TRPC_ERROR_CODE_NUMBER,
} from "@trpc/server/unstable-core-do-not-import"
import type { ComponentProps, FC, ReactNode } from "react"
import type { z } from "zod"
import React from "react"
import { CopyIcon, PlusIcon, SaveIcon, TrashIcon } from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "nextjs-toploader/app"
import { useFormContext } from "react-hook-form"
import innerText from "react-innertext"

import type { Session } from "@kreios/auth"
import type { ButtonProps } from "@kreios/ui/button"
import { Button } from "@kreios/ui/button"
import { ButtonLink } from "@kreios/ui/button-link"
import { useConfirm } from "@kreios/ui/confirm-dialog"
import { toast } from "@kreios/ui/sonner"
import { capitalize } from "@kreios/utils/capitilize"

import { PathBreadcrumb } from "../automatic-breadcrumbs"

type RootTypes = {
  ctx: {
    session: Session | null
  }
  meta: object
  errorShape: {
    data: {
      zodError: z.typeToFlattenedError<any, string> | null
      code:
        | "PARSE_ERROR"
        | "BAD_REQUEST"
        | "INTERNAL_SERVER_ERROR"
        | "NOT_IMPLEMENTED"
        | "UNAUTHORIZED"
        | "FORBIDDEN"
        | "NOT_FOUND"
        | "METHOD_NOT_SUPPORTED"
        | "TIMEOUT"
        | "CONFLICT"
        | "PRECONDITION_FAILED"
        | "UNSUPPORTED_MEDIA_TYPE"
        | "PAYLOAD_TOO_LARGE"
        | "UNPROCESSABLE_CONTENT"
        | "TOO_MANY_REQUESTS"
        | "CLIENT_CLOSED_REQUEST"
      httpStatus: number
      path?: string | undefined
      stack?: string | undefined
    }
    message: string
    code: TRPC_ERROR_CODE_NUMBER
  }
  transformer: true
}

type PageHeaderProps = {
  /** The title of the page */
  title: string | React.ReactElement
  /** Whether to use the title inside the breadcrumb */
  includeInBreadcrumb?: boolean
} & (
  | {
      children: ReactNode
    }
  | {
      /** @deprecated please use children */
      actions?: React.ReactElement | React.ReactElement[]
    }
)

export const PageHeader: React.FC<PageHeaderProps> = ({ title, includeInBreadcrumb = true, ...props }) => {
  const children = "children" in props ? props.children : props.actions
  return (
    <header className="flex min-h-14 flex-col flex-wrap gap-2 px-4 sm:flex-row sm:items-center md:gap-4 md:px-8">
      {includeInBreadcrumb && <PathBreadcrumb id={-1}>{innerText(title)}</PathBreadcrumb>}
      {typeof title === "string" ? (
        <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight">{title}</h2>
      ) : (
        title
      )}
      <div className="flex flex-wrap gap-2 md:ml-auto">{children}</div>
    </header>
  )
}

/**
 * A safe button integrated with react-hook-form to correctly handle form submission & form states
 */
export const SaveButton: FC<{ children?: ReactNode }> = ({ children }) => {
  const { formState } = useFormContext()
  const hasDirtyFields = !!Object.keys(formState.dirtyFields).length && !!formState.isDirty

  return (
    <Button disabled={formState.isSubmitting || !formState.isValid || !hasDirtyFields} type="submit" size="sm">
      <SaveIcon className="mr-2 size-4" />
      {children ?? "Save"}
    </Button>
  )
}

type DuplicateRouterGenerator<
  TInput,
  TOutput extends {
    aggregateId: string
  },
> = {
  create: MutationProcedure<{
    input: TInput
    output: AsyncGenerator<{ message: string; aggregate: TOutput }, TOutput, unknown>
  }>

  get: QueryProcedure<{
    input: string
    output: TOutput
  }>
}

type DuplicateRouter<
  TInput,
  TOutput extends {
    aggregateId: string
  },
> = {
  create: MutationProcedure<{
    input: TInput
    output: TOutput
  }>

  get: QueryProcedure<{
    input: string
    output: TOutput
  }>
}

export const NewButton: FC<ComponentProps<typeof ButtonLink>> = ({ children, ...props }) => (
  <ButtonLink variant="outline" size="sm" {...props}>
    <PlusIcon className="mr-2 size-4" />
    {children ?? "New"}
  </ButtonLink>
)

export const DuplicateButtonGenerator = <
  TInput,
  TOutput extends {
    aggregateId: string
  },
>({
  onDuplicate,
  router: api,
  utils,
  label,
  ...props
}: {
  label: string
  router: RouterLikeInner<RootTypes, DuplicateRouterGenerator<TInput, TOutput>>
  utils: DecoratedProcedureUtilsRecord<RootTypes, DuplicateRouterGenerator<TInput, TOutput>>
  onDuplicate: () => NoInfer<TInput>
} & Omit<ButtonProps, "onClick">) => {
  const router = useRouter()

  const { mutateAsync: duplicate } = api.create.useMutation({
    onSuccess(data) {
      void (async () => {
        let navigated = false
        for await (const { aggregate } of data) {
          if (!navigated) {
            router.push(aggregate.aggregateId)
            navigated = true
          }
          utils.get.setData(aggregate.aggregateId, aggregate)
        }
      })()
    },
  })

  return (
    <Button
      size="sm"
      onClick={() =>
        toast.asyncIterator(duplicate(onDuplicate()), {
          next: ({ message }) => message,
          loading: `Duplicating ${label.toLocaleLowerCase()}`,
          success: `${capitalize(label)} duplicated`,
          error: `Failed to duplicate ${label.toLocaleLowerCase()}`,
        })
      }
      {...props}
    >
      <CopyIcon className="mr-2 size-4" />
      Duplicate
    </Button>
  )
}

export const DuplicateButton = <
  TInput,
  TOutput extends {
    aggregateId: string
  },
>({
  onDuplicate,
  router: api,
  utils,
  label,
  ...props
}: {
  label: string
  router: RouterLikeInner<RootTypes, DuplicateRouter<TInput, TOutput>>
  utils: DecoratedProcedureUtilsRecord<RootTypes, DuplicateRouter<TInput, TOutput>>
  onDuplicate: () => NoInfer<TInput>
} & Omit<ButtonProps, "onClick">) => {
  const router = useRouter()

  const { mutateAsync: duplicate } = api.create.useMutation({
    onSuccess(data) {
      utils.get.setData(data.aggregateId, data)
      router.push(data.aggregateId)
    },
  })

  return (
    <Button
      size="sm"
      onClick={() =>
        toast.promise(duplicate(onDuplicate()), {
          loading: `Duplicating ${label.toLocaleLowerCase()}`,
          success: `${capitalize(label)} duplicated`,
          error: `Failed to duplicate ${label.toLocaleLowerCase()}`,
        })
      }
      {...props}
    >
      <CopyIcon className="mr-2 size-4" />
      Duplicate
    </Button>
  )
}

type DeleteRouter = {
  delete: MutationProcedure<{
    input: string
    output: any
  }>

  get: QueryProcedure<{
    input: any
    output: any
  }>
}

export const DeleteButton = ({
  id,
  router: api,
  utils,
  label,
  href,
  ...props
}: {
  label: string
  id: string
  href: string
  router: RouterLikeInner<RootTypes, DeleteRouter>
  utils: DecoratedProcedureUtilsRecord<RootTypes, DeleteRouter>
} & ButtonProps) => {
  const router = useRouter()
  const t = useTranslations("toast")

  const confirm = useConfirm()

  const { mutateAsync: deleteAggregate, isPending } = api.delete.useMutation({
    async onSuccess(data, id) {
      await utils.get.invalidate(id)
      router.push(href)
    },
  })

  return (
    <Button
      disabled={isPending}
      onClick={async () => {
        try {
          await confirm({
            title: `Delete ${label}`,
            description: `Are you sure you want to delete this ${label}?`,
            confirmLabel: "Delete",
            cancelLabel: "Cancel",
          })

          toast.promise(deleteAggregate(id), {
            loading: `Deleting ${label}`,
            success: `${capitalize(label)} deleted`,
            error: `Failed to delete ${label}`,
          })
        } catch {
          toast.info(t("info.deletionCanceled"), { duration: 2000 })
        }
      }}
      variant="destructive"
      size="sm"
      {...props}
    >
      <TrashIcon className="mr-2 size-4" />
      Delete
    </Button>
  )
}
