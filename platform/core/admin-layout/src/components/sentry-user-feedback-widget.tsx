/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC } from "react"
import { useEffect, useRef } from "react"
import * as Sentry from "@sentry/nextjs"
import { useTheme } from "next-themes"

type Widget = ReturnType<Feedback["createWidget"]>
type Feedback = NonNullable<ReturnType<typeof Sentry.getFeedback>>
type WidgetParams = Parameters<Feedback["createWidget"]>[0]

const useSentryWidget = (widgetParams?: WidgetParams, enabled = true) => {
  const widget = useRef<Widget | null>(null)

  useEffect(() => {
    const feedback = Sentry.getFeedback()
    if (enabled && !widget.current) {
      widget.current = feedback?.createWidget(widgetParams) ?? null
    }

    if (!enabled && widget.current) {
      widget.current.removeFromDom()
      feedback?.remove()
      widget.current = null
    }

    return () => {
      widget.current?.removeFromDom()
      feedback?.remove()
      widget.current = null
    }
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enabled, ...Object.values(widgetParams ?? {})])

  return widget.current
}

export const SentryFeedbackWidget: FC<{ enabled: boolean }> = ({ enabled }) => {
  const { theme } = useTheme()

  useSentryWidget(
    {
      colorScheme: theme as "light" | "dark" | "system",
    },
    enabled
  )

  return null
}
