/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { QueryLike } from "@trpc/react-query/shared"
import type { TRPCQueryProcedure } from "@trpc/server"
import type { AnyRootTypes } from "@trpc/server/unstable-core-do-not-import"
import type { FC } from "react"
import { useState } from "react"
import { ChevronDownIcon } from "lucide-react"

import { cn } from "@kreios/ui"
import { Combobox } from "@kreios/ui/combobox"

export const AggregateSearchCombobox: FC<{
  procedure: QueryLike<
    AnyRootTypes,
    TRPCQueryProcedure<{
      input: {
        limit: number
        search: string
        template: string[]
      }
      output: { data: { label: string; aggregateId: string }[] }
    }>
  >
  label: string
  template?: string[]
  className?: string
  defaultValue?: { value: string; label: string }
  onSelect?: (value: { value: string; label: string } | null) => void
}> = ({ procedure, label, className, defaultValue = null, template = [], onSelect }) => {
  const [value, setValue] = useState(defaultValue)
  const [search, setSearch] = useState("")

  // eslint-disable-next-line react-compiler/react-compiler
  const { data, isLoading } = procedure.useQuery({ limit: 100, search, template })

  const options = data?.data.map(({ label, aggregateId }) => ({ label: label, value: aggregateId })) ?? []

  return (
    <Combobox
      shouldFilter={false}
      className={cn("px-0", className)}
      variant="ghost"
      indicatorIcon={ChevronDownIcon}
      loading={isLoading}
      label={label}
      options={options}
      search={search}
      onSearchChange={setSearch}
      value={value}
      onChange={(value) => {
        onSelect?.(value)
        setValue(value)
      }}
    />
  )
}
