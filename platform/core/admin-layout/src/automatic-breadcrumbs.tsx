/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ComponentProps, FC, ReactNode } from "react"
import { Fragment, useCallback, useLayoutEffect, useState } from "react"
import Link from "next/link"
import { useParams, usePathname } from "next/navigation"
import constate from "constate"
import { useTranslations } from "next-intl"

import { cn } from "@kreios/ui"
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@kreios/ui/breadcrumb"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@kreios/ui/dropdown-menu"
import { Skeleton } from "@kreios/ui/skeleton"
import { capitalizeWords } from "@kreios/utils/capitlize-words"
import { isTruthy } from "@kreios/utils/isTruthy"

type CustomBreadcrumbs = Record<string | -1, ReactNode>
type CustomBreadcrumbKeys = keyof CustomBreadcrumbs
type CustomBreadcrumbValues = CustomBreadcrumbs[keyof CustomBreadcrumbs]

// Context to store the map to map path segments to breadcrumbs for child pages
const [BreadcrumbProvider, useCustomBreadcrumbs, useSetCustomBreadcrumb] = constate(
  () => {
    const [customBreadcrumbs, setCustomBreadcrumbs] = useState({} as CustomBreadcrumbs)

    const setCustomBreadcrumb = useCallback(
      (path: CustomBreadcrumbKeys, breadcrumb: CustomBreadcrumbValues) =>
        setCustomBreadcrumbs((prev) => {
          const res = { ...prev, [path]: breadcrumb }
          if (!breadcrumb) delete res[path] // if breadcrumb is falsy, remove it from the map
          return res
        }),
      []
    )

    return { customBreadcrumbs, setCustomBreadcrumb }
  },
  (value) => value.customBreadcrumbs,
  (value) => value.setCustomBreadcrumb
)

export { BreadcrumbProvider }

const useBreadcrumbs = () => {
  const t = useTranslations("breadcrumbs.paths")
  const params = useParams()
  const paramValues = Object.values(params).flat()

  const context = useCustomBreadcrumbs()
  const pathname = usePathname()

  const paths = pathname.split("/").filter(isTruthy)

  return paths.slice().map((path, index, array) => {
    // children set by page using PathBreadcrumb using the path segment or -1 for the last breadcrumb
    const children = context[path] ?? (array.length - 1 === index ? context[-1] : null)

    // full path to the current breadcrumb
    const href = "/" + array.slice(0, index + 1).join("/")

    // Breadcrumb was set by page using PathBreadcrumb
    if (children)
      return typeof children === "string" // if children is a string, it is the label for the breadcrumb
        ? {
            label: children,
            href,
          }
        : { children, href }

    // the path is param value for example /projects/[id]
    // display a skeleton loader until the label is using <PathBreadcrumb>
    if (paramValues.includes(path))
      return {
        href,
        children: (
          <BreadcrumbItem>
            <Skeleton className="h-5 w-32" />
          </BreadcrumbItem>
        ),
      }

    // Try to get translation, fall back to capitalized path
    let label: string
    try {
      label = t(path)
    } catch {
      label = capitalizeWords(path)
    }

    return {
      label,
      href,
    }
  })
}

/**
 * PathBreadcrumb component sets a custom breadcrumb for the given path segment.
 * @component
 * @example
 * <PathBreadcrumb id={id}>
 *  {project.name} // strings will display inside a BreadcrumbItem
 * </PathBreadcrumb>
 * @example
 * <PathBreadcrumb id={id}>
 *  <BreadcrumbItem>
 *    <BreadcrumbPage>{persona.name}</BreadcrumbPage>
 *  </BreadcrumbItem>
 * </PathBreadcrumb>
 */
export const PathBreadcrumb: FC<{
  /** The path segment to set the breadcrumb for */
  id: CustomBreadcrumbKeys
  /** The content to be displayed as the breadcrumb.
   * If a string is provided, it will be displayed as the label.
   * Otherwise provide a BreadcrumbItem
   */
  children: CustomBreadcrumbValues
}> = ({ id, children }) => {
  const setCustomBreadcrumb = useSetCustomBreadcrumb()

  useLayoutEffect(() => {
    setCustomBreadcrumb(id, children)

    return () => {
      setCustomBreadcrumb(id, null)
    }
  }, [id, children, setCustomBreadcrumb])

  return null
}

const ITEMS_TO_DISPLAY = 3 // totol number of items to display expanded

/**
 * AutomaticBreadcrumb component dynamically generates a breadcrumb navigation based on the current URL path.
 * It uses custom breadcrumbs if set, otherwise it defaults to capitalized path segments.
 */
export const AutomaticBreadcrumb = ({
  className,
  start,
  ...props
}: { start?: ReactNode } & Omit<ComponentProps<typeof Breadcrumb>, "children">) => {
  const breadcrumbs = useBreadcrumbs()

  const [first, ...rest] = breadcrumbs.slice(1)

  return (
    <Breadcrumb className={cn("flex", className)} {...props}>
      <BreadcrumbList>
        {!!start && (
          <>
            {start}
            {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
            {!!first && <BreadcrumbSeparator />}
          </>
        )}
        {/* always render the first breadcrumb */}

        {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
        {!!first && (
          <Fragment key="first">
            {first.children ?? (
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href={first.href}>{first.label}</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
            )}
            {rest.length > 0 && <BreadcrumbSeparator />}
          </Fragment>
        )}
        {/* if there are more than 3 breadcrumbs display middle section as dropdown */}
        {breadcrumbs.length > ITEMS_TO_DISPLAY && (
          <Fragment key="middle">
            <BreadcrumbItem>
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center gap-1" aria-label="Toggle menu">
                  <BreadcrumbEllipsis className="h-4 w-4" />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  {rest.slice(0, -(ITEMS_TO_DISPLAY - 1)).map(({ children, href, label }, index) => (
                    <DropdownMenuItem key={index}>{children ?? <Link href={href}>{label}</Link>}</DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
          </Fragment>
        )}

        {/* Render the last 2 breadcrumbs as normal breadcrumbs */}
        {rest.slice(-ITEMS_TO_DISPLAY + 1).map(({ label, href, children }, index, array) => (
          <Fragment key={href}>
            {index !== array.length - 1 ? (
              <>
                {children ?? (
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link href={href}>{label}</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                )}
                <BreadcrumbSeparator />
              </>
            ) : (
              (children ?? (
                <BreadcrumbItem>
                  <BreadcrumbPage>{label}</BreadcrumbPage>
                </BreadcrumbItem>
              ))
            )}
          </Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
