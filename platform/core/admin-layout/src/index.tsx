/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC, ReactElement, ReactNode } from "react"
import { cookies } from "next/headers"

import { CommandMenu, CommandMenuContent } from "@kreios/command-menu"
import { DataTableCookieProvider } from "@kreios/datatable/data-table-cookie-provider"
import { ConfirmDialogProvider } from "@kreios/ui/confirm-dialog"

import { AutomaticBreadcrumb, BreadcrumbProvider } from "./automatic-breadcrumbs"
import { Header } from "./header/header"
import { AdminSidebar } from "./sidebar"

const Providers: FC<{ children: ReactNode }> = ({ children }) => (
  <DataTableCookieProvider cookies={cookies().getAll()}>
    <ConfirmDialogProvider>
      <BreadcrumbProvider>{children}</BreadcrumbProvider>
    </ConfirmDialogProvider>
  </DataTableCookieProvider>
)

export const AdminLayout: FC<{
  /**
   * The main content of the page.
   */
  children: ReactNode
  /**
   * The content of the @kreios/ui/sidebar component.
   */
  sidebar: ReactNode
  /**
   * A list of commands that will be displayed in the global search.
   */
  globalSearch?: ReactElement
  /**
   * The inner content of the header.
   * @default AutomaticBreadcrumb @link \@kreios/admin-layout/automatic-breadcrumbs.tsx
   */
  header?: ReactNode
}> = ({ children, sidebar, globalSearch, header = <AutomaticBreadcrumb /> }) => {
  return (
    <Providers>
      <CommandMenu>
        <AdminSidebar sidebar={sidebar}>
          <div className="flex flex-col md:gap-4">
            <Header className="sticky top-0 z-20">{header}</Header>
            <main className="flex flex-1 flex-col">{children}</main>
          </div>
        </AdminSidebar>
        <CommandMenuContent shouldFilter={false}>{globalSearch}</CommandMenuContent>
      </CommandMenu>
    </Providers>
  )
}
