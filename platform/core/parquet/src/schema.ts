/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type * as arrow from "apache-arrow"
import { UTCDate } from "@date-fns/utc"
import { z } from "zod"

/**
 * This file contains Zod schema definitions for custom types encountered in Parquet files.
 *
 * When reading Parquet files using the functions in reader.ts, we need to validate and transform
 * certain specialized data types that don't have direct JavaScript equivalents. This includes:
 *
 * - Binary data (Uint8Array)
 * - Timestamps (converted to UTCDate)
 * - Decimal numbers (using Arrow's BigNum implementations)
 * - Fixed-scale decimals (19,4 and 23,10 precision)
 *
 * These schemas are used by the parquetTypeToZod() function to automatically generate
 * full Zod validation schemas based on the Parquet file's metadata.
 *
 * @see readParquetMetadata in reader.ts for usage
 */

export const uint8ArraySchema = z.instanceof(Uint8Array)
export const timestampSchema = z.number().transform((value) => new UTCDate(value))
export const bigNumSchema = z.custom<InstanceType<typeof arrow.util.BN<Uint32Array>>>(
  (data: unknown) =>
    data instanceof Uint32Array &&
    (data.constructor.name === "DecimalBigNum" ||
      data.constructor.name === "SignedBigNum" ||
      data.constructor.name === "UnsignedBigNum")
)
export const decimal19e4Schema = bigNumSchema.transform((value) => value.valueOf(4))
export const decimal23e10Schema = bigNumSchema.transform((value) => value.valueOf(10))

// @ts-expect-error We set the name to allow inferring when stringifying the zod schema
uint8ArraySchema.name = "uint8ArraySchema"
// @ts-expect-error We set the name to allow inferring when stringifying the zod schema
timestampSchema.name = "timestampSchema"
// @ts-expect-error We set the name to allow inferring when stringifying the zod schema
bigNumSchema.name = "bigNumSchema"
// @ts-expect-error We set the name to allow inferring when stringifying the zod schema
decimal23e10Schema.name = "decimal23e10Schema"
// @ts-expect-error We set the name to allow inferring when stringifying the zod schema
decimal19e4Schema.name = "decimal19e4Schema"
