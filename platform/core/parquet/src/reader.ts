/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { readFile } from "fs/promises"
import * as arrow from "apache-arrow"
import { parseSchema, parseTable } from "arrow-js-ffi"
import wasmInit, { ParquetFile, readParquet, readSchema, wasmMemory } from "parquet-wasm/esm"
import { z } from "zod"

import { bigNumSchema, decimal19e4Schema, decimal23e10Schema, timestampSchema, uint8ArraySchema } from "./schema"

await wasmInit()

/**
 * Represents metadata information extracted from a parquet file.
 * Includes schema information, row count, file size and creation details.
 */
export interface ParquetMetadata {
  /** Number of rows in the file */
  numberOfRows: number
  /** Total size of the file in bytes */
  fileSize: number
  /** Column definitions */
  columns: ParquetColumn[]
  /** Automatically generated Zod schema based on the parquet schema */
  schema: z.ZodType
  /** Software that wrote the file, if available */
  createdBy?: string

  /** @internal Internal type information  */
  internalType: Record<string, string>
}

/**
 * Represents the schema information for a column in a parquet file.
 * Describes the name, type, nullability and path of each column in the parquet schema.
 */
export interface ParquetColumn {
  /** Column name */
  name: string
  /** Data type of the column */
  type: arrow.DataType
  /** Whether the column can contain null values */
  nullable: boolean
  /** Full path to the column in nested structures */
  path: string[]
}

/**
 * Helper type to infer the row type based on provided schema.
 * If no schema is provided, defaults to Record<string, unknown>
 */
type ParquetRow<TSchema> = TSchema extends z.ZodType ? z.infer<TSchema> : Record<string, unknown>

/**
 * Reads metadata from a parquet file including derived Zod schema.
 * Automatically filters out system-level columns like 'schema'.
 *
 * @param source - Path to parquet file or Buffer containing parquet data
 * @returns Promise resolving to ParquetMetadata containing schema and file information
 * @throws If the file cannot be read or is not a valid parquet file
 *
 * @example
 * ```ts
 * const metadata = await readParquetMetadata("data.parquet")
 * console.log(`File contains ${metadata.numberOfRows} rows`)
 * ```
 */
export async function readParquetMetadata(source: string | Buffer): Promise<ParquetMetadata> {
  const buffer = typeof source === "string" ? await readFile(source) : source
  const WASM_MEMORY = wasmMemory()
  const wasmArrowTable = readSchema(buffer).intoFFI()
  const arrowTable = parseSchema(WASM_MEMORY.buffer, wasmArrowTable.addr())

  const parquetFile = await ParquetFile.fromFile(
    new File([buffer], "fileName", {
      type: "application/vnd.apache.parquet",
    })
  )
  const rawMetadata = parquetFile.metadata()
  const fileMetadata = rawMetadata.fileMetadata()
  const numberOfRows = fileMetadata.numRows()

  const free = () => {
    fileMetadata.free()
    rawMetadata.free()
    parquetFile.free()
    wasmArrowTable.free()
  }

  // Filter out system columns and map the schema information
  const columns: ParquetColumn[] = arrowTable.fields.map((field) => {
    return {
      name: field.name,
      type: field.type,
      nullable: field.nullable,
      path: [field.name],
    }
  })

  // Map the arrow type information to record
  const internalType: Record<string, string> = arrowTable.fields.reduce(
    (acc, { name, type }) => {
      acc[name] = type
      return acc
    },
    {} as Record<string, string>
  )

  // Generate Zod schema from column definitions
  const schemaShape = columns.reduce(
    (acc, column) => {
      acc[column.name] = parquetTypeToZod(column)
      return acc
    },
    {} as Record<string, z.ZodType>
  )

  const result = {
    numberOfRows: numberOfRows,
    fileSize: buffer.byteLength,
    columns,
    internalType,
    schema: z.object(schemaShape),
    createdBy: parseParquetWriter(fileMetadata.createdBy()),
  }

  free()

  return result
}

type ParquetDataSource = string | Buffer

/**
 * Reads a parquet file and returns all rows.
 * Handles both file paths and buffers as input.
 * Optionally validates rows against a provided Zod schema.
 * Automatically filters out system-level columns like 'schema'.
 *
 * @param source - Path to parquet file or Buffer containing parquet data
 * @param schema - Optional Zod schema to validate rows against
 * @returns Promise resolving to array of parsed rows
 * @throws If the file cannot be read or validation fails
 *
 * @example Reading parquet file as an array
 * ```ts
 * const rows = await readParquetData("data.parquet")
 * console.log(rows[0])
 * ```
 * @example Reading parquet and parsing rows with a schema
 * ```ts
 * const rows = await readParquetData("data.parquet", {schema: mySchema})
 * console.log(rows) // typeof rows == z.infer<typeof mySchema>[]
 * ```
 * @example Reading parquet file as an iterator
 * ```ts
 * const rows = await readParquetData("data.parquet", {returnType: "iterator"})
 * for (const row of rows) {
 *   console.log(row)
 * }
 * ```
 */
export async function readParquetData<TSchema extends z.ZodType | undefined = undefined>(
  source: ParquetDataSource,
  options?: {
    limit?: number
    schema?: TSchema
    returnType?: "array"
  }
): Promise<Array<ParquetRow<TSchema>>>
export async function readParquetData<TSchema extends z.ZodType | undefined = undefined>(
  source: ParquetDataSource,
  options?: {
    limit?: number
    schema?: TSchema
    returnType?: "iterator"
  }
): Promise<IterableIterator<ParquetRow<TSchema>>>
export async function readParquetData<
  TSchema extends z.ZodType | undefined = undefined,
  TReturn extends "array" | "iterator" = "array",
>(
  source: ParquetDataSource,
  {
    limit,
    schema,
    returnType = "array" as TReturn,
  }: {
    limit?: number
    schema?: TSchema
    returnType?: TReturn
  } = {
    returnType: "array" as TReturn,
  }
): Promise<TReturn extends "array" ? Array<ParquetRow<TSchema>> : IterableIterator<ParquetRow<TSchema>>> {
  const buffer = typeof source === "string" ? await readFile(source) : source
  const WASM_MEMORY = wasmMemory()
  const wasmArrowTable = readParquet(buffer, {
    columns: schema && schema instanceof z.ZodObject ? Object.keys(schema.shape) : undefined,
    limit,
    batchSize: 10922,
  }).intoFFI()

  const table: arrow.Table = parseTable(WASM_MEMORY.buffer, wasmArrowTable.arrayAddrs(), wasmArrowTable.schemaAddr())

  if (returnType === "iterator") {
    const iterator = table[Symbol.iterator]()
    wasmArrowTable.drop()
    const result: IterableIterator<ParquetRow<TSchema>> = {
      next() {
        const { value, done } = iterator.next()
        return {
          value: done ? undefined : schema ? schema.parse(value) : value,
          done,
        }
      },
      [Symbol.iterator]() {
        return this
      },
    }

    return result as TReturn extends "array" ? ParquetRow<TSchema>[] : IterableIterator<ParquetRow<TSchema>>
  }
  const data = table.toArray()
  wasmArrowTable.drop()
  const result: Array<ParquetRow<TSchema>> = schema ? z.array(schema).parse(data) : data

  return result as TReturn extends "array" ? Array<ParquetRow<TSchema>> : IterableIterator<ParquetRow<TSchema>>
}

/**
 * Converts a ParquetColumn type to corresponding Zod schema.
 *
 * @param column - ParquetColumn definition
 * @returns Zod schema for the column
 */
const parquetTypeToZod = (column: ParquetColumn): z.ZodType => {
  // Map parquet types to Zod schemas
  const baseType = (() => {
    const type = column.type

    if (arrow.DataType.isNull(type)) return z.null()
    if (arrow.DataType.isBinary(type)) return uint8ArraySchema
    if (arrow.DataType.isUtf8(type)) return z.string()
    if (arrow.DataType.isInt(type)) return type.bitWidth > 32 ? z.bigint() : z.number()
    if (type.TType === arrow.Type.Int64) return z.bigint()
    if (arrow.DataType.isBool(type)) return z.boolean()
    if (arrow.DataType.isFloat(type)) return z.number()
    if (arrow.DataType.isTimestamp(type)) return timestampSchema
    if (arrow.DataType.isDecimal(type)) {
      if (type.scale === 4) return decimal19e4Schema
      if (type.scale === 10) return decimal23e10Schema
      return bigNumSchema.transform((value) => value.valueOf(type.scale))
    }
    return z.unknown()
  })()

  return column.nullable ? baseType.nullable() : baseType
}

/**
 * Extracts writer information from parquet metadata.
 *
 * @param createdBy - Created by string from parquet metadata
 * @returns Writer string or undefined if not found
 */
const parseParquetWriter = (createdBy?: string): string | undefined => createdBy?.split(" ")[0]
