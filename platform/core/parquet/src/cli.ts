/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Command } from "@commander-js/extra-typings"
import { z, ZodObject } from "zod"

import { isTruthy } from "@kreios/utils/isTruthy"

import { readParquetData, readParquetMetadata } from "./reader"

const program = new Command().name("parquet").description("CLI tool for working with Parquet files").version("0.1.0")

program
  .command("metadata")
  .description("Display metadata for a Parquet file")
  .argument("<file>", "Path to the Parquet file")
  .action(async (file: string) => {
    try {
      const metadata = await readParquetMetadata(file)

      console.log("File Information:")
      console.log("----------------")
      console.log(`Number of rows: ${metadata.numberOfRows}`)
      console.log(`File size: ${metadata.fileSize} bytes`)
      if (metadata.createdBy) {
        console.log(`Created by: ${metadata.createdBy}`)
      }

      console.log("\nZod Schema:")
      console.log("-----------")
      console.log(formatZodSchema(metadata.schema))
    } catch (error) {
      console.error("Error reading Parquet file:", error)
      process.exit(1)
    }
  })

program
  .command("test")
  .description("Test parsing a parquet file using the generated schema")
  .argument("<file>", "Path to the Parquet file")
  .action(async (file: string) => {
    try {
      const metadata = await readParquetMetadata(file)
      const data = await readParquetData(file, { schema: metadata.schema, returnType: "iterator" })

      let length = 0
      let totalLength = Infinity

      const object: Record<string, unknown> = {}
      for (const row of data) {
        if (length >= totalLength) break
        if (totalLength === Infinity) totalLength = Object.keys(row).length
        Object.entries(row)
          .filter(([_, value]) => isTruthy(value))
          .forEach(([key, value]) => {
            object[key] = value
            length++
          })
      }

      if (metadata.schema instanceof ZodObject) {
        console.table(
          Object.fromEntries(
            Object.entries(metadata.schema.shape as Record<string, z.ZodType>).map(([key, value]) => [
              key,
              {
                "Zod Schema": formatZodSchema(value),
                "Arrow Type": metadata.internalType[key],
                "Example Value": object[key],
              },
            ])
          )
        )
      } else console.error("Schema is not ZodObject", metadata.schema)
    } catch (error) {
      console.error("Error reading Parquet file:")
      console.error(error)
      process.exit(1)
    }
  })

program
  .command("head")
  .description("Display the first N rows of a Parquet file")
  .argument("<file>", "Path to the Parquet file")
  .option("-n, --rows <number>", "Number of rows to display", (value) => Number(value), 5)
  .option("-s, --schema [boolean]", "Use schema to validate rows", (value) => value === "true", false)
  .action(async (file, options) => {
    try {
      const numRows = options.rows

      if (isNaN(numRows) || numRows < 1) {
        throw new Error("Number of rows must be a positive integer")
      }

      const rows = await readParquetData(file, {
        schema: options.schema ? (await readParquetMetadata(file)).schema : undefined,
        limit: numRows,
      })

      console.log(`First ${numRows} rows of ${rows.length} total rows:`)
      console.log("-".repeat(20))
      rows.slice(0, numRows).forEach((row, index) => {
        console.log(`\nRow ${index + 1}:`)
        console.log(
          JSON.stringify(row, (_, value) => (typeof value === "bigint" ? Number(value) : value), 2)
            .split("\n")
            .map((line) => "  " + line)
            .join("\n")
        )
      })
    } catch (error) {
      console.error("Error reading Parquet file:", error)
      process.exit(1)
    }
  })

program.parse()

/**
 * Formats a Zod schema into a readable TypeScript-like representation
 */
function formatZodSchema(schema: z.ZodType): string {
  if (schema instanceof z.ZodObject) {
    const shape = schema.shape as Record<string, z.ZodType>
    const fields = Object.entries(shape)
      .map(([key, value]) => `  ${key}: ${formatZodSchema(value)}`)
      .join(",\n")
    return `z.object({\n${fields}\n})`
  }

  if ("name" in schema && schema.name && typeof schema.name === "string") return schema.name

  if (schema instanceof z.ZodString) return "z.string()"
  if (schema instanceof z.ZodNumber) return "z.number()"
  if (schema instanceof z.ZodBigInt) return "z.bigint()"
  if (schema instanceof z.ZodBoolean) return "z.boolean()"
  if (schema instanceof z.ZodArray) return `z.array(${formatZodSchema(schema.element)})`
  if (schema instanceof z.ZodEnum) {
    const options = schema._def.values as string[]
    return `z.enum([${options.map((o) => `"${o}"`).join(", ")}])`
  }
  if (schema instanceof z.ZodUnion) {
    const options = schema._def.options as z.ZodType[]
    return `z.union([${options.map((o) => formatZodSchema(o)).join(", ")}])`
  }
  if (schema instanceof z.ZodOptional) return `${formatZodSchema(schema._def.innerType)}.optional()`
  if (schema instanceof z.ZodNullable) return `${formatZodSchema(schema._def.innerType)}.nullable()`
  if (schema instanceof z.ZodNull) return "z.null()"
  if (schema instanceof z.ZodEffects) {
    if (schema._def.effect.type === "transform")
      return `${formatZodSchema(schema.innerType())}.transform(${schema._def.effect.transform.toString()})`
    if (schema._def.effect.type === "refinement")
      return `${formatZodSchema(schema.innerType())}.refine(${schema._def.effect.refinement.toString()})`
    return `${formatZodSchema(schema.innerType())}.preprocess(${schema._def.effect.transform.toString()})`
  }

  return "z.unknown()"
}
