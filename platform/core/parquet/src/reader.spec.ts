/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { readFile } from "fs/promises"
import { join } from "path"
import { Int64 } from "apache-arrow"
import { describe, expect, it } from "vitest"
import { z } from "zod"

import { readParquetData, readParquetMetadata } from "./reader"

const HousePriceSchema = z.object({
  price: z.bigint().nullable(),
  area: z.bigint().nullable(),
  bedrooms: z.bigint().nullable(),
  bathrooms: z.bigint().nullable(),
  stories: z.bigint().nullable(),
  mainroad: z.string().nullable(),
  guestroom: z.string().nullable(),
  basement: z.string().nullable(),
  hotwaterheating: z.string().nullable(),
  airconditioning: z.string().nullable(),
  parking: z.bigint().nullable(),
  prefarea: z.string().nullable(),
  furnishingstatus: z.string().nullable(),
})

describe("readParquetData", () => {
  const fixturePath = join(__dirname, "../test-fixtures/house-price.parquet")

  it("should read parquet file from path without schema", async () => {
    const rows = await readParquetData(fixturePath)

    expect(rows).toBeInstanceOf(Array)

    expect(rows.length).toBeGreaterThan(0)
    expect(rows[0]).toHaveProperty("price")
    expect(rows[0]).toHaveProperty("bedrooms")
  })

  it("should read parquet file returning an iterator", async () => {
    const rows = await readParquetData(fixturePath, { returnType: "iterator" })

    expect(Symbol.iterator in rows).toBe(true)

    for (const row of rows) {
      expect(row).toHaveProperty("price")
      expect(row).toHaveProperty("bedrooms")
    }
  })

  it("should read parquet file from buffer with schema", async () => {
    const buffer = await readFile(fixturePath)
    const rows = await readParquetData(buffer, { schema: HousePriceSchema })

    expect(rows.length).toBeGreaterThan(0)
    expect(rows[0].price).toBeTypeOf("bigint")
    expect(rows[0].bedrooms).toBeTypeOf("bigint")
    expect(rows[0].furnishingstatus).toBeTypeOf("string")
  })

  it("should validate data against schema", async () => {
    const invalidSchema = z.object({
      price: z.string(), // Price should be number, not string
    })

    await expect(readParquetData(fixturePath, { schema: invalidSchema })).rejects.toThrow(/Expected string/)
  })

  it("should throw on invalid buffer", async () => {
    const invalidBuffer = Buffer.from("not a parquet file")
    await expect(readParquetData(invalidBuffer)).rejects.toThrow()
  })

  it("should throw on corrupted data", async () => {
    const validData = await readFile(fixturePath)
    const corruptedData = Buffer.concat([validData.subarray(0, validData.length / 2), Buffer.from("corrupted data")])

    await expect(readParquetData(corruptedData)).rejects.toThrow()
  })
})

describe("readParquetMetadata", () => {
  const fixturePath = join(__dirname, "../test-fixtures/house-price.parquet")

  it("should read metadata from file path", async () => {
    const metadata = await readParquetMetadata(fixturePath)

    expect(metadata.numberOfRows).toBeGreaterThan(0)
    expect(metadata.fileSize).toBeGreaterThan(0)
    expect(metadata.columns).toContainEqual(
      expect.objectContaining({
        name: "price",
        type: new Int64(),
        nullable: true,
      })
    )
    expect(metadata.schema).toBeInstanceOf(z.ZodObject)
  })

  it("should generate valid schema matching the data", async () => {
    const metadata = await readParquetMetadata(fixturePath)
    const rows = await readParquetData(fixturePath, { schema: metadata.schema })

    expect(rows.length).toBeGreaterThan(0)
  })

  it("should handle both nullable and required fields", async () => {
    const metadata = await readParquetMetadata(fixturePath)

    const nullableColumn = metadata.columns.find((c) => c.nullable)
    const requiredColumn = metadata.columns.find((c) => !c.nullable)

    const zodObject = metadata.schema as z.ZodObject<Record<string, z.ZodType>>

    if (nullableColumn) {
      const field = zodObject.shape[nullableColumn.name]
      expect(field.isNullable()).toBe(true)
    }
    if (requiredColumn) {
      const field = zodObject.shape[requiredColumn.name]
      expect(field.isNullable()).toBe(false)
    }
  })

  it("should throw on invalid parquet file", async () => {
    const invalidBuffer = Buffer.from("not a parquet file")
    await expect(readParquetMetadata(invalidBuffer)).rejects.toThrow()
  })
})
