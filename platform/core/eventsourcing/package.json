{"name": "@kreios/eventsourcing", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./cdc": "./src/cdc/index.ts", "./defaults": "./src/defaults.ts", "./delta": "./src/delta/index.ts", "./drizzle": "./src/drizzle/index.ts", "./drizzle/mocks/*": "./src/drizzle/mocks/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@castore/command-zod": "2.3.1", "@castore/core": "2.3.1", "@castore/event-type-zod": "2.3.1", "@kreios/utils": "workspace:*", "@neondatabase/serverless": "0.9.5", "@vercel/postgres": "0.10.0", "better-sqlite3": "11.3.0", "drizzle-orm": "0.39.2", "isomorphic-ws": "5.0.0", "mysql2": "3.11.2", "postgres": "3.4.4", "rfc6902": "5.1.1", "superjson": "2.2.1", "ulidx": "2.4.1", "ws": "8.18.0", "zod": "3.23.8"}, "devDependencies": {"@castore/event-storage-adapter-in-memory": "2.3.1", "@electric-sql/pglite": "0.2.6", "@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/better-sqlite3": "7.6.11", "@types/ws": "8.5.12", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}