/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { EventStore } from "@castore/core"
import type { ZodEventType } from "@castore/event-type-zod"
import { ulid } from "ulidx"
import { z } from "zod"

import { split } from "@kreios/utils/split"

import { defineCommand } from "./commands"

/**
 * Represents the structure of an event type string.
 * Format: `${aggregateType}:${eventType}:v${versionNumber}`
 */
type EventTypeString = `${string}:${string}:v${number}`

/**
 * Represents an event type with a defined payloadSchema.
 */
type EventWithPayload<TEventType extends EventTypeString> = ZodEventType<TEventType, z.AnyZodObject> & {
  payloadSchema: z.AnyZodObject
}

/**
 * Generates default CRUD commands for an aggregate based on provided event types.
 */
export const defaultCommands = <
  TEventStore extends EventStore,
  TCreatedEvent extends EventWithPayload<EventTypeString>,
  TUpdatedEvent extends EventWithPayload<EventTypeString>,
  TDeletedEvent extends EventWithPayload<EventTypeString>,
>({
  eventStore,
  events,
}: {
  eventStore: TEventStore
  events: {
    created: TCreatedEvent
    updated: TUpdatedEvent
    deleted: TDeletedEvent
  }
}) => {
  // Extract the aggregate type from the 'created' event type
  const [aggregateType] = split(events.created.type, ":")

  // Define the create command
  const createCommand = defineCommand({
    commandId: `${aggregateType}:create`,
    eventStores: [eventStore],
    inputSchema: events.created.payloadSchema.extend({
      aggregateId: z
        .string()
        .ulid()
        .optional()
        .default(() => ulid()),
    }),
    outputSchema: z.object({
      aggregateId: z.string().ulid(),
    }),
    // @ts-expect-error Generic zod types can't be extended without loosing type safety
    handler: async (
      commandInput: Omit<z.infer<typeof events.created.payloadSchema>, "aggregateId"> & {
        aggregateId: string
      },
      [eventStore],
      _context
    ) => {
      const { aggregateId, ...payload } = commandInput

      await eventStore.pushEvent({
        type: events.created.type,
        aggregateId,
        version: 1,
        timestamp: new Date().toISOString(),
        payload,
      })

      return { aggregateId }
    },
  })

  // Define the update command
  const updateCommand = defineCommand({
    commandId: `${aggregateType}:update`,
    eventStores: [eventStore],
    inputSchema: events.updated.payloadSchema.extend({
      aggregateId: z.string().ulid(),
    }),
    // @ts-expect-error Generic zod types can't be extended without loosing type safety
    handler: async (
      commandInput: Omit<z.infer<typeof events.updated.payloadSchema>, "aggregateId"> & {
        aggregateId: string
      },
      [eventStore],
      _context
    ) => {
      const { aggregateId, ...payload } = commandInput

      const { aggregate } = await eventStore.getAggregate(aggregateId)
      if (!aggregate) {
        throw new Error(`Aggregate not found for ${aggregateType}:update command with aggregateId ${aggregateId}`)
      }

      await eventStore.pushEvent({
        type: events.updated.type,
        aggregateId,
        version: aggregate.version + 1,
        timestamp: new Date().toISOString(),
        payload,
      })
    },
  })

  // Define the delete command
  const deleteCommand = defineCommand({
    commandId: `${aggregateType}:delete`,
    eventStores: [eventStore],
    inputSchema: z.object({
      aggregateId: z.string().ulid(),
    }),
    handler: async (commandInput, [eventStore], _context) => {
      const { aggregateId } = commandInput

      const { aggregate } = await eventStore.getAggregate(aggregateId)
      if (!aggregate) {
        throw new Error(`Aggregate not found for ${aggregateType}:delete command with aggregateId ${aggregateId}`)
      }

      await eventStore.pushEvent({
        type: events.deleted.type,
        aggregateId,
        version: aggregate.version + 1,
        timestamp: new Date().toISOString(),
        payload: {},
      })
    },
  })

  // Return an object containing all three commands
  return {
    create: createCommand,
    update: updateCommand,
    delete: deleteCommand,
  }
}
