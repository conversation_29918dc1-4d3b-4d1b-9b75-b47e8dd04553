/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { AnyZodObject, ZodOptional, ZodRawShape, ZodType } from "zod"
import { EventType } from "@castore/core"
import z, { ZodObject } from "zod"

export class ZodEventType<
  TYPE extends string = string,
  PAYLOAD_SCHEMA extends ZodType = ZodType,
  PAYLOAD = ZodType extends PAYLOAD_SCHEMA
    ? string extends TYPE
      ? unknown
      : never
    : PAYLOAD_SCHEMA extends ZodType
      ? z.infer<PAYLOAD_SCHEMA>
      : never,
  METADATA_SCHEMA extends ZodType | undefined = ZodType | undefined,
  METADATA = ZodType extends METADATA_SCHEMA
    ? string extends TYPE
      ? unknown
      : never
    : METADATA_SCHEMA extends ZodType
      ? z.infer<METADATA_SCHEMA>
      : never,
> extends EventType<TYPE, PAYLOAD, METADATA> {
  payloadSchema: PAYLOAD_SCHEMA
  metadataSchema?: METADATA_SCHEMA

  constructor({
    type,
    payloadSchema,
    metadataSchema,
  }: {
    type: TYPE
    payloadSchema: PAYLOAD_SCHEMA
    metadataSchema?: METADATA_SCHEMA
  }) {
    super({ type })
    this.payloadSchema = payloadSchema
    this.metadataSchema = metadataSchema
  }
}

/**
 * Creates an event type from an event definition.
 * @param eventConfig - The event definition.
 * @returns The (castor) event type.
 */
export const defineEventType = <
  AggregateType extends string,
  EventType extends string,
  SchemaVersion extends number,
  Schema extends ZodObject<ZodRawShape>,
  MetadataSchema extends ZodObject<ZodRawShape> | undefined = undefined,
>(eventConfig: {
  aggregateType: AggregateType
  eventType: EventType
  schemaVersion: SchemaVersion
  schema: Schema
  metadataSchema?: MetadataSchema
}) => {
  const typeString = `${eventConfig.aggregateType}:${eventConfig.eventType}:v${eventConfig.schemaVersion}` as const
  return new ZodEventType({
    type: typeString,
    payloadSchema: eventConfig.schema,
    metadataSchema: eventConfig.metadataSchema,
  })
}

// EVERYTHING BELOW THIS POINT IS DEPRECATED, PLEASE DO NOT USE OR ADD TO IT

/**
 * Creates CRUD event types for a given aggregate type.
 * @param aggregateType The name of the aggregate type.
 * @param createPayloadSchema The schema for the creation payload.
 * @param updatePayloadSchema Optional schema for the update payload. If not provided, it will be inferred.
 * @returns An object containing the created, updated, and deleted event types.
 * @deprecated
 */
export const createCrudEventTypes = <
  const AggregateType extends string,
  CPS extends AnyZodObject,
  UPS extends AnyZodObject | undefined,
>(
  aggregateType: AggregateType,
  createPayloadSchema: CPS,
  updatePayloadSchema?: UPS
) => {
  const createdEvent = new ZodEventType({
    type: `${aggregateType}Created`,
    payloadSchema: createPayloadSchema,
  })

  const inferredUpdatePayloadSchema = (updatePayloadSchema ??
    inferUpdatePayloadSchema(createPayloadSchema)) as undefined extends UPS ? InferUpdatePayloadSchema<CPS> : UPS

  const updatedEvent = new ZodEventType<
    `${typeof aggregateType}Updated`,
    // @ts-expect-error - This is a valid type, but the type inference is not working as expected
    undefined extends UPS ? InferUpdatePayloadSchema<CPS> : UPS,
    // @ts-expect-error - This is a valid type, but the type inference is not working as expected
    undefined extends UPS ? z.infer<InferUpdatePayloadSchema<CPS>> : z.infer<UPS>,
    undefined,
    never
  >({
    type: `${aggregateType}Updated`,
    payloadSchema: inferredUpdatePayloadSchema,
  })

  const deletedEvent = new ZodEventType({
    type: `${aggregateType}Deleted`,
    payloadSchema: z.object({}),
  })

  return {
    createdEvent,
    updatedEvent,
    deletedEvent,
    inferredUpdatePayloadSchema,
  }
}

/**
 * A type that infers the update payload schema by making all properties optional,
 * except for properties named 'id', which remain required.
 * @template T - The Zod schema for the created payload.
 * @deprecated
 */
type InferUpdatePayloadSchema<T extends ZodObject<ZodRawShape>> = ZodObject<{
  [K in keyof T["shape"]]: K extends "id" | "aggregateId"
    ? T["shape"][K] // id should remain required
    : T["shape"][K] extends ZodObject<ZodRawShape>
      ? ZodOptional<InferUpdatePayloadSchema<T["shape"][K]>> // the recursive case
      : ZodOptional<T["shape"][K]> // make all other properties optional
}>

/**
 * Recursively infers an update payload schema by making all properties optional,
 * except for properties named 'id' or 'aggregateId', which remain required.
 * @param schema The original schema to infer the update payload from.
 * @returns The inferred update payload schema.
 * @deprecated
 */
const inferUpdatePayloadSchema = <T extends ZodObject<ZodRawShape>>(schema: T) => {
  const shape = schema.shape
  const updatedShape: ZodRawShape = {}

  for (const key in shape) {
    const fieldSchema = shape[key]
    if (key === "id" || key === "aggregateId") {
      updatedShape[key] = fieldSchema
    } else if (fieldSchema instanceof ZodObject) {
      updatedShape[key] = inferUpdatePayloadSchema(fieldSchema).optional()
    } else {
      updatedShape[key] = fieldSchema.optional()
    }
  }

  return z.object(updatedShape) as InferUpdatePayloadSchema<T>
}
