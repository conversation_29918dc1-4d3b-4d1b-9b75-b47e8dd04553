/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { EventDetail, EventType, EventTypeDetails } from "@castore/core"
import type { ZodObject, ZodRawShape } from "zod"
import { z } from "zod"

import type { Prettify } from "@kreios/utils/types/prettify"

/**
 * The base schema for all our aggregates.
 */
export const aggregateBaseSchema = z.object({
  aggregateId: z.string(),
  version: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deletedAt: z.date().nullable(),
  deleted: z.boolean(),
})

/**
 * The base type for all our aggregates.
 */
export type AggregateBase = z.infer<typeof aggregateBaseSchema>

/**
 * Defines an aggregate schema by combining the provided custom schema with the base aggregate schema.
 * @template Schema - The Zod schema for the custom aggregate fields.
 * @param customSchema - The custom schema for the aggregate.
 * @returns The combined schema of the base aggregate and the custom fields.
 */
export const defineAggregate = <Schema extends ZodObject<ZodRawShape>>(customSchema: Schema) => {
  return aggregateBaseSchema.merge(customSchema)
}

/**
 * Defines a reducer function for an aggregate.
 * @template TAggregate - The type of the aggregate.
 * @template TEventTypes - The types of the events.
 * @param innerFunction - The function to be used for reducing the aggregate.
 * @returns The reducer function.
 */
export const defineReducer = <
  TAggregate extends AggregateBase,
  TEventTypes extends EventType<string, unknown, unknown>[],
>(
  innerFunction: (state: TAggregate | undefined, event: EventTypeDetails<TEventTypes>) => TAggregate
) => {
  return (state: TAggregate | undefined, event: EventTypeDetails<TEventTypes>) => {
    return innerFunction(state, event)
  }
}

// EVERYTHING BELOW THIS POINT IS DEPRECATED, PLEASE DO NOT USE OR ADD TO IT

/**
 * Infers the aggregate type from the created payload schema.
 * @template T - The Zod schema for the payload of the created event.
 * @deprecated
 */
export type InferAggregateFromCreatedEvent<CreatedEventSchema extends z.ZodTypeAny> = Prettify<
  z.infer<CreatedEventSchema> & AggregateBase
>

/**
 * @deprecated
 */
type CrudAggregate<
  AggregateType extends string,
  Aggregate extends AggregateBase,
  EventType extends EventDetail,
> = Aggregate &
  Prettify<
    `${AggregateType}Created` extends EventType["type"]
      ? EventType["payload"]
      : `${AggregateType}Updated` extends EventType["type"]
        ? EventType["payload"]
        : Record<string, never>
  >

/**
 * Handles the default CRUD events for an aggregate.
 * @param aggregateType - The name of the aggregate type.
 * @param aggregate - The current state of the aggregate.
 * @param event - The event to be processed.
 * @returns The updated aggregate state.
 * @deprecated
 */
export const handleCrudEvent = <
  const AggregateType extends string,
  Aggregate extends AggregateBase,
  EventType extends EventDetail,
>(
  aggregateType: AggregateType,
  aggregate: Aggregate | undefined,
  event: EventType
): CrudAggregate<AggregateType, Aggregate, EventType> => {
  const { version, aggregateId } = event

  switch (event.type) {
    case `${aggregateType}Created`: {
      return {
        aggregateId,
        version,
        createdAt: new Date(event.timestamp),
        updatedAt: new Date(event.timestamp),
        deletedAt: null,
        deleted: false,
        ...(event.payload as object),
      } as CrudAggregate<AggregateType, Aggregate, EventType>
    }
    case `${aggregateType}Updated`: {
      if (!aggregate) {
        throw new Error("Cannot update non-existing aggregate")
      }
      return {
        ...aggregate,
        version,
        updatedAt: new Date(event.timestamp),
        ...(event.payload as object),
      } as CrudAggregate<AggregateType, Aggregate, EventType>
    }
    case `${aggregateType}Deleted`: {
      if (!aggregate) {
        throw new Error("Cannot delete non-existing aggregate")
      }
      return {
        ...aggregate,
        version,
        deleted: true,
        deletedAt: new Date(event.timestamp),
      } as CrudAggregate<AggregateType, Aggregate, EventType>
    }
    default:
      throw new Error(`Unhandled CRUD event type: ${event.type}`)
  }
}
