/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { GroupedEvent } from "@castore/core"

/**
 * Ensure all grouped events have the same timestamp or no timestamp, and then returns the timestamp to use.
 *
 * @param groupedEvents - The grouped events to ensure have the same timestamp or no timestamp.
 */
export const ensureUniqueTimestamp = (groupedEvents: GroupedEvent[]) => {
  // Ensure all grouped events have the same timestamp or no timestamp
  const uniqueTimestamp = groupedEvents
    .map((event) => event.event.timestamp) // Get timestamp (might be null/undefined)
    .reduce((acc, timestamp) => {
      if (timestamp && acc !== timestamp) throw new Error("All events must have the same timestamp or no timestamp.")
      return acc
    })

  // If no timestamp is provided, use the current time
  return uniqueTimestamp ?? new Date().toISOString()
}
