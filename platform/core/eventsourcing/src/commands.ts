/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { EventStore } from "@castore/core"
import { ZodCommand } from "@castore/command-zod"
import { tuple } from "@castore/core"
import { z } from "zod"

import { screamingSnakeCase } from "@kreios/utils/screamingSnakeCase"

/**
 * Configuration object for defining a command.
 * @template TInputSchema - The Zod schema type for the command input.
 * @template TOutputSchema - The Zod schema type for the command output.
 * @template TEventStores - The array type of EventStore instances.
 */
type CommandConfig<
  TCommandId extends string,
  TInputSchema extends z.ZodType,
  TOutputSchema extends z.ZodType,
  TEventStores extends EventStore[],
> = {
  commandId: TCommandId
  eventStores: [...TEventStores]
  inputSchema: TInputSchema
  outputSchema?: TOutputSchema
  handler: (
    commandInput: z.infer<TInputSchema>,
    eventStores: TEventStores,
    context?: Record<string, never>
  ) => Promise<z.infer<TOutputSchema>>
}

/**
 * Defines a new command with input validation, output validation, and event store integration.
 *
 * @template TCommandId - The ID of the command.
 * @template TInputSchema - The Zod schema type for the command input.
 * @template TOutputSchema - The Zod schema type for the command output.
 * @template TEventStores - The array type of EventStore instances.
 * @param {CommandConfig<TInputSchema, TOutputSchema, TEventStores>} config - The configuration object for the command.
 * @returns {ZodCommand<TInputSchema, TOutputSchema>} A ZodCommand instance representing the defined command.
 */
export const defineCommand = <
  TCommandId extends string,
  TInputSchema extends z.ZodType,
  TOutputSchema extends z.ZodType = z.ZodVoid,
  TEventStores extends EventStore[] = EventStore[],
>(
  config: CommandConfig<TCommandId, TInputSchema, TOutputSchema, TEventStores>
) => {
  return new ZodCommand({
    commandId: config.commandId,
    requiredEventStores: config.eventStores,
    inputSchema: config.inputSchema,
    outputSchema: config.outputSchema ?? z.void(),
    handler: config.handler,
  })
}

// EVERYTHING BELOW THIS POINT IS DEPRECATED, PLEASE DO NOT USE OR ADD TO IT

/**
 * Creates CRUD commands for a given aggregate type.
 * @param aggregateType The name of the aggregate type.
 * @param eventStore The event store associated with the aggregate.
 * @param createPayloadSchema The schema for the creation payload.
 * @param updatePayloadSchema The schema for the update payload.
 * @returns An object containing the create, update, and delete commands.
 * @deprecated
 */
export const createCrudCommands = <
  const AggregateType extends string,
  CPS extends z.AnyZodObject,
  UPS extends z.AnyZodObject,
  EVENT_STORE extends EventStore,
>(
  aggregateType: AggregateType,
  eventStore: EVENT_STORE,
  createPayloadSchema: CPS,
  updatePayloadSchema: UPS
) => {
  // We'll use a snake-cased version of the aggregate type as the common suffix for all command IDs
  const commandIdBase = screamingSnakeCase(aggregateType)

  // Create command
  const createCommand = new ZodCommand({
    commandId: `CREATE_${commandIdBase}`,
    requiredEventStores: tuple(eventStore),
    inputSchema: createPayloadSchema.extend({
      aggregateId: z.string().ulid().optional(),
    }),
    outputSchema: z.object({
      aggregateId: z.string().ulid(),
    }),
    handler: async (
      commandInput: Omit<z.infer<typeof createPayloadSchema>, "aggregateId"> & {
        aggregateId?: string
      },
      [eventStore],
      { generateUuid }: { generateUuid: () => string }
    ) => {
      const aggregateId = commandInput.aggregateId ?? generateUuid()

      await eventStore.pushEvent({
        type: `${aggregateType}Created`,
        aggregateId,
        version: 1,
        timestamp: new Date().toISOString(),
        payload: commandInput,
      })

      return { aggregateId }
    },
  })

  // Update command
  const updateCommand = new ZodCommand({
    commandId: `UPDATE_${commandIdBase}`,
    requiredEventStores: tuple(eventStore),
    inputSchema: updatePayloadSchema.extend({
      aggregateId: z.string().ulid(),
    }),
    handler: async (
      commandInput: Omit<z.infer<typeof updatePayloadSchema>, "aggregateId"> & {
        aggregateId: string
      },
      [eventStore]
    ) => {
      const { aggregateId, ...payload } = commandInput

      // Get the last known state of the aggregate
      const { aggregate } = await eventStore.getAggregate(aggregateId)
      if (!aggregate) {
        throw new Error(`${aggregateType} not found`)
      }

      await eventStore.pushEvent({
        type: `${aggregateType}Updated`,
        aggregateId,
        version: aggregate.version + 1,
        timestamp: new Date().toISOString(),
        payload,
      })
    },
  })

  // Delete command
  const deleteCommand = new ZodCommand({
    commandId: `DELETE_${commandIdBase}`,
    requiredEventStores: tuple(eventStore),
    inputSchema: z.object({
      aggregateId: z.string().ulid(),
    }),
    handler: async (commandInput, [eventStore]) => {
      const { aggregateId } = commandInput

      // Get the last known state of the aggregate
      const { aggregate } = await eventStore.getAggregate(aggregateId)
      if (!aggregate) {
        throw new Error(`${aggregateType} not found`)
      }

      await eventStore.pushEvent({
        type: `${aggregateType}Deleted`,
        aggregateId,
        version: aggregate.version + 1,
        timestamp: new Date().toISOString(),
        payload: {},
      })
    },
  })

  return {
    createCommand,
    updateCommand,
    deleteCommand,
  }
}
