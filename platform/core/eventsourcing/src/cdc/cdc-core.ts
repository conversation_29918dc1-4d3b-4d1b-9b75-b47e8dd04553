/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { EventStore, EventTypeDetail } from "@castore/core"
import { z } from "zod"

import type { JsonPatch } from "./cdc-utils"
import { defineCommand } from "../commands"
import { defineEventType } from "../events"
import { calculateDelta, jsonPatchSchema } from "./cdc-utils"

/**
 * Creates a CDC event type for a given aggregate type and data schema
 */
export const defineCDCEventType = <
  TSchemaVersion extends number,
  TAggregate extends string,
  TSource extends string,
  TSourceType extends string,
  TSchema extends z.ZodType,
>(eventConfig: {
  aggregateType: TAggregate
  schemaVersion: TSchemaVersion
  source: TSource
  sourceType: TSourceType
  schema: TSchema
}) => {
  const eventType = defineEventType({
    aggregateType: eventConfig.aggregateType,
    eventType: `changeCaptured/${eventConfig.source}/${eventConfig.sourceType}` as const,
    schemaVersion: 1,
    schema: z.object({
      source: z.literal(eventConfig.source).describe('Identifier of the source system (e.g. "crm", "tacton")'),
      sourceType: z
        .literal(eventConfig.sourceType)
        .describe('Type of the record in source system (e.g. "customer", "order")'),
      sourceId: z.string().describe("Unique identifier of the record in the source system"),
      sourceData: eventConfig.schema.describe("Complete record data from the source system"),
      sourceChanges: jsonPatchSchema.describe("JSON Patch describing changes from the previous state"),
    }),
  })

  return {
    ...eventType,
    source: eventConfig.source,
    sourceType: eventConfig.sourceType,
  }
}

/**
 * Creates a CDC command for processing changes from an external system
 */
export const defineCDCCommand = <
  TCommandId extends string,
  TEventStore extends EventStore,
  TEventType extends CDCEventType,
  TSourceData = SourceData<TEventType>,
>(commandConfig: {
  commandId: TCommandId
  eventStore: TEventStore
  eventType: TEventType
  options?: {
    getCanonicalForm?: (state: TSourceData) => unknown
    shouldPushChanges?: (newSourceData: TSourceData, oldSourceData: TSourceData | null, changes: JsonPatch) => boolean
  }
}) => {
  return defineCommand({
    commandId: commandConfig.commandId,
    eventStores: [commandConfig.eventStore],
    inputSchema: z.object({
      aggregateId: z.string(),
      sourceId: z.string(),
      sourceData: commandConfig.eventType.payloadSchema.shape.sourceData as SourceDataSchema<TEventType>,
      forceFresh: z.boolean().optional().default(false),
    }),
    outputSchema: z.object({
      changesDetected: z.boolean(),
    }),
    handler: async (commandInput, [eventStore], _context) => {
      const { aggregateId, sourceId, sourceData, forceFresh } = commandInput

      // Step 1: Get both latest CDC event and latest overall event
      const { latestCDCEvent, latestVersion } = await getLatestCDCEvent(
        commandConfig.eventType,
        eventStore,
        aggregateId
      )

      // Step 2: Calculate the delta between the latest CDC event and the source data
      const oldSourceData = forceFresh ? null : (latestCDCEvent?.payload.sourceData as TSourceData)
      const newSourceData = sourceData as TSourceData
      const changes = calculateDelta(oldSourceData, newSourceData, {
        getCanonicalForm: commandConfig.options?.getCanonicalForm,
      })

      // Step 3: If there are changes, append a corresponding CDC event
      if (changes.length > 0) {
        const shouldPushChanges = commandConfig.options?.shouldPushChanges ?? (() => true)
        if (shouldPushChanges(newSourceData, oldSourceData, changes)) {
          await eventStore.pushEvent({
            type: commandConfig.eventType.type,
            aggregateId,
            version: latestVersion ? latestVersion + 1 : 1,
            timestamp: new Date().toISOString(),
            payload: {
              source: getSource(commandConfig.eventType),
              sourceType: getSourceType(commandConfig.eventType),
              sourceId,
              sourceData,
              sourceChanges: changes,
            },
          })

          // Changes were detected and pushed
          return { changesDetected: true }
        }
      }

      // No changes detected and pushed
      return { changesDetected: false }
    },
  })
}

// ===== Types =====

/**
 * Type alias for CDC event types
 */
export type CDCEventType = ReturnType<typeof defineCDCEventType<number, string, string, string, z.ZodType>>

/**
 * Type alias for CDC command types
 */
export type CDCCommand<TSourceDataSchema extends z.ZodType, TEventStore extends EventStore = EventStore> = {
  handler: (
    input: { aggregateId: string; sourceId: string; sourceData: z.infer<TSourceDataSchema> },
    eventStores: [TEventStore],
    context: Record<string, never>
  ) => Promise<{ changesDetected: boolean }>
}

/**
 * Helper type to extract the source data schema from a CDC event type
 */
export type SourceDataSchema<T extends CDCEventType> = T["payloadSchema"]["shape"]["sourceData"]

/**
 * Helper type to extract the source data type from a CDC event type
 */
export type SourceData<T extends CDCEventType> = z.infer<SourceDataSchema<T>>

// ===== Helper Functions =====

/**
 * Gets the source from a CDC event type
 */
const getSource = <T extends CDCEventType>(eventType: T): string => eventType.source

/**
 * Gets the source type from a CDC event type
 */
const getSourceType = <T extends CDCEventType>(eventType: T): string => eventType.sourceType

/**
 * Gets both the latest CDC event of a specific type, as well as the latest version overall.
 */
const getLatestCDCEvent = async <TEventType extends CDCEventType, TEventStore extends EventStore>(
  eventType: TEventType,
  eventStore: TEventStore,
  aggregateId: string
): Promise<{
  latestCDCEvent: EventTypeDetail<TEventType> | null
  latestVersion: number | null
}> => {
  const { events } = await eventStore.getEvents(aggregateId, { reverse: true })
  if (events.length === 0) return { latestCDCEvent: null, latestVersion: null }

  const source = getSource(eventType)
  const sourceType = getSourceType(eventType)

  // Find the latest CDC event with matching source and sourceType
  const latestCDCEvent =
    events.find((event): event is EventTypeDetail<TEventType> => {
      const payload = event.payload as { source: string; sourceType: string }
      return event.type === eventType.type && payload.source === source && payload.sourceType === sourceType
    }) ?? null

  // The latest event overall is just the first one since we fetched in reverse
  const latestEvent = events[0]

  return { latestCDCEvent, latestVersion: latestEvent.version }
}
