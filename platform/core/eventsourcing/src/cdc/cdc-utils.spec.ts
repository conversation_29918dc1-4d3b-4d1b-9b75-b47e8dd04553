/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { describe, expect, it } from "vitest"

import { applyDelta, calculateDelta } from "./cdc-utils"

describe("Delta Utilities", () => {
  describe("calculateDelta and applyDelta", () => {
    it("should calculate and apply delta for simple objects", () => {
      const oldState = { name: "<PERSON>", age: 30 }
      const newState = { name: "<PERSON>", age: 31 }

      const delta = calculateDelta(oldState, newState)
      expect(delta).toEqual([{ op: "replace", path: "/age", value: 31 }])

      const updatedState = applyDelta(oldState, delta)
      expect(updatedState).toEqual(newState)
    })

    it("should handle nested objects", () => {
      const oldState = { user: { name: "Alice", details: { city: "New York" } } }
      const newState = { user: { name: "Alice", details: { city: "London" } } }

      const delta = calculateDelta(oldState, newState)
      expect(delta).toEqual([{ op: "replace", path: "/user/details/city", value: "London" }])

      const updatedState = applyDelta(oldState, delta)
      expect(updatedState).toEqual(newState)
    })

    it("should handle array modifications", () => {
      const oldState = { items: ["apple", "banana", "cherry"] }
      const newState = { items: ["apple", "grape", "cherry", "date"] }

      const delta = calculateDelta(oldState, newState)
      expect(delta).toEqual([
        { op: "replace", path: "/items/1", value: "grape" },
        { op: "add", path: "/items/-", value: "date" },
      ])

      const updatedState = applyDelta(oldState, delta)
      expect(updatedState).toEqual(newState)
    })
  })
})
