/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { Operation } from "rfc6902"
import { applyPatch, createPatch } from "rfc6902"
import { z } from "zod"

// Define a schema for JSON Patch (operations)
export const jsonPatchOperationSchema = z.object({
  op: z.enum(["add", "remove", "replace", "move", "copy", "test"]),
  from: z.string().optional(),
  path: z.string(),
  value: z.string().optional(),
})
export const jsonPatchSchema = z.array(jsonPatchOperationSchema)

// The corresponding types
export type JsonPatchOperation = z.infer<typeof jsonPatchOperationSchema>
export type JsonPatch = z.infer<typeof jsonPatchSchema>

/**
 * Calculates the delta between two states.
 * @param oldState - The old state (can be null for new items).
 * @param newState - The new state.
 * @param options - Optional options object
 * @param options.getCanonicalForm - Optional function to transform states before comparison
 * @returns The delta as a JSON Patch.
 */
export function calculateDelta<T>(
  oldState: T | null,
  newState: T,
  options?: { getCanonicalForm?: (state: T) => unknown }
): JsonPatch {
  const getCanonicalForm = options?.getCanonicalForm ?? ((state: T) => state)
  const oldCanonical = oldState ? getCanonicalForm(oldState) : null
  const newCanonical = getCanonicalForm(newState)
  return createPatch(oldCanonical, newCanonical)
}

/**
 * Applies a JSON Patch to a state.
 * @param state - The state to apply the patch to.
 * @param changes - The JSON Patch to apply.
 * @returns The new state.
 */
export function applyDelta<T>(state: T, changes: JsonPatch): T {
  const newState = JSON.parse(JSON.stringify(state)) as T
  applyPatch(newState, changes as Operation[])
  return newState
}
