/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import type { PoolConfig } from "@neondatabase/serverless"
import type { VercelPostgresPoolConfig } from "@vercel/postgres"
import type { PostgresJsDatabase } from "drizzle-orm/postgres-js"
import type postgres from "postgres"
import { is } from "drizzle-orm"
import { MySqlDatabase } from "drizzle-orm/mysql-core"
import { PgDatabase } from "drizzle-orm/pg-core"
import { BaseSQLiteDatabase } from "drizzle-orm/sqlite-core"
import ws from "isomorphic-ws"

import { eventsTableMySQL } from "./tables/mysql"
import { eventsTablePostgres } from "./tables/postgres"
import { eventsTableSQLite } from "./tables/sqlite"

/**
 * Union type for the database configuration.
 */
type DatabaseConfig =
  | {
      type: "neon"
      url: string
      options?: Omit<PoolConfig, "connectionString">
    }
  | {
      type: "vercel"
      url: string
      options?: Omit<VercelPostgresPoolConfig, "connectionString">
    }
  | {
      type: "pglite"
    }
  | {
      type: "postgres"
      url: string
      options?: postgres.Options<Record<string, never>>
    }

interface DatabaseClient {
  close: () => Promise<void>
}

/**
 * Union type for the database, itself.
 */
export type Database<TSchema extends Record<string, any> = Record<string, never>> =
  | PgDatabase<any, TSchema>
  | BaseSQLiteDatabase<any, any, TSchema>
  | MySqlDatabase<any, any, TSchema>
  | PostgresJsDatabase<TSchema>

/**
 * Connects to a database based on the provided configuration.
 * @param config The database configuration object.
 * @returns The connected database instance.
 */
export const connect = async (config: DatabaseConfig): Promise<{ db: Database; client: DatabaseClient }> => {
  switch (config.type) {
    case "neon": {
      const [{ Pool, neonConfig }, { drizzle }] = await Promise.all([
        import("@neondatabase/serverless"),
        import("drizzle-orm/neon-serverless"),
      ])
      // The package we use does not seem to have a way to set these things on a per-connection basis, so a global hack it is :-(.
      neonConfig.webSocketConstructor = ws
      neonConfig.wsProxy = (host) => `${host}:5433/v1`
      neonConfig.useSecureWebSocket = false
      neonConfig.pipelineTLS = false
      neonConfig.pipelineConnect = false

      console.info(`Connecting to Neon database at ${config.url}`)
      const pool = new Pool({ connectionString: config.url, ...config.options })

      return {
        db: drizzle(pool, {}),
        client: {
          close: () => pool.end(),
        },
      }
    }
    case "vercel": {
      const [{ createPool }, { drizzle }] = await Promise.all([
        import("@vercel/postgres"),
        import("drizzle-orm/vercel-postgres"),
      ])
      console.info(`Connecting to Vercel PostgreSQL database at ${config.url}`)

      const pool = createPool({
        connectionString: config.url,
        ...config.options,
      })

      return {
        db: drizzle(pool, {}),
        client: {
          close: () => pool.end(),
        },
      }
    }

    case "pglite": {
      if (process.env.NODE_ENV != "production") {
        const [{ PGlite }, { drizzle }] = await Promise.all([
          import("@electric-sql/pglite"),
          import("drizzle-orm/pglite"),
        ])
        console.info(`Connecting to in-memory PGLite database`)
        const client = new PGlite()

        return { db: drizzle(client), client }
      }
      throw new Error("PGLite is only available in development mode.")
    }
    case "postgres": {
      const [{ default: postgres }, { drizzle }] = await Promise.all([
        import("postgres"),
        import("drizzle-orm/postgres-js"),
      ])
      const client = postgres(config.url, {
        ...config.options,
      })
      return {
        db: drizzle(client),
        client: {
          close: () => client.end(),
        },
      }
    }
  }
}

export const isPostgresDatabase = <TSchema extends Record<string, any>>(
  db: Database<TSchema>
): db is PgDatabase<any, TSchema> => is(db, PgDatabase)

export const isSQLiteDatabase = <TSchema extends Record<string, any>>(
  db: Database<TSchema>
): db is BaseSQLiteDatabase<any, any, TSchema> => is(db, BaseSQLiteDatabase)

export const isMySQLDatabase = <TSchema extends Record<string, any>>(
  db: Database<TSchema>
): db is MySqlDatabase<any, any, TSchema> => is(db, MySqlDatabase)

export const getDatabaseDialect = <TSchema extends Record<string, any>>(
  db: Database<TSchema>
): "sqlite" | "postgres" | "mysql" => {
  if (isPostgresDatabase(db)) return "postgres"
  else if (isSQLiteDatabase(db)) return "sqlite"
  else if (isMySQLDatabase(db)) return "mysql"

  throw new Error("Unknown database dialect")
}

/**
 * Returns the Drizzle schema for a table that can be used to store the events associated with a specific aggregate.
 *
 * @param tableName The name of the table.
 */
export const eventsTable = <const TableName extends string>(
  tableName: TableName,
  dialect: "sqlite" | "postgres" | "mysql" = process.env.DATABASE_URL?.startsWith("postgres")
    ? "postgres"
    : process.env.DATABASE_URL?.startsWith("mysql")
      ? "mysql"
      : "sqlite"
) => {
  switch (dialect) {
    case "postgres":
      return eventsTablePostgres(tableName)
    case "mysql":
      return eventsTableMySQL(tableName)
    case "sqlite":
      return eventsTableSQLite(tableName)
  }
}
