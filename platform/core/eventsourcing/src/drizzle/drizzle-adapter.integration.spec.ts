/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { EventStore } from "@castore/core"
import { ulid } from "ulidx"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { z } from "zod"

import type { DataLoader } from "@kreios/utils/dataloader"

import { defineAggregate, defineReducer } from "../aggregate"
import { defineCommand } from "../commands"
import { defineEventType } from "../events"
import { DrizzleEventAlreadyExistsError, DrizzleEventStorageAdapter } from "./drizzle-adapter"
import { mockSqliteDatabase } from "./mocks/mock-sqlite"

describe("EvenStore using Drizzle Adapter", () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const thingAggregate = defineAggregate(
    z.object({
      foo: z.string(),
    })
  )

  type ThingAggregate = z.infer<typeof thingAggregate>

  const thingSchema = z.object({
    foo: z.string(),
  })

  const thingCreatedV1 = defineEventType({
    aggregateType: "thing",
    eventType: "created",
    schemaVersion: 1,
    schema: thingSchema,
  })

  const thingUpdatedV1 = defineEventType({
    aggregateType: "thing",
    eventType: "updated",
    schemaVersion: 1,
    schema: thingSchema.partial(),
  })

  const thingDeletedV1 = defineEventType({
    aggregateType: "thing",
    eventType: "deleted",
    schemaVersion: 1,
    schema: z.object({}),
  })

  const thingEvents = [thingCreatedV1, thingUpdatedV1, thingDeletedV1]

  const thingReducer = defineReducer<ThingAggregate, typeof thingEvents>((state, event): ThingAggregate => {
    const { aggregateId, version, timestamp } = event

    switch (event.type) {
      case "thing:created:v1": {
        return {
          ...event.payload,
          aggregateId: aggregateId,
          version,
          createdAt: new Date(timestamp),
          updatedAt: new Date(timestamp),
          deleted: false,
          deletedAt: null,
        }
      }
      case "thing:updated:v1": {
        return {
          ...state!,
          ...event.payload,
          version,
          updatedAt: new Date(timestamp),
        }
      }
      case "thing:deleted:v1": {
        return {
          ...state!,
          version,
          deleted: true,
          deletedAt: new Date(timestamp),
        }
      }
    }
  })

  type ThingEventStore = EventStore<"things", typeof thingEvents>

  let eventStore: ThingEventStore

  beforeEach(async () => {
    // Each test should start with an empty event store database
    const drizzle = await mockSqliteDatabase("things")

    eventStore = new EventStore({
      eventStoreId: "things",
      eventStorageAdapter: new DrizzleEventStorageAdapter(drizzle, "things"),
      eventTypes: thingEvents,
      reducer: thingReducer,
    })
  })

  it("retries the operation if an aggregate is concurrently updated", async () => {
    const { createHandler, updateHandler, deleteHandler, createThingCommand, updateThingCommand, deleteThingCommand } =
      createCommands()

    const aggregateId = ulid()

    await createThingCommand.handler({ aggregateId, foo: "bar" }, [eventStore])
    expect(createHandler).toHaveBeenCalledTimes(1)

    const { aggregate } = await eventStore.getExistingAggregate(aggregateId)

    await Promise.all([
      updateThingCommand.handler({ ...aggregate, foo: "bar" }, [eventStore]),
      updateThingCommand.handler({ ...aggregate, foo: "bizz" }, [eventStore]),
      deleteThingCommand.handler({ aggregateId: aggregate.aggregateId }, [eventStore]),
    ])

    // Check if the update handler has thrown the DrizzleEventAlreadyExistsError 1 time
    expect(updateHandler.mock.settledResults.filter((result) => result.type === "rejected")).toHaveLength(1)
    expect(updateHandler.mock.settledResults.find((result) => result.type === "rejected")?.value).toBeInstanceOf(
      DrizzleEventAlreadyExistsError
    )

    // Check if the update handler has correctly retried the operation 1 time i.e. 2 calls + 1 retry
    // to have been called with the correct arguments
    expect(updateHandler).toHaveBeenCalledTimes(3)
    expect(updateHandler).toHaveBeenCalledWith({ ...aggregate, foo: "bar" }, [eventStore])
    expect(updateHandler).toHaveBeenCalledWith({ ...aggregate, foo: "bizz" }, [eventStore])
    expect(updateHandler).toHaveBeenCalledWith({ ...aggregate, foo: "bizz" }, [eventStore])

    // check if the delete hanlder has been called 3 times ie. 3 tries with the correct arguments
    expect(deleteHandler).toHaveBeenCalledTimes(3)
    expect(deleteHandler).toHaveBeenCalledWith({ aggregateId: aggregate.aggregateId }, [eventStore])

    // check if the delete handler has thrown the DrizzleEventAlreadyExistsError 2 times
    expect(deleteHandler.mock.settledResults.filter((result) => result.type === "rejected")).toHaveLength(2)
    for (const result of deleteHandler.mock.settledResults.filter((result) => result.type === "rejected")) {
      expect(result.value).toBeInstanceOf(DrizzleEventAlreadyExistsError)
    }
  })

  it("batches events of different aggregates into a single batch", async () => {
    const { createHandler, createThingCommand } = createCommands()

    const batchPushEvent = vi.spyOn(
      (eventStore.eventStorageAdapter as unknown as { pushEventLoader: DataLoader<any, any> }).pushEventLoader,
      // @ts-expect-error hidden property on dataloader see https://github.com/graphql/dataloader/blob/77c2cd7ca97e8795242018ebc212ce2487e729d2/src/index.js#L54
      "_batchLoadFn"
    )

    await Promise.all(
      Array.from({ length: 100 }).map((_, i) =>
        createThingCommand.handler({ aggregateId: ulid(), foo: `bar-${i}` }, [eventStore])
      )
    )

    expect(createHandler).toHaveBeenCalledTimes(100)

    expect(batchPushEvent).toHaveBeenCalledTimes(1)
  })

  it("batches events of different aggregates into one batch & keeps changes to the same aggregate in different batches", async () => {
    const { createThingCommand, updateHandler, updateThingCommand } = createCommands()

    const res = await Promise.all(
      Array.from({ length: 100 }).map((_, i) =>
        createThingCommand.handler({ aggregateId: ulid(), foo: `bar-${i}` }, [eventStore])
      )
    )

    const batchPushEvent = vi.spyOn(
      (eventStore.eventStorageAdapter as unknown as { pushEventLoader: DataLoader<any, any> }).pushEventLoader,
      // @ts-expect-error hidden property on dataloader see https://github.com/graphql/dataloader/blob/77c2cd7ca97e8795242018ebc212ce2487e729d2/src/index.js#L54
      "_batchLoadFn"
    )

    await Promise.all(
      res.flatMap((aggregate) => [
        updateThingCommand.handler({ ...aggregate, foo: "bizz" }, [eventStore]),
        updateThingCommand.handler({ ...aggregate, foo: "bazz" }, [eventStore]),
      ])
    )

    expect(updateHandler).toHaveBeenCalledTimes(300)

    expect(batchPushEvent).toHaveBeenCalledTimes(2)
  })

  /**
   * Helper function to create commands without needing to type them globally
   */
  const createCommands = () => {
    const createThingHandler = async (
      commandInput: z.infer<typeof thingCreatedV1.payloadSchema> & { aggregateId?: string },
      [eventStore]: [ThingEventStore]
    ) => {
      const aggregateId = commandInput.aggregateId ?? ulid()

      await eventStore.pushEvent({
        type: thingCreatedV1.type,
        aggregateId,
        version: 1,
        timestamp: new Date().toISOString(),
        payload: commandInput,
      })

      return { aggregateId }
    }

    const updateThingHandler = async (
      commandInput: z.infer<typeof thingUpdatedV1.payloadSchema> & { aggregateId: string },
      [eventStore]: [EventStore<"things", typeof thingEvents>]
    ) => {
      const { aggregateId, ...payload } = commandInput

      const { aggregate } = await eventStore.getAggregate(aggregateId)
      if (!aggregate) {
        throw new Error(`Thing not found`)
      }

      await eventStore.pushEvent({
        type: thingUpdatedV1.type,
        aggregateId,
        version: aggregate.version + 1,
        timestamp: new Date().toISOString(),
        payload,
      })
    }

    const deleteThingHandler = async (commandInput: { aggregateId: string }, [eventStore]: [ThingEventStore]) => {
      const { aggregateId } = commandInput

      const { aggregate } = await eventStore.getAggregate(aggregateId)
      if (!aggregate) {
        throw new Error(`Thing not found`)
      }

      await eventStore.pushEvent({
        type: thingDeletedV1.type,
        aggregateId,
        version: aggregate.version + 1,
        timestamp: new Date().toISOString(),
        payload: {},
      })
    }

    const createHandler = vi.fn(createThingHandler)
    const updateHandler = vi.fn(updateThingHandler)
    const deleteHandler = vi.fn(deleteThingHandler)

    const createThingCommand = defineCommand({
      commandId: "createThing",
      eventStores: [eventStore],
      inputSchema: thingCreatedV1.payloadSchema.extend({
        aggregateId: z.string().ulid().optional(),
      }),
      outputSchema: z.object({
        aggregateId: z.string().ulid(),
      }),
      handler: createHandler,
    })

    const updateThingCommand = defineCommand({
      commandId: "updateThing",
      eventStores: [eventStore],
      inputSchema: thingUpdatedV1.payloadSchema.extend({
        aggregateId: z.string().ulid(),
      }),
      handler: updateHandler,
    })

    const deleteThingCommand = defineCommand({
      commandId: "deleteThing",
      eventStores: [eventStore],
      inputSchema: z.object({
        aggregateId: z.string().ulid(),
      }),
      handler: deleteHandler,
    })

    return {
      createHandler,
      updateHandler,
      deleteHandler,
      createThingCommand,
      updateThingCommand,
      deleteThingCommand,
    }
  }
})
