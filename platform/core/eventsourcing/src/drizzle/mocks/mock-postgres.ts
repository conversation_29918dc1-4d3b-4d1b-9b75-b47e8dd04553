/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { PGlite } from "@electric-sql/pglite"
import { drizzle as pgliteDrizzle } from "drizzle-orm/pglite/driver"

export const createEventsTablePostgres = (table_name: string) => {
  return `CREATE TABLE IF NOT EXISTS "${table_name}"
          (
            "aggregateid" varchar(36)              NOT NULL,
            "version"     integer                  NOT NULL,
            "type"        varchar(50)              NOT NULL,
            "timestamp"   timestamp with time zone NOT NULL,
            "payload"     jsonb                    NOT NULL,
            "metadata"    jsonb                    NOT NULL,
            CONSTRAINT "${table_name}_aggregateid_version_pk" PRIMARY KEY ("aggregateid", "version")
          )`
}

export const mockPostgresLiteDatabase = async (table: string) => {
  const pglite = new PGlite()
  await pglite.exec(createEventsTablePostgres(table))
  const db = pgliteDrizzle(pglite)
  return db
}
