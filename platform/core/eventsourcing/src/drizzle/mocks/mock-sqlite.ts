/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import Database from "better-sqlite3"
import { drizzle as sqliteDrizzle } from "drizzle-orm/better-sqlite3"

export const createEventsTableSQLite = (tableName: string) => {
  return `CREATE TABLE IF NOT EXISTS "${tableName}" (
    "aggregateid" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "timestamp" TEXT NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    "payload" BLOB NOT NULL,
    "metadata" BLOB NOT NULL,
    PRIMARY KEY ("aggregateid", "version")
  )`
}

export const mockSqliteDatabase = async (table: string) => {
  const db = new Database()
  db.prepare(createEventsTableSQLite(table)).run()
  return sqliteDrizzle(db)
}
