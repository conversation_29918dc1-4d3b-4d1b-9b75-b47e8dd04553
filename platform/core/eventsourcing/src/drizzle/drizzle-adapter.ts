/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  Aggregate,
  EventAlreadyExistsError,
  EventDetail,
  EventsQueryOptions,
  EventStorageAdapter,
  EventStoreContext,
  ListAggregateIdsOptions,
  ListAggregateIdsOutput,
  OptionalTimestamp,
  PushEventOptions,
} from "@castore/core"
import type { InferSelectModel, SQL, Table } from "drizzle-orm"
import type { MySqlDatabase } from "drizzle-orm/mysql-core"
import type { PgDatabase } from "drizzle-orm/pg-core"
import type { BaseSQLiteDatabase } from "drizzle-orm/sqlite-core"
import { eventAlreadyExistsErrorCode, GroupedEvent } from "@castore/core"
import { desc, eq, getTableColumns, gte, inArray, is, lte, sql } from "drizzle-orm"
import { MySqlTable, MySqlTransaction } from "drizzle-orm/mysql-core"
import { PgTransaction } from "drizzle-orm/pg-core"
import { SQLiteTransaction } from "drizzle-orm/sqlite-core"

import { capitalize } from "@kreios/utils/capitilize"
import { DataLoader } from "@kreios/utils/dataloader"

import type { Database } from "./database"
import { ensureUniqueTimestamp } from "../adapter-utils"
import { isMySQLDatabase, isPostgresDatabase, isSQLiteDatabase } from "./database"
import { eventsTableMySQL } from "./tables/mysql"
import { eventsTablePostgres } from "./tables/postgres"
import { eventsTableSQLite } from "./tables/sqlite"

// A type representing a row in the events DB table
type EventRow = InferSelectModel<
  ReturnType<typeof eventsTableMySQL | typeof eventsTableSQLite | typeof eventsTablePostgres>
>

/**
 * Converts an event to row data for database insertion.
 *
 * @param eventDetail - The event details.
 * @returns The row data.
 */
const eventToRowData = (eventDetail: EventDetail): EventRow => {
  return {
    aggregateId: eventDetail.aggregateId,
    version: eventDetail.version,
    type: eventDetail.type,
    timestamp: eventDetail.timestamp,
    payload: eventDetail.payload ?? {},
    metadata: eventDetail.metadata ?? {},
  }
}

/**
 * Converts row data from the database to an event.
 *
 * @param rowData - The row data.
 * @returns The event details.
 */
const rowDataToEvent = (rowData: EventRow): EventDetail => {
  return {
    aggregateId: rowData.aggregateId,
    version: rowData.version,
    type: rowData.type,
    timestamp: new Date(rowData.timestamp).toISOString(),
    payload: rowData.payload,
    metadata: rowData.metadata,
  }
}

/**
 * DrizzleEventStorageAdapter
 *
 * Adapter for storing and retrieving events using Drizzle ORM and Supabase.
 */
export class DrizzleEventStorageAdapter implements EventStorageAdapter {
  private readonly database: Database
  private readonly tableName: string
  private readonly eventLoader: DataLoader<string, EventDetail[]>
  private readonly pushEventLoader: DataLoader<OptionalTimestamp<EventDetail>, { event: EventDetail }>

  /**
   * Constructor for DrizzleEventStorageAdapter
   *
   * @param database - The Drizzle PgDatabase client.
   * @param tableName - The name of the table to store events in.
   */
  constructor(database: Database, tableName: string) {
    this.tableName = tableName
    this.database = database
    const batchLoadEvents = this.batchLoadEvents.bind(this)
    this.eventLoader = new DataLoader(batchLoadEvents, {
      cache: false,
      name: `getEvents${capitalize(tableName)}`,
      // Since the database protocol have different limits for the max number of parameters in a query
      // we need to set the maxBatchSize accordingly
      // for the select query we only have one parameter per element in the array
      maxBatchSize: isSQLiteDatabase(this.database) ? 32760 : 65533,
    })

    const batchPushEvent = this.batchPushEvent.bind(this)
    this.pushEventLoader = new DataLoader(batchPushEvent, {
      cache: false,
      name: `pushEvents${capitalize(tableName)}`,
      // Since the database protocol have different limits for the max number of parameters in a query
      // we need to set the maxBatchSize accordingly
      // for the insert query we have 6 parameters per element in the array
      maxBatchSize: isSQLiteDatabase(this.database) ? 5461 : 10922,
      // Add a small delay to allow more events to accumulate in a batch
      batchScheduleFn: (callback) => setTimeout(callback, 10),
    })
  }

  /**
   * Batch loads events for multiple aggregate IDs.
   *
   * @param aggregateIds - The list of aggregate IDs.
   * @returns A promise that resolves to a list of events for each aggregate ID.
   */
  private async batchLoadEvents(aggregateIds: readonly string[]): Promise<EventDetail[][]> {
    let result: {
      aggregateId: string
      version: number
      type: string
      timestamp: string
      payload: unknown
      metadata: unknown
    }[] = []

    if (isPostgresDatabase(this.database)) {
      const table = eventsTablePostgres(this.tableName)
      result = await this.database
        .select()
        .from(table)
        .where(inArray(table.aggregateId, aggregateIds as unknown as string[]))
        .orderBy(table.aggregateId, table.version)
        .execute()
    } else if (isSQLiteDatabase(this.database)) {
      const table = eventsTableSQLite(this.tableName)
      result = await this.database
        .select()
        .from(table)
        .where(inArray(table.aggregateId, aggregateIds as unknown as string[]))
        .orderBy(table.aggregateId, table.version)
        .execute()
    } else if (isMySQLDatabase(this.database)) {
      const table = eventsTableMySQL(this.tableName)
      const res = await this.database
        .select()
        .from(table)
        .where(inArray(table.aggregateId, aggregateIds as unknown as string[]))
        .orderBy(table.aggregateId, table.version)

      result = res.map((row) => ({
        ...row,
        timestamp: row.timestamp + "+0000",
      }))
    }

    const eventsMap = new Map<string, EventDetail[]>(aggregateIds.map((id) => [id, []]))

    result.forEach((row) => {
      const event = rowDataToEvent(row)
      eventsMap.get(event.aggregateId)!.push(event)
    })

    return aggregateIds.map((id) => eventsMap.get(id)!)
  }

  /**
   * Retrieves events for a given aggregate ID.
   *
   * @param aggregateId - The ID of the aggregate.
   * @param context - The event store context.
   * @param options - Options for querying events.
   * @returns An array of event details.
   */
  async getEvents(
    aggregateId: string,
    context: EventStoreContext,
    options?: EventsQueryOptions
  ): Promise<{ events: EventDetail[] }> {
    // Load events using Dataloader
    const events = await this.eventLoader.load(aggregateId)

    const minVersion = options?.minVersion
    const maxVersion = options?.maxVersion

    // Apply version filters if provided
    let filteredEvents = events
    if (minVersion !== undefined) {
      filteredEvents = filteredEvents.filter((event) => event.version >= minVersion)
    }
    if (maxVersion !== undefined) {
      filteredEvents = filteredEvents.filter((event) => event.version <= maxVersion)
    }

    // Apply limit if provided
    if (options?.limit !== undefined) {
      filteredEvents = filteredEvents.slice(0, options.limit)
    }

    // Apply ordering based on the reverse flag
    if (options?.reverse) {
      filteredEvents = filteredEvents.slice().reverse()
    }

    return { events: filteredEvents }
  }

  /**
   * Checks if an error is a unique constraint violation for all database types
   *
   * @param error - The error to check.
   * @returns True if the error is a unique constraint violation, false otherwise.
   */
  private isUniqueConstraintViolation(error: unknown): boolean {
    // Check if this is a unique constraint violation, which is something we need to pay special attention to
    // (it amazes me that there in 2024 there are still ORMs that require you to check for DB-specific error codes)
    return (
      !!error &&
      typeof error === "object" &&
      (("errno" in error && isMySQLDatabase(this.database) && error.errno === 1062) ||
        ("code" in error &&
          ((isPostgresDatabase(this.database) && error.code === "23505") ||
            (isSQLiteDatabase(this.database) && error.code === "SQLITE_CONSTRAINT_PRIMARYKEY"))))
    )
  }

  /**
   * Function to push mutliple events to the database in a single query
   * To be used inside a Dataloader
   */
  private async batchPushEvent(events: readonly OptionalTimestamp<EventDetail>[]): Promise<
    | {
        event: EventDetail
      }[]
    | DrizzleEventAlreadyExistsError[]
  > {
    // Get the current timestamp for events that don't have a timestamp
    const timestamp = new Date().toISOString()

    // Create a new array with the timestamp to be used as return value
    const eventsWithTimestamp: EventDetail[] = events.map((event) => ({ timestamp, ...event }))

    // Convert the events to row data
    const rowData = eventsWithTimestamp.map((event) => eventToRowData(event))

    // save the database reference to allow type narrowing
    const db = this.database

    try {
      if (isPostgresDatabase(db)) {
        const table = eventsTablePostgres(this.tableName)
        await db.insert(table).values(rowData)
      } else if (isMySQLDatabase(db)) {
        const table = eventsTableMySQL(this.tableName)
        await db
          .insert(table)
          .values(rowData.map((row) => ({ ...row, timestamp: row.timestamp.slice(0, -1).replace("T", " ") })))
      } else if (isSQLiteDatabase(db)) {
        const table = eventsTableSQLite(this.tableName)
        await db.insert(table).values(rowData)
      }
    } catch (error) {
      if (this.isUniqueConstraintViolation(error)) {
        // Return an array of DrizzleEventAlreadyExistsError for each event
        // This will be resolved by the dataloader to throw the error for each call
        // We need to do that since we can't determine which event caused the unique constraint violation
        return eventsWithTimestamp.map(
          (event) =>
            new DrizzleEventAlreadyExistsError(
              { aggregateId: event.aggregateId, version: event.version },
              { cause: error }
            )
        )
      }

      // In case of any other (unexpected) error, rethrow it
      throw error
    }

    return eventsWithTimestamp.map((event) => ({ event }))
  }

  /**
   * Pushes a new event to the event store.
   *
   * @param eventDetail - The event details.
   * @param options - Options for pushing the event.
   * @returns The pushed event detail.
   */
  async pushEvent(
    eventDetail: OptionalTimestamp<EventDetail>,
    options: PushEventOptions
  ): Promise<{ event: EventDetail }> {
    // if option.force is true, we don't try to insert the event inside a batch hence we need to fine grain override the onConflict logic for that element
    // therefore we handle it the same way groupEvent are handled
    if (options.force) {
      // Group the event
      const groupedEvent = new GroupedEvent({
        event: eventDetail,
        context: { eventStoreId: options.eventStoreId },
        eventStorageAdapter: this,
      })

      // Push the grouped event
      const details = await this.pushEventGroup(options, groupedEvent)
      return details.eventGroup[0]
    }

    // Type of the internal batch object of the dataloader
    type Batch<K, V> = {
      hasDispatched: boolean
      keys: Array<K>
      callbacks: Array<{
        resolve: (value: V) => void
        reject: (error: Error) => void
      }>
      cacheHits?: Array<() => void>
    }

    // type the pushEventLoader to access the private properties without type errors
    const pushEventLoader = this.pushEventLoader as unknown as DataLoader<
      OptionalTimestamp<EventDetail>,
      { event: EventDetail }
    > & {
      _batch?: Batch<OptionalTimestamp<EventDetail>, { event: EventDetail }>
      _batchScheduleFn: (fn: () => void) => void
    }

    // Validation if an event with the same aggregateId and version is already being pushed
    if (
      // If there is a batch in progress
      pushEventLoader._batch &&
      // And it hasn't dispatched yet
      !pushEventLoader._batch.hasDispatched &&
      // And the batch contains an event with the same aggregateId and version
      // in theory this could get slow since we are iterating over all the events in the batch
      pushEventLoader._batch.keys.some(
        (key) => key.aggregateId === eventDetail.aggregateId && key.version === eventDetail.version
      )
    ) {
      // Wait for the batch to finish by awaiting the next process.tick
      await new Promise<void>((resolve) => pushEventLoader._batchScheduleFn(resolve))

      // Then we throw the error so it can be handled using retry logic which may access the now written event
      throw new DrizzleEventAlreadyExistsError({ aggregateId: eventDetail.aggregateId, version: eventDetail.version })
    }

    return this.pushEventLoader.load(eventDetail)
  }

  /**
   * Pushes a group of events to the event store.
   *
   * @param options - Options for pushing events.
   * @param groupedEvents - The grouped events to push.
   * @returns The pushed events.
   */
  async pushEventGroup(
    options: { force?: boolean },
    ...groupedEvents: [GroupedEvent, ...GroupedEvent[]]
  ): Promise<{ eventGroup: { event: EventDetail }[] }> {
    // Ensure all grouped events use the same instance of DrizzleEventStorageAdapter
    if (groupedEvents.some((event) => !(event.eventStorageAdapter instanceof DrizzleEventStorageAdapter))) {
      throw new Error("All events of an event group must use the same event storage adapter.")
    }

    // Always use the transaction approach for event groups to avoid DataLoader batching issues
    // Insert all events in a single transaction
    const timestamp = ensureUniqueTimestamp(groupedEvents)
    await this.database.transaction(async (tx) => {
      for (const groupedEvent of groupedEvents) {
        const { event, eventStorageAdapter } = groupedEvent
        const rowData = eventToRowData({ ...event, timestamp })

        // With this adapter, every event store (i.e. aggregate) has its own table
        const tableName = (eventStorageAdapter as DrizzleEventStorageAdapter).tableName

        // Try inserting the row
        try {
          if (is(tx, PgTransaction)) {
            const table = eventsTablePostgres(tableName)
            if (options.force) {
              await tx
                .insert(table)
                .values(rowData)
                .onConflictDoUpdate({
                  target: [table.aggregateId, table.version],
                  set: buildConflictUpdateColumns(table, ["type", "timestamp", "payload", "metadata"]),
                })
            } else {
              await tx.insert(table).values(rowData)
            }
          } else if (is(tx, MySqlTransaction)) {
            const table = eventsTableMySQL(tableName)
            const mysqlRowData = {
              ...rowData,
              timestamp: rowData.timestamp.slice(0, -1).replace("T", " "),
            }
            if (options.force) {
              await tx
                .insert(table)
                .values(mysqlRowData)
                .onDuplicateKeyUpdate({
                  set: buildConflictUpdateColumns(table, ["type", "timestamp", "payload", "metadata"]),
                })
            } else {
              await tx.insert(table).values(mysqlRowData)
            }
          } else if (is(tx, SQLiteTransaction)) {
            const table = eventsTableSQLite(tableName)
            if (options.force) {
              await tx
                .insert(table)
                .values(rowData)
                .onConflictDoUpdate({
                  target: [table.aggregateId, table.version],
                  set: buildConflictUpdateColumns(table, ["type", "timestamp", "payload", "metadata"]),
                })
            } else {
              await tx.insert(table).values(rowData)
            }
          }
        } catch (error) {
          if (this.isUniqueConstraintViolation(error)) {
            const { aggregateId, version } = rowData
            throw new DrizzleEventAlreadyExistsError({ aggregateId, version }, { cause: error })
          }

          // In case of any other (unexpected) error, rethrow it
          throw error
        }
      }
    })

    // Return the pushed events
    return {
      eventGroup: groupedEvents.map((groupedEvent) => ({
        event: { ...groupedEvent.event, timestamp },
      })),
    }
  }

  /**
   * Creates a grouped event.
   *
   * @param eventDetail - The event detail.
   * @returns A grouped event.
   */
  groupEvent(eventDetail: OptionalTimestamp<EventDetail>): GroupedEvent {
    return new GroupedEvent({ event: eventDetail, eventStorageAdapter: this })
  }

  private async listAggregateIdsPostgres<TSchema extends Record<string, any>>(
    database: PgDatabase<any, TSchema>,
    context: EventStoreContext,
    options?: ListAggregateIdsOptions
  ) {
    // Build the base query
    const eventTable = eventsTablePostgres(this.tableName)
    let query = database
      .select({ aggregateId: eventTable.aggregateId, timestamp: eventTable.timestamp })
      .from(eventTable)
      .where(eq(eventTable.version, 1))
      .$dynamic()

    // Apply timestamp filters if provided
    if (options?.initialEventAfter) {
      query = query.where(gte(eventTable.timestamp, options.initialEventAfter))
    }

    if (options?.initialEventBefore) {
      query = query.where(lte(eventTable.timestamp, options.initialEventBefore))
    }

    // Apply limit if provided
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    // Apply ordering based on the reverse flag
    if (!options?.reverse) {
      query = query.orderBy(eventTable.timestamp)
    } else {
      query = query.orderBy(desc(eventTable.timestamp))
    }

    // Execute the query and map the results to aggregate IDs
    return query.execute()
  }

  private async listAggregateIdsSQLite<TSchema extends Record<string, any>>(
    database: BaseSQLiteDatabase<any, any, TSchema>,
    context: EventStoreContext,
    options?: ListAggregateIdsOptions
  ) {
    // Build the base query
    const eventTable = eventsTableSQLite(this.tableName)
    let query = database
      .select({ aggregateId: eventTable.aggregateId, timestamp: eventTable.timestamp })
      .from(eventTable)
      .where(eq(eventTable.version, 1))
      .$dynamic()

    // Apply timestamp filters if provided
    if (options?.initialEventAfter) {
      query = query.where(gte(eventTable.timestamp, options.initialEventAfter))
    }

    if (options?.initialEventBefore) {
      query = query.where(lte(eventTable.timestamp, options.initialEventBefore))
    }

    // Apply limit if provided
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    // Apply ordering based on the reverse flag
    if (!options?.reverse) {
      query = query.orderBy(eventTable.timestamp)
    } else {
      query = query.orderBy(desc(eventTable.timestamp))
    }

    return query.execute()
  }

  private async listAggregateIdsMySQL<TSchema extends Record<string, any>>(
    database: MySqlDatabase<any, any, TSchema>,
    context: EventStoreContext,
    options?: ListAggregateIdsOptions
  ) {
    // Build the base query
    const eventTable = eventsTableMySQL(this.tableName)
    let query = database
      .select({ aggregateId: eventTable.aggregateId, timestamp: eventTable.timestamp })
      .from(eventTable)
      .where(eq(eventTable.version, 1))
      .$dynamic()

    // Apply timestamp filters if provided
    if (options?.initialEventAfter) {
      query = query.where(gte(eventTable.timestamp, options.initialEventAfter))
    }

    if (options?.initialEventBefore) {
      query = query.where(lte(eventTable.timestamp, options.initialEventBefore))
    }

    // Apply limit if provided
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    // Apply ordering based on the reverse flag
    if (!options?.reverse) {
      query = query.orderBy(eventTable.timestamp)
    } else {
      query = query.orderBy(desc(eventTable.timestamp))
    }

    return (await query).map((row) => ({
      aggregateId: row.aggregateId,
      timestamp: row.timestamp + "+0000",
    }))
  }

  /**
   * Lists aggregate IDs with their initial event timestamps.
   *
   * @param context - The event store context.
   * @param options - Options for listing aggregate IDs.
   * @returns A list of aggregate IDs and the next page token.
   */
  async listAggregateIds(
    context: EventStoreContext,
    options?: ListAggregateIdsOptions
  ): Promise<ListAggregateIdsOutput> {
    let result: { aggregateId: string; timestamp: string }[] = []
    if (isPostgresDatabase(this.database)) {
      result = await this.listAggregateIdsPostgres(this.database, context, options)
    } else if (isSQLiteDatabase(this.database)) {
      result = await this.listAggregateIdsSQLite(this.database, context, options)
    } else if (isMySQLDatabase(this.database)) {
      result = await this.listAggregateIdsMySQL(this.database, context, options)
    }
    const aggregateIds = result.map(({ aggregateId, timestamp }) => ({
      aggregateId,
      initialEventTimestamp: new Date(timestamp).toISOString(),
    }))

    return { aggregateIds, nextPageToken: undefined } // TODO: Pagination logic to be added
  }

  // Snapshot methods (not used for now)

  async putSnapshot(_aggregate: Aggregate): Promise<void> {
    throw new Error("Method not implemented.")
  }

  async getLastSnapshot(_aggregateId: string, _options?: { maxVersion?: number }): Promise<{ snapshot?: Aggregate }> {
    throw new Error("Method not implemented.")
  }

  async listSnapshots(
    _aggregateId: string,
    _options?: { minVersion?: number; maxVersion?: number; limit?: number; reverse?: boolean }
  ): Promise<{ snapshots: Aggregate[] }> {
    throw new Error("Method not implemented.")
  }
}

/**
 * Custom error class for handling unique constraint violations
 */
export class DrizzleEventAlreadyExistsError extends Error implements EventAlreadyExistsError {
  code: typeof eventAlreadyExistsErrorCode
  aggregateId: string
  version: number

  constructor({ aggregateId, version }: { aggregateId: string; version: number }, options?: ErrorOptions) {
    super(`Event already exists for aggregate ${aggregateId} and version ${version}`, options)
    this.code = eventAlreadyExistsErrorCode
    this.aggregateId = aggregateId
    this.version = version
  }
}

/**
 * Builds the columns to update in case of conflict.
 * Supports Postgres and SQLite & MySQL.
 * @param table - The database table.
 * @param columns - The columns to update.
 * @returns An object with the columns to update.
 */
const buildConflictUpdateColumns = <T extends Table, Q extends keyof T["_"]["columns"]>(table: T, columns: Q[]) => {
  if (is(table, MySqlTable)) {
    const cls = getTableColumns(table)
    return columns.reduce(
      (acc, column) => {
        acc[column] = sql`values(${cls[column]})`
        return acc
      },
      {} as Record<Q, SQL>
    )
  } else {
    const cls = getTableColumns(table)

    return columns.reduce(
      (acc, column) => {
        const colName = cls[column].name
        acc[column] = sql.raw(`excluded.${colName}`)

        return acc
      },
      {} as Record<Q, SQL>
    )
  }
}
