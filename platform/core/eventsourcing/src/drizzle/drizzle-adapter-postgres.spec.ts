/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { EventDetail, GroupedEvent } from "@castore/core"
import { ulid } from "ulidx"
import { beforeEach, describe, expect, it } from "vitest"

import { DrizzleEventStorageAdapter } from "./drizzle-adapter"
import { mockPostgresLiteDatabase } from "./mocks/mock-postgres"

describe("DrizzleEventStorageAdapter (Postgres)", () => {
  let eventStorageAdapter: DrizzleEventStorageAdapter

  let eventStoreId: string
  let eventStoreId2: string

  let dummyEvent1: EventDetail
  let dummyEvent2: EventDetail

  beforeEach(async () => {
    // Fake data to use for the tests
    eventStoreId = ulid()
    eventStoreId2 = ulid()
    dummyEvent1 = createFakeEvent()
    dummyEvent2 = createFakeEvent()

    // Each test should start with an empty event store database
    const db = await mockPostgresLiteDatabase("things")
    eventStorageAdapter = new DrizzleEventStorageAdapter(db, "things")
  })

  it("pushes and retrieves events correctly", async () => {
    await eventStorageAdapter.pushEvent(dummyEvent1, { eventStoreId })

    const { events } = await eventStorageAdapter.getEvents(dummyEvent1.aggregateId, { eventStoreId })

    expect(events).toHaveLength(1)
    expect(events[0]).toEqual(dummyEvent1)
  })

  it("throws error when pushing an event with an existing version", async () => {
    // First push the event using force option to bypass DataLoader
    await eventStorageAdapter.pushEvent(dummyEvent1, { eventStoreId, force: true })

    // Now try to push the same event again, which should fail
    await expect(eventStorageAdapter.pushEvent(dummyEvent1, { eventStoreId })).rejects.toThrowError(
      `Event already exists for aggregate ${dummyEvent1.aggregateId} and version 1`
    )
  })

  it("lists aggregate ids correctly", async () => {
    await eventStorageAdapter.pushEvent(dummyEvent1, { eventStoreId })

    const { aggregateIds } = await eventStorageAdapter.listAggregateIds({ eventStoreId })

    expect(aggregateIds).toContainEqual({
      aggregateId: dummyEvent1.aggregateId,
      initialEventTimestamp: dummyEvent1.timestamp,
    })
  })

  it("pushes event group with consistent timestamps correctly", async () => {
    const event1 = { ...dummyEvent1 }
    const event2 = { ...dummyEvent2, timestamp: event1.timestamp }
    const groupedEvents = groupEvents(eventStorageAdapter, event1, event2)

    const { eventGroup } = await eventStorageAdapter.pushEventGroup({}, ...groupedEvents)

    expect(eventGroup).toHaveLength(2)
    expect(eventGroup[0].event).toEqual(event1)
    expect(eventGroup[1].event).toEqual(event2)
  })

  it("throws error when pushing event group with inconsistent timestamps", async () => {
    const event1 = { ...dummyEvent1 }
    const event2 = { ...dummyEvent2, timestamp: new Date(Date.now() + 1000).toISOString() } // Different timestamp
    const groupedEvents = groupEvents(eventStorageAdapter, event1, event2)

    await expect(eventStorageAdapter.pushEventGroup({}, ...groupedEvents)).rejects.toThrowError(
      "All events must have the same timestamp or no timestamp."
    )
  })

  it("returns events in the same order as persisted", async () => {
    const event1 = { ...dummyEvent1, version: 1 }
    const event2 = { ...dummyEvent1, version: 2, timestamp: new Date(Date.now() + 1000).toISOString() }
    await eventStorageAdapter.pushEvent(event1, { eventStoreId })
    await eventStorageAdapter.pushEvent(event2, { eventStoreId })

    const { events } = await eventStorageAdapter.getEvents(dummyEvent1.aggregateId, { eventStoreId })

    expect(events).toHaveLength(2)
    expect(events[0]).toEqual(event1)
    expect(events[1]).toEqual(event2)
  })

  it("distinguishes events for different event stores", async () => {
    const event1 = { ...dummyEvent1 }
    const event2 = { ...dummyEvent2 }
    await eventStorageAdapter.pushEvent(event1, { eventStoreId })
    await eventStorageAdapter.pushEvent(event2, { eventStoreId: eventStoreId2 })

    const { events: eventsStore1 } = await eventStorageAdapter.getEvents(dummyEvent1.aggregateId, { eventStoreId })
    const { events: eventsStore2 } = await eventStorageAdapter.getEvents(dummyEvent2.aggregateId, {
      eventStoreId: eventStoreId2,
    })

    expect(eventsStore1).toHaveLength(1)
    expect(eventsStore1[0]).toEqual(event1)
    expect(eventsStore2).toHaveLength(1)
    expect(eventsStore2[0]).toEqual(event2)
  })

  it("handles force:true option for pushEvent", async () => {
    const event1 = { ...dummyEvent1 }
    const event2 = { ...dummyEvent1, payload: { foo: "baz" }, metadata: { baz: "quux" } }
    await eventStorageAdapter.pushEvent(event1, { eventStoreId })
    await eventStorageAdapter.pushEvent(event2, { eventStoreId, force: true })

    const { events } = await eventStorageAdapter.getEvents(dummyEvent1.aggregateId, { eventStoreId })

    expect(events).toHaveLength(1)
    expect(events[0]).toEqual(event2)
  })

  it("handles listAggregateIds options correctly", async () => {
    // Push events individually to avoid timestamp issues
    await eventStorageAdapter.pushEvent(dummyEvent1, { eventStoreId })
    await eventStorageAdapter.pushEvent(dummyEvent2, { eventStoreId })

    // Test with limit option
    const { aggregateIds } = await eventStorageAdapter.listAggregateIds({ eventStoreId }, { limit: 1 })
    expect(aggregateIds).toHaveLength(1)
  })

  // Helper methods

  function groupEvents(
    eventStorageAdapter: DrizzleEventStorageAdapter,
    ...events: [EventDetail, ...EventDetail[]]
  ): [GroupedEvent, ...GroupedEvent[]] {
    return events.map((event) => {
      const groupedEvent = eventStorageAdapter.groupEvent(event)
      groupedEvent.context = { eventStoreId }
      return groupedEvent
    }) as [GroupedEvent, ...GroupedEvent[]]
  }

  const createFakeEvent = (): EventDetail => ({
    aggregateId: ulid(),
    version: 1,
    type: "EVENT_TYPE",
    timestamp: new Date().toISOString(),
    payload: { foo: "bar" },
    metadata: { baz: "qux" },
  })
})
