/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { sql } from "drizzle-orm"
import { blob, integer, primaryKey, sqliteTable, text } from "drizzle-orm/sqlite-core"

/**
 * Returns the Drizzle schema for a table that can be used to store the events associated with a specific aggregate.
 *
 * @param tableName The name of the table.
 */
export const eventsTableSQLite = <const TableName extends string>(tableName: TableName) =>
  sqliteTable(
    tableName,
    {
      aggregateId: text("aggregateid", { length: 36 }).notNull(),
      version: integer("version").notNull(),
      type: text("type", { length: 255 }).notNull(),
      timestamp: text("timestamp")
        .default(sql`(CURRENT_TIMESTAMP)`)
        .notNull(),
      payload: blob("payload", { mode: "json" }).notNull(),
      metadata: blob("metadata", { mode: "json" }).notNull(),
    },
    (table) => {
      return {
        pk: primaryKey({ columns: [table.aggregateId, table.version] }),
      }
    }
  )
