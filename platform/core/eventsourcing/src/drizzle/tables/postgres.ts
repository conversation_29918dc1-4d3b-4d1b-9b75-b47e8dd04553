/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type {
  ColumnBaseConfig,
  ColumnBuilderBaseConfig,
  ColumnBuilderRuntimeConfig,
  MakeColumnConfig,
} from "drizzle-orm"
import type { AnyPgTable } from "drizzle-orm/pg-core"
import { entityKind } from "drizzle-orm"
import { integer, PgColumn, PgColumnBuilder, pgTable, primaryKey, timestamp, varchar } from "drizzle-orm/pg-core"
import superjson from "superjson"

export type PgJsonbBuilderInitial<TName extends string> = PgJsonbBuilder<{
  name: TName
  dataType: "json"
  columnType: "PgJsonb"
  data: unknown
  driverParam: unknown
  enumValues: undefined
  generated: undefined
}>

export class PgJsonbBuilder<T extends ColumnBuilderBaseConfig<"json", "PgJsonb">> extends PgColumnBuilder<T> {
  static readonly [entityKind]: string = "PgJsonbBuilder"

  constructor(name: T["name"]) {
    super(name, "json", "PgJsonb")
  }

  build<TTableName extends string>(table: AnyPgTable<{ name: TTableName }>): PgJsonb<MakeColumnConfig<T, TTableName>> {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return new PgJsonb<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>)
  }
}

export class PgJsonb<T extends ColumnBaseConfig<"json", "PgJsonb">> extends PgColumn<T> {
  static readonly [entityKind]: string = "PgJsonb"

  constructor(table: AnyPgTable<{ name: T["tableName"] }>, config: PgJsonbBuilder<T>["config"]) {
    super(table, config)
  }

  getSQLType(): string {
    return "jsonb"
  }

  override mapToDriverValue(value: T["data"]): string {
    return superjson.stringify(value)
  }

  override mapFromDriverValue(value: T["data"] | string): T["data"] {
    if (typeof value === "string") {
      try {
        const payload = JSON.parse(value)
        if ("json" in payload) return superjson.deserialize(payload)

        return payload
      } catch {
        return value as T["data"]
      }
    }
    // @ts-expect-error superjson is not typed
    if ("json" in value) return superjson.deserialize(value)
    return value
  }
}

function jsonb<TName extends string>(name: TName): PgJsonbBuilderInitial<TName> {
  return new PgJsonbBuilder(name)
}

/**
 * Returns the Drizzle schema for a table that can be used to store the events associated with a specific aggregate.
 *
 * @param tableName The name of the table.
 */
export const eventsTablePostgres = <const TableName extends string>(tableName: TableName) =>
  pgTable(
    tableName,
    {
      aggregateId: varchar("aggregateid", { length: 36 }).notNull(),
      version: integer("version").notNull(),
      type: varchar("type", { length: 255 }).notNull(),
      timestamp: timestamp("timestamp", { withTimezone: true, mode: "string" }).notNull(),
      payload: jsonb("payload").notNull(),
      metadata: jsonb("metadata").notNull(),
    },
    (table) => {
      return {
        pk: primaryKey({ columns: [table.aggregateId, table.version] }),
      }
    }
  )
