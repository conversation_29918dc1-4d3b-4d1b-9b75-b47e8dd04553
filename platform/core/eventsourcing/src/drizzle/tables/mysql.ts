/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { int, json, mysqlTable, primaryKey, timestamp, varchar } from "drizzle-orm/mysql-core"

/**
 * Returns the Drizzle schema for a table that can be used to store the events associated with a specific aggregate.
 *
 * @param tableName The name of the table.
 */
export const eventsTableMySQL = <const TableName extends string>(tableName: TableName) =>
  mysqlTable(
    tableName,
    {
      aggregateId: varchar("aggregateid", { length: 36 }).notNull(),
      version: int("version").notNull(),
      type: varchar("type", { length: 255 }).notNull(),
      timestamp: timestamp("timestamp", { mode: "string", fsp: 3 }).defaultNow().notNull(),
      payload: json("payload").notNull(),
      metadata: json("metadata").notNull(),
    },
    (table) => {
      return {
        pk: primaryKey({ columns: [table.aggregateId, table.version] }),
      }
    }
  )
