/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { defineReducer } from "@kreios/eventsourcing"

import type { BookAggregate } from "./aggregate"
import type { bookEvents } from "./events"

export const bookReducer = defineReducer<BookAggregate, typeof bookEvents>((state, event): BookAggregate => {
  const { aggregateId, version, timestamp } = event

  switch (event.type) {
    case "books:created:v1": {
      return {
        aggregateId,
        version,
        createdAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
        deletedAt: null,
        deleted: false,
        ...event.payload,
      }
    }
    case "books:updated:v1": {
      return {
        ...state!,
        ...event.payload,
        version,
        updatedAt: new Date(timestamp),
      }
    }
    case "books:deleted:v1": {
      return {
        ...state!,
        version,
        deleted: true,
        deletedAt: new Date(timestamp),
        updatedAt: new Date(timestamp),
      }
    }
    case "books:changeCaptured/amazon/books:v1": {
      return {
        ...state!,
        // If a book pops up on Amazon, we know that it has been published!
        status: state!.status === "draft" ? "published" : state!.status,
        price: event.payload.sourceData.price,
        currency: event.payload.sourceData.currency,
        version,
        updatedAt: new Date(timestamp),
      }
    }
  }
})
