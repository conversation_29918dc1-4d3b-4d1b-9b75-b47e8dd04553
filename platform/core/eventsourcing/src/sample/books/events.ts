/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { z } from "zod"

import { defineEventType } from "@kreios/eventsourcing"
import { defineCDCEventType } from "@kreios/eventsourcing/cdc"

const authorSchema = z.object({
  id: z.string().ulid(),
  name: z.string().min(1),
  gender: z.enum(["Male", "Female", "Other", "Unknown"]),
})

const bookSchema = z.object({
  title: z.string().min(1),
  status: z.enum(["draft", "published", "archived"]),
  isbn: z.string().length(13),
  publicationDate: z.coerce.date(),
  pageCount: z.number().int().positive(),
  isHardcover: z.boolean(),
  authors: z.array(authorSchema).min(1),
  genres: z.array(z.enum(["Fiction", "Non-fiction", "Science", "History", "Biography"])),
  rating: z.number().min(0).max(5).optional(),
})

export const bookCreatedV1 = defineEventType({
  aggregateType: "books",
  eventType: "created",
  schemaVersion: 1,
  schema: bookSchema,
})

export const bookUpdatedV1 = defineEventType({
  aggregateType: "books",
  eventType: "updated",
  schemaVersion: 1,
  schema: bookSchema.partial(),
})

export const bookDeletedV1 = defineEventType({
  aggregateType: "books",
  eventType: "deleted",
  schemaVersion: 1,
  schema: z.object({}),
})

// Event type for a hypothetical CDC on Amazon.com
export const bookChangedOnAmazonV1 = defineCDCEventType({
  aggregateType: "books",
  schemaVersion: 1,
  source: "amazon",
  sourceType: "books",
  schema: z.object({
    asin: z.string(),
    title: z.string(),
    authorNames: z.array(z.string()),
    publicationDate: z.string(),
    categories: z.array(z.string()),
    price: z.number(),
    currency: z.string(),
    description: z.string().optional(),
    pageCount: z.number().optional(),
  }),
})

export const bookEvents = [bookCreatedV1, bookUpdatedV1, bookDeletedV1, bookChangedOnAmazonV1]
