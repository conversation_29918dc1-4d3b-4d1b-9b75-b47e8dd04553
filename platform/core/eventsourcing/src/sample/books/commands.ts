/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { ulid } from "ulidx"
import { z } from "zod"

import { defineCommand } from "@kreios/eventsourcing"
import { defineCDCCommand } from "@kreios/eventsourcing/cdc"

import { bookChangedOnAmazonV1, bookCreatedV1, bookDeletedV1, bookUpdatedV1 } from "./events"
import { bookEventStore } from "./store"

export const createBookCommand = defineCommand({
  commandId: "createBook",
  eventStores: [bookEventStore],
  inputSchema: bookCreatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid().optional(),
  }),
  outputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const aggregateId = commandInput.aggregateId ?? ulid()

    await eventStore.pushEvent({
      type: bookCreatedV1.type,
      aggregateId,
      version: 1,
      timestamp: new Date().toISOString(),
      payload: commandInput,
    })

    return { aggregateId }
  },
})

export const updateBookCommand = defineCommand({
  commandId: "updateBook",
  eventStores: [bookEventStore],
  inputSchema: bookUpdatedV1.payloadSchema.extend({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId, ...payload } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Book not found`)
    }

    await eventStore.pushEvent({
      type: bookUpdatedV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload,
    })
  },
})

export const deleteBookCommand = defineCommand({
  commandId: "deleteBook",
  eventStores: [bookEventStore],
  inputSchema: z.object({
    aggregateId: z.string().ulid(),
  }),
  handler: async (commandInput, [eventStore], _context) => {
    const { aggregateId } = commandInput

    const { aggregate } = await eventStore.getAggregate(aggregateId)
    if (!aggregate) {
      throw new Error(`Book not found`)
    }

    await eventStore.pushEvent({
      type: bookDeletedV1.type,
      aggregateId,
      version: aggregate.version + 1,
      timestamp: new Date().toISOString(),
      payload: {},
    })
  },
})

export const captureBookChangesOnAmazonCommand = defineCDCCommand({
  commandId: "captureBookChangesOnAmazon",
  eventStore: bookEventStore,
  eventType: bookChangedOnAmazonV1,
})
