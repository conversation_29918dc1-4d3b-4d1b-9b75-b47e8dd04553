/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { z } from "zod"

import { defineAggregate } from "@kreios/eventsourcing"

const authorSchema = z.object({
  id: z.string().ulid(),
  name: z.string().min(1),
  gender: z.enum(["Male", "Female", "Other", "Unknown"]),
})

export const bookAggregateSchema = defineAggregate(
  z.object({
    title: z.string().min(1),
    status: z.enum(["draft", "published", "archived"]),
    isbn: z.string().length(13),
    publicationDate: z.coerce.date(),
    pageCount: z.number().int().positive(),
    isHardcover: z.boolean(),
    price: z.number().positive().multipleOf(0.01).optional(),
    currency: z.string().optional(),
    authors: z.array(authorSchema).min(1),
    genres: z.array(z.enum(["Fiction", "Non-fiction", "Science", "History", "Biography"])),
    rating: z.number().min(0).max(5).optional(),
  })
)

export type BookAggregate = z.infer<typeof bookAggregateSchema>
