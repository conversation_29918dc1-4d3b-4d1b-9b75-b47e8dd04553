/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { z } from "zod"
import { ulid } from "ulidx"
import { describe, expect, it } from "vitest"

import type { bookCreatedV1 } from "./books"
import {
  bookEventStore,
  captureBookChangesOnAmazonCommand,
  createBookCommand,
  deleteBookCommand,
  updateBookCommand,
} from "./books"

describe("Book Aggregate", () => {
  const bookCreatedEvent: z.infer<typeof bookCreatedV1.payloadSchema> = {
    title: "The Great Gatsby",
    status: "draft",
    isbn: "9780743273565",
    publicationDate: new Date("1925-04-10"),
    pageCount: 180,
    isHardcover: true,
    authors: [{ id: ulid(), name: "<PERSON><PERSON>", gender: "Male" }],
    genres: ["Fiction"],
    rating: 4.5,
  }

  describe("Event Handling", () => {
    it("should create a new aggregate when a book creation event is processed", async () => {
      // When
      const { aggregateId } = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.title).toBe(bookCreatedEvent.title)
      expect(aggregate?.authors).toHaveLength(1)
      expect(aggregate?.authors[0].name).toBe(bookCreatedEvent.authors[0].name)
    })

    it("should update an existing aggregate when a book update event is processed", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})

      // When
      const updatePayload = {
        aggregateId,
        price: 11.99,
        isHardcover: !bookCreatedEvent.isHardcover,
        lastUpdated: new Date(),
      }
      await updateBookCommand.handler(updatePayload, [bookEventStore], {})

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.price).toBe(updatePayload.price)
      expect(aggregate?.isHardcover).toBe(updatePayload.isHardcover)
    })

    it("should mark an aggregate as deleted when a book deletion event is processed", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})

      // When
      await deleteBookCommand.handler({ aggregateId }, [bookEventStore], {})

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.deleted).toBe(true)
      expect(aggregate?.deletedAt).toBeDefined()
    })

    it("should store and retrieve multiple events for a single book", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})

      // When
      await updateBookCommand.handler(
        {
          aggregateId,
          rating: 5,
        },
        [bookEventStore],
        {}
      )

      // Then
      const { events } = await bookEventStore.getEvents(aggregateId)
      expect(events).toHaveLength(2)
      expect(events[0].type).toBe("books:created:v1")
      expect(events[1].type).toBe("books:updated:v1")
    })

    it("should handle multiple authors in the aggregate state", async () => {
      // Given
      const multiAuthorBook = {
        ...bookCreatedEvent,
        authors: [
          { id: ulid(), name: "Terry Pratchett", gender: "Male" as const },
          { id: ulid(), name: "Neil Gaiman", gender: "Male" as const },
        ],
      }

      // When
      const { aggregateId } = await createBookCommand.handler(multiAuthorBook, [bookEventStore], {})

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.authors).toHaveLength(2)
      expect(aggregate?.authors.map((a) => a.name)).toEqual(["Terry Pratchett", "Neil Gaiman"])
    })
  })

  describe("Command Handling", () => {
    it("should execute the create command and produce a new book aggregate", async () => {
      // When
      const result = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})
      const { aggregateId } = result

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.title).toBe(bookCreatedEvent.title)
      expect(aggregate?.authors[0].name).toBe(bookCreatedEvent.authors[0].name)
    })

    it("should execute the update command and modify an existing book aggregate", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})

      // When
      await updateBookCommand.handler(
        {
          aggregateId,
          rating: 5,
        },
        [bookEventStore],
        {}
      )

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.rating).toBe(5)
    })

    it("should execute the delete command and mark a book aggregate as deleted", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(bookCreatedEvent, [bookEventStore], {})

      // When
      await deleteBookCommand.handler({ aggregateId }, [bookEventStore], {})

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.deleted).toBe(true)
      expect(aggregate?.deletedAt).toBeDefined()
    })
  })

  describe("CDC Event Handling", () => {
    it("should handle Amazon CDC events and update book status and pricing", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(
        {
          ...bookCreatedEvent,
          status: "draft",
        },
        [bookEventStore],
        {}
      )

      // When
      const amazonData = {
        asin: "B00EXAMPLE",
        title: "The Great Gatsby",
        authorNames: ["F. Scott Fitzgerald"],
        publicationDate: "1925-04-10",
        categories: ["Fiction", "Classics"],
        price: 9.99,
        currency: "USD",
        description: "A story of the Jazz Age",
        pageCount: 180,
      }

      await captureBookChangesOnAmazonCommand.handler(
        {
          aggregateId,
          sourceId: amazonData.asin,
          sourceData: amazonData,
          forceFresh: process.env.CDC_FORCE_FRESH_MODE === "true",
        },
        [bookEventStore],
        {}
      )

      // Then
      const { aggregate } = await bookEventStore.getAggregate(aggregateId)
      expect(aggregate).toBeDefined()
      expect(aggregate?.status).toBe("published") // Draft should be changed to published
      expect(aggregate?.price).toBe(amazonData.price)
      expect(aggregate?.currency).toBe(amazonData.currency)
    })

    it("should only increment version when CDC data actually changes", async () => {
      // Given
      const { aggregateId } = await createBookCommand.handler(
        {
          ...bookCreatedEvent,
          status: "draft",
        },
        [bookEventStore],
        {}
      )

      const initialAmazonData = {
        asin: "B00EXAMPLE",
        title: "The Great Gatsby",
        authorNames: ["F. Scott Fitzgerald"],
        publicationDate: "1925-04-10",
        categories: ["Fiction", "Classics"],
        price: 9.99,
        currency: "USD",
        description: "A story of the Jazz Age",
        pageCount: 180,
      }

      // When - First CDC event
      await captureBookChangesOnAmazonCommand.handler(
        {
          aggregateId,
          sourceId: initialAmazonData.asin,
          sourceData: initialAmazonData,
          forceFresh: process.env.CDC_FORCE_FRESH_MODE === "true",
        },
        [bookEventStore],
        {}
      )

      // Then - Check first update
      const afterFirstUpdate = await bookEventStore.getAggregate(aggregateId)
      expect(afterFirstUpdate.aggregate?.version).toBe(2)
      expect(afterFirstUpdate.aggregate?.price).toBe(initialAmazonData.price)

      // When - Second CDC event with same data
      await captureBookChangesOnAmazonCommand.handler(
        {
          aggregateId,
          sourceId: initialAmazonData.asin,
          sourceData: initialAmazonData,
          forceFresh: process.env.CDC_FORCE_FRESH_MODE === "true",
        },
        [bookEventStore],
        {}
      )

      // Then - Version should not have changed since data was the same
      const afterSecondUpdate = await bookEventStore.getAggregate(aggregateId)
      expect(afterSecondUpdate.aggregate?.version).toBe(2) // Should still be 2 since no real changes
      expect(afterSecondUpdate.aggregate?.price).toBe(initialAmazonData.price)

      // When - Third CDC event with different price
      const updatedAmazonData = {
        ...initialAmazonData,
        price: 14.99,
      }

      await captureBookChangesOnAmazonCommand.handler(
        {
          aggregateId,
          sourceId: updatedAmazonData.asin,
          sourceData: updatedAmazonData,
          forceFresh: process.env.CDC_FORCE_FRESH_MODE === "true",
        },
        [bookEventStore],
        {}
      )

      // Then - Version should increment since price changed
      const afterThirdUpdate = await bookEventStore.getAggregate(aggregateId)
      expect(afterThirdUpdate.aggregate?.version).toBe(3)
      expect(afterThirdUpdate.aggregate?.price).toBe(updatedAmazonData.price)

      // When - Fourth CDC event with same updated price
      await captureBookChangesOnAmazonCommand.handler(
        {
          aggregateId,
          sourceId: updatedAmazonData.asin,
          sourceData: updatedAmazonData,
          forceFresh: process.env.CDC_FORCE_FRESH_MODE === "true",
        },
        [bookEventStore],
        {}
      )

      // Then - Version should not increment since no changes
      const afterFourthUpdate = await bookEventStore.getAggregate(aggregateId)
      expect(afterFourthUpdate.aggregate?.version).toBe(3) // Should still be 3 since no real changes
      expect(afterFourthUpdate.aggregate?.price).toBe(updatedAmazonData.price)
    })
  })
})
