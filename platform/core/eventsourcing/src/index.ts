/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export const name = "@kreios/eventsourcing"
export { defineAggregate, defineReducer, handleCrudEvent } from "./aggregate"
export type { InferAggregateFromCreatedEvent, AggregateBase } from "./aggregate"
export { createCrudCommands, defineCommand } from "./commands"
export { createCrudEventTypes, defineEventType } from "./events"
