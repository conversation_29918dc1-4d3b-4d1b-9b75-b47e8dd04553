# Event Sourcing Module

This module provides event sourcing capabilities for the platform.

## Installation

```bash
pnpm install
```

## Development Setup

After cloning the repository and installing dependencies with `pnpm install`, you need to rebuild the SQLite bindings:

```bash
cd platform/core/eventsourcing
pnpm rebuild better-sqlite3
```

This step is necessary because SQLite bindings are platform-specific native code that needs to be compiled for your specific environment.

## Running Tests

```bash
pnpm test
```

## Common Issues

### SQLite Bindings Error

If you encounter an error like:

```
Error: Could not locate the bindings file. Tried:
 → /path/to/better-sqlite3/build/better_sqlite3.node
 → /path/to/better-sqlite3/build/Debug/better_sqlite3.node
 ...
```

This means the SQLite bindings haven't been built for your platform. Run:

```bash
pnpm rebuild better-sqlite3
```

If that doesn't work, make sure you have the required system dependencies:

- On Ubuntu/Debian: `sudo apt-get install -y sqlite3 libsqlite3-dev`
- On macOS: `brew install sqlite`
- On Windows: Install SQLite using the installer from the [SQLite website](https://www.sqlite.org/download.html)

## Architecture

The event sourcing module uses a combination of:

1. Event storage adapters (Drizzle-based) for different databases:

   - PostgreSQL
   - MySQL
   - SQLite

2. Event stores for managing aggregates and their events

3. Command handlers for processing commands and generating events

## License

See the project's main license file.
