/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useMutation } from "@tanstack/react-query"
import { ulid } from "ulidx"

export const useFileUpload = (uploadUrl: string) => {
  const mutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData()

      formData.append("key", `${ulid()}.${file.name.split(".").pop()}`)
      formData.append("file", file)

      const response = await fetch(uploadUrl, {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw Error(`Upload failed with status ${response.status}`)
      }

      const data = (await response.json()) as { key: string }
      const { key } = data

      if (!key) {
        throw Error("Upload response missing key")
      }

      return key
    },
  })

  return mutation
}
