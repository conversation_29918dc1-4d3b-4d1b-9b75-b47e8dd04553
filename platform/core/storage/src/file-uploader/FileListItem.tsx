/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"
import { BoxIcon, FileTextIcon, ImageIcon, Trash2Icon } from "lucide-react"

import type { StoredFile } from "../core"
import { useFileUploaderContext } from "./FileUploaderContext"

export const FileListItem: FC<{ item: StoredFile & { url?: string }; children?: React.ReactNode }> = ({
  item,
  children,
}) => {
  const { disabled, onValueChange } = useFileUploaderContext()

  const handleDeleteFile = (key: string) => {
    onValueChange((value) => value.filter((file) => file.key !== key))
  }

  return (
    <div className="flex h-5 w-full flex-row items-center justify-between rounded-md border border-input px-2 py-4">
      <div className="flex h-5 flex-row items-center gap-2">
        {(() => {
          switch (item.mimeType) {
            case "image/jpeg":
            case "image/png":
              return <ImageIcon className="h-5 w-5 text-rose-700" />
            case "application/step":
              return <BoxIcon className="h-5 w-5 text-rose-700" />
            default:
              return <FileTextIcon className="h-5 w-5 text-rose-700" />
          }
        })()}

        <div className="flex flex-col gap-0 text-sm">
          {item.url ? (
            <a href={item.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
              {item.name}
            </a>
          ) : (
            item.name
          )}
        </div>
      </div>
      {children}
      {!disabled && <Trash2Icon className="ml-4 h-5 w-5 cursor-pointer" onClick={() => handleDeleteFile(item.key)} />}
    </div>
  )
}
