/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { SetStateAction } from "react"
import { createContext, useContext } from "react"

import type { StoredFile } from "../core"

type FileUploaderContextType = {
  value: StoredFile[]
  onValueChange: (arg: SetStateAction<StoredFile[]>) => void
  prepareDownloadUrl?: {
    /** Fetches a download URL for a given file key */
    fetch: (key: string) => Promise<{ url: string }>
  }
  disabled?: boolean
}

export const FileUploaderContext = createContext<FileUploaderContextType | null>(null)

export function useFileUploaderContext() {
  const context = useContext(FileUploaderContext)

  if (!context) {
    throw new Error("useFileUploaderContext must be used within a FileUploaderContext provider")
  }

  return context
}
