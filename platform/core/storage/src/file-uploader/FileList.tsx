/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"
import { useQueries } from "@tanstack/react-query"

import type { StoredFile } from "../core"
import { FileUploader } from "./FileUploader"
import { useFileUploaderContext } from "./FileUploaderContext"

/**
 * A component that renders a list of uploaded files.
 *
 * @component
 * @param {Object} props - The component props
 * @param {function} [props.children] - Optional render prop that receives each file item and can return custom content
 * @returns {React.ReactElement|null} The rendered list of files or null if empty
 *
 * @example
 * ```tsx
 * <List>
 *   {(file) => <CustomFileItem file={file} />}
 * </List>
 * ```
 */
export const FileList: FC<{
  children?: (item: StoredFile & { url?: string }) => React.ReactNode
}> = ({ children }) => {
  const { value: uploadedFiles, prepareDownloadUrl } = useFileUploaderContext()

  const fileQueries = useQueries({
    queries: uploadedFiles.map((file) => ({
      queryKey: ["fileKey", file.key],
      queryFn: async () => {
        if (!prepareDownloadUrl) return file
        const { url } = await prepareDownloadUrl.fetch(file.key)
        return { ...file, url }
      },
      enabled: !!prepareDownloadUrl,
    })),
  })

  const filesWithUrls = fileQueries.map((query, index) => query.data ?? uploadedFiles[index])

  if (filesWithUrls.length === 0) return null

  return (
    <div
      className={`flex w-full flex-col gap-4 ${filesWithUrls.length > 2 ? "h-48" : "h-fit"} ${filesWithUrls.length > 0 ? "mt-4 pb-2" : ""}`}
    >
      {filesWithUrls.map((item) => (children ? children(item) : <FileUploader.ListItem item={item} key={item.key} />))}
    </div>
  )
}
