/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { SetStateAction } from "react"
import type { DropzoneState, FileError } from "react-dropzone"
import { useMemo } from "react"
import { UploadIcon } from "lucide-react"
import { useTranslations } from "next-intl"
import { useDropzone } from "react-dropzone"

import { toast } from "@kreios/ui/sonner"

import type { StoredFile } from "../core"
import { FileList } from "./FileList"
import { FileListItem } from "./FileListItem"
import { FileUploaderContext } from "./FileUploaderContext"

type FileUploaderProps = {
  value?: StoredFile[]
  onValueChange: (arg: SetStateAction<StoredFile[]>) => void

  fileListComponent?: React.ReactNode

  uploadProcedure: (file: File) => Promise<string>
  prepareDownloadUrl?: {
    /** Fetches a download URL for a given file key */
    fetch: (key: string) => Promise<{ url: string }>
  }

  maxSize?: number
  multiple?: boolean
  accept?: Record<string, string[]>
  disabled?: boolean
  containerClassName?: string
  dropZoneClassName?: string

  // File handling callbacks

  onFileAccepted?: (files: Pick<StoredFile, "name" | "mimeType">) => void
  onFileRejected?: (file: Pick<StoredFile, "name" | "mimeType"> & { errors: readonly FileError[] }) => void
  onUploadError?: (error: Error) => void

  children?: (dropzone: DropzoneState) => React.ReactNode
}

/**
 * A component for handling file uploads with drag-and-drop functionality.
 *
 * @component
 * @param {Object} props - The component props
 * @param {StoredFile[]} props.value - Array of currently stored files
 * @param {function} props.onValueChange - Callback when files are added/removed
 * @param {React.ReactNode} [props.fileListComponent] - Optional custom component to display the file list
 * @param {function} props.uploadProcedure - Async function to handle the actual file upload, returns file key
 * @param {number} [props.maxSize] - Maximum allowed file size in bytes
 * @param {boolean} [props.multiple] - Whether multiple file uploads are allowed
 * @param {Record<string, string[]>} [props.accept] - Map of accepted file types
 * @param {boolean} [props.disabled] - Whether the uploader is disabled
 * @param {string} [props.containerClassName] - Additional CSS class for container
 * @param {string} [props.dropZoneClassName] - Additional CSS class for dropzone
 * @param {function} [props.onFileAccepted] - Callback when a file is accepted
 * @param {function} [props.onFileRejected] - Callback when a file is rejected
 * @param {function} [props.onUploadError] - Callback when an upload error occurs
 * @param {function} [props.children] - Render prop for custom dropzone content
 * @returns {React.ReactElement} The file uploader component
 */

export function FileUploader({
  value = [],
  onValueChange,
  fileListComponent,
  uploadProcedure,
  prepareDownloadUrl,
  maxSize,
  multiple,
  accept,
  disabled,
  containerClassName,
  dropZoneClassName,
  onFileAccepted,
  onFileRejected,
  onUploadError,
  children,
}: FileUploaderProps) {
  const t = useTranslations("toast")

  const isDisabled = useMemo(() => {
    return (!multiple && value.length > 0) || disabled
  }, [value, disabled])

  const dropzone = useDropzone({
    maxSize,
    disabled: isDisabled,
    accept,
    multiple,
    onDrop: (acceptedFiles, rejectedFiles) => {
      if (rejectedFiles.length > 0) {
        rejectedFiles.forEach(({ file, errors }) => {
          toast.error(
            t("error.fileRejected", {
              fileName: file.name,
              errors: errors.map((error) => error.message).join(", "),
            })
          )

          onFileRejected?.({
            name: file.name,
            mimeType: file.type,
            errors,
          })
        })
      }

      // If no files are accepted, return
      if (acceptedFiles.length === 0) return

      acceptedFiles.forEach((file) => {
        onFileAccepted?.({
          name: file.name,
          mimeType: file.type,
        })
      })

      const uploadToast = toast.loading(
        t("loading.uploadingFiles", {
          count: acceptedFiles.length,
          plural: acceptedFiles.length > 1 ? "s" : "",
        })
      )

      Promise.all(
        acceptedFiles.map(async (file) => {
          const key = await uploadProcedure(file)
          const { name, type: mimeType } = file

          return {
            key,
            name,
            mimeType,
          }
        })
      )
        .then((uploadedFiles) => {
          toast.dismiss(uploadToast)
          toast.success(
            t("success.filesUploaded", {
              count: uploadedFiles.length,
              plural: uploadedFiles.length > 1 ? "s" : "",
            })
          )
          onValueChange((value) => [...value, ...uploadedFiles])
        })
        .catch((error: Error) => {
          toast.dismiss(uploadToast)
          toast.error(
            t("error.fileError", {
              message: error.message,
            })
          )
          // Return the error to the caller
          onUploadError?.(error)
        })
    },
  })

  return (
    <FileUploaderContext.Provider value={{ value, onValueChange, prepareDownloadUrl, disabled }}>
      {!isDisabled && (
        <div className={["flex flex-col gap-2", containerClassName].join(" ")}>
          <div
            {...dropzone.getRootProps()}
            className={[
              "flex w-full cursor-pointer select-none items-center justify-center rounded-md border border-solid bg-background p-4 py-10 transition-all hover:bg-accent hover:text-accent-foreground focus-visible:outline-none",
              dropZoneClassName,
            ].join(" ")}
          >
            <input {...dropzone.getInputProps()} />
            {children ? (
              children(dropzone)
            ) : dropzone.isDragAccept ? (
              <div className="text-sm font-medium">Drop your files here!</div>
            ) : (
              <div className="flex flex-col items-center gap-1.5">
                <div className="flex flex-row items-center gap-0.5 text-sm font-medium">
                  <UploadIcon className="mr-2 h-6 w-6" /> {t("info.uploadFiles")}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      {fileListComponent}
    </FileUploaderContext.Provider>
  )
}

FileUploader.List = FileList
FileUploader.ListItem = FileListItem
