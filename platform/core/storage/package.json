{"name": "@kreios/storage", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./azure": "./src/azure/azure-provider.ts", "./s3": "./src/s3/s3-provider.ts", "./file-uploader": "./src/file-uploader/index.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@aws-sdk/client-s3": "3.614.0", "@aws-sdk/s3-request-presigner": "3.614.0", "@azure/storage-blob": "12.23.0", "@kreios/ui": "workspace:*", "@radix-ui/react-use-controllable-state": "1.1.0", "@tanstack/react-query": "5.55.4", "lucide-react": "0.457.0", "next-intl": "4.0.2", "react-dropzone": "14.3.5", "ulidx": "2.4.1"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/node": "20.14.10", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}