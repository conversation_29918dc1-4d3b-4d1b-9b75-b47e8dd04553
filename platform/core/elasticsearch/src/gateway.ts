/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type * as T from "@elastic/elasticsearch/lib/api/types"
import type { SearchTotalHits } from "@elastic/elasticsearch/lib/api/types"
import { Client } from "@elastic/elasticsearch"
import { waitUntil } from "@vercel/functions"
import batch from "it-batch"

import { capitalize } from "@kreios/utils/capitilize"
import { createMultiBar } from "@kreios/utils/progress-bar"
import { isSyncContext } from "@kreios/utils/sync-execution-context"

import type { Document, GatewayConfig } from "./config"

// The original @elastic/elasticsearch types can be quite complex and difficult to work with.
// They aim to support multiple versions of Elasticsearch as a backend while also being a thin wrapper
// around its HTTP API. As a result, these types can be both overly broad and overly narrow at the same time,
// making them challenging to use effectively.
// Therefore, it's still undecided whether the rest of this application should use them directly
// or whether we should abstract them behind a simpler API.
export type Aggregations = Record<T.AggregateName, T.AggregationsAggregate>
export type SearchRequest = T.SearchRequest
export type SearchResponse<TDocument extends Document, TAggregations extends Aggregations> = {
  total: number
  hits: T.SearchHit<TDocument>[]
  documents: TDocument[]
  max_score: number
  aggregations: TAggregations
}
export type TermsAggregation = {
  buckets: Array<{
    key: string
    doc_count: number
  }>
}

/**
 * To be used for aggregations that include their own labels.
 */
export type TermsAggregationWithLabels<TDocument> = {
  buckets: Array<{
    key: string
    doc_count: number
    labels: {
      hits: {
        hits: [
          {
            _source: TDocument
          },
        ]
      }
    }
  }>
}

export type ExtractGatewayConfig<TGatway> = TGatway extends ElasticsearchGateway<infer TConfig> ? TConfig : never

/**
 * The Elasticsearch Gateway is a thin wrapper around the Elasticsearch client that provides a more
 * convenient and type-safe interface for interacting with Elasticsearch.
 */
export class ElasticsearchGateway<TConfig extends GatewayConfig> {
  private client: Client | null = null

  constructor(readonly config: TConfig) {}

  /**
   * Get the Elasticsearch client instance. If the client does not exist yet, it will be created.
   */
  private async getClient(): Promise<Client> {
    if (!this.client) {
      console.info(`Attempting to connect to Elasticsearch at ${this.config.url}`)
      this.client = new Client({
        node: this.config.url,
        ...(this.config.apiKey ? { auth: { apiKey: this.config.apiKey } } : {}),
        tls: { rejectUnauthorized: true },
        // Disable sniffing - crucial for Elastic Cloud
        sniffOnStart: false,
        sniffInterval: false,
        sniffOnConnectionFault: false,
      })
      await this.status()
    }
    return this.client
  }

  /**
   * Close the Elasticsearch client if it exists.
   */
  public async close(): Promise<void> {
    if (this.client) {
      console.info("Closing the connection to Elasticsearch")
      await this.client.close()
      this.client = null
      console.info("Connection to Elasticsearch closed")
    }
  }

  /**
   * Implement the async disposal method.
   */
  public [Symbol.asyncDispose] = async (): Promise<void> => {
    await this.close()
  }

  /**
   * List all indices matching the given pattern.
   *
   * @param pattern - The pattern to match the indices to list.
   * @returns The names of all indices matching the pattern.
   */
  public async listIndices(pattern?: string): Promise<string[]> {
    const client = await this.getClient()
    const fullPattern = pattern ? this.prependIndexPrefix(pattern) : undefined
    const indices = await client.cat.indices({ index: fullPattern, format: "json" })
    return indices
      .map((index) => index.index)
      .filter((index): index is string => index !== undefined)
      .map((index) => this.stripIndexPrefix(index))
  }

  /**
   * Check the status of the Elasticsearch cluster.
   *
   * @returns The status and version of the Elasticsearch cluster.
   */
  public async status(): Promise<{ status: string; version: string }> {
    const client = await this.getClient()

    // Check if the Elasticsearch cluster is ready to accept requests.
    const isClusterReady = await client.ping()
    if (!isClusterReady) {
      throw new Error("Elasticsearch cluster is not ready to accept requests")
    }

    // Get the version info without using cluster.health()
    const info = await client.info()
    console.info(`Connected to Elasticsearch, version: ${info.version.number}`)
    return { status: "connected", version: info.version.number }
  }

  /**
   * Check the status of the Elasticsearch cluster.
   *
   * @returns A boolean indicating whether the cluster is ready to accept requests.
   */
  public async ping(): Promise<boolean> {
    const client = await this.getClient()
    return await client.ping()
  }

  /**
   * List all indices with their aliases.
   *
   * @returns An array of objects containing the alias and index name for each index.
   */
  public listIndicesWithAlias(): { alias: string; index: string }[] {
    return this.config.indices.map(({ name }) => ({ alias: name, index: this.prependIndexPrefix(name) }))
  }

  /**
   * Purge dangling indicies
   *
   *
   * @param indices - The indices to purge.
   */
  public async purge(indices = this.config.indices.map(({ name }) => name)): Promise<void> {
    const client = await this.getClient()

    const indiciesToReindex = this.config.indices.filter(({ name }) => indices.includes(name))

    // Get the indicies that are currently used
    const currentAliasIndicies = Object.keys(
      await client.indices.getAlias({
        name: indiciesToReindex.map((indexConfig) => this.prependIndexPrefix(indexConfig.name)).join(","),
      })
    )

    // Get all indicies for the given indixconfigs to purge
    const allIndicies = (
      await Promise.all(indiciesToReindex.map((indexConfig) => this.listIndices(`${indexConfig.name}_*`)))
    )
      .flat()
      .map((index) => this.prependIndexPrefix(index))

    // Filter out the indicies that are currently used
    const currentIndicies = allIndicies.filter((index) => !currentAliasIndicies.includes(index))

    if (currentIndicies.length === 0) {
      console.info("No dangling indicies found")
      return
    }

    // Delete the indicies that are not currently used
    await Promise.all(
      currentIndicies.map((oldIndex) => {
        console.info(`Deleting old index ${oldIndex}...`)
        return client.indices.close({ index: oldIndex }).then(() => client.indices.delete({ index: oldIndex }))
      })
    )
  }

  /**
   * Recreate all indices and reindex all relevant data.
   *
   * @param purgeOldIndices - Whether to delete old indices after reindexing.
   * @returns The new indices that were created.
   */
  public async reindexAll(
    purgeOldIndices = false,
    batchSize?: number,
    indices = this.config.indices.map(({ name }) => name)
  ): Promise<{ alias: string; index: string }[]> {
    const client = await this.getClient()

    const indiciesToReindex = this.config.indices.filter(({ name }) => indices.includes(name))

    indiciesToReindex.forEach((indexConfig) => {
      console.info(`Index ${indexConfig.name} detected...`)
    })

    // We will not update any aliases or delete any old indices until all new indices have been created and populated.
    const newIndices: { alias: string; index: string }[] = []
    const oldIndicesPromises: Promise<string[]>[] = []

    // Collect old indices if needed
    if (purgeOldIndices) {
      for (const indexConfig of indiciesToReindex) {
        oldIndicesPromises.push(this.listIndices(`${indexConfig.name}_*`))
      }
    }

    const multibar = createMultiBar()

    // Create and populate new indices in parallel
    await Promise.all(
      indiciesToReindex.map(async (indexConfig) => {
        const newIndexName = this.prependIndexPrefix(`${indexConfig.name}_${Date.now()}`)
        const indexTitle = capitalize(indexConfig.name)
        const totalCount = await indexConfig.getTotalCount()
        const bar = multibar.create(indexTitle, totalCount)
        bar.update({ message: `Creating index...` })

        await client.indices.create({
          index: newIndexName,
          settings: {
            // this may be a bad idea and we should switch to cursor based pagination
            // max_result_window: 100_000_000,
            analysis: indexConfig.analysis,
          },
          mappings: {
            properties: indexConfig.properties,
          },
        })

        bar.update({
          message: `Indexing...`,
        })

        const iteratorOrPromise = indexConfig.fetchAll()

        // if fetchAll returns a promise of an iterator, we need to await it first
        const data: Generator<Promise<unknown>[], void, unknown> | AsyncGenerator<unknown[], void, unknown> = batch(
          iteratorOrPromise instanceof Promise ? await iteratorOrPromise : iteratorOrPromise,
          indexConfig.maxBatchSize ?? batchSize
        )

        for await (const models of data) {
          // To allow for batching models, we need to handle the case that the batch might include elements that should not be included i.e. null
          const truthyModels = (await Promise.all(models)).filter(Boolean)

          await this.set(
            newIndexName as IndexName<TConfig>,
            truthyModels.map((model) => indexConfig.toDocument(model))
          )

          bar.increment(truthyModels.length)
        }

        bar.update({ message: `Indexing done` })

        bar.stop()

        newIndices.push({ alias: indexConfig.name, index: newIndexName })
      })
    )

    multibar.stop()

    // Update aliases
    await Promise.all(
      newIndices.map(({ alias, index }) => {
        const fullAlias = this.prependIndexPrefix(alias)
        console.info(`Redirecting alias ${fullAlias} to index ${index}...`)

        return client.indices.updateAliases({
          actions: [{ remove: { index: "_all", alias: fullAlias } }, { add: { index, alias: fullAlias } }],
        })
      })
    )

    // Delete old indices if requested
    if (purgeOldIndices) {
      const oldIndices = (await Promise.all(oldIndicesPromises)).flat()
      await Promise.all(
        oldIndices.map((oldIndex) => {
          console.info(`Deleting old index ${oldIndex}...`)
          return client.indices.close({ index: oldIndex }).then(() => client.indices.delete({ index: oldIndex }))
        })
      )
    }

    return newIndices
  }

  /**
   * Reindex a specific model instance.
   *
   * @param index - The index to reindex the model instance in.
   * @param model - The model instance to reindex.
   */
  public reindex<TName extends IndexName<TConfig>>(
    index: TName,
    model: ModelType<TConfig, TName>
  ): Promise<undefined | void>

  /**
   * Reindex multiple model instances.
   *
   * @param index - The index to reindex the model instances in.
   * @param models - The model instances to reindex.
   */
  public reindex<TName extends IndexName<TConfig>>(
    index: TName,
    models: ModelType<TConfig, TName>[]
  ): Promise<undefined | void>

  async reindex<TName extends IndexName<TConfig>>(
    index: TName,
    modelOrModels: ModelType<TConfig, TName> | ModelType<TConfig, TName>[]
  ): Promise<undefined | void> {
    const models = Array.isArray(modelOrModels) ? modelOrModels : [modelOrModels]
    const documents = models.map((model) => this.toDocument(index, model))
    if (isSyncContext()) return this.set(index, documents)

    return waitUntil(this.set(index, documents))
  }

  /**
   * Transform a model instance to a document for a specific index.
   *
   * @param index - The index to transform the model for.
   * @param model - The model instance to transform.
   */
  public toDocument<TName extends IndexName<TConfig>>(
    index: TName,
    model: ModelType<TConfig, TName>
  ): DocumentType<TConfig, TName> {
    const indexConfig = this.config.indices.find((i) => i.name === index)
    if (!indexConfig) {
      throw new Error(`Index ${index} not found in the configuration`)
    }
    return indexConfig.toDocument(model) as DocumentType<TConfig, TName>
  }

  /**
   * Execute a search request.
   *
   * @param searchRequest - The search request to execute.
   * @returns The search response.
   */
  public async search<TDocument extends Document, TAggregations extends Aggregations>(
    searchRequest: SearchRequest
  ): Promise<SearchResponse<TDocument, TAggregations>> {
    //console.dir(searchRequest, { depth: 10000 })
    const client = await this.getClient()
    const fullIndexSearchRequest = {
      ...searchRequest,
      // this may be a bad idea and we should switch to cursor based pagination
      track_total_hits: true,
      index: this.prependIndexPrefixToIndices(searchRequest.index),
    }

    const response = await client.search<TDocument, TAggregations>(fullIndexSearchRequest)
    const result = {
      total: (response.hits.total as SearchTotalHits).value,
      hits: response.hits.hits,
      documents: response.hits.hits.map((hit) => hit._source!),
      max_score: response.hits.max_score!,
      aggregations: response.aggregations!,
    }
    console.info(
      `Sent a search request to Elasticsearch, which yielded ${result.total} results and took ${response.took}ms to complete`
    )
    return result
  }

  /**
   * Retrieves one or more documents by their IDs. Doesn't support pagination or sorting and doesn't throw an error
   * if some of the documents are not found. Only works if the documents themselves are stored in the index, which is
   * the default behavior of Elasticsearch.
   *
   * @param index - The index to search in.
   * @param ids - The IDs of the documents to retrieve.
   * @returns A map of the documents that were found, indexed by their IDs.
   */
  public async get<TDocument extends Document>(
    index: IndexName<TConfig>,
    ids: string[]
  ): Promise<Map<string, TDocument | undefined>>

  /**
   * Retrieves a single document by its ID. Returns `undefined` if the document is not found.
   *
   * @param index - The index to search in.
   * @param id - The ID of the document to retrieve.
   */
  public async get<TDocument extends Document>(index: IndexName<TConfig>, id: string): Promise<TDocument | undefined>

  // Implementation
  async get<TDocument extends Document>(
    index: IndexName<TConfig>,
    idOrIds: string | string[]
  ): Promise<Map<string, TDocument | undefined> | TDocument | undefined> {
    const client = await this.getClient()
    const ids = Array.isArray(idOrIds) ? idOrIds : [idOrIds]

    // Don't do anything if there are no IDs to fetch
    if (ids.length === 0) {
      return Array.isArray(idOrIds) ? new Map() : undefined
    }

    // Make the request to Elasticsearch
    const fullIndex = this.prependIndexPrefix(index)
    const result = await client.mget<TDocument>({ index: fullIndex, ids })

    // Create a map of the documents by their IDs for easier access
    const map = new Map<string, TDocument | undefined>()
    result.docs.forEach((doc) => {
      map.set(doc._id, "_source" in doc ? doc._source : undefined)
    })

    // Log some basic info about what we just did
    console.info(
      `Send a request to retrieve ${ids.length} documents from index ${index}, got ${result.docs.length} documents back`
    )

    // We're done!
    return Array.isArray(idOrIds) ? map : map.get(idOrIds)
  }

  /**
   * Create or update one or more documents in the specified index.
   * @param index - The index to create/update the document in.
   * @param document - The document or documents to create/update.
   */
  public async set<TDocument extends Document>(index: IndexName<TConfig>, document: TDocument): Promise<void>

  /**
   * Create or update one or more documents in the specified index.
   *
   * @param index - The index to create/update the documents in.
   * @param documents - The document or documents to create/update.
   */
  public async set<TDocument extends Document>(index: IndexName<TConfig>, documents: TDocument[]): Promise<void>

  // Implementation
  async set<TDocument extends Document>(
    index: IndexName<TConfig>,
    documentOrDocuments: TDocument | TDocument[]
  ): Promise<void> {
    const client = await this.getClient()
    const documents = Array.isArray(documentOrDocuments) ? documentOrDocuments : [documentOrDocuments]
    const fullIndex = this.prependIndexPrefix(this.stripIndexPrefix(index))
    const operations = documents.flatMap(({ _id, ...doc }) => [{ index: { _index: fullIndex, _id: _id } }, doc])

    if (operations.length === 0) {
      console.log(`⏭️    [ES] No documents to index for ${fullIndex}`)
      return
    }

    console.log(`🔍   [ES] Starting bulk index operation: ${documents.length} documents to ${fullIndex}`)
    const startTime = Date.now()

    try {
      const result = await client.bulk({ refresh: true, operations })
      const duration = Date.now() - startTime

      if (result.errors) {
        console.error(`❌   [ES] Failed to create/update documents in index ${fullIndex} after ${duration}ms`)
        console.error(result.items)
        throw new Error(`Failed to create/update documents in index ${fullIndex}`)
      }

      console.log(
        `✅   [ES] Bulk index completed: ${documents.length} documents indexed to ${fullIndex} in ${duration}ms`
      )
      console.log(`📈   [ES] Performance: ${Math.round(documents.length / (duration / 1000))} docs/second`)
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`❌   [ES] Bulk index failed for ${fullIndex} after ${duration}ms`, error)
      throw error
    }
  }

  /**
   * Delete one or more documents by their IDs.
   *
   * @param index - The index to delete the documents from.
   * @param id - The ID of the document to delete.
   */
  async delete(index: IndexName<TConfig>, id: string): Promise<undefined | void>

  /**
   * Delete one or more documents by their IDs.
   *
   * @param index - The index to delete the documents from.
   * @param ids - The IDs of the documents to delete.
   */
  async delete(index: IndexName<TConfig>, ids: string[]): Promise<undefined | void>

  async delete(index: IndexName<TConfig>, idOrIds: string | string[]): Promise<undefined | void> {
    const promise = (async () => {
      const client = await this.getClient()
      const ids = Array.isArray(idOrIds) ? idOrIds : [idOrIds]
      const fullIndex = this.prependIndexPrefix(index)
      const operations = ids.flatMap((id) => [{ delete: { _index: fullIndex, _id: id } }])

      if (operations.length === 0) {
        console.log(`⏭️    [ES] No documents to delete from ${fullIndex}`)
        return
      }

      console.log(`🗑️    [ES] Starting bulk delete operation: ${ids.length} documents from ${fullIndex}`)
      console.log(
        `🗑️    [ES] Deleting IDs: ${ids.slice(0, 5).join(", ")}${ids.length > 5 ? ` ... and ${ids.length - 5} more` : ""}`
      )
      const startTime = Date.now()

      try {
        await client.bulk({ refresh: true, operations })
        const duration = Date.now() - startTime
        console.log(
          `✅   [ES] Bulk delete completed: ${ids.length} documents deleted from ${fullIndex} in ${duration}ms`
        )
      } catch (error) {
        const duration = Date.now() - startTime
        console.error(`❌   [ES] Bulk delete failed for ${fullIndex} after ${duration}ms`, error)
        throw error
      }
    })()

    return isSyncContext() ? promise : waitUntil(promise)
  }

  /**
   * Find the first document matching the given query in the specified index.
   *
   * @param index - The index to search in.
   * @param query - The query to match documents against.
   * @returns The first matching document, or undefined if no matches are found.
   */
  public async findFirst<TIndex extends IndexName<TConfig>, TDocument extends Document = DocumentType<TConfig, TIndex>>(
    index: TIndex,
    query: T.QueryDslQueryContainer
  ): Promise<TDocument | undefined> {
    try {
      const result = await this.search<TDocument, never>({
        index,
        query,
        size: 1, // Limit to one result
      })

      if (result.documents.length > 0) return result.documents[0]

      return undefined
    } catch (error) {
      console.error(`Error in findFirst for index ${index}:`, error)
      throw new Error(`Failed to find document in index ${index}`)
    }
  }

  /**
   * Prepends the configured index prefix to the given index name.
   * If no prefix is configured, returns the original index name.
   *
   * @param indexName - The original index name.
   * @returns The index name with the prefix prepended, if a prefix is configured.
   */
  private prependIndexPrefix(indexName: string): string {
    return this.config.indexPrefix ? `${this.config.indexPrefix}_${indexName}` : indexName
  }

  /**
   * Removes the configured index prefix from the given index name.
   * If no prefix is configured or the index name doesn't start with the prefix, returns the original index name.
   *
   * @param indexName - The index name that may include the prefix.
   * @returns The index name with the prefix removed, if it was present.
   */
  private stripIndexPrefix(indexName: string): string {
    return this.config.indexPrefix && indexName.startsWith(`${this.config.indexPrefix}_`)
      ? indexName.slice(this.config.indexPrefix.length + 1)
      : indexName
  }

  /**
   * Prepends the configured index prefix to the given index or indices.
   * If no prefix is configured, returns the original index or indices.
   *
   * @param indices - The original index, indices, or undefined.
   * @returns The index or indices with the prefix prepended, if a prefix is configured.
   */
  private prependIndexPrefixToIndices(indices: string | string[] | undefined): string | string[] | undefined {
    if (indices === undefined) {
      return undefined
    }
    if (Array.isArray(indices)) {
      return indices.map(this.prependIndexPrefix.bind(this))
    }
    return this.prependIndexPrefix(indices)
  }
}

/**
 * Helper type to get the union type of all index names in a given GatewayConfig.
 */
export type IndexName<T> = T extends GatewayConfig
  ? T["indices"][number]["name"]
  : T extends ElasticsearchGateway<infer C>
    ? C["indices"][number]["name"]
    : never

/**
 * Helper type to get the index configuration of specific index in a given GatewayConfig.
 */
export type IndexConfigHelper<TConfig extends GatewayConfig, TIndexName extends IndexName<TConfig>> = Extract<
  TConfig["indices"][number],
  { name: TIndexName }
>

/**
 * Helper type to get the document type of specific index in a given GatewayConfig.
 */
export type DocumentType<TConfig extends GatewayConfig, TIndexName extends IndexName<TConfig>> = ReturnType<
  IndexConfigHelper<TConfig, TIndexName>["toDocument"]
>

/**
 * Helper type to get the model type of specific index in a given GatewayConfig.
 */
export type ModelType<TConfig extends GatewayConfig, TIndexName extends IndexName<TConfig>> = Parameters<
  IndexConfigHelper<TConfig, TIndexName>["toDocument"]
>[0]
