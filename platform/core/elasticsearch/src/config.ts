/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type {
  IndicesIndexSettingsAnalysis as ESAnalysisSettings,
  MappingTypeMapping as ESMappings,
} from "@elastic/elasticsearch/lib/api/types"

/** A type that represents either an async generator function or a simple array of model instances. */
type DataSource<TModel> = () =>
  | AsyncGenerator<TModel | null, void>
  | Iterable<TModel | null>
  | Promise<Generator<Promise<TModel | null>, void>>

// The minimal type for a document in Elasticsearch.
export type Document = { _id: string; label: string }

/** Configuration for an individual index in Elasticsearch.  */
export interface IndexConfig<
  TName extends string = string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  TModel = any,
  TDocument extends Document = Document,
> {
  /** Maximum number of documents to index in a single batch. */
  maxBatchSize?: number
  /** Name of the Elasticsearch index. */
  name: TName
  /** Display name of the index. */
  displayName: string
  /** URL to the detail page of the index. */
  buildUrl?: (aggregateId: string) => string
  /** Custom analyzers, tokenizers, or filters to be used in the index, if any. */
  analysis?: ESAnalysisSettings
  /** Explicit properties mapping for the index, if any (ES will fall back to its standard analyzer for any property not listed here). */
  properties?: ESMappings["properties"]
  /** Function to generate/enumerate the data to be indexed.
   * I can return any kind of iterable, including an async generator function.
   * @example
   * ```typescript
   * async function* fetchAll() {
   *  for await (const model of fetchModels()) {
   *    yield model
   *  }
   * }
   * ```
   *
   * If the function is consumed in batches and the resolver for each of your models allows batching (e.g. a DataLoader), you should return `Promise<Generator<Promise<TModel | null>, void>>`
   * @example
   * ```typescript
   * async function fetchAll() {
   *   const { aggregateIds } = await mailsEventStore.listAggregateIds()
   *   return (function* () {
   *     for (const entry of aggregateIds) {
   *       yield mailsEventStore
   *         .getExistingAggregate(entry.aggregateId)
              // We yield elements to no be included as null
   *         .then(({ aggregate }) => (aggregate.deleted ? null : aggregate))
   *     }
   *   })()
   * }
   * ```
   */
  fetchAll: DataSource<TModel>
  getTotalCount: () => Promise<number>
  fetchById: (id: string) => Promise<TModel>
  /** Function to transform a model instance to an Elasticsearch document. */
  toDocument: (modelInstance: TModel) => TDocument
}

// Configuration for the Elasticsearch setup including the URL and indices.
export type GatewayConfig<TIndices extends IndexConfig[] = IndexConfig[]> = {
  url: string // URL of the Elasticsearch server.
  apiKey?: string // API key for the Elasticsearch server, if any.
  indexPrefix?: string // Prefix for the Elasticsearch index names.
  indices: TIndices // List of index configurations.
}

// Factory function to create ElasticAppConfig and IndexConfig objects. Provides type safety for the configuration.
export const createGatewayConfig = <TConfig extends GatewayConfig>(config: TConfig) => config
export const createIndexConfig = <const TName extends string, TModel = unknown, TDocument extends Document = Document>(
  config: IndexConfig<TName, TModel, TDocument>
): IndexConfig<TName, TModel, TDocument> => config
