/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { program } from "@commander-js/extra-typings"

import type { GatewayConfig } from "./config"
import { ElasticsearchGateway } from "./gateway"

/**
 * Define the CLI program and its commands.
 *
 * @param config The Elasticsearch gateway configuration.
 */
export const defineCLI = (config: GatewayConfig) => {
  // Define the program
  program.name("pnpm elastic").description("CLI tool for all things related to Elasticsearch").version("0.1.0")

  // Define the 'ping' command
  program
    .command("ping")
    .description("Check the status of the Elasticsearch cluster")
    .action(async () => {
      await using gateway = new ElasticsearchGateway(config)
      await gateway.status()
      await container.dispose()
    })

  program
    .command("info")
    .description("Get information about the Elasticsearch configuration")
    .action(async () => {
      await using gateway = new ElasticsearchGateway(config)
      const indices = gateway.listIndicesWithAlias()
      indices.forEach((index) => {
        console.log(index.alias)
      })
      await container.dispose()
    })
  program
    .command("purge")
    .description("Purge the old indices")
    .option("-i, --indices <indices...>", "Indices to purge", (value) => value.split(","))
    .action(async (options) => {
      await using gateway = new ElasticsearchGateway(config)
      await gateway.purge(options.indices)

      await container.dispose()
    })

  program
    .command("reindex")
    .description("Reindex all documents from one index to another")
    .option("-b, --batch-size <number>", "Batch size for reindexing", (value) => Number(value), 10000)
    .option("-i, --indices <indices...>", "Indices to reindex", (value) => value.split(","))
    .option("-P, --purge-old-indices", "Purge the old indices after reindexing", false)
    .action(async (options) => {
      await using gateway = new ElasticsearchGateway(config)
      await gateway.reindexAll(
        options.purgeOldIndices,
        options.batchSize,
        options.indices?.length ? options.indices : undefined
      )

      await container.dispose()
    })

  return program
}

/**
 * Parse the command line arguments and execute the appropriate command.
 *
 * @param config The Elasticsearch gateway configuration.
 * @param args The command line arguments.
 */
export const parseCLI = (config: GatewayConfig, args: string[]) => {
  const program = defineCLI(config)
  program.parse(args)
}
