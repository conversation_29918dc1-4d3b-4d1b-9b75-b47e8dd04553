/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { ElasticsearchGateway, GatewayConfig, IndexName } from "@kreios/elasticsearch"
import { DataLoader } from "@kreios/utils/dataloader"
import { split } from "@kreios/utils/split"

/**
 * Creates a function that uses DataLoaders for re-indexing documents.
 *
 * @param gateway The Elasticsearch gateway
 * @returns A function with a similar signature to gateway.reindex but using DataLoaders and accepting IDs
 */
export const createReindexDataLoaderFunction = <TConfig extends GatewayConfig>(
  gateway: ElasticsearchGateway<TConfig>,
  maxBatchSize = 10000,
  batchScheduleMs = 5000
) => {
  const loaders: Record<IndexName<TConfig>, DataLoader<string, void>> = {} as Record<
    IndexName<TConfig>,
    DataLoader<string, void>
  >

  for (const indexConfig of gateway.config.indices) {
    const index = indexConfig.name
    // @ts-expect-error - TODO: fix the typesignature
    loaders[index] = new DataLoader(
      async (ids: readonly string[]) => {
        const models = await Promise.all(ids.map((id) => indexConfig.fetchById(id)))
        // @ts-expect-error - TODO: fix the typesignature
        await gateway.reindex(index, models)
        return ids.map(() => undefined)
      },
      {
        maxBatchSize,
        batchScheduleFn: (cb) => setTimeout(cb, batchScheduleMs),
        cache: false,
      }
    )
  }

  return function reindexWithDataLoader<TIndex extends IndexName<TConfig>>(
    index: TIndex,
    idOrIds: string | string[]
  ): Promise<void | undefined> {
    const loader = loaders[index]
    if (Array.isArray(idOrIds)) return loader.loadMany(idOrIds).then(() => undefined)
    else return loader.load(idOrIds)
  }
}

/**
 * Finds the index configuration from a gateway config that matches a bucket from an Elasticsearch aggregation.
 * Handles cases where the index name has a prefix that needs to be stripped.
 *
 * @param gatewayConfig - The gateway configuration containing index configurations
 * @param bucket - An Elasticsearch aggregation bucket with a key property
 * @returns The matching index configuration if found, otherwise undefined
 */
export function findIndexConfig(gatewayConfig: GatewayConfig, name: string) {
  return gatewayConfig.indices.find((index) => index.name === name)
}

/**
 * Extracts the clean index name from a potentially prefixed index name.
 * If the gateway config has an index prefix and it's present in the name, removes it.
 *
 * @param gatewayConfig - The gateway configuration containing index prefix
 * @param name - The raw index name that may contain a prefix
 * @returns The clean index name with any prefix removed
 */
export function extractLogicalIndexName(gatewayConfig: GatewayConfig, name: string) {
  const bucketNameSplit = split(name, "_")
  if (bucketNameSplit.includes(gatewayConfig.indexPrefix ?? "")) {
    bucketNameSplit.shift()
  }

  return bucketNameSplit[0]
}
