/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { beforeEach, describe, expect, it, vi } from "vitest"

import { promiseStatus } from "@kreios/utils/promise-status"
import { runAsync, runSync } from "@kreios/utils/sync-execution-context"

import type { Document, GatewayConfig, IndexConfig } from "./config"
import { ElasticsearchGateway } from "./gateway"

// Mock @vercel/functions waitUntil
// vi.mock("@vercel/functions", () => ({
//   waitUntil: vi.fn((promise: Promise<unknown>) => promise),
// }))

// Helper to create a delayed promise
const createDelayedPromise = <T>(value: T, delay = 10): Promise<T> => {
  return new Promise((resolve) => setTimeout(() => resolve(value), delay))
}

// Create a mock Elasticsearch client with all required methods
const createMockClient = () => ({
  ping: vi.fn().mockResolvedValue(true),
  bulk: vi.fn().mockImplementation(() => createDelayedPromise({ errors: false })),
  close: vi.fn().mockResolvedValueOnce(undefined),
  mget: vi.fn().mockResolvedValue({ docs: [] }),
  search: vi.fn().mockResolvedValue({
    hits: {
      total: { value: 0 },
      hits: [],
      max_score: 0,
    },
    aggregations: {},
  }),
  cat: {
    indices: vi.fn().mockResolvedValue([]),
  },
  cluster: {
    health: vi.fn().mockResolvedValue({ status: "green" }),
  },
  indices: {
    create: vi.fn().mockResolvedValue({}),
    delete: vi.fn().mockResolvedValue({}),
    close: vi.fn().mockResolvedValue({}),
    updateAliases: vi.fn().mockResolvedValue({}),
    getAlias: vi.fn().mockResolvedValue({}),
  },
  info: vi.fn().mockResolvedValue({ version: { number: "8.0.0" } }),
})

// Mock Elasticsearch Client
vi.mock("@elastic/elasticsearch", () => ({
  Client: vi.fn().mockImplementation(() => createMockClient()),
}))

interface TestDocument extends Document {
  _id: string
  data: string
  label: string
}

interface TestModel {
  id: string
  data: string
  label: string
}

type TestConfig = GatewayConfig & {
  indices: [IndexConfig<"test", TestModel, TestDocument>]
}

describe("ElasticsearchGateway", () => {
  let gateway: ElasticsearchGateway<TestConfig>
  let mockClient: ReturnType<typeof createMockClient>

  beforeEach(async () => {
    vi.clearAllMocks()

    // Create gateway instance with mock config
    gateway = new ElasticsearchGateway<TestConfig>({
      url: "http://localhost:9200",
      indices: [
        {
          name: "test",
          displayName: "Test Index",
          properties: {},
          toDocument: (model: TestModel): TestDocument => ({
            _id: model.id,
            data: model.data,
            label: model.label,
          }),
          fetchAll: async function* () {
            const testModel: TestModel = { id: "test", data: "test", label: "test" }
            yield testModel
          },
          getTotalCount: async () => 0,
          fetchById: async (id: string) => ({ id, data: "test", label: "test" }),
        },
      ],
    })

    // @ts-expect-error Get the mocked client instance
    mockClient = await gateway.getClient()
  })

  describe("Run gateway in sync context", () => {
    describe("run reindex", () => {
      it("should await the elastic result", async () => {
        const testModel: TestModel = { id: "test-id", data: "test-data", label: "test-label" }

        await runSync(async () => {
          await gateway.reindex("test", testModel)
        })

        expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("resolved")
      })
    })

    describe("run delete", () => {
      it("should await the elastic result", async () => {
        const id = "test-id"

        await runSync(async () => {
          await gateway.delete("test", id)
        })

        expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("resolved")
      })
    })
  })

  describe("Run gateway without sync context", () => {
    describe("run reindex", () => {
      it("should not await the elastic result", async () => {
        const testModel: TestModel = { id: "test-id", data: "test-data", label: "test-label" }

        await runAsync(async () => {
          await gateway.reindex("test", testModel)
        })

        expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("pending")
      })
    })

    describe("run delete", () => {
      it("should not await the elastic result", async () => {
        const id = "test-id"

        await runAsync(async () => {
          await gateway.delete("test", id)
        })

        expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("pending")
      })
    })
  })

  describe("Run gateway with nested contexts", () => {
    describe("withAsync inside runSync", () => {
      it("should respect the inner async context for reindex", async () => {
        const testModel: TestModel = { id: "test-id", data: "test-data", label: "test-label" }

        await runSync(async () => {
          await gateway.reindex("test", testModel)
          expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("resolved")

          await runAsync(async () => {
            await gateway.reindex("test", testModel)
          })
        })

        expect(mockClient.bulk.mock.results.length).toBe(2)

        // Verify that the inner async operation was not awaited
        expect(await promiseStatus(mockClient.bulk.mock.results[1].value as Promise<unknown>)).toBe("pending")
      })

      it("should respect the inner async context for delete", async () => {
        const id = "test-id"

        await runSync(async () => {
          await gateway.delete("test", id)
          expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("resolved")

          await runAsync(async () => {
            await gateway.delete("test", id)
          })
        })

        expect(mockClient.bulk.mock.results.length).toBe(2)
        expect(await promiseStatus(mockClient.bulk.mock.results[1].value as Promise<unknown>)).toBe("pending")
      })
    })

    describe("runSync inside withAsync", () => {
      it("should respect the inner sync context for reindex", async () => {
        const testModel: TestModel = { id: "test-id", data: "test-data", label: "test-label" }

        await runAsync(async () => {
          await gateway.reindex("test", testModel)

          // Verify that the outer async operation was not awaited
          expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("pending")

          await runSync(async () => {
            await gateway.reindex("test", testModel)
          })
        })

        expect(mockClient.bulk.mock.results.length).toBe(2)

        // Verify that the inner sync operation was awaited
        expect(await promiseStatus(mockClient.bulk.mock.results[1].value as Promise<unknown>)).toBe("resolved")
      })

      it("should respect the inner sync context for delete", async () => {
        const id = "test-id"

        await runAsync(async () => {
          // The outer async context should not be awaited
          await gateway.delete("test", id)

          expect(await promiseStatus(mockClient.bulk.mock.results[0].value as Promise<unknown>)).toBe("pending")

          await runSync(async () => {
            await gateway.delete("test", id)
          })
        })

        expect(mockClient.bulk.mock.results.length).toBe(2)

        // Verify that the inner sync operation was awaited
        expect(await promiseStatus(mockClient.bulk.mock.results[1].value as Promise<unknown>)).toBe("resolved")
      })
    })
  })
})
