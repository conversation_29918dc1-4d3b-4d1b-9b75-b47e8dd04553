{"name": "@kreios/elasticsearch", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": {"default": "./src/index.ts"}, "./env": "./env.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "tsx": "pnpm with-env tsx --conditions=react-server", "tsx:debug": "pnpm with-env tsx --conditions=react-server --inspect-brk", "typecheck": "tsc --noEmit", "with-env": "dotenvx run -f ../../../.env.local -f ../../../.env --overload --"}, "prettier": "@kreios/prettier-config", "dependencies": {"@commander-js/extra-typings": "12.1.0", "@elastic/elasticsearch": "8.15.0", "@kreios/utils": "workspace:*", "@vercel/functions": "1.4.1", "awilix": "12.0.3", "it-batch": "3.0.6"}, "devDependencies": {"@dotenvx/dotenvx": "1.14.0", "@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "eslint": "9.10.0", "prettier": "3.4.2", "tsx": "4.19.0", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}