{"name": "@kreios/copilot", "version": "0.1.0", "private": true, "license": "UNLICENSED", "type": "module", "exports": {".": "./src/index.tsx", "./flags": "./src/flags.ts", "./env": "./env.ts", "./persistance": "./src/persistance.ts", "./*": "./src/*.tsx"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@kreios/prettier-config", "dependencies": {"@ai-sdk/azure": "0.0.38", "@ai-sdk/openai": "0.0.60", "@kreios/auth": "workspace:*", "@kreios/hooks": "workspace:*", "@kreios/ui": "workspace:*", "@kreios/utils": "workspace:*", "@radix-ui/react-icons": "1.3.0", "@t3-oss/env-core": "0.11.1", "@vercel/flags": "2.6.1", "@vercel/kv": "2.0.0", "ai": "3.3.33", "framer-motion": "11.9.0", "js-cookie": "3.0.5", "lucide-react": "0.457.0", "nanoid": "5.0.9", "next-intl": "4.0.2", "nextjs-better-unstable-cache": "1.1.0", "nextjs-toploader": "3.7.15", "openai": "4.58.2", "react-markdown": "8.0.7", "react-syntax-highlighter": "15.5.0", "react-textarea-autosize": "8.5.3", "remark-gfm": "3.0.1", "remark-math": "5.1.1", "server-only": "0.0.1", "sonner": "1.5.0", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/js-cookie": "3.0.6", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "@types/react-syntax-highlighter": "15.5.13", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}, "peerDependencies": {"next": "14.2.13", "react": "18.3.1", "react-dom": "18.3.1"}}