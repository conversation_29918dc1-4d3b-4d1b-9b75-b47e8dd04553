/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use server"

import { revalidatePath, revalidateTag } from "next/cache"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { kv } from "@vercel/kv"
import { memoize } from "nextjs-better-unstable-cache"

import { auth } from "@kreios/auth"

import type { Chat } from "./types"
import { env } from "../env"

const MEMOIZE_LOGGING = (
  env.NODE_ENV === "development" ? ["datacache", "dedupe", "verbose"] : ["datacache"]
) satisfies NonNullable<Parameters<typeof memoize>[1]>["log"]

const getChats = memoize(
  async (userId: string) => {
    try {
      const pipeline = kv.pipeline()
      const chats: string[] = await kv.zrange(`user:chat:${userId}`, 0, -1, {
        rev: true,
      })

      for (const chat of chats) {
        pipeline.hgetall(chat)
      }

      const results = await pipeline.exec()

      return results as Chat[]
    } catch (error) {
      console.error(error)
      return []
    }
  },
  {
    persist: true,
    log: MEMOIZE_LOGGING,
    revalidateTags: (userId) => [`chats-${userId}`],
  }
)

export async function getUserChats() {
  const session = await auth()
  const userId = session?.user.id
  if (!userId) return []

  return getChats(userId)
}

const getChat = memoize(async (id: string) => await kv.hgetall<Chat>(`chat:${id}`), {
  persist: true,
  log: MEMOIZE_LOGGING,
  revalidateTags: (id) => [`chat-${id}`],
})

export async function getUserChat(id: string) {
  const session = await auth()

  const userId = session?.user.id

  if (!userId)
    return {
      error: "Unauthorized",
    }

  const chat = await getChat(id)

  if (!chat || chat.userId !== userId) return null

  return chat
}

export async function removeChat({ id, path }: { id: string; path: string }) {
  const session = await auth()

  const userId = session?.user.id

  if (!userId) {
    return {
      error: "Unauthorized",
    }
  }

  //Convert uid to string for consistent comparison with session.user.id
  const uid = String(await kv.hget(`chat:${id}`, "userId"))

  if (uid !== userId) {
    return {
      error: "Unauthorized",
    }
  }

  await kv.del(`chat:${id}`)
  await kv.zrem(`user:chat:${userId}`, `chat:${id}`)

  cookies().delete("copilotchatid")

  revalidateTag(`chat-${id}`)
  revalidateTag(`chats-${userId}`)
  revalidatePath("/")
  return revalidatePath(path)
}

export async function clearChats() {
  const session = await auth()

  const userId = session?.user.id

  if (!userId)
    return {
      error: "Unauthorized",
    }

  const chats: string[] = await kv.zrange(`user:chat:${userId}`, 0, -1)
  if (!chats.length) return redirect("/")

  const pipeline = kv.pipeline()

  for (const chat of chats) {
    pipeline.del(chat)
    pipeline.zrem(`user:chat:${userId}`, chat)
    revalidateTag(`chat-${chat}`)
  }

  await pipeline.exec()

  revalidateTag(`chats-${userId}`)
  revalidatePath("/")
  return redirect("/")
}

export async function saveChat(chat: Chat) {
  const session = await auth()

  if (!session?.user.id) return

  const pipeline = kv.pipeline()
  pipeline.hmset(`chat:${chat.id}`, chat)
  pipeline.zadd(`user:chat:${chat.userId}`, {
    score: Date.now(),
    member: `chat:${chat.id}`,
  })
  await pipeline.exec()
  revalidateTag(`chat-${chat.id}`)
  revalidateTag(`chats-${chat.userId}`)
}
