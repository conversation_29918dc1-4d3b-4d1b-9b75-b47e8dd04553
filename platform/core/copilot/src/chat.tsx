/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import Cookies from "js-cookie"
import { CornerDownLeftIcon, PlusIcon } from "lucide-react"
import { useRouter } from "nextjs-toploader/app"
import Textarea from "react-textarea-autosize"

import type { Session } from "@kreios/auth"
import { useScrollAnchor } from "@kreios/hooks/use-scroll-anchor"
import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from "@kreios/ui/tooltip"

import type { Message } from "./types"
import { ChatList } from "./chat-list"
import { ChatPanel } from "./chat-panel"
import { EmptyScreen } from "./empty-screen"
import { useAIState, useUIState } from "./hooks"

export interface ChatProps extends React.ComponentProps<"div"> {
  initialMessages?: Message[]
  id?: string
  session?: Session
}

export const ChatSkeleton = () => (
  <div className="group relative w-full overflow-auto pl-0 peer-[[data-state=open]]:lg:pl-[250px] peer-[[data-state=open]]:xl:pl-[300px]">
    <div className={cn("pb-[200px] pt-4 md:pt-10")}>
      {/* <div className="relative mx-auto max-w-2xl px-4">
        {new Array(4).fill(0).map((_, index, array) => (
          <div key={index}>
            <SpinnerMessage />
            {index < array.length - 1 && <Separator className="my-4" />}
          </div>
        ))}
      </div> */}
      <div className="h-px w-full" />
    </div>
    <div className="fixed inset-x-0 bottom-0 w-full bg-gradient-to-b from-muted/30 from-0% to-muted/30 to-50% duration-300 ease-in-out animate-in dark:from-background/10 dark:from-10% dark:to-background/80 peer-[[data-state=open]]:group-[]:lg:pl-[250px] peer-[[data-state=open]]:group-[]:xl:pl-[300px]">
      <div className="mx-auto sm:max-w-2xl sm:px-4">
        <div className="space-y-4 border-t bg-background px-4 py-2 shadow-lg sm:rounded-t-xl sm:border md:py-4">
          <form>
            <div className="relative flex max-h-60 w-full grow flex-col overflow-hidden bg-background px-8 sm:rounded-md sm:border sm:px-12">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    className="absolute left-0 top-[14px] size-8 rounded-full bg-background p-0 sm:left-4"
                  >
                    <PlusIcon />
                    <span className="sr-only">New Chat</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>New Chat</TooltipContent>
              </Tooltip>
              <Textarea
                disabled
                tabIndex={0}
                placeholder="Send a message."
                className="min-h-[60px] w-full resize-none bg-transparent px-4 py-[1.3rem] focus-within:outline-none sm:text-sm"
                autoFocus
                spellCheck={false}
                autoComplete="off"
                autoCorrect="off"
                name="message"
                rows={1}
              />
              <div className="absolute right-0 top-[13px] sm:right-4">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button type="submit" size="icon" disabled>
                      <CornerDownLeftIcon />
                      <span className="sr-only">Send message</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Send message</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
)

export function Chat({ id, className, session }: ChatProps) {
  const router = useRouter()
  const path = usePathname()
  const [input, setInput] = useState("")
  const [messages] = useUIState()
  const [aiState] = useAIState()

  useEffect(() => {
    if (session?.user) {
      if (messages.length === 1 && id) {
        Cookies.set("copilotchatid", id)
      }
    }
  }, [id, path, session?.user, messages])

  useEffect(() => {
    const messagesLength = aiState.messages.length
    if (messagesLength === 2) {
      router.refresh()
    }
  }, [aiState.messages, router])

  const { messagesRef, scrollRef, visibilityRef, isAtBottom, scrollToBottom } = useScrollAnchor()

  return (
    <div
      className="group relative w-full overflow-auto pl-0 peer-[[data-state=open]]:lg:pl-[250px] peer-[[data-state=open]]:xl:pl-[300px]"
      ref={scrollRef}
    >
      <div className={cn("pb-[200px] pt-4 md:pt-10", className)} ref={messagesRef}>
        {messages.length ? <ChatList messages={messages} session={session} /> : <EmptyScreen />}
        <div className="h-px w-full" ref={visibilityRef} />
      </div>
      <ChatPanel input={input} setInput={setInput} isAtBottom={isAtBottom} scrollToBottom={scrollToBottom} />
    </div>
  )
}
