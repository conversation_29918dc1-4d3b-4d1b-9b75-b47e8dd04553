/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import { AnimatePresence, motion } from "framer-motion"

import type { Chat } from "./types"
import { removeChat } from "./persistance"
import { SidebarActions } from "./sidebar-actions"
import { SidebarItem } from "./sidebar-item"

interface SidebarItemsProps {
  chats?: Chat[]
}

export function SidebarItems({ chats }: SidebarItemsProps) {
  if (!chats?.length) return null

  return (
    <AnimatePresence>
      {chats.map((chat, index) => (
        <motion.div
          key={chat.id}
          exit={{
            opacity: 0,
            height: 0,
          }}
        >
          <SidebarItem index={index} chat={chat}>
            <SidebarActions chat={chat} removeChat={removeChat} />
          </SidebarItem>
        </motion.div>
      ))}
    </AnimatePresence>
  )
}
