/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { nanoid } from "nanoid"

import { ButtonScrollToBottom } from "./button-scroll-to-bottom"
import { useActions, useUIState } from "./hooks"
import { UserMessage } from "./messages"
import { PromptForm } from "./prompt-form"

export interface ChatPanelProps {
  input: string
  setInput: (value: string) => void
  isAtBottom: boolean
  scrollToBottom: () => void
}

export function ChatPanel({ input, setInput, isAtBottom, scrollToBottom }: ChatPanelProps) {
  const [messages, setMessages] = useUIState()
  const { submitUserMessage } = useActions()

  const exampleMessages = [
    {
      heading: "Run a quick analysis on",
      subheading: "the sales data for Q1 2024",
      message: "Run a quick analysis on the sales data for Q1 2024",
    },
    {
      heading: "Compare the performance of",
      subheading: "Product A vs. Product B last year",
      message: "Compare the performance of Product A vs. Product B last year",
    },
    {
      heading: "Generate a forecast for",
      subheading: "customer growth in the next quarter",
      message: "Generate a forecast for customer growth in the next quarter",
    },
    {
      heading: "Identify the top 5",
      subheading: "revenue-generating products this month",
      message: "Identify the top 5 revenue-generating products this month",
    },
  ]

  return (
    <div className="fixed inset-x-0 bottom-0 w-full bg-gradient-to-b from-muted/30 from-0% to-muted/30 to-50% duration-300 ease-in-out animate-in dark:from-background/10 dark:from-10% dark:to-background/80 peer-[[data-state=open]]:group-[]:lg:pl-[250px] peer-[[data-state=open]]:group-[]:xl:pl-[300px]">
      <ButtonScrollToBottom isAtBottom={isAtBottom} scrollToBottom={scrollToBottom} />

      <div className="mx-auto sm:max-w-2xl sm:px-4">
        <div className="mb-4 grid grid-cols-2 gap-2 px-4 sm:px-0">
          {messages.length === 0 &&
            exampleMessages.map((example, index) => (
              <div
                key={example.heading}
                className={`cursor-pointer rounded-lg border bg-white p-4 hover:bg-zinc-50 dark:bg-zinc-950 dark:hover:bg-zinc-900 ${
                  index > 1 && "hidden md:block"
                }`}
                onClick={async () => {
                  setMessages((currentMessages) => [
                    ...currentMessages,
                    {
                      id: nanoid(),
                      display: <UserMessage>{example.message}</UserMessage>,
                    },
                  ])

                  const responseMessage = await submitUserMessage(example.message)

                  setMessages((currentMessages) => [...currentMessages, responseMessage])
                }}
              >
                <div className="text-sm font-semibold">{example.heading}</div>
                <div className="text-sm text-zinc-600">{example.subheading}</div>
              </div>
            ))}
        </div>

        <div className="space-y-4 border-t bg-background px-4 py-2 shadow-lg sm:rounded-t-xl sm:border md:py-4">
          <PromptForm input={input} setInput={setInput} />
        </div>
      </div>
    </div>
  )
}
