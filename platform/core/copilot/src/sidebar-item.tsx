/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-unnecessary-condition */
"use client"

import * as React from "react"
import { useState } from "react"
import { motion } from "framer-motion"
import Cookies from "js-cookie"
import { MessageSquareIcon } from "lucide-react"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { SheetClose } from "@kreios/ui/sheet"

import type { Chat } from "./types"
import { useNavigateToChat } from "./sidebar"

interface SidebarItemProps {
  index: number
  chat: Chat
  children: React.ReactNode
}

export function SidebarItem({ index, chat, children }: SidebarItemProps) {
  const chatId = Cookies.get("copilotchatid")

  const isActive = chatId === chat.id
  const [newChatId, setNewChatId] = useState(null)
  const shouldAnimate = index === 0 && isActive && newChatId

  const navigateToChat = useNavigateToChat()

  if (!chat.id) return null

  return (
    <motion.div
      className="relative h-8"
      variants={{
        initial: {
          height: 0,
          opacity: 0,
        },
        animate: {
          height: "auto",
          opacity: 1,
        },
      }}
      initial={shouldAnimate ? "initial" : undefined}
      animate={shouldAnimate ? "animate" : undefined}
      transition={{
        duration: 0.25,
        ease: "easeIn",
      }}
    >
      <div className="absolute left-2 top-1 flex size-6 items-center justify-center">
        <MessageSquareIcon className="mr-2 mt-1 size-4 text-zinc-500" />
      </div>
      <SheetClose asChild>
        <Button
          onClick={() => navigateToChat(chat.id)}
          variant="ghost"
          className={cn(
            "group w-full px-8 text-left transition-colors hover:bg-zinc-200/40 dark:hover:bg-zinc-300/10",
            isActive && "bg-zinc-200 pr-16 font-semibold dark:bg-zinc-800"
          )}
        >
          <div
            className="relative max-h-5 flex-1 select-none overflow-hidden text-ellipsis break-all"
            title={chat.title}
          >
            <span className="whitespace-nowrap">
              {shouldAnimate ? (
                chat.title.split("").map((character, index) => (
                  <motion.span
                    key={index}
                    variants={{
                      initial: {
                        opacity: 0,
                        x: -100,
                      },
                      animate: {
                        opacity: 1,
                        x: 0,
                      },
                    }}
                    initial={shouldAnimate ? "initial" : undefined}
                    animate={shouldAnimate ? "animate" : undefined}
                    transition={{
                      duration: 0.25,
                      ease: "easeIn",
                      delay: index * 0.05,
                      staggerChildren: 0.05,
                    }}
                    onAnimationComplete={() => {
                      if (index === chat.title.length - 1) {
                        setNewChatId(null)
                      }
                    }}
                  >
                    {character}
                  </motion.span>
                ))
              ) : (
                <span>{chat.title}</span>
              )}
            </span>
          </div>
        </Button>
      </SheetClose>
      {isActive && <div className="absolute right-2 top-1">{children}</div>}
    </motion.div>
  )
}
