/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC } from "react"
import { PlusIcon } from "lucide-react"

import { Button } from "@kreios/ui/button"
import { SheetClose } from "@kreios/ui/sheet"

import { useNavigateToChat } from "./sidebar"

export const NewChatButton: FC = () => {
  const navigateToChat = useNavigateToChat()
  return (
    <SheetClose asChild>
      <Button
        onClick={() => navigateToChat()}
        variant="outline"
        className="h-10 w-full justify-start bg-zinc-50 px-4 text-left shadow-none transition-colors hover:bg-zinc-200/40 dark:bg-zinc-900 dark:hover:bg-zinc-300/10"
      >
        <PlusIcon className="-translate-x-2 stroke-2" />
        New Chat
      </Button>
    </SheetClose>
  )
}
