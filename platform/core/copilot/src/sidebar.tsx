/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC, ReactNode, TransitionStartFunction } from "react"
import { createContext, useCallback, useContext, useMemo, useTransition } from "react"
import Cookies from "js-cookie"
import { PanelLeftIcon, XIcon } from "lucide-react"
import { useRouter } from "nextjs-toploader/app"

import { Button } from "@kreios/ui/button"
import { DrawerClose } from "@kreios/ui/drawer"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@kreios/ui/sheet"
import { Skeleton } from "@kreios/ui/skeleton"

interface SidebarProps {
  children: React.ReactNode
}

const CopilotTransitionContext = createContext<{
  isPending: boolean
  startTransition: TransitionStartFunction
}>({
  isPending: false,
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  startTransition: () => {},
})

export const CopilotTransitionProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [isPending, startTransition] = useTransition()

  return (
    <CopilotTransitionContext.Provider
      value={useMemo(() => ({ isPending, startTransition }), [isPending, startTransition])}
    >
      {children}
    </CopilotTransitionContext.Provider>
  )
}

/**
 * Custom hook that returns a function to navigate to a specific chat
 * If chatId is null, it will create a new chat
 * @returns
 */
export const useNavigateToChat = () => {
  const router = useRouter()
  const { startTransition } = useContext(CopilotTransitionContext)

  return useCallback(
    (chatId?: string | null) =>
      startTransition(() => {
        if (chatId) Cookies.set("copilotchatid", chatId)
        else Cookies.remove("copilotchatid")
        router.refresh()
      }),
    [router, startTransition]
  )
}

export function Sidebar({ children }: SidebarProps) {
  const { isPending } = useContext(CopilotTransitionContext)
  return (
    <Sheet>
      <div className="flex items-center justify-between p-4">
        {isPending ? (
          <Skeleton className="inline-block h-5 w-[200px]" />
        ) : (
          <h3 className="text-lg font-semibold text-foreground">Current chat</h3>
        )}
        <div className="flex gap-2">
          <DrawerClose asChild>
            <Button variant="ghost" className="flex size-9 p-0">
              <XIcon className="size-6" />
              <span className="sr-only">Close Sidebar</span>
            </Button>
          </DrawerClose>
          <SheetTrigger asChild>
            <Button variant="ghost" className="flex size-9 p-0">
              <PanelLeftIcon className="size-6" />
              <span className="sr-only">Toggle Sidebar</span>
            </Button>
          </SheetTrigger>
        </div>
      </div>

      <SheetContent side="right" className="inset-y-0 flex h-auto w-[300px] flex-col p-0">
        <div className="flex h-full">{children}</div>
      </SheetContent>
    </Sheet>
  )
}
