/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import "server-only"

import type { OpenAIProvider } from "@ai-sdk/openai"
import type { ComponentPropsWithoutRef, FC, ReactNode } from "react"
import { createAzure } from "@ai-sdk/azure"
import { createOpenAI } from "@ai-sdk/openai"
import { createAI, createStreamableValue, getAIState, getMutableAIState, streamUI } from "ai/rsc"

import { auth } from "@kreios/auth"
import { DrawerContent, DrawerTrigger } from "@kreios/ui/drawer"
import { ResponsiveDrawer } from "@kreios/ui/responsive-drawer"
import { nanoid } from "@kreios/utils/nanoid"

import type { AIState, Chat, UIState } from "./types"
import { env } from "../env"
import { ChatHistory } from "./chat-history"
import { enableCopilot } from "./flags"
import { BotMessage, SpinnerMessage, UserMessage } from "./messages"
import { getUserChat, saveChat } from "./persistance"
import { CopilotTransitionProvider, Sidebar } from "./sidebar"

const model = env.OPENAI_API_BASE_URL?.includes("microsoft")
  ? (createAzure({
      apiKey: env.OPENAI_API_KEY,
      baseURL: env.OPENAI_API_BASE_URL,
    }) as OpenAIProvider)
  : createOpenAI({
      apiKey: env.OPENAI_API_KEY,
      baseURL: env.OPENAI_API_BASE_URL,
    })

async function submitUserMessage(content: string): Promise<{
  id: string
  display: ReactNode
}> {
  "use server"

  const aiState = getMutableAIState<typeof AI>()

  aiState.update({
    ...aiState.get(),
    messages: [
      ...aiState.get().messages,
      {
        id: nanoid(),
        role: "user",
        content,
      },
    ],
  })

  let textStream: undefined | ReturnType<typeof createStreamableValue<string>>
  let textNode: undefined | React.ReactNode

  const result = await streamUI({
    model: model("gpt-4o"),
    initial: <SpinnerMessage />,
    messages: [
      {
        role: "system",
        content: `\
You are an integrated bot of the Impactly platform. You should assist the user with an requests to the functionality or data of the platform.`,
        // Messages inside [] means that it's a UI element or a user event. For example:
        // - "[Price of AAPL = 100]" means that an interface of the stock price of AAPL is shown to the user.
        // - "[User has changed the amount of AAPL to 10]" means that the user has changed the amount of AAPL to 10 in the UI.

        // If the user requests purchasing a stock, call \`show_stock_purchase_ui\` to show the purchase UI.
        // If the user just wants the price, call \`show_stock_price\` to show the price.
        // If you want to show trending stocks, call \`list_stocks\`.
        // If you want to show events, call \`get_events\`.
        // If the user wants to sell stock, or complete another impossible task, respond that you are a demo and cannot do that.

        // Besides that, you can also chat with users and do some calculations if needed.`,
      },
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      ...aiState.get().messages.map((message) => ({
        role: message.role,
        content: message.content,
        name: message.name,
      })),
    ],
    text: ({ content, done, delta }) => {
      if (!textStream) {
        textStream = createStreamableValue("")
        textNode = <BotMessage content={textStream.value} />
      }

      if (done) {
        textStream.done()
        aiState.done({
          ...aiState.get(),
          messages: [
            ...aiState.get().messages,
            {
              id: nanoid(),
              role: "assistant",
              content,
            },
          ],
        })
      } else {
        textStream.update(delta)
      }

      return textNode
    },
    // functions: {
    // listStocks: {
    //   description: "List three imaginary stocks that are trending.",
    //   parameters: z.object({
    //     stocks: z.array(
    //       z.object({
    //         symbol: z.string().describe("The symbol of the stock"),
    //         price: z.number().describe("The price of the stock"),
    //         delta: z.number().describe("The change in price of the stock"),
    //       }),
    //     ),
    //   }),
    //   render: async function* ({ stocks }) {
    //     yield (
    //       <BotCard>
    //         <StocksSkeleton />
    //       </BotCard>
    //     );
    //     await delay(1000);
    //     aiState.done({
    //       ...aiState.get(),
    //       messages: [
    //         ...aiState.get().messages,
    //         {
    //           id: nanoid(),
    //           role: "function",
    //           name: "listStocks",
    //           content: JSON.stringify(stocks),
    //         },
    //       ],
    //     });
    //     return (
    //       <BotCard>
    //         <Stocks props={stocks} />
    //       </BotCard>
    //     );
    //   },
    // },
    // showStockPrice: {
    //   description:
    //     "Get the current stock price of a given stock or currency. Use this to show the price to the user.",
    //   parameters: z.object({
    //     symbol: z
    //       .string()
    //       .describe("The name or symbol of the stock or currency. e.g. DOGE/AAPL/USD."),
    //     price: z.number().describe("The price of the stock."),
    //     delta: z.number().describe("The change in price of the stock"),
    //   }),
    //   render: async function* ({ symbol, price, delta }) {
    //     yield (
    //       <BotCard>
    //         <StockSkeleton />
    //       </BotCard>
    //     );
    //     await delay(1000);
    //     aiState.done({
    //       ...aiState.get(),
    //       messages: [
    //         ...aiState.get().messages,
    //         {
    //           id: nanoid(),
    //           role: "function",
    //           name: "showStockPrice",
    //           content: JSON.stringify({ symbol, price, delta }),
    //         },
    //       ],
    //     });
    //     return (
    //       <BotCard>
    //         <Stock props={{ symbol, price, delta }} />
    //       </BotCard>
    //     );
    //   },
    // },
    // showStockPurchase: {
    //   description:
    //     "Show price and the UI to purchase a stock or currency. Use this if the user wants to purchase a stock or currency.",
    //   parameters: z.object({
    //     symbol: z
    //       .string()
    //       .describe("The name or symbol of the stock or currency. e.g. DOGE/AAPL/USD."),
    //     price: z.number().describe("The price of the stock."),
    //     numberOfShares: z
    //       .number()
    //       .describe(
    //         "The **number of shares** for a stock or currency to purchase. Can be optional if the user did not specify it.",
    //       ),
    //   }),
    //   render: async function* ({ symbol, price, numberOfShares = 100 }) {
    //     if (numberOfShares <= 0 || numberOfShares > 1000) {
    //       aiState.done({
    //         ...aiState.get(),
    //         messages: [
    //           ...aiState.get().messages,
    //           {
    //             id: nanoid(),
    //             role: "system",
    //             content: `[User has selected an invalid amount]`,
    //           },
    //         ],
    //       });
    //       return <BotMessage content={"Invalid amount"} />;
    //     }
    //     aiState.done({
    //       ...aiState.get(),
    //       messages: [
    //         ...aiState.get().messages,
    //         {
    //           id: nanoid(),
    //           role: "function",
    //           name: "showStockPurchase",
    //           content: JSON.stringify({
    //             symbol,
    //             price,
    //             numberOfShares,
    //           }),
    //         },
    //       ],
    //     });
    //     return (
    //       <BotCard>
    //         <Purchase
    //           props={{
    //             numberOfShares,
    //             symbol,
    //             price: +price,
    //             status: "requires_action",
    //           }}
    //         />
    //       </BotCard>
    //     );
    //   },
    // },
    // getEvents: {
    //   description:
    //     "List funny imaginary events between user highlighted dates that describe stock activity.",
    //   parameters: z.object({
    //     events: z.array(
    //       z.object({
    //         date: z.string().describe("The date of the event, in ISO-8601 format"),
    //         headline: z.string().describe("The headline of the event"),
    //         description: z.string().describe("The description of the event"),
    //       }),
    //     ),
    //   }),
    //   render: async function* ({ events }) {
    //     yield (
    //       <BotCard>
    //         <EventsSkeleton />
    //       </BotCard>
    //     );
    //     await delay(1000);
    //     aiState.done({
    //       ...aiState.get(),
    //       messages: [
    //         ...aiState.get().messages,
    //         {
    //           id: nanoid(),
    //           role: "function",
    //           name: "getEvents",
    //           content: JSON.stringify(events),
    //         },
    //       ],
    //     });
    //     return (
    //       <BotCard>
    //         <Events props={events} />
    //       </BotCard>
    //     );
    //   },
    // },
    // },
  })

  return {
    id: nanoid(),
    display: result.value,
  }
}

export const CopilotTrigger = async (props: ComponentPropsWithoutRef<typeof DrawerTrigger>) => {
  if (!(await enableCopilot())) return null
  return <DrawerTrigger {...props} />
}

/**
 * Function to create a new chat
 * @returns
 */
const createNewChat = () => {
  const id = nanoid()
  return { chatId: id, messages: [] }
}

export const AdminCopilotDrawer: FC<{ modal: ReactNode; children: ReactNode }> = async ({ children, modal }) => {
  if (!(await enableCopilot())) return <>{children}</>
  return (
    <ResponsiveDrawer direction="bottom md:right">
      <DrawerContent direction="right" className="left-auto h-full w-full max-w-screen-md">
        <CopilotTransitionProvider>
          <Sidebar>
            <ChatHistory />
          </Sidebar>
          {modal}
        </CopilotTransitionProvider>
      </DrawerContent>
      {children}
    </ResponsiveDrawer>
  )
}

/**
 * Function that either returns the current chat from the chat id in cookies or creates a new chat
 * @returns
 */
export const getChatOrCreateNew = async (chatId?: string | null): Promise<AIState> => {
  if (!chatId) return createNewChat()

  const chatOrError = await getUserChat(chatId)

  if (!chatOrError || "error" in chatOrError) return createNewChat()
  return { chatId: chatOrError.id, messages: chatOrError.messages }
}

export const AI = createAI<AIState, UIState, { submitUserMessage: typeof submitUserMessage }>({
  actions: {
    submitUserMessage,
    // confirmPurchase,
  },
  initialUIState: [],
  initialAIState: { chatId: nanoid(), messages: [] },
  onGetUIState: async () => {
    "use server"

    const session = await auth()

    if (!session) return

    const aiState = getAIState<typeof AI>()

    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (aiState) {
      const uiState = getUIStateFromAIState(aiState)
      return uiState
    }
  },
  onSetAIState: async ({ state }) => {
    "use server"

    const session = await auth()

    if (!session) return

    const { chatId, messages } = state

    const createdAt = new Date()
    const userId = session.user.id
    const title = messages.at(0)!.content.substring(0, 100)

    const chat: Chat = {
      id: chatId,
      title,
      userId,
      createdAt,
      messages,
    }

    await saveChat(chat)
  },
})

export const getUIStateFromAIState = (aiState: AIState) => {
  return aiState.messages
    .filter((message) => message.role !== "system")
    .map((message, index) => ({
      id: `${aiState.chatId}-${index}`,
      display:
        message.role === "function" ? null : message.role === "user" ? (
          <UserMessage>{message.content}</UserMessage>
        ) : (
          <BotMessage content={message.content} />
        ),
    }))
}
