/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import { Suspense } from "react"

import type { DataTableProps } from "./data-table-client"
import { DataTableClient } from "./data-table-client"
import { DataTableSkeleton } from "./data-table-skeleton"

/**
 * DataTable component with server-side rendering, client-side hydration & suspense
 */
export function DataTable<TData, TValue, TFacetMode extends "toolbar" | "sidebar" = "toolbar">(
  props: DataTableProps<TData, TValue, TFacetMode>
) {
  return (
    <Suspense fallback={<DataTableSkeleton columns={props.columns} facetMode={props.facetMode} />}>
      <DataTableClient {...props} />
    </Suspense>
  )
}

export { useDataTableContext } from "./data-table-context"
