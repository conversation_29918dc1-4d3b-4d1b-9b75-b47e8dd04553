/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ComponentProps } from "react"
import { useEffect, useState } from "react"
import { addDays, subSeconds } from "date-fns"
import { useDebouncedCallback } from "use-debounce"

import type { DateRange } from "@kreios/ui/date-range-picker"
import type { RangeInputProps } from "@kreios/ui/range-input"
import { cn } from "@kreios/ui"
import { DatePickerWithRange } from "@kreios/ui/date-range-picker"
import { Label } from "@kreios/ui/label"
import { MultiCombobox } from "@kreios/ui/multi-combobox"
import RangeInput from "@kreios/ui/range-input"
import { Slider } from "@kreios/ui/slider"

import { useDataTableContext } from "./data-table-context"

export const DataTableDateRangeFilter = <TData,>({
  column,
  ...props
}: { column: string } & Omit<ComponentProps<typeof DatePickerWithRange>, "onChange" | "dateRange">) => {
  const table = useDataTableContext<TData>()
  const columnRef = table.getColumn(column)

  if (!columnRef)
    throw new Error(
      `Column ${column} not found. Available columns: ${table
        .getAllColumns()
        .map((c) => c.id)
        .join(", ")}`
    )

  if (!columnRef.getIsVisible()) return null

  return (
    <DatePickerWithRange
      size="sm"
      dateRange={columnRef.getFilterValue() as DateRange | undefined}
      onChange={(range) => {
        const rangeWithFullLastDay = {
          ...range,
          // when we select range from 1st - 2nd, we want to select the full day of the 2nd
          to: range?.to && addDays(subSeconds(range.to, 1), 1),
        }
        columnRef.setFilterValue(rangeWithFullLastDay)
      }}
      label={(columnRef.columnDef as unknown as { title: string | undefined }).title}
      {...props}
    />
  )
}

export const DataTableRangeInputFilter = <TData,>({
  column,
  valueFormat,
}: { column: string } & Pick<RangeInputProps, "valueFormat">) => {
  const table = useDataTableContext<TData>()
  const columnRef = table.getColumn(column)

  if (!columnRef)
    throw new Error(
      `Column ${column} not found. Available columns: ${table
        .getAllColumns()
        .map((c) => c.id)
        .join(", ")}`
    )

  const columnFilterValue = (columnRef.getFilterValue() ?? [null, null]) as [number | null, number | null]
  const [min, max] = columnFilterValue

  if (!columnRef.getIsVisible()) return null

  return (
    <RangeInput
      value={{ min, max }}
      onChange={(v) => {
        columnRef.setFilterValue(v ? [v.min, v.max] : undefined)
      }}
      label={(columnRef.columnDef as unknown as { title: string | undefined }).title}
      valueFormat={valueFormat}
    />
  )
}

/**
 * Number range Picker component to be used as a filter for datatable column
 */
export const DataTableSliderFilter = <TData,>({
  className,
  column,
  defaultValue = [0, 100],
  ...props
}: {
  column: string
  className?: string
  defaultValue?: [number, number]
} & Omit<ComponentProps<typeof Slider>, "value" | "onValueChange" | "defaultValue">) => {
  const table = useDataTableContext<TData>()
  const columnRef = table.getColumn(column)

  if (!columnRef)
    throw new Error(
      `Column ${column} not found. Available columns: ${table
        .getAllColumns()
        .map((c) => c.id)
        .join(", ")}`
    )

  // debounce changes to the datatable column filter value to prevent unnecessary refetching
  const setColumnFilterValue = useDebouncedCallback((value: number[]) => columnRef.setFilterValue(value), 300)
  const columnFilterValue = columnRef.getFilterValue() as number[] | undefined

  // local state to store the slider value
  const [range, setRange] = useState<number[]>(defaultValue)

  // override the local state if the column filter value is changed externally for example reset
  useEffect(() => {
    setRange(columnFilterValue ?? defaultValue)
  }, [columnFilterValue, defaultValue])

  if (!columnRef.getIsVisible()) return null

  return (
    <div className={cn("flex h-8 w-56 flex-col justify-between", className)}>
      <Label className="text-xs" htmlFor={`${column}-slider`}>
        {(columnRef.columnDef as unknown as { title: string | undefined }).title}
      </Label>
      <Slider
        id={`${column}-slider`}
        defaultValue={defaultValue}
        value={range}
        showTooltip
        onValueChange={(value) => {
          setRange(value)
          setColumnFilterValue(value)
        }}
        {...props}
      />
    </div>
  )
}

/**
 * Multi select component to be used as a filter for datatable column
 */
export const DataTableMultiSelectFilter = <TData,>({
  column,
  ...props
}: { column: string } & Omit<ComponentProps<typeof MultiCombobox>, "value" | "onValueChange">) => {
  const table = useDataTableContext<TData>()
  const columnRef = table.getColumn(column)

  if (!columnRef)
    throw new Error(
      `Column ${column} not found. Available columns: ${table
        .getAllColumns()
        .map((c) => c.id)
        .join(", ")}`
    )

  if (!columnRef.getIsVisible()) return null

  return (
    <MultiCombobox
      className="max-h-[34px] min-h-[34px] w-72 bg-background"
      onValueChange={(value) => columnRef.setFilterValue(value)}
      value={columnRef.getFilterValue() as string[]}
      placeholder={`Select ${(columnRef.columnDef as unknown as { title: string | undefined }).title}`}
      variant="default"
      {...props}
    />
  )
}
