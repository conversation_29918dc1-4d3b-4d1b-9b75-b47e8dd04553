/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { Column } from "@tanstack/react-table"
import * as React from "react"
import { CheckIcon, PlusCircledIcon } from "@radix-ui/react-icons"
import { useVirtualizer } from "@tanstack/react-virtual"

import { cn } from "@kreios/ui"
import { Badge } from "@kreios/ui/badge"
import { Button } from "@kreios/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@kreios/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@kreios/ui/popover"
import { Separator } from "@kreios/ui/separator"

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>
  title?: string
  icon?: React.ReactNode
  options: {
    label: string
    value: string | number
    count?: number
    icon?: React.ComponentType<{ className?: string }>
  }[]
}

export function DataTableFacetedFilter<TData, TValue>({
  column,
  icon = <PlusCircledIcon className="mr-2 size-4" />,
  title,
  options,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const selectedValues = new Set(column?.getFilterValue() as (string | number)[])

  const facets = column?.getFacetedUniqueValues()
  const [mounted, setContentMounted] = React.useState(false)
  const parentRef = React.useRef<HTMLDivElement>(null)
  const [search, setSearch] = React.useState("")

  // Filter options based on search
  const filteredOptions = React.useMemo(() => {
    if (!search.trim()) return options
    const searchLower = search.toLowerCase().trim()
    return options.filter((option) => option.label.toLowerCase().includes(searchLower))
  }, [options, search])

  const virtualizer = useVirtualizer({
    getScrollElement: () => parentRef.current,
    estimateSize: () => 35,
    overscan: 20,
    count: filteredOptions.length,
    enabled: mounted,
  })

  React.useEffect(() => {
    virtualizer.measure()
  }, [filteredOptions, virtualizer])

  // Reset scroll position and force recalculation when search changes
  React.useEffect(() => {
    if (parentRef.current) {
      parentRef.current.scrollTop = 0
    }
    virtualizer.scrollToIndex(0)
    virtualizer.measure()
  }, [search, virtualizer])

  const handleSelect = (value: string | number) => {
    const newSelected = new Set(selectedValues)
    if (newSelected.has(value)) {
      newSelected.delete(value)
    } else {
      newSelected.add(value)
    }

    column?.setFilterValue(newSelected.size ? Array.from(newSelected) : undefined)
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className={cn("h-8", selectedValues.size === 0 && "text-muted-foreground")}>
          {/* Icons for filters can be defined in the column definition using the meta property */}
          {column?.columnDef.meta?.icon ? <column.columnDef.meta.icon className="mr-2 size-4" /> : icon}
          {title}
          {selectedValues.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge variant="secondary" className="rounded-sm px-1 font-normal lg:hidden">
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                    {selectedValues.size} selected
                  </Badge>
                ) : (
                  options
                    .filter((option) => selectedValues.has(option.value))
                    .map((option) => (
                      <Badge variant="secondary" key={option.value} className="rounded-sm px-1 font-normal">
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[250px] p-0"
        align="start"
        onOpenAutoFocus={() => setContentMounted(true)}
        onCloseAutoFocus={() => {
          setContentMounted(false)
          setSearch("")
        }}
      >
        <Command shouldFilter={false}>
          <CommandInput placeholder={title} value={search} onValueChange={setSearch} />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <div
              ref={parentRef}
              style={{
                height: "300px",
                overflow: "auto",
              }}
            >
              <CommandGroup>
                <div
                  style={{
                    height: `${virtualizer.getTotalSize()}px`,
                    width: "100%",
                    position: "relative",
                  }}
                >
                  {virtualizer.getVirtualItems().map((virtualRow) => {
                    const option = filteredOptions[virtualRow.index]
                    const isSelected = selectedValues.has(option.value)

                    return (
                      <CommandItem
                        key={virtualRow.key}
                        onSelect={() => handleSelect(option.value)}
                        style={{
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "100%",
                          height: `${virtualRow.size}px`,
                          transform: `translateY(${virtualRow.start}px)`,
                        }}
                      >
                        <div
                          className={cn(
                            "mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
                            isSelected ? "bg-primary text-primary-foreground" : "opacity-50 [&_svg]:invisible"
                          )}
                        >
                          <CheckIcon className={cn("h-4 w-4")} />
                        </div>
                        {option.icon && <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />}
                        <span className="overflow-hidden text-ellipsis whitespace-nowrap">{option.label}</span>
                        {facets?.get(option.value) && (
                          <span className="ml-auto flex items-center justify-center font-mono text-xs">
                            {facets.get(option.value)}
                          </span>
                        )}
                      </CommandItem>
                    )
                  })}
                </div>
              </CommandGroup>
            </div>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      column?.setFilterValue(undefined)
                    }}
                    className="justify-center text-center"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
