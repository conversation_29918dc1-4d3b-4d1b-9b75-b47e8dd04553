/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { Table } from "@tanstack/react-table"
import type { ReactNode } from "react"
import { Cross2Icon } from "@radix-ui/react-icons"
import { useDebouncedCallback } from "use-debounce"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import { Input } from "@kreios/ui/input"

import { DataTableFacetedFilter } from "./data-table-faceted-filter"
import { DataTableViewOptions } from "./data-table-view-options"

// An individual filter configuration
export interface FilterConfig {
  column: string // The column to apply the filter on
  title: string // The title of the filter to be displayed
  icon?: ReactNode
  options: {
    label: string
    value: string | number
    count?: number
  }[] // Options for the filter
}

// All props for the DataTableToolbar component
interface DataTableToolbarProps<TData> {
  table: Table<TData>
  filters: FilterConfig[]
  children?: ReactNode
  /** Placeholder for the search input */
  searchPlaceholder?: string
  /** Class name for the search input */
  searchClassName?: string
  /** Custom callback, fired on change table search string, optional **/
  onTableSearchChange?: (value: string) => void
}

// The DataTableToolbar component with generic type TData
export function DataTableToolbar<TData>({
  table,
  filters,
  children,
  searchPlaceholder,
  searchClassName,
  onTableSearchChange,
}: DataTableToolbarProps<TData>) {
  // Check if any column filters are currently applied
  const isFiltered = table.getState().columnFilters.length > 0

  const setGlobalFilter = useDebouncedCallback((value: string) => {
    table.setGlobalFilter(value)
    onTableSearchChange?.(value)
  }, 300)

  return (
    <div className="flex items-center justify-between">
      {/* Filter input and dynamic filters section */}
      <div className="flex flex-1 flex-wrap items-center gap-2">
        {/* Text input for filtering tasks by title */}
        <Input
          placeholder={searchPlaceholder ?? "Search..."}
          // value={(table.glo0bal as string) ?? ""}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className={cn("h-8 w-[150px] bg-background lg:w-[250px]", searchClassName)}
        />

        {/* Dynamic rendering of faceted filters based on the provided filters prop */}
        {filters.map((filter) => {
          const column = table.getColumn(filter.column)

          // don't display the filter if the column or filter is not visible
          if (!column?.getIsVisible() || column.columnDef.meta?.hideFilter) return null

          return (
            <DataTableFacetedFilter
              key={filter.column} // Use the column name as a key for React's list rendering
              column={column}
              icon={filter.icon}
              title={filter.title}
              options={filter.options}
            />
          )
        })}

        {children}

        {/* Button to reset all column filters, shown only if any filter is active */}
        {isFiltered && (
          <Button variant="ghost" onClick={() => table.resetColumnFilters()} className="h-8 px-2 lg:px-3">
            Reset
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Component for handling view options, unrelated to filtering */}
      <DataTableViewOptions table={table} />
    </div>
  )
}
