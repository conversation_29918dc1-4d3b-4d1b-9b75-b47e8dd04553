/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC } from "react"
import { createContext, useContext } from "react"
import Cookies from "js-cookie"

import { DataTableCookies } from "./data-table-cookie-config"

/**
 * Inling the type signature of the cookie object since we don't want to import it from next/headers
 */
type RequestCookie = {
  /** A string with the name of a cookie. */
  name: string
  /** A string containing the value of the cookie. */
  value: string
}

/**
 * Context used to provide the cookies to the DataTable component.
 */
const DataTableCookieContext = createContext<RequestCookie[]>([])

export const DataTableCookieProviderInternal: FC<{
  cookies: RequestCookie[]
  children: React.ReactNode
}> = ({ cookies, children }) => {
  return <DataTableCookieContext.Provider value={cookies}>{children}</DataTableCookieContext.Provider>
}

/**
 * Hook used to get the value of a cookie.
 */
export const useDataTableCookie = <T extends keyof typeof DataTableCookies>(key: T) => {
  const cookieKey = DataTableCookies[key]
  const cookies = useContext(DataTableCookieContext)
  return cookies.find((cookie) => cookie.name === cookieKey)?.value ?? Cookies.get(cookieKey)
}
