/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { Column, Table } from "@tanstack/react-table"
import type { ReactNode } from "react"

import { Checkbox } from "@kreios/ui/checkbox"
import { Label } from "@kreios/ui/label"
import { ScrollArea } from "@kreios/ui/scroll-area"

import type { FilterConfig } from "./data-table-toolbar"

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>
  title?: string
  icon?: React.ReactNode
  options: {
    label: string
    value: string | number
    count?: number
  }[]
}

const DataTableSidebarFacetFilter = <TData, TValue>({
  column,
  title,
  options,
}: DataTableFacetedFilterProps<TData, TValue>) => {
  const facets = column?.getFacetedUniqueValues()
  const selectedValues = new Set(column?.getFilterValue() as (string | number)[])

  return (
    <div className="flex flex-col gap-2">
      <Label className="text-base">{title}</Label>

      {options.map((option) => (
        <div key={option.value} className="flex flex-row items-start gap-3">
          <Checkbox
            id={`${column?.id}-${option.value}`}
            checked={selectedValues.has(option.value)}
            onCheckedChange={(checked) => {
              if (checked) selectedValues.add(option.value)
              else selectedValues.delete(option.value)

              const filterValues = Array.from(selectedValues)
              column?.setFilterValue(filterValues.length ? filterValues : undefined)
            }}
          />
          <Label htmlFor={`${column?.id}-${option.value}`}>
            {option.label} ({facets?.get(option.value) ?? 0})
          </Label>
        </div>
      ))}
    </div>
  )
}

interface DataTableSidebarProps<TData> {
  table: Table<TData>
  filters: FilterConfig[]
  title: ReactNode
}

export const DataTableSidebar = <TData,>({ filters, table, title }: DataTableSidebarProps<TData>) => {
  return (
    <div className="h-full min-w-60">
      <div className="flex items-center px-4">{title}</div>

      <ScrollArea className="h-[calc(100vh-9.4rem)]">
        <div className="p-4">
          <form className="flex flex-col gap-6">
            {filters.map((filter) => {
              const column = table.getColumn(filter.column)
              return (
                column && (
                  <DataTableSidebarFacetFilter
                    key={filter.column} // Use the column name as a key for React's list rendering
                    column={column}
                    icon={filter.icon}
                    title={filter.title}
                    options={filter.options}
                  />
                )
              )
            })}
          </form>
        </div>
      </ScrollArea>
    </div>
  )
}
