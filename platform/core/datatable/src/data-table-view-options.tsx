/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { Table } from "@tanstack/react-table"
import type { FC, SVGProps } from "react"
import { useCallback, useState } from "react"
import { ColumnsIcon, MixerHorizontalIcon } from "@radix-ui/react-icons"
import Cookies from "js-cookie"
import { useTranslations } from "next-intl"

import { cn } from "@kreios/ui"
import { Button } from "@kreios/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@kreios/ui/dropdown-menu"

import { DataTableCookies } from "./data-table-cookie-config"

/**
 * Lucidcon not part of the package therfore inlined
 */
const RotateCcwIcon: FC<SVGProps<SVGSVGElement>> = ({ className, ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={cn("lucide lucide-rotate-ccw", className)}
    {...props}
  >
    <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
    <path d="M3 3v5h5" />
  </svg>
)

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>
  className?: string
}

export function DataTableViewOptions<TData>({ table, className }: DataTableViewOptionsProps<TData>) {
  const [menuOpen, setMenuOpen] = useState(false)
  const hasChangedVisibility = table.getAllColumns().some((column) => {
    const visible = column.columnDef.meta?.visible == false ? false : true
    return visible !== column.getIsVisible()
  })

  const hasHiddenColumns = table.getAllColumns().some((column) => !column.getIsVisible())
  const t = useTranslations("common.table")
  const viewColumns = t("viewColumns")
  const toggleColumns = t("toggleColumns")
  const showAll = t("showAllColumns")
  const resetToDefault = t("resetToDefault")

  const handleCloseMenu = useCallback(() => {
    setMenuOpen(false)
  }, [])

  return (
    <DropdownMenu open={menuOpen}>
      <DropdownMenuTrigger asChild onClick={() => setMenuOpen(true)}>
        <Button variant="outline" size="sm" className={cn("h-8 self-end", className)}>
          <MixerHorizontalIcon className="mr-2 h-4 w-4" />
          {viewColumns}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-56"
        onEscapeKeyDown={handleCloseMenu}
        onPointerDownOutside={handleCloseMenu}
      >
        <DropdownMenuLabel>{toggleColumns}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup className="max-h-[calc(var(--radix-dropdown-menu-content-available-height)-75px)] overflow-auto">
          {table
            .getAllColumns()
            .filter((column) => typeof column.accessorFn !== "undefined" && column.getCanHide())
            .map((column) => {
              return (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={(value) => column.toggleVisibility(!!value)}
                >
                  {/* @ts-expect-error since we check if column.title is available this is fine */}
                  {column.columnDef.title ?? column.id}
                </DropdownMenuCheckboxItem>
              )
            })}
        </DropdownMenuGroup>
        {(hasChangedVisibility || hasHiddenColumns) && <DropdownMenuSeparator />}
        {hasHiddenColumns && (
          <DropdownMenuItem
            onSelect={() => {
              table.toggleAllColumnsVisible(true)
            }}
          >
            <ColumnsIcon className="mr-2 h-4 w-4" />
            <span>{showAll}</span>
          </DropdownMenuItem>
        )}

        {/* only display reset to default if the user has changed the visibility of at least one column */}
        {hasChangedVisibility && (
          <DropdownMenuItem
            onSelect={() => {
              Cookies.remove(DataTableCookies.columnVisibility)
              table.resetColumnVisibility()
            }}
          >
            <RotateCcwIcon />
            <span>{resetToDefault}</span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
