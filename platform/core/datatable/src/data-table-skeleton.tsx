/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC } from "react"

import { Skeleton } from "@kreios/ui/skeleton"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

export const DataTableSkeleton: FC<{
  searchableColumnCount?: number
  rowCount?: number
  filters?: unknown[] | number
  columns?: unknown[] | number
  facetMode?: "sidebar" | "toolbar"
}> = ({ rowCount = 10, searchableColumnCount = 1, filters = [1], columns = [1], facetMode = "toolbar" }) => {
  const columnCount = typeof columns === "number" ? columns : columns.length
  const filterableColumnCount = typeof filters === "number" ? filters : filters.length

  return (
    <>
      {facetMode === "sidebar" && (
        <div className="h-full min-w-60">
          <div className="flex items-center px-4">
            <h1 className="text-xl font-bold">Filter</h1>
          </div>

          <div className="h-[calc(100vh-9.4rem)]">
            <div className="p-4">
              <div className="flex flex-col gap-6">
                {Array.from({ length: 4 })
                  .fill(0)
                  .map((__, index) => (
                    <div key={index} className="space-y-2">
                      <Skeleton className="h-6 w-24" />
                      <Skeleton className="h-4 w-52" />
                      <Skeleton className="h-4 w-52" />
                      <Skeleton className="h-4 w-52" />
                      <Skeleton className="h-4 w-52" />
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="w-full space-y-3 overflow-auto">
        <div className="flex w-full items-center justify-between space-x-2 overflow-auto p-1">
          <div className="flex flex-1 items-center space-x-2">
            {searchableColumnCount > 0
              ? Array.from({ length: searchableColumnCount }).map((_, i) => (
                  <Skeleton key={i} className="h-7 w-[150px] lg:w-[250px]" />
                ))
              : null}
            {facetMode === "toolbar" && filterableColumnCount > 0
              ? Array.from({ length: filterableColumnCount }).map((_, i) => (
                  <Skeleton key={i} className="h-7 w-[70px] border-dashed" />
                ))
              : null}
          </div>
          <Skeleton className="ml-auto hidden h-7 w-[70px] lg:flex" />
        </div>
        <div className="rounded-md border bg-background p-1">
          <Table>
            <TableHeader>
              {Array.from({ length: 1 }).map((_, i) => (
                <TableRow key={i} className="hover:bg-transparent">
                  {Array.from({ length: columnCount }).map((_, i) => (
                    <TableHead key={i}>
                      <Skeleton className="h-6 w-full" />
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {Array.from({ length: rowCount }).map((_, i) => (
                <TableRow key={i} className="hover:bg-transparent">
                  {Array.from({ length: columnCount }).map((_, i) => (
                    <TableCell key={i}>
                      <Skeleton className="h-6 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="flex w-full flex-col items-center justify-between gap-4 overflow-auto px-2 py-1 sm:flex-row sm:gap-8">
          <div className="flex-1">
            <Skeleton className="h-8 w-40" />
          </div>
          <div className="flex flex-col items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-8 w-[70px]" />
            </div>
            <div className="flex items-center justify-center text-sm font-medium">
              <Skeleton className="h-8 w-20" />
            </div>
            <div className="flex items-center space-x-2">
              <Skeleton className="hidden h-8 w-8 lg:block" />
              <Skeleton className="h-8 w-8" />
              <Skeleton className="h-8 w-8" />
              <Skeleton className="hidden h-8 w-8 lg:block" />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
