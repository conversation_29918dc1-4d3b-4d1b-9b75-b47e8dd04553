/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type {
  ColumnDef,
  ColumnFiltersState,
  ColumnMeta,
  OnChangeFn,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table"
import type { QueryLike } from "@trpc/react-query/shared"
import type { TRPCQueryProcedure } from "@trpc/server"
import type { AnyRootTypes } from "@trpc/server/unstable-core-do-not-import"
import type { FC } from "react"
import * as React from "react"
import { useMemo, useRef } from "react"
import { flexRender, getCoreRowModel, getSortedRowModel, useReactTable } from "@tanstack/react-table"
import Cookies from "js-cookie"
import { useTranslations } from "next-intl"
import { useSpinDelay } from "spin-delay"
import { useDeepCompareMemo } from "use-deep-compare"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

import type { FilterConfig } from "./data-table-toolbar"
import { DataTableContextProvider } from "./data-table-context"
import { DataTableCookies } from "./data-table-cookie-config"
import { useDataTableCookie } from "./data-table-cookie-provider-client"
import { DataTablePagination } from "./data-table-pagination"
import { DataTableSidebar } from "./data-table-sidebar"
import { DataTableToolbar } from "./data-table-toolbar"

export type DataTableAction<TData> = (props: {
  page: number
  limit: number
  filters?: Record<string, unknown>
  search?: string
  sort?: Record<string, "asc" | "desc">
}) => Promise<{ data: TData[]; count: number; facets: FilterConfig[] }>

type DataTableQueryProcedure<TData> = QueryLike<
  AnyRootTypes,
  TRPCQueryProcedure<{
    input: {
      page: number
      limit: number
      filters?: Record<string, unknown>
      search?: string
      sort?: Record<string, "asc" | "desc">
    }
    output: { data: TData[]; count: number; facets: FilterConfig[] }
  }>
>

export type DataTableProps<TData, TValue, TFacetMode extends "toolbar" | "sidebar" = "toolbar"> = {
  /** DataTable columns array (must be client side) */
  columns: ColumnDef<TData, TValue>[]
  /** React nodes to render inside the Datatable toolbar */
  toolbar?: React.ReactNode
  /** TRPC Query procedure to fetch data */
  procedure: DataTableQueryProcedure<TData>

  renderRow?: (row: Row<TData>, node: React.ReactNode) => React.ReactNode

  facetMode?: TFacetMode
  sidebarTitle?: TFacetMode extends "sidebar" ? React.ReactNode : never
  /** Base filters that are always applied to the table */
  baseFilters?: ColumnFiltersState
  /** Initial sorting state */
  initialSorting?: SortingState
  /** Placeholder for the search input */
  searchPlaceholder?: string
  /** Class name for the search input */
  searchClassName?: string
  /** Field name to use as the unique row ID (instead of using auto-generated indices) */
  rowIdKey?: keyof TData
  /** Callback when rows are selected */
  onRowSelectionChange?: (rows: {
    selectedRows: Record<string, boolean> // Selection state
    selectedData: TData[] // Actual row data
  }) => void
  /**Selected rows value, passed from outside the table, optional**/
  externalSelectedRows?: RowSelectionState
}

/** Table sliding loading indicator */
const TableLoadingIndicator: FC<{ colSpan: number }> = ({ colSpan }) => (
  <TableRow>
    <TableCell className="absolute h-1 w-full overflow-hidden rounded-full p-0" colSpan={colSpan}>
      <div className="h-1 w-[12.5%] animate-slide bg-blue-500" />
    </TableCell>
  </TableRow>
)

export function DataTableClient<TData, TValue, TFacetMode extends "toolbar" | "sidebar" = "toolbar">({
  columns,
  procedure,
  toolbar,
  renderRow,
  // TODO: remove ts-expect-error if possible
  // @ts-expect-error facetMode is optional we want to default to toolbar
  facetMode = "toolbar",
  sidebarTitle,
  baseFilters = [],
  initialSorting = [],
  searchPlaceholder,
  searchClassName,
  rowIdKey,
  onRowSelectionChange,
  externalSelectedRows,
}: DataTableProps<TData, TValue, TFacetMode>) {
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: 20,
  })
  const [search, setSearch] = React.useState<string>("")
  const [rowSelection, setRowSelection] = React.useState<Record<string, boolean>>({})
  const cookie = useDataTableCookie("columnVisibility")

  // Create VisibilityState based on the columns meta.visible property
  const initialColumnVisibility = React.useMemo(
    () =>
      Object.fromEntries(
        // @ts-expect-error since we check if column.visible is available allow undefined is fine
        columns.map((column) => [column.id ?? column.accessorKey, column.meta?.visible])
      ) as VisibilityState,
    [columns]
  )

  const [columnVisibility, setColumnVisibilityInternal] = React.useState<VisibilityState>(() => {
    if (cookie) return JSON.parse(cookie) as VisibilityState

    return initialColumnVisibility
  })

  /**
   * Set the column visibility state and saves it value to cookies
   */
  const setColumnVisibility: OnChangeFn<VisibilityState> = React.useCallback((value) => {
    if (typeof value == "function")
      return setColumnVisibilityInternal((cur) => {
        const newValue = value(cur)
        Cookies.set(DataTableCookies.columnVisibility, JSON.stringify(newValue), {
          path: document.location.pathname,
        })

        return newValue
      })

    Cookies.set(DataTableCookies.columnVisibility, JSON.stringify(value), {
      path: document.location.pathname,
    })
    setColumnVisibilityInternal(value)
  }, [])

  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>(initialSorting)

  const input = useDeepCompareMemo(
    () => ({
      page: pagination.pageIndex + 1,
      limit: pagination.pageSize,
      search,
      filters: [...columnFilters, ...baseFilters].reduce((acc, { id, value }) => ({ ...acc, [id]: value }), {}),
      sort: sorting.reduce((acc, { id, desc }) => ({ ...acc, [id]: desc ? "desc" : "asc" }), {}),
    }),
    [pagination.pageIndex, pagination.pageSize, search, baseFilters, columnFilters, sorting]
  )

  const deferredInput = React.useDeferredValue(input)

  // eslint-disable-next-line react-compiler/react-compiler
  const [data] = procedure.useSuspenseQuery(deferredInput)

  const isSuspending = useSpinDelay(input !== deferredInput)

  const facets = useMemo(
    () =>
      Object.fromEntries(
        data.facets.map((facet) => [facet.column, new Map(facet.options.map((option) => [option.value, option.count]))])
      ),
    [data.facets]
  )

  const uniqueFacetRef = useRef(facets)
  uniqueFacetRef.current = facets

  const handleRowSelectionChange: OnChangeFn<typeof rowSelection> = (updater) => {
    const newSelection = typeof updater === "function" ? updater(externalSelectedRows ?? rowSelection) : updater
    setRowSelection(newSelection)

    // Get the selected row data
    const selectedData = table
      .getFilteredRowModel()
      .rows.filter((row) => newSelection[row.id])
      .map((row) => row.original)

    onRowSelectionChange?.({
      selectedRows: newSelection,
      selectedData: selectedData,
    })
  }

  const handlePaginationChange: OnChangeFn<PaginationState> = (updater) => {
    if (externalSelectedRows) handleRowSelectionChange({})
    setPagination(updater)
  }

  const handleSortingChange: OnChangeFn<SortingState> = (updater) => {
    if (externalSelectedRows) handleRowSelectionChange({})
    setSorting(updater)
  }

  const handleColumnFiltersChange: OnChangeFn<ColumnFiltersState> = (updater) => {
    if (externalSelectedRows) handleRowSelectionChange({})
    setColumnFilters(updater)
  }

  const table = useReactTable({
    data: data.data,
    columns,
    rowCount: data.count,
    // This is the state that is used when resetting the table
    initialState: {
      columnVisibility: initialColumnVisibility,
    },
    state: {
      globalFilter: search,
      sorting,
      columnVisibility,
      rowSelection: externalSelectedRows ?? rowSelection,
      columnFilters,
      pagination,
    },
    enableRowSelection: true,
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    enableGlobalFilter: true,

    // Use custom row IDs based on rowIdKey if provided
    getRowId: rowIdKey ? (row) => String(row[rowIdKey]) : undefined,

    // @ts-expect-error since we check if facet count is available allow undefined is fine
    getFacetedUniqueValues: (_, columnId) => () => uniqueFacetRef.current[columnId],
    onGlobalFilterChange: setSearch,
    onPaginationChange: handlePaginationChange,
    onRowSelectionChange: handleRowSelectionChange,
    onSortingChange: handleSortingChange,
    onColumnFiltersChange: handleColumnFiltersChange,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  })

  const t = useTranslations("common.table")

  return (
    <DataTableContextProvider table={table}>
      {facetMode === "sidebar" && (
        <DataTableSidebar
          title={sidebarTitle ?? <h1 className="text-xl font-bold">{t("filter")}</h1>}
          table={table}
          filters={data.facets}
        />
      )}
      <div className="h-full w-full space-y-4 overflow-visible">
        {toolbar !== false && (
          <DataTableToolbar
            searchPlaceholder={searchPlaceholder}
            searchClassName={searchClassName}
            table={table}
            filters={facetMode === "toolbar" ? data.facets : []}
          >
            {toolbar}
          </DataTableToolbar>
        )}
        <div className="rounded-md border bg-background p-1">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        align={header.column.columnDef.meta?.align}
                        key={header.id}
                        colSpan={header.colSpan}
                        style={{
                          maxWidth: (header.column.columnDef as ColumnMeta<TData, TValue>).maxWidth ?? "none",
                        }}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {/* Show sliding loading indicator for client-side fetches */}
              {isSuspending && <TableLoadingIndicator colSpan={columns.length} />}
              {table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => {
                  const node = (
                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          align={cell.column.columnDef.meta?.align}
                          key={cell.id}
                          style={{
                            maxWidth: (cell.column.columnDef as ColumnMeta<TData, TValue>).maxWidth ?? "none",
                          }}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  )

                  return renderRow ? renderRow(row, node) : node
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {t("noResults")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <DataTablePagination table={table} />
      </div>
    </DataTableContextProvider>
  )
}
