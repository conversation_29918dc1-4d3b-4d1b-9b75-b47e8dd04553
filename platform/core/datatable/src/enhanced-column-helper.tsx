/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import type {
  AccessorFn,
  AccessorFnColumnDef,
  AccessorKeyColumnDef,
  ColumnHelper,
  DeepKeys,
  DeepValue,
  DisplayColumnDef,
  IdentifiedColumnDef,
  RowData,
} from "@tanstack/react-table"
import { createColumnHelper } from "@tanstack/react-table"

import type { BadgeProps } from "@kreios/ui/badge"
import type { ISOString } from "@kreios/utils/date-to-iso-string"
import { cn } from "@kreios/ui"
import { Badge } from "@kreios/ui/badge"
import { Link } from "@kreios/ui/link"

import { DataTableColumnHeader } from "./data-table-column-header"
import { DateField } from "./fields"

declare module "@tanstack/react-table" {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    align?: "left" | "center" | "right"
    visible?: boolean
    /** Icon to be used in the faceted filter */
    icon?: React.ComponentType<{ className?: string }>
    maxWidth?: number
    hideFilter?: boolean
  }
}

type ExtractTValue<TData, TAccessor> =
  TAccessor extends AccessorFn<TData, infer TReturn>
    ? TReturn
    : TAccessor extends DeepKeys<TData>
      ? DeepValue<TData, TAccessor>
      : never

type DeepKeysOfType<TData, TType> = {
  [K in DeepKeys<TData>]: DeepValue<TData, K> extends TType ? K : never
}[DeepKeys<TData>]

type Accessor<TData> = AccessorFn<TData> | DeepKeys<TData>

type RestrictedAccessor<TData, TType> = AccessorFn<TData, TType> | DeepKeysOfType<TData, TType>

type Options<TAccessor, TData, TValue> = (TAccessor extends AccessorFn<TData>
  ? DisplayColumnDef<TData, TValue>
  : IdentifiedColumnDef<TData, TValue>) & {
  title: string
  align?: "left" | "center" | "right"
  visible?: boolean
  maxWidth?: number
  hideFilter?: boolean
}

type AccessorReturn<TAccessor, TData, TValue> =
  TAccessor extends AccessorFn<TData> ? AccessorFnColumnDef<TData, TValue> : AccessorKeyColumnDef<TData, TValue>

export type EnhancedColumnHelper<TData extends RowData> = ColumnHelper<TData> & {
  date: <
    TAccessor extends RestrictedAccessor<TData, Date | ISOString | undefined | null>,
    TValue extends Extract<ExtractTValue<TData, TAccessor>, Date | ISOString | undefined | null>,
  >(
    accessor: TAccessor,
    options: Options<TAccessor, TData, TValue> & { format?: string }
  ) => AccessorReturn<TAccessor, TData, TValue>

  link: <TAccessor extends Accessor<TData>, TValue extends ExtractTValue<TData, TAccessor>>(
    accessor: TAccessor,
    column: Options<TAccessor, TData, TValue> & {
      href: (row: TData) => string
      className?: string
      isDisabled?: (row: TData) => boolean
    }
  ) => AccessorReturn<TAccessor, TData, TValue>

  badge: <
    TAccessor extends RestrictedAccessor<TData, string | undefined | null>,
    TValue extends ExtractTValue<TData, TAccessor>,
  >(
    accessor: TAccessor,
    column: Options<TAccessor, TData, TValue> & {
      colorMap?: Record<NonNullable<TValue>, string>
      transform?: (value: NonNullable<TValue>) => string
      variant?: BadgeProps["variant"]
    }
  ) => AccessorReturn<TAccessor, TData, TValue>

  custom: <TAccessor extends Accessor<TData>, TValue extends ExtractTValue<TData, TAccessor>>(
    accessor: TAccessor,
    column: Options<TAccessor, TData, TValue>
  ) => AccessorReturn<TAccessor, TData, TValue>

  array: <
    TAccessor extends RestrictedAccessor<TData, any[]>,
    TValue extends Extract<ExtractTValue<TData, TAccessor>, any[]>,
  >(
    accessor: TAccessor,
    column: Options<TAccessor, TData, TValue>
  ) => AccessorReturn<TAccessor, TData, TValue>
}

export function createEnhancedColumnHelper<TData extends RowData>(): EnhancedColumnHelper<TData> {
  const columnHelper = createColumnHelper<TData>()
  return {
    ...columnHelper,

    /**
     * Creates a date column with formatted date display.
     * @param accessor - The accessor for the date field
     * @param options - Column options
     * @example
     * columnHelper.date("createdAt", {
     *   title: "Creation Date",
     *   enableSorting: true,
     * })
     */
    date: (accessor, options) =>
      columnHelper.accessor(accessor, {
        header: ({ column }) => <DataTableColumnHeader column={column} title={options.title} />,
        cell: (context) => <DateField format={options.format} value={context.getValue()} />,
        ...options,
        meta: {
          visible: options.visible,
          align: options.align,
          ...options.meta,
        },
      }),

    /**
     * Creates a link column with customizable href.
     * @param accessor - The accessor for the link text
     * @param options - Column options including href function
     * @param options.title - The title of the column
     * @param options.href - A function that returns the URL for the link based on the row data
     * @param options.align - Optional alignment of the column content ('left', 'center', 'right')
     * @param options.visible - Optional visibility of the column
     * @param options.cell - Optional custom cell renderer or static content
     * @example
     * columnHelper.link("name", {
     *   title: "Project Name",
     *   href: (row) => `/admin/projects/${row.id}`,
     *   align: "left",
     *   cell: (context) => `View ${context.getValue()}`,
     * })
     */
    link: (accessor, options) =>
      columnHelper.accessor(accessor, {
        ...options,
        header: ({ column }) => <DataTableColumnHeader column={column} title={options.title} />,
        cell: (context) => {
          const isDisabled = options.isDisabled ? options.isDisabled(context.row.original) : false
          return (
            <Link
              href={isDisabled ? "#" : options.href(context.row.original)}
              className={cn(
                "max-w-[200px] truncate",
                options.className,
                isDisabled && "pointer-events-none opacity-60"
              )}
            >
              {options.cell
                ? typeof options.cell === "string"
                  ? options.cell
                  : options.cell(context)
                : context.getValue()}
            </Link>
          )
        },
        meta: {
          align: options.align,
          visible: options.visible,
          ...options.meta,
        },
      }),

    /**
     * Creates a badge column with customizable colors and transform function.
     * @param accessor - The accessor for the badge value
     * @param options - Column options including colorMap and transform function
     * @param options.title - The title of the column
     * @param options.colorMap - Optional object mapping values to badge colors
     * @param options.transform - Optional function to transform the displayed value
     * @param options.align - Optional alignment of the column content ('left', 'center', 'right')
     * @example
     * columnHelper.badge("status", {
     *   title: "Status",
     *   colorMap: { active: "green", inactive: "red" },
     *   transform: (value) => value.toUpperCase(),
     *   align: "center",
     * })
     */
    badge: (accessor, options) =>
      columnHelper.accessor(accessor, {
        header: ({ column }) => <DataTableColumnHeader column={column} title={options.title} />,
        cell: (context) => {
          const value = context.getValue()
          if (!value) return <span>-</span>
          return (
            <Badge variant={options.variant} color={options.colorMap?.[value]}>
              {options.transform?.(value) ?? value}
            </Badge>
          )
        },
        ...options,
        meta: {
          align: options.align,
          visible: options.visible,
          ...options.meta,
        },
      }),

    /**
     * Creates an array column with comma-separated display.
     * @param accessor - The accessor for the array field
     * @param options - Column options
     * @example
     * columnHelper.array("tags", {
     *   title: "Tags",
     *   align: "left",
     * })
     */
    array: (accessor, options) =>
      columnHelper.accessor(accessor, {
        header: ({ column }) => <DataTableColumnHeader column={column} title={options.title} />,
        cell: (context) => (
          <div className="flex flex-wrap gap-2">
            {(context.getValue() as any[]).map((value, index) => (
              <span key={index}>{value}</span>
            ))}
          </div>
        ),
        ...options,
        meta: {
          align: options.align,
          visible: options.visible,
          ...options.meta,
        },
      }),

    /**
     * Creates a custom column with full control over the cell render.
     * @param accessor - The accessor for the custom field
     * @param options - Column options
     * @param options.title - The title of the column
     * @param options.cell - Custom cell renderer function
     * @param options.align - Optional alignment of the column content ('left', 'center', 'right')
     * @example
     * columnHelper.custom("actions", {
     *   title: "Actions",
     *   align: "right",
     *   cell: (context) => (
     *     <Button onClick={() => handleAction(context.row.original)}>
     *       Edit
     *     </Button>
     *   ),
     * })
     */
    custom: (accessor, options) =>
      columnHelper.accessor(accessor, {
        header: ({ column }) => <DataTableColumnHeader column={column} title={options.title} />,
        ...options,
        meta: {
          align: options.align,
          visible: options.visible,
          ...options.meta,
        },
      }),
  }
}
