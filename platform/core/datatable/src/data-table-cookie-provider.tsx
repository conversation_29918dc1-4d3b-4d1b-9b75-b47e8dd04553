/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import "server-only"

import type { ComponentProps, FC } from "react"

import { DataTableCookies } from "./data-table-cookie-config"
import { DataTableCookieProviderInternal } from "./data-table-cookie-provider-client"

/**
 * List of cookie names that are used by the DataTable component.
 * To be used
 */
const cookieNames = Object.values(DataTableCookies)

/**
 * This component is used to provide the cookies to the DataTable component.
 * Its save to pass all cookies since this server component and it only includes cookies prefixed with "datatable"
 */
export const DataTableCookieProvider: FC<ComponentProps<typeof DataTableCookieProviderInternal>> = ({
  cookies,
  children,
}) => (
  <DataTableCookieProviderInternal
    // need to cast to the key of cookieNames since otherwise typescript will show an error that string is not assignable to the specific template string
    cookies={cookies.filter((cookie) => cookieNames.includes(cookie.name as (typeof cookieNames)[number]))}
  >
    {children}
  </DataTableCookieProviderInternal>
)
