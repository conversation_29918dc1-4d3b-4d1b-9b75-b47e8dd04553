/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"
import { format } from "date-fns"

export const DateField: FC<{ value?: Date | string | null; format?: string }> = ({
  value,
  format: formatString = "P",
}) => {
  if (!value) return null
  const date = typeof value === "string" ? new Date(value) : value
  return (
    <time dateTime={date.toISOString()} suppressHydrationWarning>
      {format(date, formatString)}
    </time>
  )
}
