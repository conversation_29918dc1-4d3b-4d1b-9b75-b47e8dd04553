/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ColumnMeta, VisibilityState } from "@tanstack/react-table"
import * as React from "react"
import { Fragment, useEffect } from "react"
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { useTranslations } from "next-intl"

import { cn } from "@kreios/ui"
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

import type { DataTableInlineProps } from "./data-table-inline"
import { DataTableContextProvider } from "./data-table-context"
import { DataTablePagination } from "./data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"

export function DataTableInlineFill<TData, TValue>({
  columns,
  data,
  toolbar,
  renderRow,
  wrapperClassName,
  searchPlaceholder,
  onTableSearchChange,
  enableSorting = true,
  enablePagination = true,
  footerTitle,
}: DataTableInlineProps<TData, TValue> & {
  footerTitle?: string | null
}) {
  // Create VisibilityState based on the columns meta.visible property
  const initialColumnVisibility = React.useMemo(
    () =>
      Object.fromEntries(
        // @ts-expect-error since we check if column.visible is available allow undefined is fine
        columns.map((column) => [column.id ?? column.accessorKey, column.meta?.visible])
      ) as VisibilityState,
    [columns]
  )

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableSorting,
    manualPagination: !enablePagination,
    initialState: {
      columnVisibility: initialColumnVisibility,
    },
  })

  const t = useTranslations("common.table")

  useEffect(() => {
    console.log("getFooterGroups", table.getFooterGroups())
  }, [table])

  return (
    // todo: Find a way to merge this layout with original data table inline
    <DataTableContextProvider table={table}>
      <div className="grid grid-cols-[100%] grid-rows-[auto_1fr_auto] gap-3 overflow-auto">
        {toolbar !== false && (
          <DataTableToolbar
            searchPlaceholder={searchPlaceholder}
            table={table}
            filters={[]}
            onTableSearchChange={onTableSearchChange}
          >
            {toolbar}
          </DataTableToolbar>
        )}
        <div className="overflow-auto rounded-md border bg-background p-1">
          <Table wrapperClassName={cn(wrapperClassName, "h-full w-full")}>
            <TableHeader className="sticky top-0 bg-white">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        align={header.column.columnDef.meta?.align}
                        key={header.id}
                        colSpan={header.colSpan}
                        style={{
                          maxWidth: (header.column.columnDef as ColumnMeta<TData, TValue>).maxWidth ?? "none",
                        }}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => {
                  const node = (
                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          align={cell.column.columnDef.meta?.align}
                          key={cell.id}
                          style={{
                            maxWidth: (cell.column.columnDef as ColumnMeta<TData, TValue>).maxWidth ?? "none",
                          }}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  )

                  return renderRow ? renderRow(row, node) : node
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {t("noResults")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
            {table.getFooterGroups().some(({ headers }) => headers.length > 0) && (
              <TableFooter className="sticky bottom-0 bg-primary">
                {table.getFooterGroups().map((footer) => {
                  return (
                    <Fragment key={footer.id}>
                      {footerTitle ? (
                        <TableRow className="hover:bg-primary">
                          <TableCell colSpan={footer.headers.length}>{footerTitle}</TableCell>
                        </TableRow>
                      ) : null}
                      <TableRow className="hover:bg-primary">
                        {footer.headers.map((footerCell) => {
                          return (
                            <TableCell key={footerCell.id}>
                              {footerCell.isPlaceholder
                                ? null
                                : flexRender(footerCell.column.columnDef.footer, footerCell.getContext())}
                            </TableCell>
                          )
                        })}
                      </TableRow>
                    </Fragment>
                  )
                })}
              </TableFooter>
            )}
          </Table>
        </div>
        {enablePagination && <DataTablePagination table={table} />}
      </div>
    </DataTableContextProvider>
  )
}
