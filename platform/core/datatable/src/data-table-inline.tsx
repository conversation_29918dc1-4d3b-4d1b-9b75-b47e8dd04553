/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ColumnDef, ColumnMeta, Row } from "@tanstack/react-table"
import * as React from "react"
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@kreios/ui/table"

import { DataTableContextProvider } from "./data-table-context"
import { DataTablePagination } from "./data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"

export type DataTableInlineProps<TData, TValue> = {
  /** DataTable columns array */
  columns: ColumnDef<TData, TValue>[]
  /** React nodes to render inside the Datatable toolbar */
  toolbar?: React.ReactNode
  /** Data to be displayed in the table */
  data: TData[]
  /** Optional function to render custom row */
  renderRow?: (row: Row<TData>, node: React.ReactNode) => React.ReactNode
  wrapperClassName?: string
  /** Placeholder for the search input */
  searchPlaceholder?: string
  /** Enable Table sorting, optional **/
  enableSorting?: boolean
  /** Enable Table pagination, optional **/
  enablePagination?: boolean
  /** Custom callback, fired on change table search string, optional **/
  onTableSearchChange?: (value: string) => void
  /** Field name to use as the unique row ID (instead of using auto-generated indices) */
  rowIdKey?: keyof TData
}

export function DataTableInline<TData, TValue>({
  columns,
  data,
  toolbar,
  renderRow,
  wrapperClassName,
  searchPlaceholder,
  onTableSearchChange,
  enableSorting = true,
  enablePagination = true,
  rowIdKey,
}: DataTableInlineProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    enableSorting,
    manualPagination: !enablePagination,
    // Use custom row IDs based on rowIdKey if provided
    getRowId: rowIdKey ? (row) => String(row[rowIdKey]) : undefined,
  })

  return (
    <DataTableContextProvider table={table}>
      <div className="h-full w-full space-y-4 overflow-visible">
        {toolbar !== false && (
          <DataTableToolbar
            searchPlaceholder={searchPlaceholder}
            table={table}
            filters={[]}
            onTableSearchChange={onTableSearchChange}
          >
            {toolbar}
          </DataTableToolbar>
        )}
        <div className="rounded-md border bg-background p-1">
          <Table wrapperClassName={wrapperClassName}>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        align={header.column.columnDef.meta?.align}
                        key={header.id}
                        colSpan={header.colSpan}
                        style={{
                          maxWidth: (header.column.columnDef as ColumnMeta<TData, TValue>).maxWidth ?? "none",
                        }}
                      >
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.length ? (
                table.getRowModel().rows.map((row) => {
                  const node = (
                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          align={cell.column.columnDef.meta?.align}
                          key={cell.id}
                          style={{
                            maxWidth: (cell.column.columnDef as ColumnMeta<TData, TValue>).maxWidth ?? "none",
                          }}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  )

                  return renderRow ? renderRow(row, node) : node
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {enablePagination && <DataTablePagination table={table} />}
      </div>
    </DataTableContextProvider>
  )
}
