/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { Table as TanstackTable } from "@tanstack/react-table"
import * as React from "react"

type DataTableContextValue<TData> = TanstackTable<TData>

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DataTableContext = React.createContext<DataTableContextValue<any> | undefined>(undefined)

export const useDataTableContext = <TData,>(): DataTableContextValue<TData> => {
  const context = React.useContext<DataTableContextValue<TData> | undefined>(DataTableContext)
  if (context === undefined) throw new Error("useDataTableContext must be used within a DataTableContextProvider")

  return context
}

export const DataTableContextProvider = <TData,>({
  table,
  children,
}: {
  children: React.ReactNode
  table: TanstackTable<TData>
}) => {
  return <DataTableContext.Provider value={{ ...table }}>{children}</DataTableContext.Provider>
}
