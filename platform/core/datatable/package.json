{"name": "@kreios/datatable", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.tsx", "./*": "./src/*.tsx"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@kreios/prettier-config", "dependencies": {"@kreios/ui": "workspace:*", "@kreios/utils": "workspace:*", "@radix-ui/react-icons": "1.3.0", "@tanstack/react-query": "5.55.4", "@tanstack/react-table": "8.20.5", "@tanstack/react-virtual": "3.11.2", "@trpc/react-query": "11.0.0-rc.401", "@trpc/server": "11.0.0-rc.401", "date-fns": "4.1.0", "js-cookie": "3.0.5", "next-intl": "4.0.2", "server-only": "0.0.1", "spin-delay": "2.0.1", "use-debounce": "10.0.3", "use-deep-compare": "1.3.0", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/js-cookie": "3.0.6", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}}