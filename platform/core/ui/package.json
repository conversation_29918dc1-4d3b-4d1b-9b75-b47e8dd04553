{"name": "@kreios/ui", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./form": "./src/form/index.tsx", "./form/utils": "./src/form/utils.ts", "./*": "./src/*.tsx"}, "scripts": {"add": "pnpm dlx shadcn-ui add", "clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest --watch", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "ui-add": "pnpm dlx shadcn@latest add", "postui-add": "prettier src --write --list-different"}, "prettier": "@kreios/prettier-config", "dependencies": {"@hookform/devtools": "4.3.1", "@hookform/resolvers": "3.9.0", "@kreios/hooks": "workspace:*", "@kreios/utils": "workspace:*", "@radix-ui/react-accordion": "1.2.0", "@radix-ui/react-alert-dialog": "1.1.1", "@radix-ui/react-avatar": "1.1.0", "@radix-ui/react-checkbox": "1.1.1", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-dialog": "1.1.1", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "1.3.0", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-radio-group": "1.2.1", "@radix-ui/react-scroll-area": "1.1.0", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.0", "@radix-ui/react-tabs": "1.1.0", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.0", "class-variance-authority": "0.7.0", "cmdk": "1.0.0", "constate": "3.3.2", "date-fns": "4.1.0", "embla-carousel-react": "8.2.1", "embla-carousel-wheel-gestures": "8.0.1", "lodash-es": "4.17.21", "lucide-react": "0.457.0", "next-intl": "4.0.2", "next-themes": "0.3.0", "react-day-picker": "8.10.1", "react-hook-form": "7.53.0", "react-innertext": "1.1.5", "react-resizable-panels": "2.1.2", "react-use": "17.5.1", "react-use-event-hook": "0.9.6", "recharts": "2.12.7", "sonner": "1.5.0", "tailwind-merge": "2.5.2", "use-debounce": "10.0.3", "use-resize-observer": "9.1.0", "vaul": "0.9.2", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/lodash-es": "4.17.12", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}, "peerDependencies": {"next": "14.2.13", "react": "18.3.1", "react-dom": "18.3.1", "zod": "3.23.8"}}