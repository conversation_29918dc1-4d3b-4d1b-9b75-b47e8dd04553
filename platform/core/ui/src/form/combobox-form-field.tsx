/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { Control, FieldPath, FieldValues } from "react-hook-form"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "../"
import { Button } from "../button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandLoading } from "../command"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "../form"
import { Popover, PopoverContent, PopoverTrigger } from "../popover"

export const ComboboxFormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  search,
  onSearchChange,
  loading,
  shouldFilter,
  control,
  name,
  options,
  className,
  label,
  itemLabel = label,
}: {
  shouldFilter?: boolean
  search?: string
  loading?: boolean
  onSearchChange?: (value: string) => void
  label: string
  itemLabel?: string
  control: Control<TFieldValues>
  name: TName
  options: { label: string; value: string }[]
  className?: string
}) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={cn("flex flex-col", className)}>
        <FormLabel>{label}</FormLabel>
        <Popover>
          <PopoverTrigger asChild>
            <FormControl>
              <Button
                variant="outline"
                role="combobox"
                className={cn("justify-between", !field.value && "text-muted-foreground")}
              >
                {field.value ? options.find((option) => option.value === field.value)?.label : <>Select {itemLabel}</>}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </FormControl>
          </PopoverTrigger>
          <PopoverContent className="p-0">
            <Command shouldFilter={shouldFilter}>
              <CommandInput value={search} onValueChange={onSearchChange} placeholder={`Search ${itemLabel}...`} />
              <CommandList>
                {loading && <CommandLoading />}
                <CommandEmpty>No {itemLabel} found.</CommandEmpty>

                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem value={option.label} key={option.value} onSelect={() => field.onChange(option.value)}>
                      <Check
                        className={cn("mr-2 h-4 w-4", option.value === field.value ? "opacity-100" : "opacity-0")}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <FormMessage />
      </FormItem>
    )}
  />
)
