/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { describe, expect, it } from "vitest"
import { z } from "zod"

import { get } from "./schema-utils"

describe("get function", () => {
  it("should handle simple object schemas", () => {
    const schema = z.object({
      name: z.string(),
      age: z.number(),
    })

    expect(get(schema, "name").typeName).toBe("ZodString")
    expect(get(schema, "age").typeName).toBe("ZodNumber")
  })

  it("should handle nested object schemas with optional fields", () => {
    const schema = z.object({
      user: z.object({
        project: z.object({
          name: z.string().min(1).optional(),
        }),
      }),
    })

    expect(get(schema, "user.project.name").typeName).toBe("ZodString")
  })

  it("should handle array schemas", () => {
    const schema = z.object({
      items: z.array(z.string()),
      nestedArray: z.array(z.object({ id: z.number() })),
    })

    expect(get(schema, "items").typeName).toBe("ZodArray")
    expect(get(schema, "items.element").typeName).toBe("ZodString")
    expect(get(schema, "nestedArray.element.id").typeName).toBe("ZodNumber")
  })

  it("should handle optional fields", () => {
    const schema = z.object({
      optionalField: z.string().optional(),
      optionalNumber: z.number().optional(),
    })

    expect(get(schema, "optionalField").typeName).toBe("ZodString")
    expect(get(schema, "optionalNumber").typeName).toBe("ZodNumber")
  })

  it("should handle union types", () => {
    const schema = z.object({
      unionField: z.union([z.string(), z.number(), z.boolean()]),
      deepUnion: z.union([
        z.object({ type: z.literal("a"), value: z.string() }),
        z.object({ type: z.literal("b"), value: z.number() }),
      ]),
    })

    expect(get(schema, "unionField").typeName).toBe("ZodString")
    expect(get(schema, "deepUnion").typeName).toBe("ZodObject")
    expect(get(schema, "deepUnion.type").typeName).toBe("ZodLiteral")
    expect(get(schema, "deepUnion.value").typeName).toBe("ZodString")
  })

  it("should handle discriminated union types", () => {
    const schema = z.discriminatedUnion("type", [
      z.object({ type: z.literal("a"), value: z.string() }),
      z.object({ type: z.literal("b"), value: z.number() }),
    ])

    expect(get(schema, "type").typeName).toBe("ZodLiteral")
    expect(get(schema, "value").typeName).toBe("ZodString") // Returns the type of the first option
  })

  it("should handle complex nested union and discriminated union types", () => {
    const schema = z.object({
      nested: z.union([
        z.object({
          type: z.literal("a"),
          data: z.discriminatedUnion("kind", [
            z.object({ kind: z.literal("x"), value: z.string() }),
            z.object({ kind: z.literal("y"), value: z.number() }),
          ]),
        }),
        z.object({ type: z.literal("b"), data: z.array(z.string()) }),
      ]),
    })

    expect(get(schema, "nested").typeName).toBe("ZodObject")
    expect(get(schema, "nested.type").typeName).toBe("ZodLiteral")
    expect(get(schema, "nested.data").typeName).toBe("ZodObject")
    expect(get(schema, "nested.data.kind").typeName).toBe("ZodLiteral")
    expect(get(schema, "nested.data.value").typeName).toBe("ZodString")
  })

  it("should handle union types with objects", () => {
    const schema = z.object({
      test: z.union([z.object({ name: z.string() }), z.object({ id: z.number() })]),
    })

    expect(get(schema, "test").typeName).toBe("ZodObject")
    expect(get(schema, "test.name").typeName).toBe("ZodString")
    expect(get(schema, "test.id").typeName).toBe("ZodNumber")
  })

  it("should throw an error for non-existent paths in union schemas", () => {
    const schema = z.union([z.object({ a: z.string() }), z.object({ b: z.number() })])

    expect(() => get(schema, "c")).toThrow('Could not resolve path "c" in union schema')
  })

  it("should handle enum types", () => {
    const MyEnum = z.enum(["A", "B", "C"])
    const schema = z.object({
      enumField: MyEnum,
    })

    expect(get(schema, "enumField").typeName).toBe("ZodEnum")
  })

  it("should handle intersection types", () => {
    const schema = z.intersection(z.object({ a: z.string() }), z.object({ b: z.number() }))

    expect(get(schema, "a").typeName).toBe("ZodString")
    expect(get(schema, "b").typeName).toBe("ZodNumber")
  })

  it("should handle record types", () => {
    const schema = z.record(z.string())

    expect(get(schema, "").typeName).toBe("ZodRecord")
    expect(get(schema, "anyKey").typeName).toBe("ZodString")

    const complexSchema = z.record(z.object({ name: z.string(), age: z.number() }))
    expect(get(complexSchema, "anyKey.name").typeName).toBe("ZodString")
    expect(get(complexSchema, "anyKey.age").typeName).toBe("ZodNumber")
  })

  it("should handle tuple types", () => {
    const schema = z.tuple([z.string(), z.number(), z.boolean()])

    expect(get(schema, "0").typeName).toBe("ZodString")
    expect(get(schema, "1").typeName).toBe("ZodNumber")
    expect(get(schema, "2").typeName).toBe("ZodBoolean")
    expect(() => get(schema, "3")).toThrow()
  })

  it("should handle nullable types", () => {
    const schema = z.object({
      nullableField: z.string().nullable(),
    })

    expect(get(schema, "nullableField").typeName).toBe("ZodString")
  })

  it("should handle literal types", () => {
    const schema = z.object({
      literalString: z.literal("hello"),
      literalNumber: z.literal(42),
    })

    expect(get(schema, "literalString").typeName).toBe("ZodLiteral")
    expect(get(schema, "literalNumber").typeName).toBe("ZodLiteral")
  })

  it("should handle deep nested structures", () => {
    const schema = z.object({
      level1: z.object({
        level2: z.array(
          z.object({
            level3: z.union([z.string(), z.number()]).optional(),
          })
        ),
      }),
    })

    expect(get(schema, "level1.level2.element.level3").typeName).toBe("ZodString")
  })

  it("should handle complex nested union and discriminated union types", () => {
    const schema = z.object({
      nested: z.union([
        z.object({
          type: z.literal("a"),
          data: z.discriminatedUnion("kind", [
            z.object({ kind: z.literal("x"), value: z.string() }),
            z.object({ kind: z.literal("y"), value: z.number() }),
          ]),
        }),
        z.object({ type: z.literal("b"), data: z.array(z.string()) }),
      ]),
    })

    expect(get(schema, "nested").typeName).toBe("ZodObject")
    expect(get(schema, "nested.type").typeName).toBe("ZodLiteral")
    expect(get(schema, "nested.data").typeName).toBe("ZodObject")
    expect(get(schema, "nested.data.kind").typeName).toBe("ZodLiteral")
    expect(get(schema, "nested.data.value").typeName).toBe("ZodString")
  })

  it("should throw an error for non-existent paths", () => {
    const schema = z.object({
      name: z.string(),
    })

    expect(() => get(schema, "nonexistent")).toThrow()
    expect(() => get(schema, "name.nonexistent")).toThrow()
  })
})
