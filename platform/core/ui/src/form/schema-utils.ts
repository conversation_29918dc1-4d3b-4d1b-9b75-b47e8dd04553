/* eslint-disable @typescript-eslint/no-unsafe-argument */
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { ZodFirstPartyTypeKind, ZodSchema, ZodTypeDef } from "zod"
import {
  ZodArray,
  ZodDiscriminatedUnion,
  ZodIntersection,
  ZodNullable,
  ZodObject,
  ZodOptional,
  ZodRecord,
  ZodTuple,
  ZodUnion,
} from "zod"

export const get = <T extends ZodSchema>(
  schema: T,
  path: string
): ZodTypeDef & {
  typeName: ZodFirstPartyTypeKind
} => {
  const pathParts = path.split(".")

  const traverse = (currentSchema: ZodSchema, remainingPath: string[]): ZodTypeDef => {
    if (
      remainingPath.length === 0 &&
      !(
        currentSchema instanceof ZodOptional ||
        currentSchema instanceof ZodNullable ||
        currentSchema instanceof ZodUnion ||
        currentSchema instanceof ZodDiscriminatedUnion ||
        currentSchema instanceof ZodIntersection
      )
    ) {
      return currentSchema._def
    }

    const [currentPart, ...rest] = remainingPath

    if (currentSchema instanceof ZodObject) {
      const shape = currentSchema.shape as Record<string, ZodSchema>
      if (currentPart in shape) {
        return traverse(shape[currentPart], rest)
      }
    } else if (currentSchema instanceof ZodArray) {
      return traverse(currentSchema.element, rest)
    } else if (currentSchema instanceof ZodOptional || currentSchema instanceof ZodNullable) {
      return traverse(currentSchema.unwrap(), remainingPath)
    } else if (currentSchema instanceof ZodUnion || currentSchema instanceof ZodDiscriminatedUnion) {
      for (const option of currentSchema.options) {
        try {
          return traverse(option, remainingPath)
        } catch {
          // If traversal fails, try the next option
        }
      }
      // If no option matches, throw an error
      throw new Error(`Could not resolve path "${path}" in union schema`)
    } else if (currentSchema instanceof ZodIntersection) {
      try {
        return traverse(currentSchema._def.left, remainingPath)
      } catch {
        return traverse(currentSchema._def.right, remainingPath)
      }
    } else if (currentSchema instanceof ZodRecord) {
      if (remainingPath.length === 0 || currentPart === "") {
        return currentSchema._def
      }
      // For any key in a record, return the value schema
      return traverse(currentSchema.valueSchema, rest)
    } else if (currentSchema instanceof ZodTuple) {
      const index = parseInt(currentPart, 10)
      if (isNaN(index))
        throw new Error(`Could not resolve path "${path}" in tuple schema because ${currentPart} is not a number`)
      const items = currentSchema.items as ZodSchema[]
      if (index < 0 || index >= items.length)
        throw new Error(`Could not resolve path "${path}" in tuple schema because ${currentPart} is out of bounds`)

      return traverse(items[index], rest)
    }

    throw new Error(`Could not resolve path "${path}" in schema`)
  }

  const result = traverse(schema, pathParts)

  return result as ZodTypeDef & { typeName: ZodFirstPartyTypeKind }
}
