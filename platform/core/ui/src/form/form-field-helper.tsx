/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ComponentPropsWithoutRef, ElementRef, InputHTMLAttributes, ReactNode } from "react"
import type {
  ControllerFieldState,
  ControllerRenderProps,
  FieldPath,
  FieldPathValue,
  FieldValues,
  UseControllerProps,
  UseFormStateReturn,
} from "react-hook-form"
import type { z, ZodSchema } from "zod"
import { forwardRef, useState } from "react"
import { SelectLabel } from "@radix-ui/react-select"
import { Check, ChevronsUpDown } from "lucide-react"
import innerText from "react-innertext"

import type { TextareaProps } from "../textarea"
import { cn } from "../"
import { Button } from "../button"
import { Checkbox } from "../checkbox"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandLoading } from "../command"
import { DatePicker } from "../date-picker"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "../form"
import { Input } from "../input"
import {
  MultiSelector,
  MultiSelectorContent,
  MultiSelectorInput,
  MultiSelectorList,
  MultiSelectorTrigger,
} from "../multi-select"
import { Popover, PopoverContent, PopoverTrigger } from "../popover"
import { RadioGroup, RadioGroupItem } from "../radio-group"
import { Select, SelectContent, SelectGroup, SelectTrigger, SelectValue } from "../select"
import { Switch } from "../switch"
import { Textarea } from "../textarea"
import { get } from "./schema-utils"

function camelCase(string: string) {
  const output = string.replace(/([A-Z])/g, " $1")
  return output.charAt(0).toUpperCase() + output.slice(1)
}

const INPUT_COMPONENTS = {
  checkbox: forwardRef<ElementRef<typeof Checkbox>, ComponentPropsWithoutRef<typeof Checkbox>>(
    // @ts-expect-error - The type of the event is not known
    ({ value, onChange, ...props }, ref) => <Checkbox ref={ref} checked={value} onCheckedChange={onChange} {...props} />
  ),
  // @ts-expect-error - The type of the event is not known
  date: DatePicker,
  switch: forwardRef<ElementRef<typeof Switch>, ComponentPropsWithoutRef<typeof Switch>>(
    // @ts-expect-error - The type of the event is not known
    ({ value, onChange, ...props }, ref) => <Switch ref={ref} checked={value} onCheckedChange={onChange} {...props} />
  ),
  number: forwardRef<
    HTMLInputElement,
    InputHTMLAttributes<HTMLInputElement> & { isYearlyField?: boolean; isFormInput?: boolean }
  >(({ ...props }, ref) => <Input type="number" ref={ref} {...props} />),
  fallback: forwardRef<HTMLInputElement, InputHTMLAttributes<HTMLInputElement> & { isFormInput?: boolean }>(
    ({ ...props }, ref) => <Input type="text" ref={ref} {...props} />
  ),
} satisfies Record<
  string,
  React.ComponentType<{
    onChange?: (...event: any[]) => any
    onBlur?: (...args: any[]) => any
    value?: any
    disabled?: boolean
    name?: string
  }>
>

const DEFAULT_ZOD_HANDLERS: Record<string, keyof typeof INPUT_COMPONENTS> = {
  ZodBoolean: "checkbox",
  ZodDate: "date",
  ZodNumber: "number",
}

type BaseFormFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = UseControllerProps<TFieldValues, TName> & {
  label?: ReactNode | false
  description?: ReactNode
  className?: string
  required?: boolean
  error?: boolean
  onFocus?: () => void
  onBlur?: () => void
  optionalText?: string
  isYearlyField?: boolean
  isFormInput?: boolean
}

const getPlaceholder = (props: { placeholder?: string; label?: ReactNode | false; name: string }): string => {
  const { placeholder, label, name } = props
  // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing -- label can be false
  return placeholder || (label ? innerText(label) : camelCase(name.split(".").at(-1)!))
}

const FormFieldLabel = ({
  children,
  name,
  required,
  optionalText,
}: {
  children: ReactNode | false
  name: string
  required?: boolean
  optionalText?: string
}) => {
  if (children === false) return null
  return (
    <FormLabel>
      {children ?? camelCase(name.split(".").at(-1)!)}
      {required === false && (
        <>
          {" "}
          <span className="font-normal text-muted-foreground">({optionalText ?? "optional"})</span>
        </>
      )}
    </FormLabel>
  )
}

export const createFormFieldHelper = <TSchema extends ZodSchema>(schema: TSchema) => {
  type TFieldValues = z.infer<TSchema>

  const BaseFormField = <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
    label,
    className,
    required,
    ...Rest
  }: BaseFormFieldProps<TFieldValues, TName> &
    (
      | {
          placeholder?: string
          defaultValue?: FieldPathValue<TFieldValues, TName>
        }
      | {
          render?: ({
            field,
            fieldState,
            formState,
          }: {
            field: ControllerRenderProps<TFieldValues, TName>
            fieldState: ControllerFieldState
            formState: UseFormStateReturn<TFieldValues>
          }) => React.ReactElement
        }
    )) => {
    const {
      render,
      placeholder,
      defaultValue,
      isYearlyField = false,
      isFormInput = false,
      ...props
    } = {
      placeholder: undefined,
      defaultValue: undefined,
      render: undefined,
      ...Rest,
    }
    const schemaDef = get(schema, props.name)

    const InputComponent = INPUT_COMPONENTS[DEFAULT_ZOD_HANDLERS[schemaDef.typeName] ?? "fallback"]

    return (
      <FormField
        render={(renderProps) => {
          return (
            <FormItem className={className}>
              <FormFieldLabel name={props.name} required={required} optionalText={props.optionalText}>
                {label}
              </FormFieldLabel>

              <FormControl>
                {render ? (
                  render(renderProps)
                ) : (
                  <InputComponent
                    required={required}
                    placeholder={placeholder}
                    defaultValue={defaultValue}
                    isYearlyField={isYearlyField}
                    isFormInput={isFormInput}
                    {...renderProps.field}
                  />
                )}
              </FormControl>
              {/* {Boolean(description) && <FormDescription>{description}</FormDescription>} */}
              <FormMessage />
            </FormItem>
          )
        }}
        {...props}
      />
    )
  }

  return Object.assign(BaseFormField, {
    /**
     * Renders a checkbox input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the checkbox (optional)
     * @param props.description - Additional description text (optional)
     * @example
     * <FormField.Checkbox name="isActive" label="Active" />
     */
    Checkbox: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      className,
      label,
      required,
      error,
      onFocus,
      onBlur,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render">) => (
      <FormField
        render={({ field }) => {
          return (
            <FormItem className={className}>
              <FormMessage />
              <div className="flex flex-row items-center gap-2">
                <FormControl>
                  <Checkbox
                    required={required}
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    name={field.name}
                    ref={field.ref}
                    disabled={field.disabled}
                    onFocus={onFocus}
                    onBlur={() => {
                      field.onBlur()
                      onBlur?.()
                    }}
                  />
                </FormControl>

                <div className={cn("grid gap-1.5 leading-none", error && "text-destructive")}>
                  <FormFieldLabel name={props.name} required={required} optionalText={props.optionalText}>
                    {label}
                  </FormFieldLabel>
                  {/* {Boolean(description) && <FormDescription>{description}</FormDescription>} */}
                </div>
              </div>
            </FormItem>
          )
        }}
        {...props}
      />
    ),

    /**
     * Renders a date picker input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the date picker (optional)
     * @param props.description - Additional description text (optional)
     * @param props.minDate - The minimum date that can be selected (optional)
     * @param props.maxDate - The maximum date that can be selected (optional)
     * @example
     * <FormField.DatePicker name="receptionDate" label="Reception Date" />
     */
    DatePicker: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      className,
      minDate,
      maxDate,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & { minDate?: Date; maxDate?: Date }) => (
      <BaseFormField
        {...props}
        render={({ field }) => (
          <DatePicker
            className={className}
            ref={field.ref}
            aria-required={required}
            value={field.value}
            onChange={field.onChange}
            name={field.name}
            onBlur={field.onBlur}
            disabled={field.disabled}
            minDate={minDate}
            maxDate={maxDate}
          />
        )}
      />
    ),

    /**
     * Renders a switch input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the switch (optional)
     * @param props.description - Additional description text (optional)
     * @example
     * <FormField.Switch name="notifications" label="Enable Notifications" />
     */
    Switch: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render">) => (
      <BaseFormField
        {...props}
        render={({ field }) => (
          <Switch
            required={required}
            checked={field.value}
            onCheckedChange={field.onChange}
            name={field.name}
            onBlur={field.onBlur}
            ref={field.ref}
            disabled={field.disabled}
          />
        )}
      />
    ),

    /**
     * Renders a number input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the number input (optional)
     * @param props.description - Additional description text (optional)
     * @example
     * <FormField.NumberPicker name="volume" label="Volume" />
     */
    NumberPicker: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      error,
      onFocus,
      onBlur,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render">) => (
      <BaseFormField
        {...props}
        render={({ field, fieldState }) => (
          <Input
            type="number"
            required={required}
            value={field.value}
            onChange={field.onChange}
            name={field.name}
            ref={field.ref}
            disabled={field.disabled}
            error={!!fieldState.error || !!error}
            onFocus={onFocus}
            onBlur={() => {
              field.onBlur()
              onBlur?.()
            }}
          />
        )}
      />
    ),

    /**
     * Renders a textarea input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the textarea (optional)
     * @param props.description - Additional description text (optional)
     * @param props.rows - The number of rows for the textarea (optional)
     * @param props.placeholder - The placeholder text for the textarea (optional)
     * @example
     * <FormField.Textarea name="description" label="Description" />
     */
    Textarea: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      rows,
      placeholder,
      autoGrow,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & Partial<TextareaProps> & { autoGrow?: boolean }) => (
      <BaseFormField
        {...props}
        render={({ field }) => (
          <Textarea
            placeholder={placeholder}
            required={required}
            value={field.value}
            onChange={(e) => field.onChange(e.target.value === "" ? null : e.target.value)}
            name={field.name}
            onBlur={field.onBlur}
            autoGrow={autoGrow}
            ref={field.ref}
            disabled={field.disabled}
            rows={rows}
            className={cn(autoGrow && "min-h-[80px] resize-none overflow-hidden")}
            onInput={
              autoGrow
                ? (e) => {
                    const target = e.currentTarget
                    target.style.height = "auto"
                    target.style.height = `${target.scrollHeight}px`
                  }
                : undefined
            }
          />
        )}
      />
    ),

    /**
     * Renders a radio buttons input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the radio buttons (optional)
     * @param props.description - Additional description text (optional)
     * @param props.options - The options for the radio buttons as { label: string, value: string }[]
     * @example
     * <FormField.RadioButtons name="activity" label="Activity" options={[{ label: 'Running', value: 'running' }, { label: 'Swimming', value: 'swimming' }]} />
     */
    RadioButtons: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      options,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & {
      options: Array<{ label: string; value: string }>
    }) => (
      <BaseFormField
        {...props}
        render={({ field }) => (
          <RadioGroup
            value={field.value}
            onValueChange={field.onChange}
            disabled={field.disabled}
            className="flex space-x-4"
          >
            {options.map((option) => {
              const uniqueId = `${field.name}-${option.value}`
              return (
                <label key={option.value} htmlFor={uniqueId} className="flex cursor-pointer items-center space-x-3 p-2">
                  <RadioGroupItem value={option.value} id={uniqueId} disabled={field.disabled} />
                  <span className="text-sm">{option.label}</span>
                </label>
              )
            })}
          </RadioGroup>
        )}
      />
    ),

    RadioCheckbox: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      options,
      allowUnselect = false,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & {
      options: Array<{ label: string; value: string | boolean }>
      allowUnselect?: boolean
    }) => (
      <BaseFormField
        {...props}
        render={({ field }) => (
          <div className="flex space-x-4">
            {options.map((option) => {
              const uniqueId = `${field.name}-${option.value}`
              const isChecked = field.value === option.value

              return (
                <label key={option.label} htmlFor={uniqueId} className="flex cursor-pointer items-center space-x-3 p-2">
                  <Checkbox
                    id={uniqueId}
                    checked={isChecked}
                    onCheckedChange={(checked) => {
                      const isNowChecked = checked === true
                      if (allowUnselect && isChecked && !isNowChecked) {
                        field.onChange(null)
                      } else if (isNowChecked) {
                        field.onChange(option.value)
                      }
                    }}
                    disabled={field.disabled}
                    className={cn(
                      "aspect-square h-4 w-4 rounded-full border border-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
                      isChecked && "bg-primary text-primary-foreground"
                    )}
                  />
                  <span className="text-sm">{option.label}</span>
                </label>
              )
            })}
          </div>
        )}
      />
    ),
    /**
     * Renders a select input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the select input (optional)
     * @param props.description - Additional description text (optional)
     * @param props.children - The select options as SelectItem components
     * @param props.placeholder - The placeholder text for the select input (optional)
     * @example
     * <FormField.Select name="status" label="Status">
     *   <SelectItem value="active">Active</SelectItem>
     *   <SelectItem value="inactive">Inactive</SelectItem>
     * </FormField.Select>
     */
    Select: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      error,
      onFocus,
      onBlur,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & {
      children: React.ReactNode
      placeholder?: string
    }) => (
      <BaseFormField
        {...props}
        render={({ field, fieldState }) => (
          <Select
            required={required}
            value={field.value ?? ""}
            onValueChange={field.onChange}
            name={field.name}
            disabled={field.disabled}
          >
            <SelectTrigger
              ref={field.ref}
              onFocus={onFocus}
              onBlur={() => {
                field.onBlur()
                onBlur?.()
              }}
              className={cn("w-full", (!!error || !!fieldState.error) && "border-destructive text-destructive")}
            >
              <SelectValue placeholder={`Select ${getPlaceholder(props)}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>{props.label}</SelectLabel>
                {props.children}
              </SelectGroup>
            </SelectContent>
          </Select>
        )}
      />
    ),

    /**
     * Renders a multi-select input field.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the multi-select input (optional)
     * @param props.description - Additional description text (optional)
     * @param props.children - The select options as SelectItem components
     * @param props.placeholder - The placeholder text for the multi-select input (optional)
     * @example
     * <FormField.MultiSelect name="productScope" label="Product Scope">
     *   <SelectItem value="product1">Product 1</SelectItem>
     *   <SelectItem value="product2">Product 2</SelectItem>
     * </FormField.MultiSelect>
     */
    MultiSelect: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      getLabel,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & {
      children: React.ReactNode
      placeholder?: string
      getLabel?: (value: string) => string
    }) => (
      <BaseFormField
        {...props}
        render={({ field }) => (
          <MultiSelector onValuesChange={field.onChange} values={field.value ?? []}>
            <MultiSelectorTrigger disabled={field.disabled} getLabel={getLabel} className="w-full">
              <MultiSelectorInput
                required={required}
                name={field.name}
                onBlur={field.onBlur}
                ref={field.ref}
                disabled={field.disabled}
                placeholder={getPlaceholder(props)}
              />
            </MultiSelectorTrigger>
            <MultiSelectorContent>
              <MultiSelectorList>{props.children}</MultiSelectorList>
            </MultiSelectorContent>
          </MultiSelector>
        )}
      />
    ),

    /**
     * Renders a combobox input field with search functionality.
     * @param props - The component props
     * @param props.name - React Hook Form key accessor for the field
     * @param props.label - The label for the combobox (optional)
     * @param props.description - Additional description text (optional)
     * @param props.options - An array of options with label and value properties
     * @param props.itemLabel - The label to use for individual items (optional)
     * @param props.search - The current search string (optional)
     * @param props.onSearchChange - Callback function when search value changes (optional)
     * @param props.loading - Whether the options are currently loading (optional)
     * @param props.shouldFilter - Whether to apply client-side filtering (optional)
     * @example
     * <FormField.Combobox
     *   name="salesEngineer"
     *   label="Sales Engineer"
     *   options={salesEngineers.map(se => ({ label: se.name, value: se.id }))}
     *   itemLabel="sales engineer"
     * />
     */
    Combobox: <TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({
      required,
      className,
      label,
      options,
      itemLabel,
      search,
      onSearchChange,
      loading,
      shouldFilter,
      ...props
    }: Omit<BaseFormFieldProps<TFieldValues, TName>, "render"> & {
      shouldFilter?: boolean
      search?: string
      loading?: boolean
      onSearchChange?: (value: string) => void
      itemLabel?: string
      options: { label: string; value: string }[]
      className?: string
    }) => {
      const [open, setOpen] = useState(false)
      return (
        <FormField
          render={({ field }) => (
            <FormItem className={cn("flex flex-col", className)}>
              {label !== false && <FormLabel>{label ?? camelCase(props.name.split(".").at(-1)!)}</FormLabel>}
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      aria-required={required}
                      variant="outline"
                      role="combobox"
                      className={cn("w-full justify-between", !field.value && "text-muted-foreground")}
                    >
                      {field.value ? (
                        options.find((option) => option.value === field.value)?.label
                      ) : (
                        <>Select {itemLabel}</>
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
                  <Command shouldFilter={shouldFilter}>
                    <CommandInput
                      value={search}
                      onValueChange={onSearchChange}
                      placeholder={`Search ${itemLabel}...`}
                    />
                    <CommandList>
                      {loading && <CommandLoading />}
                      <CommandEmpty>No {itemLabel} found.</CommandEmpty>

                      <CommandGroup>
                        {options.map((option) => (
                          <CommandItem
                            value={option.label}
                            key={option.value}
                            onSelect={() => {
                              setOpen(false)
                              field.onChange(option.value)
                            }}
                          >
                            <Check
                              className={cn("mr-2 h-4 w-4", option.value === field.value ? "opacity-100" : "opacity-0")}
                            />
                            {option.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
          {...props}
        />
      )
    },
  })
}
