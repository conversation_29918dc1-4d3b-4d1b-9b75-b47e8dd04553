/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FieldValues } from "react-hook-form"

const isObjectType = (value: unknown): value is object => typeof value === "object"

/**
 * Flatten a nested object into a single level object each key is the path to the value
 * Ref: https://github.com/react-hook-form/react-hook-form/blob/541b2bd0bd1ab538d19c209ee682afeba54169a6/src/utils/flatten.ts
 * @param obj - The object to flatten
 * @returns A flattened object
 * @example
 * ```ts
 * const obj = {
 *   a: {
 *     b: 1,
 *   },
 * }
 * const flattened = flatten(obj)
 * // flattened = {
 * //   "a.b": 1,
 * // }
 * ```
 */
export const flatten = (obj: FieldValues) => {
  const output: FieldValues = {}

  for (const key of Object.keys(obj)) {
    if (isObjectType(obj[key]) && obj[key] !== null) {
      const nested = flatten(obj[key])

      for (const nestedKey of Object.keys(nested)) {
        output[`${key}.${nestedKey}`] = nested[nestedKey]
      }
    } else {
      output[key] = obj[key]
    }
  }

  return output
}
