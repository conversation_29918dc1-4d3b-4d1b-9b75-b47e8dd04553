/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { DevtoolUIProps } from "@hookform/devtools/dist/devToolUI"
import type React from "react"
import type { Control, FieldValues } from "react-hook-form"
import dynamic from "next/dynamic"

const NoopComponent = () => null

// @ts-expect-error - required for dynamic import
export const ReactHookFormDevTool: <T extends FieldValues>(
  props?: {
    id?: string | undefined
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    control?: Control<T, any> | undefined
  } & Pick<DevtoolUIProps, "placement" | "styles">
) => React.ReactElement | null = dynamic(
  () =>
    process.env.NODE_ENV === "production"
      ? Promise.resolve(NoopComponent)
      : import("@hookform/devtools").then(({ DevTool }) => DevTool),
  {
    ssr: false,
  }
)
