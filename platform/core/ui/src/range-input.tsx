/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ChangeEvent, FC, ReactNode } from "react"
import type { ZodIssue } from "zod"
import { useCallback, useEffect, useState } from "react"
import { useControllableState } from "@radix-ui/react-use-controllable-state"
import { isNaN } from "lodash-es"
import z from "zod"

import { Button } from "./button"
import { cn } from "./index"
import { Input } from "./input"
import { Label } from "./label"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"

const labels = {
  min: "Minimum Value",
  max: "Maximum Value",
} as const

type InternalRangeInputValueType = { min: number | string | null; max: number | string | null }
export type RangeInputValueType = { min: number | null; max: number | null }

export type RangeInputProps = {
  value: RangeInputValueType
  onChange?: (v: RangeInputValueType | undefined) => void
  label?: string
  valueFormat?: (v: number | null | bigint) => string
  icon?: ReactNode
}

const getFormFieldSafeValue = (v: number | string | null): number | null => {
  if (typeof v === "string") {
    const parsed = parseFloat(v)
    return !isNaN(parsed) ? parsed : null
  }

  if (typeof v === "number") return v

  return null
}

const rangeInputSchema = z
  .object({
    min: z.coerce.number().nullable(),
    max: z.coerce.number().nullable(),
  })
  .superRefine((data, ctx) => {
    if (data.min !== null && data.max !== null && data.min > data.max) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Minimum value cannot be greater than maximum value",
        path: ["min"],
      })
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Maximum value cannot be less than minimum value",
        path: ["max"],
      })
    }
  })

const RangeInput: FC<RangeInputProps> = ({ value, onChange, label, icon, valueFormat = (v) => `${v}` }) => {
  const [open, setOpen] = useState(false)
  const [errors, setErrors] = useState<ZodIssue[]>([])
  const [data, setData] = useState<InternalRangeInputValueType>(value)
  const [formValue, setFormValue] = useControllableState({
    prop: data,
    defaultProp: { min: null, max: null },
    onChange: (v) => {
      onFormValidation(v)
      return setData(v)
    },
  })

  const onFormValidation = useCallback((v: InternalRangeInputValueType) => {
    const res = rangeInputSchema.safeParse(v)
    setErrors(res.error?.errors ?? [])
  }, [])

  const onValueChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>, key: "min" | "max") => {
      const newValue = e.target.value
      setFormValue((prev) => {
        const oldValue = prev ?? { min: null, max: null }
        return { ...oldValue, [key]: newValue === "" ? null : newValue }
      })
    },
    [setFormValue]
  )

  const onSubmit = useCallback(() => {
    if (!errors.length) {
      const min = getFormFieldSafeValue(data.min)
      const max = getFormFieldSafeValue(data.max)
      onChange?.(min !== null || max !== null ? { min, max } : undefined)
      setOpen(false)
    }
  }, [data.max, data.min, errors.length, onChange])

  const toggleLabel = useCallback(() => {
    const baseLabel = label ?? "Range"
    const { min, max } = value

    if (!min && !max) {
      return baseLabel
    }

    const minText = min ? `≥ ${valueFormat(min)}` : null
    const maxText = max ? `≤ ${valueFormat(max)}` : null

    return `${baseLabel}: ${[minText, maxText].filter((item) => !!item).join(" and ")}`
  }, [label, value, valueFormat])

  const getFieldErrors = useCallback(
    (field: string) => {
      return errors.reduce<string[]>((prev, { path, message }) => {
        return path.includes(field) ? [...prev, message] : prev
      }, [])
    },
    [errors]
  )

  const onOpenChange = (open: boolean) => {
    return open ? setOpen(open) : onSubmit()
  }

  // Keep form aligned with value passed by props
  useEffect(() => {
    setFormValue(value)
  }, [setFormValue, value])

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        <Button size="sm" variant={"outline"} className={cn(!value.min && !value.max && "text-muted-foreground")}>
          {icon}
          {toggleLabel()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto" align="start">
        <div className="flex w-[280px] flex-col gap-2">
          {(["min", "max"] as const).map((item) => {
            const errors = getFieldErrors(item)
            return (
              <div className="flex flex-col gap-1" key={item}>
                <Label htmlFor="min" className={cn(!!errors.length && "text-destructive")}>
                  {labels[item]}
                </Label>
                <Input
                  type="number"
                  value={formValue?.[item] ?? ""}
                  id={item}
                  onChange={(e) => onValueChange(e, item)}
                  onKeyDown={(e) => {
                    if (e.code === "Enter") {
                      onSubmit()
                    }
                  }}
                />
                {!!errors.length && <div className="text-[0.8rem] text-destructive">{errors.join("; ")}</div>}
              </div>
            )
          })}
        </div>
      </PopoverContent>
    </Popover>
  )
}

export default RangeInput
