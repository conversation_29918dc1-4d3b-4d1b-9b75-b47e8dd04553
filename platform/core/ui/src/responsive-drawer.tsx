/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { ComponentProps } from "react"

import type { Breakpoints } from "@kreios/hooks/use-media-query"
import { useBreakpoint } from "@kreios/hooks/use-media-query"
import { split } from "@kreios/utils/split"

import { Drawer as DrawerComponent } from "./drawer"

// redeclare FadeFromProps types hence using omit breaks a discriminated union
interface WithFadeFromProps {
  snapPoints: (number | string)[]
  fadeFromIndex: number
}
interface WithoutFadeFromProps {
  snapPoints?: (number | string)[]
  fadeFromIndex?: never
}

type DrawerProps = ComponentProps<typeof DrawerComponent>
type Direction = NonNullable<DrawerProps["direction"]>

type ResponsiveDrawerProps = Omit<DrawerProps, "direction" | "snapPoints" | "fadeFromIndex"> & {
  /**
   * Set the direction of the drawer based on the breakpoint
   * @example
   * "bottom sm:right" // bottom on default, right on sm breakpoint
   */
  direction: `${Direction} ${Breakpoints}:${Direction}`
} & (WithFadeFromProps | WithoutFadeFromProps)

export const ResponsiveDrawer = ({ shouldScaleBackground = true, direction, ...props }: ResponsiveDrawerProps) => {
  const [defaultDirection, breakPointProp] = split(direction, " ")
  const [breakpoint, breakpointDirection] = split(breakPointProp, ":")

  const isBreakpoints = useBreakpoint(breakpoint)

  return (
    <DrawerComponent
      direction={isBreakpoints ? breakpointDirection : defaultDirection}
      shouldScaleBackground={shouldScaleBackground}
      {...props}
    />
  )
}

ResponsiveDrawer.displayName = "Drawer"
