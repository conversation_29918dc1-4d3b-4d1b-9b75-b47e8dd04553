/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

import * as React from "react"

import { useBreakpoint } from "@kreios/hooks/use-media-query"

import { cn } from "."
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "./select"
import { Stepper, StepperList, StepperTrigger } from "./stepper"

type BreakpointProp = { breakpoint?: "sm" | "md" | "lg" | "xl" | "2xl" }

const ResponsiveStepper = React.forwardRef<
  React.ElementRef<typeof Stepper>,
  React.ComponentPropsWithoutRef<typeof Stepper>
>(({ children, value, onValueChange, ...props }, ref) => {
  return (
    <Stepper ref={ref} {...props} value={value} onValueChange={onValueChange}>
      <Select
        value={value}
        onValueChange={React.useCallback((value: string) => value && onValueChange(value), [onValueChange])}
      >
        {children}
      </Select>
    </Stepper>
  )
})
ResponsiveStepper.displayName = "ResponsiveStepper"

const ResponsiveStepperList = React.forwardRef<
  React.ElementRef<typeof StepperList>,
  React.ComponentPropsWithoutRef<typeof StepperList> & BreakpointProp
>(({ className, children, breakpoint = "sm", ...props }, ref) => {
  const issm = useBreakpoint(breakpoint)
  return issm ? (
    <StepperList ref={ref} className={cn(className)} {...props}>
      {children}
    </StepperList>
  ) : (
    <>
      <SelectTrigger className={cn(className, "mb-4")}>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>{children}</SelectGroup>
      </SelectContent>
    </>
  )
})
ResponsiveStepperList.displayName = "ResponsiveStepperList"

const ResponsiveStepperTrigger = React.forwardRef<
  React.ElementRef<typeof StepperTrigger>,
  React.ComponentPropsWithoutRef<typeof StepperTrigger> &
    React.ComponentPropsWithoutRef<typeof SelectItem> &
    BreakpointProp
>(({ className, children, value, breakpoint = "sm", ...props }, ref) => {
  const issm = useBreakpoint(breakpoint)
  return issm ? (
    <StepperTrigger ref={ref} value={value} className={cn(className)} {...props}>
      {children}
    </StepperTrigger>
  ) : (
    <SelectItem value={value} className={cn(className)} {...props}>
      {children}
    </SelectItem>
  )
})
ResponsiveStepperTrigger.displayName = "ResponsiveStepperTrigger"

export { ResponsiveStepper, ResponsiveStepperList, ResponsiveStepperTrigger }
