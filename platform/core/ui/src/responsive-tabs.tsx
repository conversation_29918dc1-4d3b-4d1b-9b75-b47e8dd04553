/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import * as React from "react"

import type { CommonProperties } from "@kreios/utils/types/common-properties"
import { useBreakpoint } from "@kreios/hooks/use-media-query"

import { cn } from "."
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "./select"
import { Tabs, TabsList, TabsTrigger } from "./tabs"

type BreakpointProp = { breakpoint?: "sm" | "md" | "lg" | "xl" | "2xl" }

const ResponsiveTabs = React.forwardRef<React.ElementRef<typeof Tabs>, React.ComponentPropsWithoutRef<typeof Tabs>>(
  ({ children, value, onValueChange, defaultValue, ...props }, ref) => {
    if (!value && !onValueChange)
      // eslint-disable-next-line react-compiler/react-compiler
      // eslint-disable-next-line react-hooks/rules-of-hooks
      [value, onValueChange] = React.useState(defaultValue)

    return (
      <Tabs ref={ref} {...props} activationMode="manual" value={value} onValueChange={onValueChange}>
        <Select
          {...props}
          value={value}
          onValueChange={React.useCallback((value: string) => value && onValueChange?.(value), [onValueChange])}
        >
          {children}
        </Select>
      </Tabs>
    )
  }
)
ResponsiveTabs.displayName = "ResponsiveTabs"

const ResponsiveTabsList = React.forwardRef<
  React.ElementRef<typeof TabsList>,
  React.ComponentPropsWithoutRef<typeof TabsList> & BreakpointProp
>(({ className, children, breakpoint = "sm", ...props }, ref) => {
  const issm = useBreakpoint(breakpoint)
  return issm ? (
    <TabsList ref={ref} className={cn(className)} {...props}>
      {children}
    </TabsList>
  ) : (
    <>
      <SelectTrigger className={cn(className)}>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>{children}</SelectGroup>
      </SelectContent>
    </>
  )
})
ResponsiveTabsList.displayName = "ResponsiveTabsList"

const ResponsiveTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsTrigger>,
  CommonProperties<
    React.ComponentPropsWithoutRef<typeof TabsTrigger>,
    React.ComponentPropsWithoutRef<typeof SelectItem>
  > &
    BreakpointProp
>(({ className, children, value, disabled, breakpoint = "sm", ...props }, ref) => {
  const issm = useBreakpoint(breakpoint)
  return issm ? (
    <TabsTrigger ref={ref} value={value} className={cn(className)} disabled={disabled} {...props}>
      {children}
    </TabsTrigger>
  ) : (
    <SelectItem value={value} className={cn(className)} disabled={disabled} {...props}>
      {children}
    </SelectItem>
  )
})
ResponsiveTabsTrigger.displayName = "ResponsiveTabsTrigger"

export { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger }
