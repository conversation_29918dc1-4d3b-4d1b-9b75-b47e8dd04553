/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

import type { DayPickerSingleProps, Matcher } from "react-day-picker"
import { forwardRef, useState } from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"

import type { ButtonProps } from "./button"
import { cn } from "."
import { Button } from "./button"
import { Calendar } from "./calendar"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"

export { type DateRange, type SelectSingleEventHandler } from "react-day-picker"

export const DatePicker = forwardRef<
  HTMLButtonElement,
  Omit<ButtonProps, "onChange"> & {
    value: Date | undefined
    onChange: NonNullable<DayPickerSingleProps["onSelect"]>
    minDate?: Date
    maxDate?: Date
    disabledDays?: Matcher | Matcher[]
  }
>(({ value, onChange, className, disabled, minDate, maxDate, disabledDays, ...props }, ref) => {
  const [open, setOpen] = useState(false)
  const handleSelect: NonNullable<DayPickerSingleProps["onSelect"]> = (date, selectedDay, activeModifiers, event) => {
    const dateString = date ? format(date, "yyyy-MM-dd") : ""
    onChange(dateString as unknown as Date | undefined, selectedDay, activeModifiers, event)
    setOpen(false)
  }
  // Combine any passed disabledDays with minDate/maxDate constraints
  const combinedDisabledDays: Matcher[] = [
    ...(Array.isArray(disabledDays) ? disabledDays : disabledDays ? [disabledDays] : []),
    ...(minDate ? [{ before: minDate }] : []),
    ...(maxDate ? [{ after: maxDate }] : []),
  ]
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          aria-disabled={disabled}
          disabled={disabled}
          variant={"outline"}
          className={cn("w-[240px] pl-3 text-left font-normal", !value && "text-muted-foreground", className)}
          ref={ref}
          {...props}
        >
          {value ? format(value, "PPP") : <span>Pick a date</span>}
          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          disabled={disabled ?? combinedDisabledDays}
          initialFocus
          mode="single"
          selected={value}
          onSelect={handleSelect}
        />
      </PopoverContent>
    </Popover>
  )
})
