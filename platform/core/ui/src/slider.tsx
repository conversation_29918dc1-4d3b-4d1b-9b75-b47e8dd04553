/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import * as React from "react"
import { useState } from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"
import { useDebouncedCallback } from "use-debounce"

import { cn } from "."
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip"

/**
 * A tooltip that displays the value of the slider thumb
 */
const SliderThumbTooltip = ({
  value,
  children,
  open,
  enabled,
  renderValue,
}: {
  value: number
  children: React.ReactNode
  open: boolean
  enabled: boolean
  renderValue?: (value: number) => React.ReactNode
}) => {
  if (!enabled) return children
  return (
    <Tooltip open={open}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side="bottom">{renderValue?.(value) ?? value}</TooltipContent>
    </Tooltip>
  )
}
const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> & {
    showTooltip?: boolean
    renderValue?: (value: number) => React.ReactNode
  }
>(({ className, showTooltip: tooltip = false, renderValue, ...props }, ref) => {
  const [showTooltip, setShowTooltip] = useState(false)
  // debounced function to keep the tooltip open for 500ms after the pointer leaves the slider
  const handlePointerEnter = useDebouncedCallback((value: boolean) => setShowTooltip(value), 500)

  return (
    <SliderPrimitive.Root
      ref={ref}
      className={cn("relative flex w-full touch-none select-none items-center", className)}
      onPointerEnter={
        tooltip
          ? () => {
              handlePointerEnter(true)
              handlePointerEnter.flush()
            }
          : undefined
      }
      onPointerLeave={tooltip ? () => handlePointerEnter(false) : undefined}
      {...props}
    >
      <SliderPrimitive.Track className="relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20">
        <SliderPrimitive.Range className="absolute h-full bg-primary" />
      </SliderPrimitive.Track>
      <SliderThumbTooltip
        renderValue={renderValue}
        enabled={tooltip}
        open={showTooltip}
        value={props.value?.[0] ?? props.defaultValue?.[0] ?? 0}
      >
        <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" />
      </SliderThumbTooltip>
      {(props.value?.length ?? props.defaultValue?.length ?? 0) > 1 && (
        <SliderThumbTooltip
          enabled={tooltip}
          open={showTooltip}
          value={props.value?.[1] ?? props.defaultValue?.[1] ?? 100}
          renderValue={renderValue}
        >
          <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" />
        </SliderThumbTooltip>
      )}
    </SliderPrimitive.Root>
  )
})
Slider.displayName = SliderPrimitive.Root.displayName

export { Slider }
