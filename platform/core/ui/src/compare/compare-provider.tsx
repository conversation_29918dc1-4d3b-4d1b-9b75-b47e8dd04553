/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { useCallback, useState } from "react"
import constate from "constate"
import { useTranslations } from "next-intl"

import { toast } from "../sonner"

/**
 * CompareProvider provides state and functions to manage a selection of IDs for comparison.
 *
 * @param {Object} props - Component props.
 * @param {Object} [props.value] - Optional configuration object.
 * @param {number} [props.value.limit=5] - Maximum number of items allowed in the selection.
 * @returns {Object} Context value with state management functions.
 */
const [CompareProvider, useCompareProvider] = constate(({ value }: { value?: { limit?: number } }) => {
  const limit = value?.limit ?? 5
  const [ids, setIds] = useState<Record<string, string[] | undefined>>({})

  /**
   * Retrieves the list of selected IDs for a given key.
   * @param {string} key - The key identifying the selection group.
   * @returns {string[]} Array of selected IDs.
   */
  const getSelectedIds = useCallback(
    (key: string) => {
      return ids[key] ?? []
    },
    [ids]
  )

  /**
   * Adds an ID (or multiple IDs) to a selection group.
   *
   * @param {string} key - The key identifying the selection group.
   * @param {string|string[]} id - The ID(s) to add.
   */
  const addIdToSelection = useCallback(
    (key: string, id: string | string[]) => {
      const t = useTranslations("toast")
      const input = typeof id === "string" ? [id] : id

      setIds((prev) => {
        const prevValue = prev[key] ?? []

        const newValue = [...prevValue, ...input.filter((v) => !prevValue.includes(v))]

        if (newValue.length > limit) {
          toast.dismiss("comparison_err")
          toast.error(t("error.comparisonLimitExceeded", { limit }), {
            id: "comparison_err",
            closeButton: true,
          })
          return prev
        }

        return { ...prev, [key]: newValue }
      })
    },
    [limit]
  )

  /**
   * Removes an ID from a selection group.
   *
   * @param {string} key - The key identifying the selection group.
   * @param {string} id - The ID to remove.
   */
  const removeIdFromSelection = useCallback((key: string, id: string) => {
    setIds((prev) => {
      if (prev[key]) {
        return { ...prev, [key]: prev[key].filter((prevId) => prevId !== id) }
      }

      return prev
    })
  }, [])

  /**
   * Clears all IDs from a selection group.
   *
   * @param {string} key - The key identifying the selection group.
   */
  const clearIds = useCallback((key: string) => {
    setIds((prev) => ({ ...prev, [key]: [] }))
  }, [])

  return { getSelectedIds, addIdToSelection, removeIdFromSelection, clearIds }
})

export { CompareProvider, useCompareProvider }
