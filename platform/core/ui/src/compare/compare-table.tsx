/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

"use client"

import type { FC } from "react"
import { Fragment, useCallback, useEffect, useMemo, useState } from "react"
import { ChevronDownIcon, ChevronUpIcon, FoldVerticalIcon, UnfoldVerticalIcon } from "lucide-react"

import { Button } from "../button"
import { Checkbox } from "../checkbox"
import { cn } from "../index"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../table"

type CompareDataRow = { label: string; value?: string | number | null }

export type CompareDataGroup = {
  id: string
  label: string
  rows: CompareDataRow[]
}

export type CompareDataType = {
  id: string
  label: string
  groups: CompareDataGroup[]
}
type ComparePagePropsType = {
  items: CompareDataType[]
}

/**
 * Compares an input string against a reference string and returns an array of
 * segments indicating which parts match and which do not.
 *
 * @param {string} inputStr - The input string to compare.
 * @param {string} refStr - The reference string for comparison.
 * @returns {Array<{ mismatch: boolean; chars: string }>} - An array of objects,
 *          each containing a `chars` string segment and a `mismatch` boolean
 *          indicating if the segment differs from the reference.
 */
const getStringSlices = (inputStr: string, refStr: string): Array<{ mismatch: boolean; chars: string }> => {
  const result: Array<{ mismatch: boolean; chars: string }> = []

  let buffer = ""
  let isMismatch = false

  for (let i = 0; i < inputStr.length; i++) {
    const char = inputStr[i]
    const mismatch = char !== refStr[i]

    if (mismatch !== isMismatch) {
      // Push buffered text before switching state
      if (buffer) {
        result.push({ mismatch: isMismatch, chars: buffer })
        buffer = ""
      }
      isMismatch = mismatch
    }
    buffer += char
  }

  // Push any remaining buffered text
  if (buffer) {
    result.push({ mismatch: isMismatch, chars: buffer })
  }

  return result
}

/**
 * Component for visually comparing an input value against a reference value.
 * Highlights differences by wrapping mismatched text segments in a background color.
 *
 * @component
 * @param {Object} props - The component props.
 * @param {string | number} props.input - The input value to compare.
 * @param {string | number} props.reference - The reference value to compare against.
 * @returns {JSX.Element} - A JSX element with differences highlighted.
 */
const StringComparison: FC<{ input: string | number; reference: string | number }> = ({ input, reference }) => {
  const inputStr = String(input)
  const refStr = String(reference)

  if (typeof input === "number") {
    return input === reference ? input : <span className="bg-amber-300">{input}</span>
  }

  const slices = getStringSlices(inputStr, refStr)
  return (
    <>
      {slices.map((slice, index) => {
        return (
          <span key={index} className={cn(slice.mismatch && "bg-amber-300")}>
            {slice.chars}
          </span>
        )
      })}
    </>
  )
}

/**
 * CompareTable component displays a table comparing multiple sets of data items,
 * allowing users to select a reference item and visually highlight differences.
 *
 * @component
 * @param {ComparePagePropsType} props - Component props
 * @param {CompareDataType[]} props.items - An array of comparison data objects
 *
 * @example
 * const items = [
 *   {
 *     id: "1",
 *     label: "Item 1",
 *     groups: [
 *       { id: "group1", label: "Group 1", rows: [{ label: "Row 1", value: "Value A" }] }
 *     ]
 *   },
 *   {
 *     id: "2",
 *     label: "Item 2",
 *     groups: [
 *       { id: "group1", label: "Group 1", rows: [{ label: "Row 1", value: "Value B" }] }
 *     ]
 *   }
 * ];
 *
 * <CompareTable items={items} />
 *
 * @returns {JSX.Element} The rendered table component
 */

const CompareTable: FC<ComparePagePropsType> = ({ items }) => {
  const [referenceItem, setReferenceItem] = useState(0)
  const [collapsedGroups, setCollapsedGroups] = useState<string[]>([])

  const allGroupIds = useMemo(() => items[0].groups.map(({ id }) => id), [items])

  /**
   * Toggles the visibility of a group section in the table.
   * @param {string} groupId - The ID of the group to toggle
   */
  const toggleGroup = useCallback((groupId: string) => {
    setCollapsedGroups((prev) => {
      if (!prev.includes(groupId)) {
        return [...prev, groupId]
      } else {
        return prev.filter((group) => group !== groupId)
      }
    })
  }, [])

  /**
   * Toggles the expansion/collapse state of all groups.
   */
  const toggleAll = useCallback(() => {
    setCollapsedGroups(!collapsedGroups.length ? allGroupIds : [])
  }, [allGroupIds, collapsedGroups.length])

  useEffect(() => {
    if (!items[referenceItem]) {
      setReferenceItem(0)
    }
  }, [items, referenceItem])

  return (
    <div>
      <div className="mb-2 flex justify-end">
        <Button size="sm" variant="outline" onClick={toggleAll}>
          {!collapsedGroups.length ? (
            <FoldVerticalIcon className="mr-2 size-4" />
          ) : (
            <UnfoldVerticalIcon className="mr-2 size-4" />
          )}
          {`${!collapsedGroups.length ? "Collapse" : "Expand"} All`}
        </Button>
      </div>
      <div className="rounded-md border bg-background p-1">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="min-w-[250px]" />
              {items.map((item) => (
                <TableHead key={item.id} className="max-w-[350px]">
                  {item.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell />
              {items.map((item, index) => (
                <TableCell key={item.id}>
                  <div className="flex items-center gap-1">
                    <Checkbox
                      checked={referenceItem === index}
                      onCheckedChange={() => setReferenceItem(index)}
                      aria-label="Use as reference"
                    />
                    Use as reference
                  </div>
                </TableCell>
              ))}
            </TableRow>
            {items[0].groups.map((group, groupIndex) => (
              <Fragment key={group.id}>
                <TableRow>
                  <td
                    className="cursor-pointer bg-gray-100 p-2 align-middle"
                    colSpan={items.length + 1}
                    onClick={() => toggleGroup(group.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="sticky left-2 text-sm font-semibold">{group.label}</span>
                      <span className="sticky right-2">
                        {collapsedGroups.includes(group.id) ? <ChevronDownIcon /> : <ChevronUpIcon />}
                      </span>
                    </div>
                  </td>
                </TableRow>
                {collapsedGroups.includes(group.id) ? null : (
                  <>
                    {group.rows.map((row, rowIndex) => (
                      <TableRow>
                        <TableCell className="min-w-[250px]" width={250}>
                          {row.label}
                        </TableCell>
                        {items.map((item) => (
                          <TableCell key={item.id} width={300}>
                            <StringComparison
                              input={item.groups[groupIndex].rows[rowIndex].value ?? "-"}
                              reference={items[referenceItem].groups[groupIndex].rows[rowIndex].value ?? "-"}
                            />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </>
                )}
              </Fragment>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

export default CompareTable
