/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { FC } from "react"
import { useCallback, useEffect, useMemo, useState } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import { ChevronDownIcon, ChevronUpIcon, Trash2Icon } from "lucide-react"

import { Button } from "../button"
import { Link } from "../link"
import { Skeleton } from "../skeleton"
import { Tooltip, TooltipContent, TooltipTrigger } from "../tooltip"
import { useCompareProvider } from "./compare-provider"

export type CompareWidgetItem = {
  id: string
  summary: string
  picture?: string
}

type CompareWidgetProps = {
  compareKey: string
  fetchItems: (ids: string[]) => Promise<CompareWidgetItem[]>
  comparisonResultPageUrl: (ids: string[]) => string
  routes: string[]
  excludedRoutes?: string[]
}

const matchRoute = (pathname: string, searchParams: URLSearchParams, routePatterns: string[]) => {
  return routePatterns.some((pattern) => {
    const [pathPattern, queryString] = pattern.split("?") // Split path & query params
    const regex = new RegExp("^" + pathPattern.replace(/:[^/]+/g, "[^/]+") + "$")

    // If path doesn't match, return false
    if (!regex.test(pathname)) return false

    // If there's no query condition, it's a match
    if (!queryString) return true

    // Parse expected query params
    const expectedParams = new URLSearchParams(queryString)
    return Array.from(expectedParams.entries()).every(([key, expectedValue]) => searchParams.get(key) === expectedValue)
  })
}

const CompareWidget: FC<CompareWidgetProps> = ({
  compareKey,
  fetchItems,
  comparisonResultPageUrl,
  routes,
  excludedRoutes,
}) => {
  const pathname = usePathname()
  const params = useSearchParams()

  const { getSelectedIds, removeIdFromSelection, clearIds } = useCompareProvider()
  const ids = getSelectedIds("techSolutions")

  const [loading, setLoading] = useState(false)
  const [items, setItems] = useState<CompareWidgetItem[] | null>(null)
  const [open, setOpen] = useState(false)

  const onRemove = useCallback(
    (id: string) => {
      return removeIdFromSelection(compareKey, id)
    },
    [compareKey, removeIdFromSelection]
  )

  const onClear = useCallback(() => {
    return clearIds(compareKey)
  }, [clearIds, compareKey])

  const popupVisible = useMemo(() => {
    return matchRoute(pathname, params, routes) && !matchRoute(pathname, params, excludedRoutes ?? []) && ids.length
  }, [excludedRoutes, ids.length, params, pathname, routes])

  useEffect(() => {
    setItems(null)
  }, [ids])

  useEffect(() => {
    if (!loading && ids.length && !items) {
      setLoading(true)
      fetchItems(ids)
        .then((data) => setItems(data))
        .catch((err) => console.error(err))
        .finally(() => setLoading(false))
    }
  }, [fetchItems, ids, items, loading])

  if (!popupVisible) return null

  return (
    <div className="w-[300px] max-w-[100%]">
      <Button className="w-[100%] justify-between rounded-b-none" onClick={() => setOpen((prev) => !prev)}>
        {ids.length} items in list
        {open ? <ChevronUpIcon className="size-4" /> : <ChevronDownIcon className="size-4" />}
      </Button>
      {open && (
        <div className="flex w-full flex-col gap-1 bg-white p-2">
          {loading && (
            <>
              {Array.from({ length: ids.length }, (_, i) => i).map((i) => (
                <Skeleton key={i} className="h-6 w-full" />
              ))}
            </>
          )}
          {!loading &&
            items?.map(({ id, summary, picture }) => (
              <div key={id} className="flex w-full items-center gap-1">
                {picture && (
                  <img
                    width={40}
                    height={40}
                    className="oblect-center h-[40px] w-[40px] shrink-0 rounded-md object-cover"
                    src={picture}
                    alt=""
                  />
                )}
                <div className="shrink grow text-sm text-gray-600">{summary}</div>
                <div className="shrink-0">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        color="secondary"
                        className="h-6 px-1"
                        onClick={() => onRemove(id)}
                      >
                        <Trash2Icon className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Remove from comparison</TooltipContent>
                  </Tooltip>
                </div>
              </div>
            ))}
          <div className="flex items-center gap-1">
            {ids.length ? (
              <Link href={comparisonResultPageUrl(ids)}>
                <Button size="sm">Compare Now</Button>
              </Link>
            ) : null}
            <Button size="sm" variant="outline" onClick={onClear}>
              Clear List
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

export default CompareWidget
