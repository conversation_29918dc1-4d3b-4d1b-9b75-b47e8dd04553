/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

import type { FC, PropsWithChildren } from "react"

import type { CompareWidgetItem } from "./compare-widget"
import { CompareProvider } from "./compare-provider"
import CompareWidget from "./compare-widget"

export type ComparisonConfig = {
  id: string
  routes: Array<string>
  excludeRoutes?: Array<string>
  fetchItems: (ids: string[]) => Promise<CompareWidgetItem[]>
  comparisonResultPageUrl: (ids: string[]) => string
}

type CompareWrapperPropsType = {
  configs: ComparisonConfig[]
  limit?: number
}

export const CompareWrapper: FC<PropsWithChildren<CompareWrapperPropsType>> = ({ children, configs, limit = 5 }) => {
  return (
    <CompareProvider value={{ limit }}>
      {children}
      <div className="fixed bottom-0 right-0">
        {configs.map((config) => (
          <CompareWidget
            key={config.id}
            compareKey={config.id}
            fetchItems={config.fetchItems}
            comparisonResultPageUrl={config.comparisonResultPageUrl}
            routes={config.routes}
            excludedRoutes={config.excludeRoutes}
          />
        ))}
      </div>
    </CompareProvider>
  )
}

export default CompareWrapper
