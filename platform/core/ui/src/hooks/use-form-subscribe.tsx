/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { Control, EventType, FieldPath, FieldPathValue, FieldPathValues, FieldValues } from "react-hook-form"
import { useCallback, useEffect, useMemo } from "react"
import useEvent from "react-use-event-hook"

import type { StartsWith } from "@kreios/utils/startsWith"
import { startsWith } from "@kreios/utils/startsWith"

// eslint-disable-next-line react-compiler/react-compiler
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */

export type UseFormSubscribeHook<TFieldValues extends FieldValues = FieldValues> = {
  <
    TFieldNames extends readonly FieldPath<TFieldValues>[],
    TCallback extends (
      value: FieldPathValues<TFieldValues, TFieldNames>,
      payload: {
        name?: StartsWith<FieldPath<TFieldValues>, TFieldNames[number]>
        type?: EventType
        values?: FieldValues
      }
    ) => void,
  >(
    names: readonly [...TFieldNames],
    callback: TCallback
  ): void

  <
    TFieldName extends FieldPath<TFieldValues>,
    TCallback extends (
      value: FieldPathValue<TFieldValues, TFieldName>,
      payload: {
        name?: StartsWith<FieldPath<TFieldValues>, TFieldName>
        type?: EventType
        values?: FieldValues
      }
    ) => void,
  >(
    name: TFieldName,
    callback: TCallback
  ): void
  <
    TCallback extends (
      value: TFieldValues,
      payload: {
        name?: FieldPath<TFieldValues>
        type?: EventType
        values?: FieldValues
      }
    ) => void,
  >(
    callback: TCallback
  ): void
}

/**
 * Creates a form subscription hook that allows subscribing to form field changes.
 *
 * @param control - The control object from react-hook-form
 * @returns A hook function that can be used to subscribe to form field changes
 *
 * @example
 * ```tsx
 * import { useCreateFormSubscribe } from 'react-hook-form-subscribe';
 *
 * const useFormSubscribe = useCreateFormSubscribe(control);
 * ```
 *
 * The returned `useFormSubscribe` hook can be used in three ways:
 *
 * 1. Subscribe to a single field:
 * ```tsx
 * useFormSubscribe('firstname', (firstname) => console.log(firstname));
 * ```
 *
 * 2. Subscribe to multiple fields:
 * ```tsx
 * useFormSubscribe(['firstname', 'lastname'], ([firstname, lastname]) =>
 *   console.log(firstname, lastname)
 * );
 * ```
 *
 * 3. Subscribe to all form changes:
 * ```tsx
 * useFormSubscribe((values) => console.log(values));
 * ```
 *
 */
export const useCreateFormSubscribe = <TFieldValues extends FieldValues = FieldValues>(
  control: Control<TFieldValues>
) => {
  const useFormSubscribe: UseFormSubscribeHook<TFieldValues> = useCallback(
    <
      TFieldNames extends readonly FieldPath<TFieldValues>[],
      TFieldName extends FieldPath<TFieldValues>,
      TCallback extends (
        value: FieldPathValue<TFieldValues, TFieldName> | FieldPathValues<TFieldValues, TFieldNames> | TFieldValues,
        payload: {
          name?:
            | StartsWith<FieldPath<TFieldValues>, TFieldName>
            | StartsWith<FieldPath<TFieldValues>, TFieldNames[number]>
            | FieldPath<TFieldValues>
          type?: EventType
          values?: FieldValues
        }
      ) => void,
    >(
      nameOrCallback: TFieldName | readonly [...TFieldNames] | TCallback,
      callbackOrNothing?: TCallback
    ) => {
      const { name, callback } =
        typeof nameOrCallback === "function"
          ? {
              name: undefined,
              callback: nameOrCallback,
            }
          : {
              name: nameOrCallback,
              callback: callbackOrNothing!,
            }
      const fn = useEvent(callback)

      const shouldSend = useMemo<(eventName: string) => boolean>(
        () => {
          if (name instanceof Array) return (eventName) => name.some((n) => startsWith(eventName, n))
          if (name) return (eventName) => startsWith(eventName, name)
          return () => true
        },
        // eslint-disable-next-line react-compiler/react-compiler
        // eslint-disable-next-line react-hooks/exhaustive-deps
        name instanceof Array ? name : [name]
      )

      useEffect(() => {
        // Watch has been renamed to values in https://github.com/react-hook-form/react-hook-form/commit/a8fb1a1ca7e9ab98545aaf1040a36f9c043cc69c
        // To support both versions, we need to use the old name for older versions
        const subject =
          // @ts-expect-error we are accessing a private property
          control._subjects.values ?? (control._subjects.watch as typeof control._subjects.values)

        return subject.subscribe({
          next: (payload) => {
            if (payload.name && shouldSend(payload.name)) {
              fn(
                // @ts-expect-error types don't align
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                control._getWatch(name),
                payload as {
                  name?:
                    | StartsWith<FieldPath<TFieldValues>, TFieldName>
                    | StartsWith<FieldPath<TFieldValues>, TFieldNames[number]>
                    | FieldPath<TFieldValues>
                  type?: EventType
                  values?: FieldValues
                }
              )
            }
          },
        }).unsubscribe
        // eslint-disable-next-line react-compiler/react-compiler
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [control, fn, ...(name instanceof Array ? name : [name]), shouldSend])
    },
    [control]
  )

  return useFormSubscribe
}
