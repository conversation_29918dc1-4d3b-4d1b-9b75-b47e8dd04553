/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

import * as React from "react"
import { CheckIcon, TriangleAlertIcon } from "lucide-react"

import { cn } from "."

interface StepperContextValue {
  value: string
  onValueChange: (value: string) => void
  getCurrentStepIndex?: () => number
}

const StepperContext = React.createContext<StepperContextValue | undefined>(undefined)

const Stepper = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value: string
    onValueChange: (value: string) => void
    defaultValue?: string
  }
>(({ className, value, onValueChange, children, ...props }, ref) => {
  const contextValue = React.useMemo(
    () => ({
      value: value,
      onValueChange: onValueChange,
      getCurrentStepIndex: undefined,
    }),
    [value, onValueChange]
  )

  return (
    <StepperContext.Provider value={contextValue}>
      <div ref={ref} className={cn("w-full", className)} {...props}>
        {children}
      </div>
    </StepperContext.Provider>
  )
})
Stepper.displayName = "Stepper"

interface StepProps {
  value: string
  children: React.ReactNode
  index?: number
  isCompleted?: boolean
  isActive?: boolean
  isLast?: boolean
}

const StepperList = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    const context = React.useContext(StepperContext)
    if (!context) throw new Error("StepperList must be used within Stepper")

    const { value } = context
    const steps = React.Children.toArray(props.children) as React.ReactElement<StepProps>[]
    const currentIndex = steps.findIndex((step) => React.isValidElement(step) && step.props.value === value)
    const getCurrentStepIndex = React.useCallback(() => currentIndex, [currentIndex])
    const contextRef = React.useRef(context)
    contextRef.current = context
    React.useEffect(() => {
      contextRef.current.getCurrentStepIndex = getCurrentStepIndex
      return () => {
        contextRef.current.getCurrentStepIndex = undefined
      }
    }, [getCurrentStepIndex])

    return (
      <div ref={ref} className={cn("mb-8 flex w-full items-start justify-between gap-4", "relative", className)}>
        {React.Children.map(steps, (step, index) => {
          if (!React.isValidElement(step)) return null
          const isCompleted = index < currentIndex
          const isActive = step.props.value === value

          return React.cloneElement(step, {
            ...step.props,
            index,
            isCompleted,
            isActive,
            isLast: index === steps.length - 1,
          })
        })}
      </div>
    )
  }
)
StepperList.displayName = "StepperList"

const StepperTrigger = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value: string
    index?: number
    isCompleted?: boolean
    isActive?: boolean
    isLast?: boolean
    color?: string
    status?: "complete" | "warning" | "default"
  }
>(({ className, children, value, index = 0, isCompleted, isActive, isLast, color, status, ...props }, ref) => {
  const context = React.useContext(StepperContext)
  if (!context) throw new Error("StepperTrigger must be used within Stepper")

  return (
    <div
      ref={ref}
      className={cn("flex w-[100%] flex-col items-center", "relative", className)}
      onClick={() => context.onValueChange(value)}
      {...props}
    >
      <div className="flex flex-col items-center">
        {/* Circle */}
        <div
          className={cn(
            "z-10 flex h-12 w-12 items-center justify-center rounded-full border-2 bg-background",
            "cursor-pointer transition-colors duration-200",
            status === "complete" && "border-green-500 bg-green-500 text-white",
            status === "warning" && "border-yellow-500 bg-yellow-500 text-white",
            !status && !isActive && !isCompleted && "border-muted text-muted-foreground"
          )}
        >
          {status === "complete" ? (
            <CheckIcon className="h-6 w-6" />
          ) : status === "warning" ? (
            <TriangleAlertIcon className="h-6 w-6" />
          ) : (
            <span className="text-md font-medium">{index + 1}</span>
          )}
        </div>

        {/* Label */}
        <span
          className={cn(
            "mt-2 cursor-pointer text-center text-sm font-medium",
            color && `text-${color}`,
            isActive ? "text-green-500 underline decoration-2 underline-offset-4" : "text-muted-foreground"
          )}
        >
          {children}
        </span>
      </div>

      {/* Connecting Line */}
      {!isLast && (
        <div
          className={cn(
            "absolute left-[calc(50%+24px)] top-6",
            "h-[2px] w-[calc(100%-24px)]",
            "transition-colors duration-200",
            status === "complete"
              ? "bg-green-500"
              : status === "warning"
                ? "bg-yellow-500"
                : isCompleted && context.getCurrentStepIndex && index < context.getCurrentStepIndex()
                  ? "bg-primary"
                  : "bg-gray-200"
          )}
        />
      )}
    </div>
  )
})
StepperTrigger.displayName = "StepperTrigger"

const StepperContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & { value: string }>(
  ({ className, value, children, ...props }, ref) => {
    const context = React.useContext(StepperContext)
    if (!context) throw new Error("StepperContent must be used within Stepper")

    return context.value === value ? (
      <div ref={ref} className={cn(className)} {...props}>
        {children}
      </div>
    ) : null
  }
)
StepperContent.displayName = "StepperContent"

export { Stepper, StepperList, StepperTrigger, StepperContent }
