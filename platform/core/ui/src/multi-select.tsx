/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

import type { KeyboardEvent } from "react"
import React, { createContext, forwardRef, useCallback, useContext, useState } from "react"
import { Command as CommandPrimitive } from "cmdk"
import { Check, X as RemoveIcon } from "lucide-react"

import { cn } from "."
import { Badge } from "./badge"
import { Command, CommandEmpty, CommandItem, CommandList } from "./command"

interface MultiSelectorProps extends React.ComponentPropsWithoutRef<typeof CommandPrimitive> {
  values: string[]
  onValuesChange: (value: string[]) => void
  loop?: boolean
}

interface MultiSelectContextProps {
  value: string[]
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onValueChange: (value: any) => void
  open: boolean
  setOpen: (value: boolean) => void
  inputValue: string
  setInputValue: React.Dispatch<React.SetStateAction<string>>
  activeIndex: number
  setActiveIndex: React.Dispatch<React.SetStateAction<number>>
  ref: React.RefObject<HTMLInputElement>
  handleSelect: (e: React.SyntheticEvent<HTMLInputElement>) => void
}

const MultiSelectContext = createContext<MultiSelectContextProps | null>(null)

const useMultiSelect = () => {
  const context = useContext(MultiSelectContext)
  if (!context) {
    throw new Error("useMultiSelect must be used within MultiSelectProvider")
  }
  return context
}

/**
 * MultiSelect Docs: {@link: https://shadcn-extension.vercel.app/docs/multi-select}
 */

// TODO : expose the visibility of the popup

const MultiSelector = ({
  values: value,
  onValuesChange: onValueChange,
  loop = false,
  className,
  children,
  dir,
  ...props
}: MultiSelectorProps) => {
  const [inputValue, setInputValue] = useState("")
  const [open, setOpen] = useState<boolean>(false)
  const [activeIndex, setActiveIndex] = useState<number>(-1)
  const inputRef = React.useRef<HTMLInputElement>(null)
  const [isValueSelected, setIsValueSelected] = React.useState(false)
  const [selectedValue, setSelectedValue] = React.useState("")

  const onValueChangeHandler = useCallback(
    (val: string) => {
      if (value.includes(val)) {
        onValueChange(value.filter((item) => item !== val))
      } else {
        onValueChange([...value, val])
      }
    },

    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [value]
  )

  const handleSelect = React.useCallback(
    (e: React.SyntheticEvent<HTMLInputElement>) => {
      e.preventDefault()
      const target = e.currentTarget
      const selection = target.value.substring(target.selectionStart ?? 0, target.selectionEnd ?? 0)

      setSelectedValue(selection)
      setIsValueSelected(selection === inputValue)
    },
    [inputValue]
  )

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLDivElement>) => {
      e.stopPropagation()
      const target = inputRef.current

      if (!target) return

      const moveNext = () => {
        const nextIndex = activeIndex + 1
        setActiveIndex(nextIndex > value.length - 1 ? (loop ? 0 : -1) : nextIndex)
      }

      const movePrev = () => {
        const prevIndex = activeIndex - 1
        setActiveIndex(prevIndex < 0 ? value.length - 1 : prevIndex)
      }

      const moveCurrent = () => {
        const newIndex = activeIndex - 1 <= 0 ? (value.length - 1 === 0 ? -1 : 0) : activeIndex - 1
        setActiveIndex(newIndex)
      }

      switch (e.key) {
        case "ArrowLeft":
          if (dir === "rtl") {
            if (value.length > 0 && (activeIndex !== -1 || loop)) {
              moveNext()
            }
          } else {
            if (value.length > 0 && target.selectionStart === 0) {
              movePrev()
            }
          }
          break

        case "ArrowRight":
          if (dir === "rtl") {
            if (value.length > 0 && target.selectionStart === 0) {
              movePrev()
            }
          } else {
            if (value.length > 0 && (activeIndex !== -1 || loop)) {
              moveNext()
            }
          }
          break

        case "Backspace":
        case "Delete":
          if (value.length > 0) {
            if (activeIndex !== -1 && activeIndex < value.length) {
              onValueChangeHandler(value[activeIndex])
              moveCurrent()
            } else {
              if (target.selectionStart === 0) {
                if (selectedValue === inputValue || isValueSelected) {
                  onValueChangeHandler(value[value.length - 1])
                }
              }
            }
          }
          break

        case "Enter":
          setOpen(true)
          break

        case "Escape":
          if (activeIndex !== -1) {
            setActiveIndex(-1)
          } else if (open) {
            setOpen(false)
          }
          break
      }
    },
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [value, inputValue, activeIndex, loop]
  )

  return (
    <MultiSelectContext.Provider
      value={{
        value,
        onValueChange: onValueChangeHandler,
        open,
        setOpen,
        inputValue,
        setInputValue,
        activeIndex,
        setActiveIndex,
        ref: inputRef,
        handleSelect,
      }}
    >
      <Command
        onKeyDown={handleKeyDown}
        className={cn("flex flex-col overflow-visible bg-transparent", className)}
        dir={dir}
        {...props}
      >
        {children}
      </Command>
    </MultiSelectContext.Provider>
  )
}

const MultiSelectorTrigger = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { disabled?: boolean; getLabel?: (value: string) => string }
>(({ className, children, disabled, getLabel, ...props }, ref) => {
  const { value, onValueChange, activeIndex } = useMultiSelect()

  const mousePreventDefault = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-wrap gap-1 rounded-md border border-input bg-transparent p-1 py-2 text-sm shadow-sm transition-colors disabled:cursor-not-allowed disabled:opacity-50",
        {
          "outline-none focus-within:ring-1 focus-within:ring-ring": activeIndex === -1,
        },
        className
      )}
      aria-disabled={disabled}
      {...props}
    >
      {value.map((item, index) => {
        const label = getLabel ? getLabel(item) : item
        return (
          <Badge
            key={item}
            className={cn(
              "flex h-[18px] items-center gap-1 rounded-xl px-1",
              activeIndex === index && "ring-2 ring-muted-foreground"
            )}
            variant={"secondary"}
          >
            <span className="text-xs">{label}</span>
            <button
              aria-disabled={disabled}
              disabled={disabled}
              aria-label={`Remove ${item} option`}
              aria-roledescription="button to remove option"
              type="button"
              onMouseDown={mousePreventDefault}
              onClick={() => onValueChange(item)}
            >
              <span className="sr-only">Remove {item} option</span>
              {!disabled && <RemoveIcon className="h-4 w-4 hover:stroke-destructive" />}
            </button>
          </Badge>
        )
      })}
      {children}
    </div>
  )
})

MultiSelectorTrigger.displayName = "MultiSelectorTrigger"

const MultiSelectorInput = forwardRef<
  React.ElementRef<typeof CommandPrimitive.Input>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>
>(({ className, ...props }, ref) => {
  const { setOpen, inputValue, activeIndex, setActiveIndex, handleSelect } = useMultiSelect()

  return (
    <CommandPrimitive.Input
      {...props}
      tabIndex={0}
      ref={ref}
      value={inputValue}
      onSelect={handleSelect}
      onBlur={() => setOpen(false)}
      onFocus={() => setOpen(true)}
      onClick={() => setActiveIndex(-1)}
      className={cn(
        "ml-2 h-[18px] flex-1 bg-transparent outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
        className,
        activeIndex !== -1 && "caret-transparent"
      )}
    />
  )
})

MultiSelectorInput.displayName = "MultiSelectorInput"

const MultiSelectorContent = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(({ children }, ref) => {
  const { open } = useMultiSelect()
  return (
    <div ref={ref} className="relative">
      {open && children}
    </div>
  )
})

MultiSelectorContent.displayName = "MultiSelectorContent"

const MultiSelectorList = forwardRef<
  React.ElementRef<typeof CommandPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>
>(({ className, children }, ref) => {
  return (
    <CommandList
      ref={ref}
      className={cn(
        "scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted-foreground dark:scrollbar-thumb-muted scrollbar-thumb-rounded-lg absolute top-0 z-10 flex w-full flex-col gap-2 rounded-md border border-input bg-background p-2 shadow-md transition-colors",
        className
      )}
    >
      {children}
      <CommandEmpty>
        <span className="text-muted-foreground">No results found</span>
      </CommandEmpty>
    </CommandList>
  )
})

MultiSelectorList.displayName = "MultiSelectorList"

const MultiSelectorItem = forwardRef<
  React.ElementRef<typeof CommandPrimitive.Item>,
  { value: string } & React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>
>(({ className, value, children, ...props }, ref) => {
  const { value: Options, onValueChange, setInputValue } = useMultiSelect()

  const mousePreventDefault = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const isIncluded = Options.includes(value)
  return (
    <CommandItem
      ref={ref}
      {...props}
      onSelect={() => {
        onValueChange(value)
        setInputValue("")
      }}
      className={cn(
        "flex cursor-pointer justify-between rounded-md px-2 py-1 transition-colors",
        className,
        isIncluded && "cursor-default opacity-50",
        props.disabled && "cursor-not-allowed opacity-50"
      )}
      onMouseDown={mousePreventDefault}
    >
      {children}
      {isIncluded && <Check className="h-4 w-4" />}
    </CommandItem>
  )
})

MultiSelectorItem.displayName = "MultiSelectorItem"

export {
  MultiSelector,
  MultiSelectorContent,
  MultiSelectorInput,
  MultiSelectorItem,
  MultiSelectorList,
  MultiSelectorTrigger,
}
