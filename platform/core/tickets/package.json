{"name": "@kreios/tickets", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./env": "./env.ts", "./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@t3-oss/env-core": "0.11.1", "azure-devops-node-api": "14.1.0", "html-to-text": "9.0.4", "marked": "14.1.3", "sanitize-html": "2.13.1", "turndown": "7.2.0", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/html-to-text": "9.0.4", "@types/sanitize-html": "2.13.0", "@types/turndown": "5.0.5", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}