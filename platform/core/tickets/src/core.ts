/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { TextContent } from "./utils"
import { normalize } from "./utils"

/**
 * Represents a ticket ID
 */
export type TicketID = string

/**
 * Represents a ticket with strongly typed custom fields
 */
export type Ticket<TCustomFields = Record<string, unknown>> = {
  id: string
  version: string
  title: string
  description: TextContent
  customFields: TCustomFields
}

/**
 * Input data for creating or updating a ticket
 */
export type TicketInput<TCustomFields = Record<string, unknown>> = {
  title: string
  description: Partial<TextContent>
  customFields?: TCustomFields
}

/**
 * Provider interface for ticket management systems with typed custom fields
 */
export type TicketProvider<TCustomFields = Record<string, unknown>> = {
  /** Provider metadata */
  readonly metadata: {
    /** Unique identifier for the provider */
    id: string
    /** Display name of the provider */
    name: string
  }

  /** Lists all available ticket IDs */
  listTicketIds(): Promise<TicketID[]>
  /** Retrieves a single ticket by ID */
  getTicket(id: TicketID): Promise<Ticket<TCustomFields>>
  /** Find tickets by custom field criteria */
  findByCustomFields(criteria: Partial<TCustomFields>): Promise<Ticket<TCustomFields>[]>
  /** Creates a new ticket and returns its ID */
  createTicket(data: TicketInput<TCustomFields>): Promise<TicketID>
  /** Updates an existing ticket */
  updateTicket(id: TicketID, data: Partial<TicketInput<TCustomFields>>): Promise<void>
  /** Deletes a ticket */
  deleteTicket(id: TicketID): Promise<void>
  /** Parse and validate custom fields */
  parseCustomFields(obj: unknown): TCustomFields
}

/**
 * Service for managing tickets across different provider implementations with typed custom fields
 */
export class TicketService<TCustomFields> {
  /**
   * Creates a new TicketService instance
   * @param provider - The ticket provider implementation to use
   */
  constructor(private readonly provider: TicketProvider<TCustomFields>) {
    console.info(`TicketService initialized with provider: ${provider.metadata.name} (${provider.metadata.id})`)
  }

  /**
   * Lists all available ticket IDs from the provider
   * @returns Array of ticket IDs
   */
  async listTicketIds(): Promise<TicketID[]> {
    console.info(`[${this.provider.metadata.name}] Listing ticket IDs`)
    try {
      const ids = await this.provider.listTicketIds()
      console.info(`[${this.provider.metadata.name}] Found ${ids.length} tickets`)
      return ids
    } catch (error) {
      console.error(`[${this.provider.metadata.name}] Failed to list ticket IDs:`, error)
      throw error
    }
  }

  /**
   * Retrieves a single ticket by ID
   * @param id - ID of the ticket to retrieve
   * @returns Normalized ticket data
   */
  async getTicket(id: TicketID): Promise<Ticket<TCustomFields>> {
    console.info(`[${this.provider.metadata.name}] Fetching ticket: ${id}`)
    try {
      const ticket = await this.provider.getTicket(id)
      const normalizedTicket = this.normalizeTicket(ticket)
      console.info(`[${this.provider.metadata.name}] Successfully fetched ticket: ${id}`)
      return normalizedTicket
    } catch (error) {
      console.error(`[${this.provider.metadata.name}] Failed to fetch ticket ${id}:`, error)
      throw error
    }
  }

  /**
   * Finds tickets matching the given custom field criteria
   * @param criteria - Map of custom field key-value pairs to filter by
   * @returns Array of matching tickets
   */
  async find(criteria: Partial<TCustomFields>): Promise<Ticket<TCustomFields>[]> {
    console.info(`[${this.provider.metadata.name}] Finding tickets with criteria:`, criteria)
    try {
      const tickets = await this.provider.findByCustomFields(criteria)
      const normalizedTickets = tickets.map((ticket) => this.normalizeTicket(ticket))
      console.info(`[${this.provider.metadata.name}] Found ${normalizedTickets.length} matching tickets`)
      return normalizedTickets
    } catch (error) {
      console.error(`[${this.provider.metadata.name}] Failed to find tickets:`, error)
      throw error
    }
  }

  /**
   * Finds the first ticket matching the given custom field criteria
   * @param criteria - Map of custom field key-value pairs to filter by
   * @returns The first matching ticket or undefined if no match is found
   */
  async findFirst(criteria: Partial<TCustomFields>): Promise<Ticket<TCustomFields> | undefined> {
    const tickets = await this.find(criteria)
    return tickets[0]
  }

  /**
   * Creates a new ticket
   * @param data - Input data for the new ticket
   * @returns Created ticket
   */
  async createTicket(data: TicketInput<TCustomFields>): Promise<Ticket<TCustomFields>> {
    console.info(`[${this.provider.metadata.name}] Creating new ticket: ${data.title}`)
    try {
      const validatedData = {
        ...data,
        description: normalize(data.description),
        customFields: this.provider.parseCustomFields(data.customFields ?? {}),
      }
      const ticketId = await this.provider.createTicket(validatedData)
      return this.getTicket(ticketId)
    } catch (error) {
      console.error(`[${this.provider.metadata.name}] Failed to create ticket:`, error)
      throw error
    }
  }

  /**
   * Updates an existing ticket
   * @param id - ID of the ticket to update
   * @param data - Partial ticket data to update
   * @returns Updated ticket
   */
  async updateTicket(id: TicketID, data: Partial<TicketInput<TCustomFields>>): Promise<Ticket<TCustomFields>> {
    console.info(`[${this.provider.metadata.name}] Updating ticket: ${id}`)
    try {
      const validatedData = {
        ...data,
        description: data.description ? normalize(data.description) : undefined,
        customFields: this.provider.parseCustomFields(data.customFields ?? {}),
      }
      await this.provider.updateTicket(id, validatedData)
      return this.getTicket(id)
    } catch (error) {
      console.error(`[${this.provider.metadata.name}] Failed to update ticket ${id}:`, error)
      throw error
    }
  }

  /**
   * Deletes a ticket
   * @param id - ID of the ticket to delete
   */
  async deleteTicket(id: TicketID): Promise<void> {
    console.info(`[${this.provider.metadata.name}] Deleting ticket: ${id}`)
    try {
      await this.provider.deleteTicket(id)
      console.info(`[${this.provider.metadata.name}] Successfully deleted ticket: ${id}`)
    } catch (error) {
      console.error(`[${this.provider.metadata.name}] Failed to delete ticket ${id}:`, error)
      throw error
    }
  }

  /**
   * Normalizes a ticket by processing its description and custom fields
   * @param ticket - Raw ticket from provider
   * @returns Normalized ticket with processed fields
   */
  private normalizeTicket(ticket: Ticket<TCustomFields>): Ticket<TCustomFields> {
    return {
      ...ticket,
      description: normalize(ticket.description),
      customFields: this.provider.parseCustomFields(ticket.customFields ?? {}),
    }
  }
}

/**
 * Configuration for a ticket service
 */
export type TicketServiceConfig<TCustomFields> = {
  provider: TicketProvider<TCustomFields>
}

/**
 * Creates a ticket service configuration object
 */
export const createTicketServiceConfig = <TCustomFields>(config: TicketServiceConfig<TCustomFields>) => config
