/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { convert as htmlToText } from "html-to-text"
import { marked } from "marked"
import sanitizeHtml from "sanitize-html"
import TurndownService from "turndown" // These are potentially outdated, see https://github.com/mixmark-io/turndown/issues/359

// Configure html-to-text options once
const htmlToTextOptions = {
  selectors: [
    { selector: "h1", options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: "h2", options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: "h3", options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: "p", options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },
    { selector: "br", format: "lineBreak" },
  ],
}

// Configure turndown options once
const turndownService = new TurndownService({
  headingStyle: "atx",
  codeBlockStyle: "fenced",
  emDelimiter: "*",
  strongDelimiter: "**",
})

// Add custom rule for div handling
turndownService.addRule("div-renders-as-block", {
  filter: "div",
  replacement: function (content) {
    return content.trim() + "\n"
  },
})

/**
 * Represents the different text representations of ticket content
 */
export interface TextContent {
  /** HTML representation with safe tags only */
  html: string
  /** Clean markdown representation */
  markdown: string
  /** Plain text without any formatting */
  text: string
}

/**
 * Normalizes a text content object by filling in missing fields, preferring HTML over Markdown over plain text
 * @param textContent - Partial text content to normalize
 * @returns Complete TextContent object with all representations
 */
export const normalize = (textContent: Partial<TextContent>): TextContent => {
  return textContent.html
    ? fromHtml(textContent.html)
    : textContent.markdown
      ? fromMarkdown(textContent.markdown)
      : fromText(textContent.text ?? "")
}

/**
 * Generates all text representations from HTML input
 * @param html - HTML string to convert
 * @returns TextContent object with all representations
 */
export const fromHtml = (html: string): TextContent => {
  const safeHtml = sanitizeHtml(html)
  return {
    html: safeHtml.trim(),
    markdown: turndownService.turndown(safeHtml).trim(),
    text: htmlToText(safeHtml, htmlToTextOptions).trim(),
  }
}

/**
 * Generates all text representations from Markdown input
 * @param markdown - Markdown string to convert
 * @returns TextContent object with all representations
 */
export const fromMarkdown = (markdown: string): TextContent => {
  const html = marked(markdown) as string
  return {
    html: html.trim(),
    markdown,
    text: stripHtml(html).trim(),
  }
}

/**
 * Generates all text representations from plain text input
 * @param text - Plain text string to convert
 * @returns TextContent object with all representations
 */
export const fromText = (text: string): TextContent => {
  return {
    html: text.trim().replace(/\n/g, "<br>"),
    markdown: text.trim(),
    text: text.trim(),
  }
}

/**
 * Strips all HTML tags from a string while preserving semantic structure
 * @param html - HTML string to strip
 * @returns Plain text with preserved structure
 */
const stripHtml = (html: string): string => {
  return htmlToText(html, htmlToTextOptions).trim()
}
