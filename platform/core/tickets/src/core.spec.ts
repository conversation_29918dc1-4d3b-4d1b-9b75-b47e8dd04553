/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { describe, expect, it } from "vitest"
import { z } from "zod"

import { TicketService } from "./core"
import { MockTicketProvider } from "./providers/mock-provider"

describe("TicketService", () => {
  const mockProvider = new MockTicketProvider({
    customFieldsSchema: z.object({}),
  })
  const service = new TicketService(mockProvider)

  it("should list ticket IDs", async () => {
    await mockProvider.createTicket({ title: "Test 1", description: { text: "Description 1" } })
    await mockProvider.createTicket({ title: "Test 2", description: { text: "Description 2" } })

    const ids = await service.listTicketIds()
    expect(ids).toHaveLength(2)
    expect(ids).toEqual(["1", "2"])
  })

  it("should normalize content from HTML", async () => {
    const ticket = await service.createTicket({
      title: "Test",
      description: { html: "<h1>Hello</h1><p>World</p>" },
    })

    const retrieved = await service.getTicket(ticket.id)
    expect(retrieved.description).toHaveProperty("html")
    expect(retrieved.description).toHaveProperty("markdown")
    expect(retrieved.description).toHaveProperty("text")
    expect(retrieved.description.text).toBe("HELLO\nWorld")
  })

  it("should normalize markdown content", async () => {
    const ticket = await service.createTicket({
      title: "Test",
      description: { markdown: "# Hello\nWorld" },
    })

    const retrieved = await service.getTicket(ticket.id)
    expect(retrieved.description.html).toContain("<h1>Hello</h1>")
    expect(retrieved.description.markdown).toBe("# Hello\n\nWorld")
    expect(retrieved.description.text).toBe("HELLO\nWorld")
  })

  it("should normalize plain text content", async () => {
    const ticket = await service.createTicket({
      title: "Test",
      description: { text: "Hello\nWorld" },
    })

    const retrieved = await service.getTicket(ticket.id)
    expect(retrieved.description.html).toBe("Hello<br />World")
    expect(retrieved.description.markdown).toBe("Hello  \nWorld")
    expect(retrieved.description.text).toBe("Hello\nWorld")
  })

  it("should handle empty description", async () => {
    const ticket = await service.createTicket({
      title: "Test",
      description: {},
    })

    const retrieved = await service.getTicket(ticket.id)
    expect(retrieved.description.html).toBe("")
    expect(retrieved.description.markdown).toBe("")
    expect(retrieved.description.text).toBe("")
  })

  it("should create and retrieve a ticket", async () => {
    const created = await service.createTicket({
      title: "New Ticket",
      description: { text: "Test Description" },
    })

    expect(created.id).toBeDefined()
    expect(created.version).toBe("1")
    expect(created.title).toBe("New Ticket")

    const retrieved = await service.getTicket(created.id)
    expect(retrieved).toEqual(created)
  })

  it("should update a ticket", async () => {
    const ticket = await service.createTicket({
      title: "Original Title",
      description: { text: "Original Description" },
    })

    const updated = await service.updateTicket(ticket.id, {
      title: "Updated Title",
    })

    expect(updated.id).toBe(ticket.id)
    expect(updated.version).toBe("2")
    expect(updated.title).toBe("Updated Title")
    expect(updated.description).toEqual(ticket.description)
  })

  it("should delete a ticket", async () => {
    const ticket = await service.createTicket({
      title: "To Delete",
      description: { text: "Will be deleted" },
    })

    await service.deleteTicket(ticket.id)
    await expect(service.getTicket(ticket.id)).rejects.toThrow("Ticket not found")
  })

  it("should find tickets by custom fields", async () => {
    const mockProvider = new MockTicketProvider({
      customFieldsSchema: z.object({
        type: z.string(),
        priority: z.number().optional(),
      }),
    })
    const service = new TicketService(mockProvider)

    // Create some test tickets
    await service.createTicket({
      title: "High Priority Task",
      description: { text: "Description 1" },
      customFields: { type: "task", priority: 1 },
    })

    await service.createTicket({
      title: "Low Priority Task",
      description: { text: "Description 2" },
      customFields: { type: "task", priority: 3 },
    })

    await service.createTicket({
      title: "Bug Report",
      description: { text: "Description 3" },
      customFields: { type: "bug", priority: 1 },
    })

    // Find by type
    const tasks = await service.find({ type: "task" })
    expect(tasks).toHaveLength(2)
    expect(tasks.map((t) => t.title)).toContain("High Priority Task")
    expect(tasks.map((t) => t.title)).toContain("Low Priority Task")

    // Find by type and priority
    const highPriorityTasks = await service.find({ type: "task", priority: 1 })
    expect(highPriorityTasks).toHaveLength(1)
    expect(highPriorityTasks[0].title).toBe("High Priority Task")

    // Find with no matches
    const noMatches = await service.find({ type: "feature" })
    expect(noMatches).toHaveLength(0)
  })

  it("should find the first matching ticket", async () => {
    const mockProvider = new MockTicketProvider({
      customFieldsSchema: z.object({
        type: z.string(),
      }),
    })
    const service = new TicketService(mockProvider)

    // Create test tickets
    await service.createTicket({
      title: "First Task",
      description: { text: "Description 1" },
      customFields: { type: "task" },
    })

    await service.createTicket({
      title: "Second Task",
      description: { text: "Description 2" },
      customFields: { type: "task" },
    })

    // Find first task
    const task = await service.findFirst({ type: "task" })
    expect(task).toBeDefined()
    expect(task?.title).toBe("First Task")

    // Find non-existent type
    const noMatch = await service.findFirst({ type: "feature" })
    expect(noMatch).toBeUndefined()
  })
})
