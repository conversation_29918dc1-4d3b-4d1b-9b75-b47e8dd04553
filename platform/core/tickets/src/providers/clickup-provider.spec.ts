/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { beforeEach } from "node:test"
import { afterAll, beforeAll, describe, expect, it } from "vitest"
import { z } from "zod"

import { fromText } from "../utils"
import { ClickUpProvider } from "./clickup-provider"

/**
 * These tests make real API calls to ClickUp.
 * To run them, you need to set the following environment variables:
 * - CLICKUP_API_TOKEN
 * - CLICKUP_WORKSPACE_ID
 * - CLICKUP_SPACE_ID_TEST (a space dedicated to running these tests)
 * - CLICKUP_LIST_ID_TEST (a list dedicated to running these tests)
 */
const requiredEnvVars = ["CLICKUP_API_TOKEN", "CLICKUP_WORKSPACE_ID", "CLICKUP_SPACE_ID_TEST", "CLICKUP_LIST_ID_TEST"]

const missingEnvVars = !requiredEnvVars.every((v) => process.env[v])

describe.skipIf(missingEnvVars)("ClickUpProvider", () => {
  // Generate a random prefix for this test run to avoid collisions with existing tickets

  const testPrefix = Math.random().toString(36).substring(2, 8)
  const makeDevOpsId = (id: string) => `${testPrefix}-${id}`

  // Test configuration
  const config = {
    apiToken: process.env.CLICKUP_API_TOKEN!,
    workspaceId: process.env.CLICKUP_WORKSPACE_ID!,
    spaceId: process.env.CLICKUP_SPACE_ID_TEST!,
    listId: process.env.CLICKUP_LIST_ID_TEST!,
    customFieldsSchema: z.object({
      "DevOps ID": z.string().optional(),
      "DevOps Version": z.string().optional(),
    }),
  }

  let provider: ClickUpProvider<{
    "DevOps ID"?: string | undefined
    "DevOps Version"?: string | undefined
  }>

  const createdTicketIds: string[] = []

  beforeEach(() => {
    provider = new ClickUpProvider(config)
  })

  // Helper to create a test ticket and track it for cleanup
  const createTestTicket = async (title: string, customFields?: Record<string, unknown>) => {
    const id = await provider.createTicket({
      title,
      description: fromText(`Test description for ${title}`),
      customFields,
    })
    createdTicketIds.push(id)
    return id
  }

  // Clean up all created tickets after tests complete
  afterAll(async () => {
    console.info("Cleaning up test tickets...")
    for (const id of createdTicketIds) {
      try {
        await provider.deleteTicket(id)
      } catch (error) {
        console.warn(`Failed to delete test ticket ${id}:`, error)
      }
    }
  }, 20000) // 20 second timeout for cleanup

  it("should have correct metadata", () => {
    expect(provider.metadata).toEqual({
      id: "clickup",
      name: "ClickUp",
    })
  })

  describe("Basic CRUD operations", () => {
    it("should create and retrieve a ticket", async () => {
      const id = await createTestTicket("Basic CRUD Test", {
        "DevOps ID": makeDevOpsId("crud-1"),
      })
      const ticket = await provider.getTicket(id)

      expect(ticket.id).toBe(id)
      expect(ticket.title).toBe("Basic CRUD Test")
      expect(ticket.description.text).toContain("Test description for Basic CRUD Test")
    })

    it("should update a ticket", async () => {
      const id = await createTestTicket("Update Test", {
        "DevOps ID": makeDevOpsId("update-1"),
      })

      await provider.updateTicket(id, {
        title: "Updated Title",
        description: fromText("Updated description"),
      })

      const updated = await provider.getTicket(id)
      expect(updated.title).toBe("Updated Title")
      expect(updated.description.text).toBe("Updated description")
    })

    it("should delete a ticket", async () => {
      const id = await createTestTicket("Delete Test", {
        "DevOps ID": makeDevOpsId("delete-1"),
      })
      await provider.deleteTicket(id)

      await expect(provider.getTicket(id)).rejects.toThrow()
    })
  })

  describe("Custom fields", () => {
    it("should handle custom fields during creation", async () => {
      const id = await createTestTicket("Custom Fields Test", {
        "DevOps ID": makeDevOpsId("test-123"),
        "DevOps Version": "1",
      })

      const ticket = await provider.getTicket(id)
      expect(ticket.customFields["DevOps ID"]).toBe(makeDevOpsId("test-123"))
      expect(ticket.customFields["DevOps Version"]).toBe("1")
    })

    it("should update custom fields", async () => {
      const id = await createTestTicket("Custom Fields Update Test", {
        "DevOps ID": makeDevOpsId("test-456"),
      })

      await provider.updateTicket(id, {
        customFields: {
          "DevOps ID": makeDevOpsId("test-789"),
        },
      })

      const updated = await provider.getTicket(id)
      expect(updated.customFields["DevOps ID"]).toBe(makeDevOpsId("test-789"))
    })
  })

  describe("Search functionality", () => {
    beforeAll(async () => {
      // Create a set of tickets for search tests
      await Promise.all([
        createTestTicket("Search Test 1", { "DevOps ID": makeDevOpsId("search-1"), "DevOps Version": "1" }),
        createTestTicket("Search Test 2", { "DevOps ID": makeDevOpsId("search-2"), "DevOps Version": "1" }),
        createTestTicket("Search Test 3", { "DevOps ID": makeDevOpsId("search-3"), "DevOps Version": "2" }),
      ])
    })

    it("should find tickets by DevOps ID", async () => {
      const results = await provider.findByCustomFields({
        "DevOps ID": makeDevOpsId("search-1"),
      })

      expect(results).toHaveLength(1)
      expect(results[0].title).toBe("Search Test 1")
    })

    it("should find tickets by multiple criteria", async () => {
      const results = await provider.findByCustomFields({
        "DevOps ID": makeDevOpsId("search-1"),
        "DevOps Version": "1",
      })

      expect(results).toHaveLength(1)
      expect(results[0].title).toBe("Search Test 1")
    })

    it("should return empty array for non-matching criteria", async () => {
      const results = await provider.findByCustomFields({
        "DevOps ID": makeDevOpsId("non-existent"),
      })

      expect(results).toHaveLength(0)
    })
  })
})
