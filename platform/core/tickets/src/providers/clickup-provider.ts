/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { z } from "zod"

import type { Ticket, TicketID, TicketInput, TicketProvider } from "../core"
import { fromMarkdown, fromText } from "../utils"

/**
 * Configuration options for the ClickUp provider
 */
export type ClickUpConfig<TCustomFields = Record<string, unknown>> = {
  /** Base API URL (defaults to https://api.clickup.com/api/v2) */
  apiUrl?: string
  /** Personal API token with appropriate permissions */
  apiToken: string
  /** Workspace ID (team_id in ClickUp API) */
  workspaceId: string
  /** Space ID within the workspace */
  spaceId: string
  /** List ID within the space */
  listId: string
  /** Custom fields schema */
  customFieldsSchema: z.ZodType<TCustomFields>
}

/**
 * Internal representation of a ClickUp task from their API
 */
type ClickUpTask = {
  id: string
  name: string
  description?: string
  markdown_description?: string
  date_updated?: number
  custom_fields: Array<{
    id: string
    name: string
    value: unknown
  }>
  [key: string]: unknown
}

/**
 * Internal representation of data we send to ClickUp's API
 */
type ClickUpTaskInput = {
  name: string
  markdown_description?: string
  custom_fields?: Array<{ id: string; value: unknown }>
  [key: string]: unknown
}

/**
 * ClickUp ticket provider implementation using ClickUp API v2.
 * This provider allows interaction with ClickUp's task management system,
 * treating tasks as tickets in our unified ticket system.
 */
export class ClickUpProvider<TCustomFields = Record<string, unknown>> implements TicketProvider<TCustomFields> {
  readonly metadata = {
    id: "clickup",
    name: "ClickUp",
  } as const

  private baseUrl: string
  private customFieldIdMap: Record<string, string> = {}

  constructor(private config: ClickUpConfig<TCustomFields>) {
    this.baseUrl = config.apiUrl ?? "https://api.clickup.com/api/v2"
  }

  async listTicketIds(): Promise<string[]> {
    const response = await this.request<{ tasks: ClickUpTask[] }>(`/list/${this.config.listId}/task?archived=false`)
    return response.tasks.map((task) => task.id)
  }

  async getTicket(id: string): Promise<Ticket<TCustomFields>> {
    const task = await this.request<ClickUpTask>(`/task/${id}?include_markdown_description=true`)
    return this.fromClickUpToInternal(task)
  }

  async findByCustomFields(criteria: Partial<TCustomFields>): Promise<Ticket<TCustomFields>[]> {
    await this.initCustomFieldMap()

    // Get all tickets from the list
    const response = await this.request<{ tasks: ClickUpTask[] }>(`/list/${this.config.listId}/task?archived=false`)
    const tasks = response.tasks

    // Convert criteria keys to ClickUp custom field IDs
    const criteriaEntries = Object.entries(criteria)
    const clickupCriteria = criteriaEntries.map(([name, value]) => ({
      id: this.customFieldIdMap[name],
      value,
    }))

    // Filter tasks based on custom field criteria
    const matchingTasks = tasks.filter((task) => {
      return clickupCriteria.every((criterion) => {
        const matchingField = task.custom_fields.find((field) => field.id === criterion.id)
        return matchingField && matchingField.value === criterion.value
      })
    })

    // Convert matching tasks to internal format
    return matchingTasks.map((task) => this.fromClickUpToInternal(task))
  }

  async createTicket(data: TicketInput<TCustomFields>): Promise<TicketID> {
    await this.initCustomFieldMap()
    const clickupData = this.fromInternalToClickUp(data)
    const task = await this.request<ClickUpTask>(`/list/${this.config.listId}/task`, {
      method: "POST",
      body: JSON.stringify(clickupData),
    })
    return task.id
  }

  /**
   * Updates a ticket in ClickUp. Due to ClickUp API limitations, custom field updates
   * need to be handled separately from other updates:
   * - Custom fields must be updated one at a time using a dedicated endpoint
   * - Other fields (title, description, etc.) can be updated in a single request
   */
  async updateTicket(id: string, data: Partial<TicketInput<TCustomFields>>): Promise<void> {
    await this.initCustomFieldMap()

    const { custom_fields, ...otherUpdates } = this.fromInternalToClickUp(data, true)

    // Handle custom field updates, if any
    if (custom_fields) {
      await Promise.all(
        Object.values(custom_fields).map(({ id: fieldId, value: fieldValue }) => {
          return this.request(`/task/${id}/field/${fieldId}`, {
            method: "POST",
            body: JSON.stringify({ value: fieldValue }),
          })
        })
      )
    }

    // Handle other updates, if any
    if (Object.keys(otherUpdates).length > 0) {
      await this.request<ClickUpTask>(`/task/${id}`, {
        method: "PUT",
        body: JSON.stringify(otherUpdates),
      })
    }
  }

  async deleteTicket(id: string): Promise<void> {
    await this.request(`/task/${id}`, { method: "DELETE" })
  }

  parseCustomFields(obj: unknown): TCustomFields {
    return this.config.customFieldsSchema.parse(obj)
  }

  /**
   * Makes an authenticated request to the ClickUp API
   */
  private async request<T>(path: string, init?: RequestInit): Promise<T> {
    const response = await fetch(`${this.baseUrl}${path}`, {
      ...init,
      headers: {
        Authorization: this.config.apiToken,
        "Content-Type": "application/json",
        ...init?.headers,
      },
    })

    if (!response.ok) {
      const error = (await response.json().catch(() => ({ err: "Unknown error" }))) as { err: string }
      throw new Error(`ClickUp API error: ${error.err}`)
    }

    return (await response.json().catch(() => ({}))) as T
  }

  /**
   * Initializes the custom field ID mapping from ClickUp's API
   */
  private async initCustomFieldMap(): Promise<void> {
    if (Object.keys(this.customFieldIdMap).length > 0) return

    const response = await this.request<{ fields: Array<{ id: string; name: string }> }>(
      `/list/${this.config.listId}/field`
    )

    this.customFieldIdMap = response.fields.reduce(
      (map, field) => {
        map[field.name] = field.id
        return map
      },
      {} as Record<string, string>
    )
  }

  /**
   * Converts our internal ticket format to ClickUp's API format
   */
  private fromInternalToClickUp(data: Partial<TicketInput<TCustomFields>>, isPartial = false): ClickUpTaskInput {
    // For new tickets, name is required
    if (!isPartial && !data.title) {
      throw new Error("Title is required when creating a new ticket")
    }

    const clickupData: Partial<ClickUpTaskInput> = {}

    // Map title to name
    if (data.title) {
      clickupData.name = data.title
    }

    // Map description
    if (data.description) {
      clickupData.markdown_description = data.description.markdown
    }

    // Map custom fields if present without validation
    if (data.customFields) {
      clickupData.custom_fields = Object.entries(data.customFields).map(([name, value]) => ({
        id: this.customFieldIdMap[name],
        value,
      }))
    }

    return clickupData as ClickUpTaskInput
  }

  /**
   * Converts a ClickUp task to our internal ticket format
   */
  private fromClickUpToInternal(task: ClickUpTask): Ticket<TCustomFields> {
    const description = task.markdown_description ? fromMarkdown(task.markdown_description) : fromText("")

    // Transform custom fields into our format without validation
    const customFields = task.custom_fields.reduce(
      (acc, field) => ({
        ...acc,
        [field.name]: field.value,
      }),
      {} as TCustomFields
    )

    return {
      id: task.id,
      version: task.date_updated?.toString() ?? "",
      title: task.name,
      description,
      customFields,
    }
  }
}
