/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { z } from "zod"

import type { Ticket, TicketID, TicketInput, TicketProvider } from "../core"
import { normalize } from "../utils"

/**
 * Configuration options for the mock provider
 */
export type MockConfig<TCustomFields = Record<string, unknown>> = {
  /** Custom fields schema */
  customFieldsSchema: z.ZodType<TCustomFields>
}

/**
 * Mock implementation of the TicketProvider interface that stores tickets in memory
 */
export class MockTicketProvider<TCustomFields = Record<string, unknown>> implements TicketProvider<TCustomFields> {
  readonly metadata = {
    id: "mock",
    name: "Mock Provider",
  }

  private tickets = new Map<string, Ticket<TCustomFields>>()
  private nextId = 1

  constructor(private config: MockConfig<TCustomFields>) {}

  async listTicketIds(): Promise<TicketID[]> {
    return Array.from(this.tickets.keys())
  }

  async getTicket(id: TicketID): Promise<Ticket<TCustomFields>> {
    const ticket = this.tickets.get(id)
    if (!ticket) throw new Error(`Ticket not found: ${id}`)
    return ticket
  }

  async findByCustomFields(criteria: Partial<TCustomFields>): Promise<Ticket<TCustomFields>[]> {
    return Array.from(this.tickets.values()).filter((ticket) => {
      // Check if all criteria match the ticket's custom fields
      return Object.entries(criteria).every(([key, value]) => {
        const ticketValue = ticket.customFields[key as keyof TCustomFields]
        return ticketValue === value
      })
    })
  }

  async createTicket(data: TicketInput<TCustomFields>): Promise<TicketID> {
    const id = String(this.nextId++)
    const ticket: Ticket<TCustomFields> = {
      id,
      version: "1",
      title: data.title,
      description: normalize(data.description),
      customFields: this.parseCustomFields(data.customFields ?? {}),
    }
    this.tickets.set(id, ticket)
    return id
  }

  async updateTicket(id: TicketID, data: Partial<TicketInput<TCustomFields>>): Promise<void> {
    const existing = await this.getTicket(id)
    const updated: Ticket<TCustomFields> = {
      ...existing,
      ...data,
      description: data.description ? normalize(data.description) : existing.description,
      customFields: data.customFields ? this.parseCustomFields(data.customFields) : existing.customFields,
      version: String(parseInt(existing.version) + 1),
    }
    this.tickets.set(id, updated)
  }

  async deleteTicket(id: TicketID): Promise<void> {
    if (!this.tickets.has(id)) {
      throw new Error(`Ticket not found: ${id}`)
    }
    this.tickets.delete(id)
  }

  parseCustomFields(obj: unknown): TCustomFields {
    return this.config.customFieldsSchema.parse(obj)
  }
}
