/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { beforeEach, describe, expect, it } from "vitest"
import { z } from "zod"

import { fromHtml } from "../utils"
import { AzureDevOpsProvider } from "./azure-devops-provider"

/**
 * These tests make real API calls to Azure DevOps.
 * To run them, you need to set the following environment variables:
 * - AZURE_DEVOPS_ORG_URL (e.g. https://dev.azure.com/myorg)
 * - AZURE_DEVOPS_PROJECT_SOLUTIONING
 * - AZURE_DEVOPS_PAT
 */
const requiredEnvVars = ["AZURE_DEVOPS_ORG_URL", "AZURE_DEVOPS_PROJECT_SOLUTIONING", "AZURE_DEVOPS_PAT"]

const missingEnvVars = !requiredEnvVars.every((v) => process.env[v])

describe.skipIf(missingEnvVars)("AzureDevOpsProvider", () => {
  // TODO: Rework these tests to make them independent of the current state in Azure DevOps
  // Currently, tests depend on ticket #17278 existing with specific content
  const AZURE_TICKET_ID = "17278"

  let azureProvider: AzureDevOpsProvider<{
    "System.WorkItemType": string
    "Microsoft.VSTS.Common.AcceptanceCriteria"?: string | undefined
    "Microsoft.VSTS.Scheduling.StoryPoints"?: number | undefined
  }>

  beforeEach(() => {
    azureProvider = new AzureDevOpsProvider({
      organizationUrl: process.env.AZURE_DEVOPS_ORG_URL!,
      project: process.env.AZURE_DEVOPS_PROJECT_SOLUTIONING!,
      pat: process.env.AZURE_DEVOPS_PAT!,
      customFieldsSchema: z.object({
        "System.WorkItemType": z.string(),
        "Microsoft.VSTS.Common.AcceptanceCriteria": z.string().optional(),
        "Microsoft.VSTS.Scheduling.StoryPoints": z.number().optional(),
      }),
    })
  })

  it("should have correct metadata", () => {
    expect(azureProvider.metadata).toEqual({
      id: "azure-devops",
      name: "Azure DevOps",
    })
  })

  it("should be able to list tickets by their ID", async () => {
    const ids = await azureProvider.listTicketIds()
    expect(ids).toContain(AZURE_TICKET_ID)
  })

  it("should be able to retrieve the basic data of an individual ticket", async () => {
    const ticket = await azureProvider.getTicket(AZURE_TICKET_ID)
    expect(ticket.id).toBe(AZURE_TICKET_ID)
    expect(ticket.version).toBeTruthy()
    expect(ticket.title).toBe("HSP | As a user, I want to log in using SSO to streamline authentication.")
    expect(ticket.description.text).toContain("Currently, users can")
    expect(ticket.description.html).toContain("<div>Currently, users can")
    expect(ticket.description.markdown).toContain("Currently, users can")
  })

  it("should be able to retrieve the custom fields of an individual ticket", async () => {
    const ticket = await azureProvider.getTicket(AZURE_TICKET_ID)
    const acceptanceCriteria = fromHtml(ticket.customFields["Microsoft.VSTS.Common.AcceptanceCriteria"] ?? "")

    expect(ticket.customFields["System.WorkItemType"]).toBe("User Story")
    expect(acceptanceCriteria.html).toContain("<li>Users with")
    expect(acceptanceCriteria.markdown).toContain("*   Users with")
    expect(acceptanceCriteria.text).toContain("Users with")
  })

  it("should filter tickets using additional query clauses", async () => {
    const provider = new AzureDevOpsProvider({
      organizationUrl: process.env.AZURE_DEVOPS_ORG_URL!,
      project: process.env.AZURE_DEVOPS_PROJECT_SOLUTIONING!,
      pat: process.env.AZURE_DEVOPS_PAT!,
      customFieldsSchema: z.object({
        "System.WorkItemType": z.string(),
        "Microsoft.VSTS.Common.AcceptanceCriteria": z.string().optional(),
        "Microsoft.VSTS.Scheduling.StoryPoints": z.number().optional(),
      }),
      additionalQueryClauses: "[Microsoft.VSTS.Scheduling.StoryPoints] > 0 AND [System.WorkItemType] = 'User Story'",
    })

    const tickets = await provider.listTicketIds()
    expect(tickets.length).toBeGreaterThan(0)

    // Verify that all returned tickets are user stories with story points
    const firstTicket = await provider.getTicket(tickets[0])
    expect(firstTicket.customFields["System.WorkItemType"]).toBe("User Story")
    expect(
      typeof firstTicket.customFields["Microsoft.VSTS.Scheduling.StoryPoints"] === "number" &&
        firstTicket.customFields["Microsoft.VSTS.Scheduling.StoryPoints"] > 0
    ).toBe(true)
  })

  it("should combine additional query clauses with custom field search", async () => {
    const provider = new AzureDevOpsProvider({
      organizationUrl: process.env.AZURE_DEVOPS_ORG_URL!,
      project: process.env.AZURE_DEVOPS_PROJECT_SOLUTIONING!,
      pat: process.env.AZURE_DEVOPS_PAT!,
      customFieldsSchema: z.object({
        "System.WorkItemType": z.string(),
        "Microsoft.VSTS.Common.AcceptanceCriteria": z.string().optional(),
        "Microsoft.VSTS.Scheduling.StoryPoints": z.number().optional(),
      }),
      additionalQueryClauses: "[System.WorkItemType] = 'User Story'",
    })

    const tickets = await provider.findByCustomFields({
      "Microsoft.VSTS.Scheduling.StoryPoints": 5,
    })

    // Verify that all returned tickets are user stories with exactly 5 story points
    for (const ticket of tickets) {
      expect(ticket.customFields["System.WorkItemType"]).toBe("User Story")
      expect(ticket.customFields["Microsoft.VSTS.Scheduling.StoryPoints"]).toBe(5)
    }
  })
})
