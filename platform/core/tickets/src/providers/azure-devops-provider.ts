/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { JsonPatchOperation } from "azure-devops-node-api/interfaces/common/VSSInterfaces"
import type { z } from "zod"
import * as azdev from "azure-devops-node-api"
import { Operation } from "azure-devops-node-api/interfaces/common/VSSInterfaces"
import { WorkItemExpand } from "azure-devops-node-api/interfaces/WorkItemTrackingInterfaces"

import type { Ticket, TicketInput, TicketProvider } from "../core"
import { fromHtml } from "../utils"

export type AzureDevOpsConfig<TCustomFields = Record<string, unknown>> = {
  /** Organization URL (e.g. https://dev.azure.com/myorg) */
  organizationUrl: string
  /** Project name */
  project: string
  /** Personal Access Token with Work Items Read/Write permissions */
  pat: string
  /** Custom fields schema */
  customFieldsSchema: z.ZodType<TCustomFields>
  /** Additional WIQL query clauses to filter work items */
  additionalQueryClauses?: string
}

/**
 * Azure DevOps ticket provider implementation using official Azure DevOps Node.js API
 */
export class AzureDevOpsProvider<TCustomFields = Record<string, unknown>> implements TicketProvider<TCustomFields> {
  readonly metadata = {
    id: "azure-devops",
    name: "Azure DevOps",
  } as const

  private connection: azdev.WebApi

  constructor(private config: AzureDevOpsConfig<TCustomFields>) {
    const authHandler = azdev.getPersonalAccessTokenHandler(config.pat)
    this.connection = new azdev.WebApi(config.organizationUrl, authHandler)
  }

  async listTicketIds(): Promise<string[]> {
    const api = await this.connection.getWorkItemTrackingApi()
    const query = this.buildWiqlQuery()
    const results = await api.queryByWiql({ query })
    return results.workItems?.map((item) => item.id!.toString()) ?? []
  }

  async getTicket(id: string): Promise<Ticket<TCustomFields>> {
    const api = await this.connection.getWorkItemTrackingApi()
    const item = await api.getWorkItem(parseInt(id), undefined, undefined, WorkItemExpand.All)

    if (!item.fields) {
      throw new Error(`Failed to get ticket ${id}`)
    }

    return {
      id: item.id!.toString(),
      version: item.rev?.toString() ?? "",
      title: item.fields["System.Title"] as string,
      description: this.parseDescription(item.fields["System.Description"] as string),
      customFields: this.extractCustomFields(item.fields),
    }
  }

  async findByCustomFields(criteria: Partial<TCustomFields>): Promise<Ticket<TCustomFields>[]> {
    const api = await this.connection.getWorkItemTrackingApi()

    // Convert custom field criteria to WIQL conditions
    const conditions = Object.entries(criteria).map(([field, value]) => {
      return `[${field}] = ${JSON.stringify(value)}`
    })

    const query = this.buildWiqlQuery(conditions)

    // Execute query and fetch matching tickets
    const results = await api.queryByWiql({ query })
    const ids = results.workItems?.map((item) => item.id!.toString()) ?? []

    // Fetch full ticket details for matches
    return Promise.all(ids.map((id) => this.getTicket(id)))
  }

  async createTicket(data: TicketInput<TCustomFields>): Promise<string> {
    const api = await this.connection.getWorkItemTrackingApi()

    const patchDocument: JsonPatchOperation[] = [
      {
        op: Operation.Add,
        path: "/fields/System.Title",
        value: data.title,
      },
      {
        op: Operation.Add,
        path: "/fields/System.Description",
        value: data.description.html,
      },
      ...this.createFieldOperations(data.customFields ?? {}),
    ]

    const item = await api.createWorkItem(undefined, patchDocument, this.config.project, "Task", false, true)

    if (!item.id) {
      throw new Error("Failed to create ticket")
    }

    return item.id.toString()
  }

  async updateTicket(id: string, data: Partial<TicketInput<TCustomFields>>): Promise<void> {
    const api = await this.connection.getWorkItemTrackingApi()
    const patchDocument: JsonPatchOperation[] = []

    if (data.title) {
      patchDocument.push({
        op: Operation.Replace,
        path: "/fields/System.Title",
        value: data.title,
      })
    }

    if (data.description) {
      patchDocument.push({
        op: Operation.Replace,
        path: "/fields/System.Description",
        value: data.description.html,
      })
    }

    if (data.customFields) {
      patchDocument.push(...this.createFieldOperations(data.customFields))
    }

    await api.updateWorkItem(undefined, patchDocument, parseInt(id))
  }

  async deleteTicket(id: string): Promise<void> {
    const api = await this.connection.getWorkItemTrackingApi()
    await api.deleteWorkItem(parseInt(id))
  }

  parseCustomFields(obj: unknown): TCustomFields {
    return this.config.customFieldsSchema.parse(obj)
  }

  /**
   * Parse the description field from the API response
   */
  private parseDescription(description?: string) {
    return fromHtml(description ?? "")
  }

  /**
   * Extract custom fields from the API response
   */
  private extractCustomFields(fields: Record<string, unknown>): TCustomFields {
    const { "System.Title": _title, "System.Description": _desc, ...rest } = fields
    return rest as TCustomFields
  }

  /**
   * Create field operations for the patch document
   */
  private createFieldOperations(fields: Record<string, unknown>): JsonPatchOperation[] {
    return Object.entries(fields).map(([key, value]) => ({
      op: Operation.Add,
      path: `/fields/${key}`,
      value,
    }))
  }

  /**
   * Builds a WIQL query with the given conditions
   * @param conditions - Additional WHERE conditions to add to the query
   * @returns Complete WIQL query string
   */
  private buildWiqlQuery(conditions: string[] = []): string {
    const baseQuery = `SELECT [System.Id] FROM WorkItems WHERE [System.TeamProject] = '${this.config.project}'`
    const criteriaClause = conditions.length > 0 ? ` AND ${conditions.join(" AND ")}` : ""
    const additionalClauses = this.config.additionalQueryClauses ? ` AND (${this.config.additionalQueryClauses})` : ""
    const orderBy = ` ORDER BY [System.ChangedDate] DESC`

    return baseQuery + criteriaClause + additionalClauses + orderBy
  }
}
