/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { describe, expect, it } from "vitest"

import { fromHtml, fromMarkdown, fromText } from "./utils"

describe("Text content conversion utils", () => {
  describe("fromHtml", () => {
    it("converts simple HTML", () => {
      const result = fromHtml("Hello <strong>World</strong>")
      expect(result).toEqual({
        html: "Hello <strong>World</strong>",
        markdown: "Hello **World**",
        text: "Hello World",
      })
    })

    it("preserves block structure", () => {
      const result = fromHtml("<h1>Title</h1><p>Paragraph</p>")
      expect(result).toEqual({
        html: "<h1>Title</h1><p>Paragraph</p>",
        markdown: "# Title\n\nParagraph",
        text: "TITLE\nParagraph",
      })
    })

    it("sanitizes unsafe HTML", () => {
      const result = fromHtml('<p>Hello</p><script>alert("xss")</script>')
      expect(result.html).not.toContain("script")
      expect(result.text).toBe("Hello")
    })

    it("handles nested elements", () => {
      const result = fromHtml("<div><h1>Title</h1><p>Text with <em>emphasis</em></p></div>")
      expect(result).toEqual({
        html: "<div><h1>Title</h1><p>Text with <em>emphasis</em></p></div>",
        markdown: "# Title\n\nText with *emphasis*",
        text: "TITLE\nText with emphasis",
      })
    })

    it("can handle real-world Azure DevOps HTML without injecting superfluous newlines", () => {
      const input =
        "<div><div><b>As a</b><span> </span>Sales Engineer </div><div><b>I want</b><span> </span>to see the list of technical solution </div><div><b>So that</b><span> </span>I can quickly see the statuses </div><br /> </div>"
      const result = fromHtml(input)
      expect(result).toEqual({
        html: input,
        markdown:
          "**As a** Sales Engineer\n**I want** to see the list of technical solution\n**So that** I can quickly see the statuses",
        text: "As a Sales Engineer\nI want to see the list of technical solution\nSo that I can quickly see the statuses",
      })
    })
  })

  describe("fromMarkdown", () => {
    it("converts simple markdown", () => {
      const result = fromMarkdown("Hello **World**")
      expect(result).toEqual({
        html: "<p>Hello <strong>World</strong></p>",
        markdown: "Hello **World**",
        text: "Hello World",
      })
    })

    it("preserves block structure", () => {
      const result = fromMarkdown("# Title\n\nParagraph")
      expect(result).toEqual({
        html: "<h1>Title</h1>\n<p>Paragraph</p>",
        markdown: "# Title\n\nParagraph",
        text: "TITLE\nParagraph",
      })
    })

    it("handles lists", () => {
      const result = fromMarkdown("- Item 1\n- Item 2")
      expect(result).toEqual({
        html: "<ul>\n<li>Item 1</li>\n<li>Item 2</li>\n</ul>",
        markdown: "- Item 1\n- Item 2",
        text: "* Item 1\n * Item 2",
      })
    })
  })

  describe("fromText", () => {
    it("converts plain text", () => {
      const result = fromText("Hello World")
      expect(result).toEqual({
        html: "Hello World",
        markdown: "Hello World",
        text: "Hello World",
      })
    })

    it("preserves newlines", () => {
      const result = fromText("Line 1\nLine 2")
      expect(result).toEqual({
        html: "Line 1<br>Line 2",
        markdown: "Line 1\nLine 2",
        text: "Line 1\nLine 2",
      })
    })

    it("handles empty string", () => {
      const result = fromText("")
      expect(result).toEqual({
        html: "",
        markdown: "",
        text: "",
      })
    })
  })
})
