/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { describe, expect, it } from "vitest"

import { GeneratedImage } from "../core"
import { PicsumProvider } from "./picsum-provider"

describe("PicsumProvider", () => {
  const provider = new PicsumProvider()

  it(
    "should generate a correct URL based on the resolution options",
    {
      timeout: 10000,
    },
    async () => {
      const imageUrl = await provider.generate({ prompt: "ignored prompt", aspectRatio: "wide" })
      expect(imageUrl).toBe("https://picsum.photos/1792/1024")
    }
  )

  it(
    "should generate a correct URL for outpainting based on the base image dimensions and options",
    {
      timeout: 10000,
    },

    async () => {
      // Given
      const generatedImageUrl = await provider.generate({ prompt: "ignored prompt", aspectRatio: "wide" })
      const baseImage = await GeneratedImage.fromURL(generatedImageUrl)

      // When
      const outpaintUrl = await provider.outpaint({ baseImage, left: 0.5, right: 0.5, up: 0.5, down: 0.5 })

      // Then
      // Assuming the base dimensions were 1792x1024, the new dimensions would be 1792 * (1 + 0.5 + 0.5) x 1024 * (1 + 0.5 + 0.5) = 3584x2048
      expect(outpaintUrl).toBe("https://picsum.photos/3584/2048")
    }
  )
})
