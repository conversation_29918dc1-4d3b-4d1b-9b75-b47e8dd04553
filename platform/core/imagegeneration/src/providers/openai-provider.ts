/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import OpenAI from "openai"

import type { AspectRatio, GenerationOptions, ImageGenerationProvider, OutpaintingOptions } from "../core"

/**
 * Provider for generating images using OpenAI's DALL-E API (version 3).
 */
export class OpenAIDallE3Provider implements ImageGenerationProvider {
  readonly metadata = {
    id: "openai-dall-e-3",
    fullName: "OpenAI DALL-E (v3)",
    shortName: "DALL-E (v3)",
    supportsOutpainting: false,
  } as const

  private readonly openai: OpenAI

  /**
   * Creates an instance of OpenAIProvider.
   *
   * @param apiKey - The OpenAI API key.
   */
  constructor(apiKey: string, baseURL?: string) {
    this.openai = new OpenAI({
      apiKey: apiKey,
      baseURL,
    })
  }

  async generate(options: GenerationOptions) {
    // Generate image using OpenAI's DALL-E API
    const response = await this.openai.images.generate({
      prompt: options.prompt,
      n: 1,
      model: "dall-e-3",
      quality: "hd",
      size: this.determineTargetResolution(options.aspectRatio),
    })

    // Raise an error if the API response is empty
    const imageUrl = response.data[0].url
    if (!imageUrl) {
      throw new Error("OpenAI DALL-E API response did not contain an image URL")
    }

    // We're done here
    return imageUrl
  }

  async outpaint(_options: OutpaintingOptions): Promise<never> {
    throw new Error("Outpainting is not yet supported by DALL-E v3")
  }

  /**
   * Converts the resolution enum to the format required by the OpenAI API.
   *
   * @param resolution - The resolution enum.
   * @returns The resolution in the format required by the OpenAI API.
   */
  private determineTargetResolution(resolution: AspectRatio | AspectRatio[]) {
    for (const res of Array.isArray(resolution) ? resolution : [resolution]) {
      switch (res) {
        case "square":
          return "1024x1024"
        case "wide":
          return "1792x1024"
        case "tall":
          return "1024x1792"
      }
    }
  }
}

/**
 * Provider for generating images using OpenAI's DALL-E API (version 2).
 */
export class OpenAIDallE2Provider implements ImageGenerationProvider {
  readonly metadata = {
    id: "openai-dall-e-2",
    fullName: "OpenAI DALL-E (v2)",
    shortName: "DALL-E (v2)",
    supportsOutpainting: false,
  } as const

  private readonly openai: OpenAI

  /**
   * Creates an instance of OpenAIProvider.
   *
   * @param apiKey - The OpenAI API key.
   */
  constructor(apiKey: string, baseURL?: string) {
    this.openai = new OpenAI({
      apiKey: apiKey,
      baseURL,
    })
  }

  async generate(options: GenerationOptions) {
    // Generate image using OpenAI's DALL-E API
    const response = await this.openai.images.generate({
      prompt: options.prompt,
      n: 1,
      model: "dall-e-2",
      size: this.determineTargetResolution(options.aspectRatio),
    })

    // Raise an error if the API response is empty
    const imageUrl = response.data[0].url
    if (!imageUrl) {
      throw new Error("OpenAI DALL-E API response did not contain an image URL")
    }

    // We're done here
    return imageUrl
  }

  async outpaint(_options: OutpaintingOptions): Promise<never> {
    throw new Error("Not yet implemented")
  }

  /**
   * Converts the resolution enum to the format required by the OpenAI API.
   *
   * @param resolution - The resolution enum.
   * @returns The resolution in the format required by the OpenAI API.
   */
  private determineTargetResolution(resolution: AspectRatio | AspectRatio[]) {
    for (const res of Array.isArray(resolution) ? resolution : [resolution]) {
      switch (res) {
        case "square":
          return "1024x1024" as const
      }
    }
    throw new Error("DALL-E v2 is limited to the generation of square images")
  }
}
