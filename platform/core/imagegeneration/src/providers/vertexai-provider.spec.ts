/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { beforeEach, describe, expect, it } from "vitest"

import { GeneratedImage } from "../core"
import { VertexAIProvider } from "./vertexai-provider"

// Get the environment variables
const projectId = process.env.GCLOUD_PROJECT_ID
const location = process.env.GCLOUD_LOCATION
const serviceAccountKey = process.env.GCLOUD_SERVICE_ACCOUNT_KEY

// Skip tests if the required environment variables are not present
describe.runIf(projectId && location && serviceAccountKey)("VertexAIProvider", () => {
  let provider: VertexAIProvider

  beforeEach(() => {
    provider = new VertexAIProvider(projectId!, location!, serviceAccountKey!)
  })

  it(
    "should generate a valid image buffer",
    async () => {
      const { image, mimeType } = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "wide" })
      expect(image.length).toBeGreaterThan(0)
      expect(mimeType).toBe("image/png")
    },
    { timeout: 60000 }
  )

  it("should throw an error if no prompt has been provided", async () => {
    await expect(provider.generate({ prompt: "", aspectRatio: "square" })).rejects.toThrow()
  })

  it(
    "should outpaint an image correctly",
    async () => {
      // Given
      const base = await provider.generate({
        prompt: "Face of an old man, looking straight into the camera",
        aspectRatio: "square",
      })
      const baseImage = await GeneratedImage.fromBuffer(base.image, base.mimeType)

      // When
      const outpainted = await provider.outpaint({
        prompt: "Old man sitting on a sofa",
        baseImage,
        left: 5,
        right: 5,
        up: 2,
        down: 3,
      })
      const outpaintedImage = await GeneratedImage.fromBuffer(outpainted.image, outpainted.mimeType)
      console.info(`Base image: ${await baseImage.toFile()}`)
      console.info(`Outpainted image: ${await outpaintedImage.toFile()}`)

      // Then
      expect(outpaintedImage.image.length).toBeGreaterThan(0)
      expect(outpaintedImage.mimeType).toBe("image/png")
      expect(outpaintedImage.dimensions.width).toBe(1920)
      expect(outpaintedImage.dimensions.height).toBe(1047)
    },
    { timeout: 60000 }
  )
})
