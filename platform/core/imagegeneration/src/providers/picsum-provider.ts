/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { AspectRatio, GenerationOptions, ImageGenerationProvider, OutpaintingOptions } from "../core"

/**
 * Provider for generating images using https://picsum.photos/.
 * This provider generates random images of a specified resolution and does not support prompts.
 * It's useful for testing purposes (among other things), as it does not require any API key or infrastructure to use.
 */
export class PicsumProvider implements ImageGenerationProvider {
  metadata = {
    id: "picsum",
    fullName: "Picsum Photos",
    shortName: "Picsum",
    supportsOutpainting: true,
  } as const

  async generate(options: GenerationOptions) {
    const { width, height } = this.determineTargetResolution(options.aspectRatio)
    return `https://picsum.photos/${width}/${height}`
  }

  async outpaint(options: OutpaintingOptions) {
    const { width, height } = options.baseImage.dimensions

    const newWidth = width * (1 + options.left + options.right)
    const newHeight = height * (1 + options.up + options.down)

    return `https://picsum.photos/${Math.round(newWidth)}/${Math.round(newHeight)}`
  }

  /**
   * Parses a resolution string into an object with width and height properties.
   *
   * @param resolution - The resolution string to parse.
   * @returns An object with width and height properties.
   */
  private determineTargetResolution(resolution: AspectRatio | AspectRatio[]) {
    for (const res of Array.isArray(resolution) ? resolution : [resolution]) {
      switch (res) {
        case "square":
          return { width: 1024, height: 1024 } as const
        case "wide":
          return { width: 1792, height: 1024 } as const
        case "tall":
          return { width: 1024, height: 1792 } as const
      }
    }
    throw new Error("Unsupported aspect ratio")
  }
}
