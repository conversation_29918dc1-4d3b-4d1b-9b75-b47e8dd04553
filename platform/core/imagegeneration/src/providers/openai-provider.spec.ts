/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { beforeEach, describe, expect, it } from "vitest"

import { OpenAIDallE2Provider, OpenAIDallE3Provider } from "./openai-provider"

// Get the OpenAI API key from environment variables
const apiKey = process.env.OPENAI_API_KEY

// Skip tests if the OpenAI API key is not present
describe.runIf(apiKey)("OpenAIDallE3Provider", () => {
  let provider: OpenAIDallE3Provider

  beforeEach(() => {
    provider = new OpenAIDallE3Provider(apiKey!)
  })

  it(
    "should generate a valid image URL",
    async () => {
      const imageUrl = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "wide" })
      expect(imageUrl).toMatch(/^https:\/\/.*$/)
    },
    { timeout: 60000 } // dall-e-3 can be quite slow
  )

  it("should throw an error if no prompt has been provided", async () => {
    // Use an incorrect prompt to force an error
    await expect(provider.generate({ prompt: "", aspectRatio: "wide" })).rejects.toThrow()
  })
})

describe.runIf(apiKey)("OpenAIDallE2Provider", () => {
  let provider: OpenAIDallE2Provider

  beforeEach(() => {
    provider = new OpenAIDallE2Provider(apiKey!)
  })

  it(
    "should generate a valid image URL",
    async () => {
      const imageUrl = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "square" })
      expect(imageUrl).toMatch(/^https:\/\/.*$/)
    },
    { timeout: 60000 } // dall-e-3 can be quite slow
  )

  it("should throw an error if no prompt has been provided", async () => {
    // Use an incorrect prompt to force an error
    await expect(provider.generate({ prompt: "", aspectRatio: "wide" })).rejects.toThrow()
  })
})
