/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { JWT } from "google-auth-library"
import { z } from "zod"

import type { AspectRatio, GenerationOptions, ImageGenerationProvider, MimeType, OutpaintingOptions } from "../core"
import { generateOutpaintingImageVariants } from "../helpers"

/**
 * Provider for generating images using Google Vertex AI.
 */
export class VertexAIProvider implements ImageGenerationProvider {
  readonly metadata = {
    id: "vertex-ai",
    fullName: "Google Vertex AI",
    shortName: "Vertex AI",
    supportsOutpainting: true,
  } as const

  /**
   * Creates an instance of VertexAIProvider.
   *
   * @param projectId - The Google Cloud project ID.
   * @param location - The location of the Vertex AI service.
   * @param serviceAccountKey - The IAM service account key in JSON format.
   * @param modelVersion - The version of the image generation model. Defaults to "imagegeneration@006".
   */
  constructor(
    private readonly projectId: string,
    private readonly location: string,
    private readonly serviceAccountKey: string,
    private readonly modelVersion = "imagen-3.0-generate-001"
  ) {}

  /**
   * Generates an image based on the given prompt and options.
   *
   * @param options - Additional options to control the image generation, such as aspect ratio.
   * @returns A promise that resolves to the URL of the generated image.
   */
  async generate(options: GenerationOptions) {
    // Send the request to Vertex AI API
    const response = await this.sendRequest(
      VertexAIRequestSchema.parse({
        instances: [{ prompt: options.prompt }],
        parameters: {
          outputOptions: {
            mimeType: "image/png",
          },
          safetySettings: "block_fewest",
          aspectRatio: this.convertAspectRatio(options.aspectRatio),
          negativePrompt: options.negativePrompt,
          sampleCount: 1,
        },
      })
    )

    // Build the image from the response
    if (response.predictions.length === 0) {
      throw new Error("Vertex AI response did not contain any predictions")
    }
    const prediction = response.predictions[0]
    const image = Buffer.from(prediction.bytesBase64Encoded, "base64")
    const mimeType = prediction.mimeType as MimeType
    return { image, mimeType }
  }

  /**
   * Outpaints (i.e. extends) an image based on the given base image and options.
   *
   * @param options - The options for outpainting.
   * @returns A promise that resolves to the URL of the extended image.
   */
  async outpaint(options: OutpaintingOptions) {
    // Vertex AI has a maximum image size and also requires 2 images (base and mask) for outpainting
    const { offsetBaseImage, maskImage } = await generateOutpaintingImageVariants({
      baseImage: options.baseImage,
      left: options.left,
      right: options.right,
      up: options.up,
      down: options.down,
      maxWidth: 1920,
      maxHeight: 1920,
    })

    // Send the request to Vertex AI API
    const response = await this.sendRequest({
      instances: [
        {
          prompt: options.prompt,
          image: { bytesBase64Encoded: offsetBaseImage.toBase64(), mimeType: offsetBaseImage.mimeType },
          mask: { image: { bytesBase64Encoded: maskImage.toBase64(), mimeType: maskImage.mimeType } },
        },
      ],
      parameters: {
        sampleCount: 1,
        editConfig: { editMode: "inpainting-insert", guidanceScale: 40 },
      },
    })

    // Build the image from the response
    if (response.predictions.length === 0) {
      throw new Error("Vertex AI response did not contain any predictions")
    }
    const prediction = response.predictions[0]
    const image = Buffer.from(prediction.bytesBase64Encoded, "base64")
    const mimeType = prediction.mimeType as MimeType
    return { image, mimeType }
  }

  /**
   * Sends a request to the Vertex AI API.
   *
   * @param requestBody - The body of the request to be sent.
   * @returns A promise that resolves to the response from the Vertex AI API.
   */
  private async sendRequest(requestBody: VertexAIRequest): Promise<VertexAIResponse> {
    // Obtain a new access token
    const accessToken = await this.getAccessToken()

    console.info("Sending request to Vertex AI API", requestBody)

    // Send the request to Vertex AI API
    const rawResponse = await fetch(
      `https://${this.location}-aiplatform.googleapis.com/v1/projects/${this.projectId}/locations/${this.location}/publishers/google/models/${this.modelVersion}:predict`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json; charset=utf-8",
        },
        body: JSON.stringify(requestBody),
      }
    )

    // Handle the response
    if (!rawResponse.ok || rawResponse.status !== 200) {
      throw new Error(`Vertex AI failed to process a request: ${await rawResponse.text()}`)
    }

    // Validate the response using the Zod schema
    const responseJson = await rawResponse.json()
    const response = VertexAIResponseSchema.safeParse(responseJson)
    if (!response.success) {
      throw new Error(
        `Vertex AI response did not match the expected schema; This most likely means that the vertex ai rejected the prompt as not save. The response was: ${JSON.stringify(responseJson)}`
      )
    }
    return response.data
  }

  /**
   * Gets a new access token using the service account key.
   *
   * @returns A promise that resolves to the new access token.
   */
  private async getAccessToken(): Promise<string> {
    // Parse the service account key
    // (the injected key is a base64-encoded JSON string, because Vercel sh*ts itself when you put a multiline string in
    // an environment variable and because I don't hate myself enough to try and figure out how to make it work any other way)
    const serviceAccountKeyJson = Buffer.from(this.serviceAccountKey, "base64").toString()
    const key = JSON.parse(serviceAccountKeyJson) as GCloudServiceAccountKey

    // Create a JWT client using the service account key
    const client = new JWT({
      email: key.client_email,
      key: key.private_key,
      scopes: ["https://www.googleapis.com/auth/cloud-platform"],
    })

    // Authorize the client to get an access token
    const tokens = await client.authorize()
    if (!tokens.access_token) {
      throw new Error("Failed to obtain access token")
    } else {
      console.info("Successfully obtained new GCloud access token")
    }

    return tokens.access_token
  }

  /**
   * Converts the aspect ratio from the internal enum to the format required by the Vertex AI API.
   *
   * @param aspectRatio - The aspect ratio enum.
   * @returns The aspect ratio in the format required by the Vertex AI API.
   */
  private convertAspectRatio(aspectRatio: AspectRatio | AspectRatio[]): string {
    for (const res of Array.isArray(aspectRatio) ? aspectRatio : [aspectRatio]) {
      switch (res) {
        case "square":
          return "1:1"
        case "wide":
          return "16:9"
        case "tall":
          return "9:16"
      }
    }
    throw new Error("Unsupported aspect ratio")
  }
}

/**
 * Schema for the request body to Vertex AI API.
 */
// Original generation request schema
const VertexAIGenerateRequestSchema = z.object({
  instances: z.array(
    z.object({
      prompt: z.string(),
      negativePrompt: z.string().optional(),
    })
  ),
  parameters: z.object({
    outputOptions: z.discriminatedUnion("mimeType", [
      z.object({
        mimeType: z.literal("image/png"),
      }),
      z.object({
        mimeType: z.literal("image/jpeg"),
        compressionQuality: z.number().min(0).max(100),
      }),
    ]),
    personGeneration: z.enum(["dont_allow", "allow_adult", "allow_all"]).default("allow_adult"),
    safetySettings: z.enum(["block_most", "block_some", "block_few", "block_fewest"]).default("block_some"),
    aspectRatio: z.enum(["1:1", "9:16", "16:9", "3:4", "4:3"]).default("1:1"),
    sampleCount: z.number(),
  }),
})

// Outpainting request schema
const VertexAIOutpaintingRequestSchema = z.object({
  instances: z.array(
    z.object({
      prompt: z.string().optional(),
      image: z.object({
        bytesBase64Encoded: z.string(),
        mimeType: z.string().optional(),
      }),
      mask: z.object({
        image: z.object({
          bytesBase64Encoded: z.string(),
          mimeType: z.string().optional(),
        }),
      }),
    })
  ),
  parameters: z.object({
    negativePrompt: z.string().optional(),
    sampleCount: z.number(),
    editConfig: z.object({
      editMode: z.enum(["outpainting", "inpainting-insert"]),
      guidanceScale: z.number().optional(),
    }),
  }),
})

// Union of both schemas
const VertexAIRequestSchema = z.union([VertexAIGenerateRequestSchema, VertexAIOutpaintingRequestSchema])

/**
 * Schema for the response body from Vertex AI API.
 */
const VertexAIResponseSchema = z.object({
  predictions: z.array(
    z.object({
      bytesBase64Encoded: z.string(),
      mimeType: z.string(),
    })
  ),
})

/**
 * Type definitions for the request and response to/from Vertex AI API.
 */
type VertexAIRequest = z.infer<typeof VertexAIRequestSchema>

/**
 * Type definition for the response from Vertex AI API.
 */
type VertexAIResponse = z.infer<typeof VertexAIResponseSchema>

/**
 * Type definition for Google Cloud Service Account Key JSON.
 */
export type GCloudServiceAccountKey = {
  type: "service_account"
  project_id: string
  private_key_id: string
  private_key: string
  client_email: string
  client_id: string
  auth_uri: string
  token_uri: string
  auth_provider_x509_cert_url: string
  client_x509_cert_url: string
  universe_domain: string
}
