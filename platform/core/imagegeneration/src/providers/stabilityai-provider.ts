/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Buffer } from "buffer"

import type { AspectRatio, GenerationOptions, ImageGenerationProvider, MimeType, OutpaintingOptions } from "../core"
import { generateOutpaintingImageVariants } from "../helpers"

/**
 * Provider for generating images using StabilityAI's Stable Image Ultra API.
 */
export class StabilityAIStableImageUltraProvider implements ImageGenerationProvider {
  readonly metadata = {
    id: "stabilityai-siu",
    fullName: "StabilityAI Stable Image Ultra",
    shortName: "Stable Image Ultra",
    supportsOutpainting: true,
  } as const

  /**
   * Creates an instance of StabilityAIStableImageUltraProvider.
   *
   * @param apiKey - The StabilityAI API key.
   */
  constructor(private readonly apiKey: string) {}

  async generate(options: GenerationOptions) {
    const formData = new FormData()
    formData.append("prompt", options.prompt)
    formData.append("aspect_ratio", this.determineTargetAspectRatio(options.aspectRatio))
    formData.append("output_format", "png")
    // formData.append("style_preset", "photographic")

    if (options.negativePrompt) formData.append("negative_prompt", options.negativePrompt)

    return performRequest("https://api.stability.ai/v2beta/stable-image/generate/ultra", this.apiKey, formData)
  }

  async outpaint(options: OutpaintingOptions) {
    // Stability.AI has a maximum image size, so we need to be careful not to exceed it
    const { scaledBaseImage } = await generateOutpaintingImageVariants({
      baseImage: options.baseImage,
      left: options.left,
      right: options.right,
      up: options.up,
      down: options.down,
      maxWidth: 1920 * 2,
      maxHeight: 1920 * 2,
    })

    // Build the request
    const formData = new FormData()
    const left = Math.round(scaledBaseImage.dimensions.width * options.left)
    const right = Math.round(scaledBaseImage.dimensions.width * options.right)
    const up = Math.round(scaledBaseImage.dimensions.height * options.up)
    const down = Math.round(scaledBaseImage.dimensions.height * options.down)

    formData.append("image", new Blob([scaledBaseImage.image]))
    formData.append("prompt", options.prompt ?? "")
    formData.append("left", left.toString())
    formData.append("right", right.toString())
    formData.append("up", up.toString())
    formData.append("down", down.toString())
    formData.append("output_format", "png")

    // Send the request
    return performRequest("https://api.stability.ai/v2beta/stable-image/edit/outpaint", this.apiKey, formData)
  }

  private determineTargetAspectRatio(aspectRatio: AspectRatio | AspectRatio[]): string {
    for (const res of Array.isArray(aspectRatio) ? aspectRatio : [aspectRatio]) {
      switch (res) {
        case "square":
          return "1:1"
        case "wide":
          return "16:9"
        case "tall":
          return "9:16"
      }
    }
    throw new Error("Unsupported aspect ratio")
  }
}

/**
 * Provider for generating images using StabilityAI's Stable Diffusion 3.0 API.
 */
export class StabilityAIStableDiffusion3Provider implements ImageGenerationProvider {
  readonly metadata = {
    id: "stabilityai-sd3",
    fullName: "StabilityAI Stable Diffusion 3.0",
    shortName: "Stable Diffusion 3.0",
    supportsOutpainting: true,
  } as const

  private readonly apiKey: string

  /**
   * Creates an instance of StabilityAIStableDiffusion3Provider.
   *
   * @param apiKey - The StabilityAI API key.
   */
  constructor(apiKey: string) {
    this.apiKey = apiKey
  }

  async generate(options: GenerationOptions) {
    const formData = new FormData()
    formData.append("prompt", options.prompt)
    formData.append("aspect_ratio", this.determineTargetAspectRatio(options.aspectRatio))
    formData.append("output_format", "png")
    // formData.append("style_preset", "photographic")

    if (options.negativePrompt) formData.append("negative_prompt", options.negativePrompt)

    return performRequest("https://api.stability.ai/v2beta/stable-image/generate/sd3", this.apiKey, formData)
  }

  async outpaint(options: OutpaintingOptions) {
    // Stability.AI has a maximum image size, so we need to be careful not to exceed it
    const { scaledBaseImage } = await generateOutpaintingImageVariants({
      baseImage: options.baseImage,
      left: options.left,
      right: options.right,
      up: options.up,
      down: options.down,
      maxWidth: 1920 * 2,
      maxHeight: 1920 * 2,
    })

    // Build the request
    const formData = new FormData()
    const left = Math.round(scaledBaseImage.dimensions.width * options.left)
    const right = Math.round(scaledBaseImage.dimensions.width * options.right)
    const up = Math.round(scaledBaseImage.dimensions.height * options.up)
    const down = Math.round(scaledBaseImage.dimensions.height * options.down)

    formData.append("image", new Blob([scaledBaseImage.image]))
    formData.append("prompt", options.prompt ?? "")
    formData.append("left", left.toString())
    formData.append("right", right.toString())
    formData.append("up", up.toString())
    formData.append("down", down.toString())
    formData.append("output_format", "png")

    // Send the request
    return performRequest("https://api.stability.ai/v2beta/stable-image/edit/outpaint", this.apiKey, formData)
  }

  private determineTargetAspectRatio(aspectRatio: AspectRatio | AspectRatio[]) {
    for (const res of Array.isArray(aspectRatio) ? aspectRatio : [aspectRatio]) {
      switch (res) {
        case "square":
          return "1:1"
        case "wide":
          return "16:9"
        case "tall":
          return "9:16"
      }
    }
    throw new Error("Unsupported aspect ratio")
  }
}

// HELPER FUNCTIONS

async function performRequest(url: string, apiKey: string, formData: FormData) {
  // Send the request
  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      Accept: "image/*",
    },
    body: formData,
  })

  // Check the response status
  if (!response.ok) {
    throw new Error(`Failed to process request (url=${url}, status=${response.status}): ${await response.text()}`)
  }

  // Extract and return the image data
  const mimeType = response.headers.get("content-type") as MimeType
  const seed = response.headers.get("seed")
  const arrayBuffer = await response.arrayBuffer()
  const image = Buffer.from(arrayBuffer)
  return { image, mimeType, seed }
}
