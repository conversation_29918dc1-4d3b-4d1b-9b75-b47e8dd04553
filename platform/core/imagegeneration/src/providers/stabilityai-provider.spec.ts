/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { beforeEach, describe, expect, it } from "vitest"

import { GeneratedImage } from "../core"
import { StabilityAIStableDiffusion3Provider, StabilityAIStableImageUltraProvider } from "./stabilityai-provider"

// Get the OpenAI API key from environment variables
const apiKey = process.env.STABILITYAI_API_KEY

// Skip tests if the OpenAI API key is not present
describe.runIf(apiKey)("StabilityAIStableImageUltraProvider", () => {
  let provider: StabilityAIStableImageUltraProvider

  beforeEach(() => {
    provider = new StabilityAIStableImageUltraProvider(apiKey!)
  })

  it(
    "should generate a valid image buffer",
    async () => {
      const { image, mimeType } = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "wide" })
      expect(image.length).toBeGreaterThan(0)
      expect(mimeType).toBe("image/png")
    },
    { timeout: 60000 }
  )

  it("should throw an error if no prompt has been provided", async () => {
    // Use an incorrect prompt to force an error
    await expect(provider.generate({ prompt: "", aspectRatio: "wide" })).rejects.toThrow()
  })

  it(
    "should outpaint an image correctly",
    async () => {
      // Given
      const base = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "wide" })
      const baseImage = await GeneratedImage.fromBuffer(base.image, base.mimeType)

      // When
      const outpainted = await provider.outpaint({
        prompt: "A futuristic cityscape",
        baseImage,
        left: 0.5,
        right: 0.5,
        up: 0.5,
        down: 0.5,
      })
      const outpaintedImage = await GeneratedImage.fromBuffer(outpainted.image, outpainted.mimeType)

      // Then
      expect(outpaintedImage.image.length).toBeGreaterThan(0)
      expect(outpaintedImage.mimeType).toBe("image/png")
      expect(outpaintedImage.dimensions.width).toBe(baseImage.dimensions.width * 2)
      expect(outpaintedImage.dimensions.height).toBe(baseImage.dimensions.height * 2)
    },
    { timeout: 60000 }
  )
})
describe.runIf(apiKey)("StabilityAIStableDiffusion3Provider", () => {
  let provider: StabilityAIStableDiffusion3Provider

  beforeEach(() => {
    provider = new StabilityAIStableDiffusion3Provider(apiKey!)
  })

  it(
    "should generate a valid image URL",
    async () => {
      const { image, mimeType } = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "wide" })
      expect(image.length).toBeGreaterThan(0)
      expect(mimeType).toBe("image/png")
    },
    { timeout: 60000 } // dall-e-3 can be quite slow
  )

  it("should throw an error if no prompt has been provided", async () => {
    // Use an incorrect prompt to force an error
    await expect(provider.generate({ prompt: "", aspectRatio: "wide" })).rejects.toThrow()
  })

  it(
    "should outpaint an image correctly",
    async () => {
      // Given
      const base = await provider.generate({ prompt: "A futuristic cityscape", aspectRatio: "wide" })
      const baseImage = await GeneratedImage.fromBuffer(base.image, base.mimeType)

      // When
      const outpainted = await provider.outpaint({
        prompt: "A futuristic cityscape",
        baseImage,
        left: 0.5,
        right: 0.5,
        up: 0.5,
        down: 0.5,
      })
      const outpaintedImage = await GeneratedImage.fromBuffer(outpainted.image, outpainted.mimeType)

      // Then
      expect(outpaintedImage.image.length).toBeGreaterThan(0)
      expect(outpaintedImage.mimeType).toBe("image/png")
      expect(outpaintedImage.dimensions.width).toBe(baseImage.dimensions.width * 2)
      expect(outpaintedImage.dimensions.height).toBe(baseImage.dimensions.height * 2)
    },
    { timeout: 60000 }
  )
})
