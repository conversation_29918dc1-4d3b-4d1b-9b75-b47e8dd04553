/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import Replicate from "replicate"
import { z } from "zod"

import type { AspectRatio, GenerationOptions, ImageGenerationProvider, OutpaintingOptions } from "../core"

const FluxInputschema = z.object({
  seed: z.number().int().describe("Random seed. Set for reproducible generation").optional(),
  steps: z.number().int().gte(1).lte(50).describe("Number of diffusion steps").optional(),
  prompt: z.string().describe("Text prompt for image generation"),
  guidance: z
    .number()
    .gte(2)
    .lte(5)
    .describe(
      "Controls the balance between adherence to the text prompt and image quality/diversity. Higher values make the output more closely match the prompt but may reduce overall image quality. Lower values allow for more creative freedom but might produce results less relevant to the prompt."
    )
    .optional(),
  interval: z
    .number()
    .gte(1)
    .lte(4)
    .describe(
      "Interval is a setting that increases the variance in possible outputs letting the model be a tad more dynamic in what outputs it may produce in terms of composition, color, detail, and prompt interpretation. Setting this value low will ensure strong prompt following with more consistent outputs, setting it higher will produce more dynamic or varied outputs."
    )
    .optional(),
  aspect_ratio: z
    .enum(["1:1", "16:9", "2:3", "3:2", "4:5", "5:4", "9:16"])
    .describe("Aspect ratio for the generated image")
    .optional(),
  output_format: z.enum(["webp", "jpg", "png"]).describe("Ouptut format for the generated image").optional(),
  safety_tolerance: z
    .number()
    .int()
    .gte(1)
    .lte(5)
    .describe("Safety tolerance, 1 is most strict and 5 is most permissive")
    .optional(),
})

const FluxOutputSchema = z.string().url().describe("URL of the generated image")

/**
 * Provider for generating images using Replicate's API.
 */
export class FluxProProvider implements ImageGenerationProvider {
  readonly metadata = {
    id: "flux-pro",
    fullName: "Flux Pro (Replicate)",
    shortName: "Flux Pro",
    supportsOutpainting: false, // Assume outpainting is not supported in this example
  } as const

  private readonly replicate: Replicate

  constructor(apiKey: string) {
    this.replicate = new Replicate({
      auth: apiKey,
    })
  }

  /**
   * Converts the aspect ratio from the internal enum to the format required by the Vertex AI API.
   *
   * @param aspectRatio - The aspect ratio enum.
   * @returns The aspect ratio in the format required by the Flux model.
   */
  private convertAspectRatio(aspectRatio: AspectRatio | AspectRatio[]): string {
    for (const res of Array.isArray(aspectRatio) ? aspectRatio : [aspectRatio]) {
      switch (res) {
        case "square":
          return "1:1"
        case "wide":
          return "16:9"
        case "tall":
          return "9:16"
      }
    }
    throw new Error("Unsupported aspect ratio")
  }

  async generate(options: GenerationOptions) {
    // Generate image using Replicate's API
    const output = await this.replicate.run("black-forest-labs/flux-pro", {
      input: FluxInputschema.parse({
        prompt: options.prompt,
        aspect_ratio: this.convertAspectRatio(options.aspectRatio),
        safety_tolerance: 5,
        output_format: "png",
        steps: 35,
        guidance: 2,
      }),
    })

    return FluxOutputSchema.parse(output)
  }

  async outpaint(_options: OutpaintingOptions): Promise<never> {
    throw new Error("Outpainting is not supported by this provider")
  }
}
