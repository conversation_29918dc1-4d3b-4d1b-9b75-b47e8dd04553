/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Jimp } from "jimp"
import { describe, expect, it } from "vitest"

import { GeneratedImage } from "./core"
import { generateOutpaintingImageVariants } from "./helpers"

describe("generateOutpaintingImageVariants", () => {
  it("should generate the correct base and masked image variants", async () => {
    // Given
    const jimpImage = new Jimp({ width: 100, height: 100, color: 0xff0000ff })
    const buffer = await jimpImage.getBuffer("image/png")
    const baseImage = await GeneratedImage.fromBuffer(buffer, "image/png")

    // When
    const { scaledBaseImage, offsetBaseImage, maskImage } = await generateOutpaintingImageVariants({
      baseImage,
      left: 0.5,
      right: 0.5,
      up: 0.5,
      down: 0.5,
      maxWidth: 1920,
      maxHeight: 1920,
    })
    console.info(`1st test, scaled base image: ${await scaledBaseImage.toFile()}`)
    console.info(`1st test, offset base image: ${await offsetBaseImage.toFile()}`)
    console.info(`1st test, mask image: ${await maskImage.toFile()}`)

    // Then
    // Check dimensions of the scaled base image
    expect(scaledBaseImage.dimensions.width).toBe(100)
    expect(scaledBaseImage.dimensions.height).toBe(100)

    // Check dimensions of the offset base image
    expect(offsetBaseImage.dimensions.width).toBe(200)
    expect(offsetBaseImage.dimensions.height).toBe(200)

    // Check dimensions of the mask image
    expect(maskImage.dimensions.width).toBe(200)
    expect(maskImage.dimensions.height).toBe(200)

    // Check if the center of the mask image is black
    const jimpMaskImage = await Jimp.read(maskImage.image)
    const centerColor = jimpMaskImage.getPixelColor(100, 100)
    expect(centerColor).toBe(0x000000ff)

    // Check if the corners of the mask image are white
    const cornerColor = jimpMaskImage.getPixelColor(0, 0)
    expect(cornerColor).toBe(0xffffffff)

    // Check if the expected area of the offset base image is red
    const jimpResizedBaseImage = await Jimp.read(offsetBaseImage.image)
    const baseImageX = (200 - 100) / 2
    const baseImageY = (200 - 100) / 2
    const redColor = jimpResizedBaseImage.getPixelColor(baseImageX, baseImageY)
    expect(redColor).toBe(0xff0000ff)

    // Check other corners of the original base image position
    const redColorTopRight = jimpResizedBaseImage.getPixelColor(baseImageX + 99, baseImageY)
    const redColorBottomLeft = jimpResizedBaseImage.getPixelColor(baseImageX, baseImageY + 99)
    const redColorBottomRight = jimpResizedBaseImage.getPixelColor(baseImageX + 99, baseImageY + 99)
    expect(redColorTopRight).toBe(0xff0000ff)
    expect(redColorBottomLeft).toBe(0xff0000ff)
    expect(redColorBottomRight).toBe(0xff0000ff)
  })

  it("should respect the maximum width and height constraints", { timeout: 10000 }, async () => {
    // Given
    const jimpImage = new Jimp({ width: 1920, height: 1920, color: 0xff0000ff })
    const buffer = await jimpImage.getBuffer("image/png")
    const baseImage = await GeneratedImage.fromBuffer(buffer, "image/png")

    // When
    const { scaledBaseImage, offsetBaseImage, maskImage } = await generateOutpaintingImageVariants({
      baseImage,
      left: 5,
      right: 5,
      up: 2,
      down: 3,
      maxWidth: 1920,
      maxHeight: 1920,
    })
    console.info(`2nd test, scaled base image: ${await scaledBaseImage.toFile()}`)
    console.info(`2nd test, offset base image: ${await offsetBaseImage.toFile()}`)
    console.info(`2nd test, mask image: ${await maskImage.toFile()}`)

    // Then
    // Check dimensions of the scaled base image
    expect(scaledBaseImage.dimensions.width).toBe(175)
    expect(scaledBaseImage.dimensions.height).toBe(175)

    // Check dimensions of the offset base image
    expect(offsetBaseImage.dimensions.width).toBe(1920)
    expect(offsetBaseImage.dimensions.height).toBe(1047)

    // Check dimensions of the mask image
    expect(maskImage.dimensions.width).toBe(1920)
    expect(maskImage.dimensions.height).toBe(1047)
  })
})
