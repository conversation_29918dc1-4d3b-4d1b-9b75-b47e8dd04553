/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { cssColorToHex, Jim<PERSON> } from "jimp"

import { GeneratedImage } from "./core"

/**
 * Options for generating the masked image pair for outpainting.
 *
 * @property {GeneratedImage} baseImage - The base image to be outpainted.
 * @property {number} left - The factor by which to extend the image to the left.
 * @property {number} right - The factor by which to extend the image to the right.
 * @property {number} up - The factor by which to extend the image upwards.
 * @property {number} down - The factor by which to extend the image downwards.
 * @property {number} maxWidth - The maximum width of the target image.
 * @property {number} maxHeight - The maximum height of the target image.
 */
type GenerateOutpaintingImageVariantsOptions = {
  baseImage: GeneratedImage
  left: number
  right: number
  up: number
  down: number
  maxWidth: number
  maxHeight: number
}

/**
 * Generates a set of image variants typically useful in an outpainting context.
 *
 * The base image is resized and positioned within the target dimensions based on the specified padding values.
 * The mask image is created where the area occupied by the original image is white, and the rest is black.
 * A scaled version of the original base image is also returned.
 *
 * @param {GenerateOutpaintingImageVariantsOptions} options - The options for generating the masked image pair.
 */
export const generateOutpaintingImageVariants = async (
  options: GenerateOutpaintingImageVariantsOptions
): Promise<{ scaledBaseImage: GeneratedImage; offsetBaseImage: GeneratedImage; maskImage: GeneratedImage }> => {
  const { baseImage, left, right, up, down, maxWidth, maxHeight } = options
  const BLACK = cssColorToHex("#000000")
  const WHITE = cssColorToHex("#ffffff")

  // Calculate the intended reference dimensions
  let referenceWidth = baseImage.dimensions.width
  let referenceHeight = baseImage.dimensions.height

  // Calculate the intended target dimensions
  let targetWidth = referenceWidth * (1 + left + right)
  let targetHeight = referenceHeight * (1 + up + down)

  // Calculate scaling factors for width and height
  const widthScale = maxWidth / targetWidth
  const heightScale = maxHeight / targetHeight

  // Apply the smallest scale to ensure both dimensions fit within the limits, but only if necessary
  const scale = Math.min(widthScale, heightScale, 1)
  referenceWidth = Math.round(referenceWidth * scale)
  referenceHeight = Math.round(referenceHeight * scale)
  targetWidth = Math.round(targetWidth * scale)
  targetHeight = Math.round(targetHeight * scale)

  // Resize the base image
  const scaledBaseImage = await Jimp.read(baseImage.image)
  scaledBaseImage.resize({ w: referenceWidth, h: referenceHeight })

  // Create the new base image offset by the specified padding values
  const x = Math.round(left * referenceWidth)
  const y = Math.round(up * referenceHeight)
  const offsetBaseImage = new Jimp({ width: targetWidth, height: targetHeight, color: BLACK }) // Black background
  offsetBaseImage.composite(scaledBaseImage, x, y)

  // Create the mask image
  const maskRect = new Jimp({ width: referenceWidth, height: referenceHeight, color: BLACK }) // Black rectangle
  const maskImage = new Jimp({ width: targetWidth, height: targetHeight, color: WHITE }) // White mask
  maskImage.composite(maskRect, x, y)

  // Convert images to buffer and create GeneratedImage instances
  const scaledBaseBuffer = await scaledBaseImage.getBuffer("image/png")
  const offsetBaseBuffer = await offsetBaseImage.getBuffer("image/png")
  const maskBuffer = await maskImage.getBuffer("image/png", {
    colorType: 0,
  })

  const scaledBaseGeneratedImage = await GeneratedImage.fromBuffer(scaledBaseBuffer, "image/png")
  const offsetBaseGeneratedImage = await GeneratedImage.fromBuffer(offsetBaseBuffer, "image/png")
  const maskGeneratedImage = await GeneratedImage.fromBuffer(maskBuffer, "image/png")

  return {
    scaledBaseImage: scale === 1 ? baseImage : scaledBaseGeneratedImage,
    offsetBaseImage: offsetBaseGeneratedImage,
    maskImage: maskGeneratedImage,
  }
}
