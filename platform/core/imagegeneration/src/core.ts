/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Buffer } from "buffer"
import { writeFile } from "fs/promises"
import { tmpdir } from "os"
import { join } from "path"
import { Jim<PERSON> } from "jimp"
import { z } from "zod"

import type { UnionToTuple } from "@kreios/utils/types/union-to-tuple"

/**
 * Service for generating images using multiple providers.
 *
 * @template T - A tuple of image generation providers.
 */
export class ImageGenerationService<T extends readonly ImageGenerationProvider[]> {
  // @ts-expect-error - zod enum require the tuple values to be known at compile time
  public providerSchema: z.ZodEnum<UnionToTuple<T[number]["metadata"]["id"]>>
  private providerMap: Map<string, T[number]["metadata"]>
  /**
   * Creates an instance of ImageGenerationService.
   *
   * @param providers - An array of image generation providers.
   */
  constructor(private readonly providers: T) {
    if (providers.length === 0) {
      throw new Error("At least one provider must be specified")
    }
    console.info(`Initialized image generation service with a total of ${providers.length} providers:`)
    providers.forEach((provider) => console.info(`- ${provider.metadata.fullName} (${provider.metadata.id})`))

    const providerIds = this.providers.map((provider) => provider.metadata.id) as T[number]["metadata"]["id"][]
    // @ts-expect-error - zod enum require the tuple values to be known at compile time
    this.providerSchema = z.enum(providerIds) as unknown as z.ZodEnum<
      // @ts-expect-error - zod enum require the tuple values to be known at compile time
      UnionToTuple<T[number]["metadata"]["id"]>
    >

    this.providerMap = new Map(this.providers.map((provider) => [provider.metadata.id, provider.metadata]))
  }

  /**
   * Enumerates the available providers.
   *
   * @returns An array of provider metadata.
   */
  public listProviders(): T[number]["metadata"][] {
    return this.providers.map((provider) => provider.metadata)
  }

  /**
   * Retrieves the metadata of a registered provider based on its ID.
   *
   * @param providerId - The ID of the provider to retrieve.
   * @returns The metadata of the provider with the given ID, or undefined if no such provider exists.
   */
  public getProvider(providerId: ProviderId<T> | string): T[number]["metadata"] | undefined {
    try {
      return this.getProviderOrThrow(providerId)
    } catch {
      return undefined
    }
  }

  /**
   * Retrieves the metadata of a registered provider based on its ID.
   *
   * @param providerId - The ID of the provider to retrieve.
   * @returns The metadata of the provider with the given ID.
   * @throws An error if no provider with the given ID exists.
   */
  public getProviderOrThrow(providerId: ProviderId<T> | string): T[number]["metadata"] {
    const provider = this.providerMap.get(providerId)
    if (!provider) throw new Error(`Provider with ID '${providerId}' not found`)

    return provider
  }

  /**
   * Generates images using the specified providers based on the given prompt and options.
   *
   * @param providerId - The ID of the provider to use for image generation.
   * @param options - Additional options for image generation such as prompt and aspect ratio.
   * @returns A promise that resolves to an array of generated images with provider ID and Buffer.
   */
  public async generate(providerId: ProviderId<T>, options: GenerationOptions): Promise<GeneratedImage> {
    // Determine which providers to use
    const provider = this.providers.find((p) => p.metadata.id === providerId)
    if (!provider) {
      throw new Error(`Provider with ID '${providerId}' not found`)
    }

    // Generate image
    let generatedImage: GeneratedImage
    const urlOrBuffer = await provider.generate(options)
    if (typeof urlOrBuffer === "string") {
      generatedImage = await GeneratedImage.fromURL(urlOrBuffer)
    } else {
      generatedImage = await GeneratedImage.fromBuffer(urlOrBuffer.image, urlOrBuffer.mimeType)
    }

    // Log some information and return the generated image
    console.log(
      `Generated image using provider ${providerId} with mime type ${generatedImage.mimeType}: ${await generatedImage.toFile()}`
    )
    return generatedImage
  }

  /**
   * Outpaints (i.e. extends) an image using the specified provider and options.
   *
   * @param providerId - The ID of the provider to use for outpainting.
   * @param options - The options for outpainting, such as the prompt, base image and extension factors.
   * @returns A promise that resolves to an extended version of the original image.
   */
  public async outpaint(providerId: ProviderId<T>, options: OutpaintingOptions): Promise<GeneratedImage> {
    const provider = this.providers.find((p) => p.metadata.id === providerId)
    if (!provider) {
      throw new Error(`Provider with ID '${providerId}' not found`)
    }

    const result = await provider.outpaint(options)
    let outpaintedImage: GeneratedImage
    if (typeof result === "string") {
      outpaintedImage = await GeneratedImage.fromURL(result)
    } else {
      outpaintedImage = await GeneratedImage.fromBuffer(result.image, result.mimeType)
    }

    console.log(
      `Outpainted image using provider ${providerId} with mime type ${outpaintedImage.mimeType}: ${await outpaintedImage.toFile()}`
    )
    return outpaintedImage
  }
}

/**
 * Options supported in the context of image generation.
 */
export type GenerationOptions = {
  /**
   * The prompt describing the image to be generated.
   */
  prompt: string

  /**
   * Desired aspect ratio for the generated image. If an array is provided, the first aspect ratio that is supported by the provider will be used.
   */
  aspectRatio: AspectRatio | AspectRatio[]
  /**
   * A blurb of text describing what you do not wish to see in the output image.
   */
  negativePrompt?: string
}

/**
 * Options for outpainting an image.
 */
export type OutpaintingOptions = {
  /**
   * The prompt describing the image to be generated.
   */
  prompt?: string

  /**
   * The base image to outpaint.
   */
  baseImage: GeneratedImage

  /**
   * The factor by which to extend the image to the left.
   * A floating-point number indicating the multiple of the image's width.
   */
  left: number

  /**
   * The factor by which to extend the image to the right.
   * A floating-point number indicating the multiple of the image's width.
   */
  right: number

  /**
   * The factor by which to extend the image upwards.
   * A floating-point number indicating the multiple of the image's height.
   */
  up: number

  /**
   * The factor by which to extend the image downwards.
   * A floating-point number indicating the multiple of the image's height.
   */
  down: number
}

/**
 * Interface for an image generation provider.
 *
 * This interface defines the methods that any image generation provider must implement.
 */
export interface ImageGenerationProvider {
  /**
   * Metadata about the provider.
   */
  metadata: {
    id: string
    fullName: string
    shortName: string
    supportsOutpainting: boolean
  }

  /**
   * Generates an image based on the given prompt and options.
   *
   * @param options - Additional options to control the image generation, such as aspect ratio.
   * @returns The URL of the generated image.
   */
  generate(options: GenerationOptions): Promise<string | { image: Buffer; mimeType: MimeType }>

  /**
   * Outpaints (i.e. extends) an image based on the given base image and options.
   *
   * @param options - The options for outpainting.
   * @returns The URL of the extended image or an object containing the image data and mime type.
   */
  outpaint(options: OutpaintingOptions): Promise<
    | string
    | {
        image: Buffer
        mimeType: MimeType
      }
  >
}

/**
 * The aspect ratios supported by the image generation service.
 */
export type AspectRatio = "square" | "wide" | "tall"

/**
 * The mime type we support.
 */
export type MimeType = "image/jpeg" | "image/png"

/**
 * Checks if the given mime type is supported.
 *
 * @param mimeType - The mime type to check.
 * @returns True if the mime type is supported, false otherwise.
 */
const isSupportedMimeType = (mimeType: string | null): mimeType is MimeType => {
  return mimeType === "image/jpeg" || mimeType === "image/png"
}

/**
 * Data structure used for representing a single generated image.
 */
export class GeneratedImage {
  private constructor(
    public image: Buffer,
    public mimeType: MimeType,
    public dimensions: { width: number; height: number }
  ) {}

  /**
   * Creates a new GeneratedImage instance from a Buffer and mime type.
   *
   * @param buffer - The image data as a Buffer.
   * @param mimeType - The mime type of the image.
   * @returns A promise that resolves to a new GeneratedImage instance.
   */
  static async fromBuffer(buffer: Buffer, mimeType: MimeType) {
    const img = await Jimp.read(buffer)
    const dimensions = { width: img.bitmap.width, height: img.bitmap.height }
    return new GeneratedImage(buffer, mimeType, dimensions)
  }

  /**
   * Downloads an image from a URL and returns it as a Buffer.
   *
   * @param url - The URL of the image to download.
   * @returns A promise that resolves to a Buffer containing the image data.
   */
  static async fromURL(url: string) {
    // Make the HTTP request
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to download image from ${url} (HTTP status ${response.status})`)
    }

    // Extract the filename and mime type from the HTTP headers, if available
    const mimeType = response.headers.get("content-type")
    if (!isSupportedMimeType(mimeType)) {
      throw new Error(`Unsupported mime type: ${mimeType}`)
    }

    // Create a new GeneratedImage instance from that
    const arrayBuffer = await response.arrayBuffer()
    const image = Buffer.from(arrayBuffer)

    return GeneratedImage.fromBuffer(image, mimeType)
  }

  /**
   * Writes the image data to a temporary file and returns the file path.
   *
   * @returns A promise that resolves to the file path of the temporary file.
   */
  async toFile(): Promise<string> {
    let extension
    switch (this.mimeType) {
      case "image/jpeg":
        extension = "jpg"
        break
      case "image/png":
        extension = "png"
        break
      default:
        throw new Error(`Unsupported mime type`)
    }

    const tempFilePath = join(tmpdir(), `image-${Date.now()}.${extension}`)
    await writeFile(tempFilePath, this.image)
    return tempFilePath
  }

  /**
   * Converts the image data to a base64 string.
   *
   * @returns The image data as a base64 string.
   */
  toBase64(): string {
    return this.image.toString("base64")
  }
}

/**
 * Type to extract provider IDs from an array of providers.
 */
export type ProviderId<T extends readonly ImageGenerationProvider[]> = T[number]["metadata"]["id"]
