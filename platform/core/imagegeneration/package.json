{"name": "@kreios/imagegeneration", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./providers": "./src/providers/index.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "format:fix": "prettier --check . --write --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@kreios/utils": "workspace:*", "google-auth-library": "9.14.1", "jimp": "1.6.0", "openai": "4.58.2", "replicate": "0.32.1", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/node": "20.14.10", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}