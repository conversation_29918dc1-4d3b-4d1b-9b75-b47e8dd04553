/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { EmailMessage } from "@azure/communication-email"
import { EmailClient } from "@azure/communication-email"

import type { MailSenderSPI, MessageId, ProviderMailOptions } from "../core"

/**
 * Implementation of the MailProvider SPI that sends emails using Azure Communication Services.
 */
export class AzureProvider implements MailSenderSPI {
  private emailClient: EmailClient

  /**
   * Constructs a new AzureProvider with the given connection string.
   * @param connectionString The connection string for Azure Communication Services.
   */
  constructor(connectionString: string) {
    this.emailClient = new EmailClient(connectionString)
  }

  /**
   * Sends an email using Azure Communication Services.
   * This method uses `beginSend` to queue the message and handle the polling process.
   * @param options The options for the email to be sent.
   */
  public async send(options: ProviderMailOptions): Promise<MessageId> {
    console.info(
      `Sending email via Azure Communication Services to: ${options.to.map((recipient) => recipient.email).join(", ")}, Subject: ${options.subject}`
    )

    // Prepare the email we want to send
    const emailPayload: EmailMessage = {
      senderAddress: options.from.email,
      content: {
        subject: options.subject,
        plainText: options.text, // Always present
        html: options.html, // Optional
      },
      recipients: {
        to: options.to.map((recipient) => ({
          address: recipient.email,
          displayName: recipient.name,
        })),
        cc: options.cc?.map((recipient) => ({
          address: recipient.email,
          displayName: recipient.name,
        })),
      },
    }

    // Send the email and wait for the process to complete
    const poller = await this.emailClient.beginSend(emailPayload)
    const result = await poller.pollUntilDone()
    return result.id
  }

  /**
   * Sends multiple emails in a batch using Azure Communication Services.
   * This method sends each email individually but in parallel.
   * @param options An array of email options to be sent in a batch.
   * @returns An array of message IDs for the sent emails.
   */
  public async sendBatch(options: ProviderMailOptions[]): Promise<MessageId[]> {
    console.info(`Sending batch of ${options.length} emails via Azure Communication Services`)

    // Create an array of promises for each email
    const sendPromises = options.map((option) => {
      // Prepare the email payload
      const emailPayload: EmailMessage = {
        senderAddress: option.from.email,
        content: {
          subject: option.subject,
          plainText: option.text, // Always present
          html: option.html, // Optional
        },
        recipients: {
          to: option.to.map((recipient) => ({
            address: recipient.email,
            displayName: recipient.name,
          })),
          cc: option.cc?.map((recipient) => ({
            address: recipient.email,
            displayName: recipient.name,
          })),
        },
      }

      // Return a promise that resolves to the message ID or an error message
      return this.emailClient
        .beginSend(emailPayload)
        .then((poller) => poller.pollUntilDone())
        .then((result) => result.id)
        .catch((error) => `error:${error instanceof Error ? error.message : "Unknown error"}`)
    })

    // Wait for all promises to settle
    const results = await Promise.all(sendPromises)

    console.info(`Batch of ${options.length} emails sent via Azure Communication Services`)
    return results
  }
}
