/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Resend } from "resend"

import type { MailSenderSPI, MessageId, ProviderMailOptions } from "../core"

// Define types for Resend API responses
type ResendBatchResponseItem = {
  id?: string
  error?: {
    message: string
    code?: string
  }
}

type ResendBatchResponse =
  | ResendBatchResponseItem[]
  | {
      data?: { id?: string }
      error?: {
        message: string
        code?: string
      }
    }

/**
 * Implementation of the MailProvider SPI that sends emails using the Resend service.
 */
export class ResendProvider implements MailSenderSPI {
  private resendClient: Resend

  /**
   * Constructs a new ResendProvider with the given API key.
   * @param apiKey The API key for the Resend service.
   */
  constructor(apiKey: string) {
    this.resendClient = new Resend(apiKey)
  }

  /**
   * Sends an email using the Resend service.
   * @param options The options for the email to be sent.
   */
  public async send(options: ProviderMailOptions): Promise<MessageId> {
    console.info(
      `Sending email via Resend to: ${options.to.map((recipient) => recipient.email).join(", ")}, Subject: ${options.subject}`
    )

    const emailPayload = {
      from: `${options.from.name} <${options.from.email}>`,
      to: options.to.map((recipient) => `${recipient.name} <${recipient.email}>`).join(", "),
      cc: options.cc?.map((recipient) => `${recipient.name} <${recipient.email}>`).join(", "),
      subject: options.subject,
      text: options.text, // Always present
      html: options.html, // Optional
    }

    const response = await this.resendClient.emails.send(emailPayload)
    if (response.error) {
      throw new Error(`Error sending email via Resend: ${response.error.name} - ${response.error.message}`)
    }
    if (!response.data) {
      throw new Error("Unexpected error: no message ID returned from Resend API")
    }
    return response.data.id
  }

  /**
   * Sends multiple emails in a batch using the Resend service.
   * @param options An array of email options to be sent in a batch.
   * @returns An array of message IDs for the sent emails.
   */
  public async sendBatch(options: ProviderMailOptions[]): Promise<MessageId[]> {
    console.info(`Sending batch of ${options.length} emails via Resend`)

    // Convert to Resend batch format

    const batchPayload = options.map((option) => ({
      from: `${option.from.name} <${option.from.email}>`,
      to: option.to.map((recipient) => recipient.email)[0],
      bcc: option.to.map((recipient) => recipient.email),
      subject: option.subject,
      text: option.text,
      html: option.html,
    }))

    try {
      // Send batch emails
      // The Resend batch.send method returns an array of responses
      batchPayload.map((batch, _index) => {
        console.log("--------------------------------------------")
        console.log("For Batch " + _index + " : ")
        console.log("Emails: ", { to: batch.to, bcc: batch.bcc })
        console.log("--------------------------------------------")
      })
      const response = (await this.resendClient.batch.send(batchPayload)) as ResendBatchResponse

      console.log("-------------------------------------------------------")
      console.log("Response: ", { response: JSON.stringify(response) })
      console.log("-------------------------------------------------------")

      // Process response - Resend returns an array of objects with either id or error
      const messageIds: MessageId[] = []

      // Handle different response formats
      if (Array.isArray(response)) {
        // Array response format
        response.forEach((item: ResendBatchResponseItem, index: number) => {
          if (item.error) {
            console.error(`Error sending email #${index} in batch: ${item.error.message}`)
            messageIds.push(`error:${item.error.message}`)
          } else if (item.id) {
            messageIds.push(item.id)
          } else {
            messageIds.push(`unknown:${index}`)
          }
        })
      } else if (typeof response === "object") {
        // Single response object format (older API versions)
        if (response.error) {
          throw new Error(`Error sending batch emails: ${response.error.message}`)
        }
        // If we have data but it's not an array, create a single ID
        messageIds.push(response.data?.id ?? "batch:success")
      } else {
        // Unknown response format
        throw new Error("Unexpected response format from Resend batch API")
      }

      return messageIds
    } catch (error) {
      console.error("Error sending batch emails via Resend:", error)
      throw new Error(
        `Error sending batch emails via Resend: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }
}
