/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { beforeEach, describe, expect, it } from "vitest"

import type { MailAddress, ProviderMailOptions } from "../core"
import { ResendProvider } from "./resend-provider"

const shouldRunTests = process.env.RESEND_API_KEY && process.env.RESEND_FROM_EMAIL

describe.runIf(shouldRunTests)("ResendProvider", () => {
  let resendProvider: ResendProvider
  const senderAddress: MailAddress = { name: "Sender", email: process.env.RESEND_FROM_EMAIL! }

  beforeEach(() => {
    resendProvider = new ResendProvider(process.env.RESEND_API_KEY!)
  })

  it("should send an email via Resend", async () => {
    const mailOptions: ProviderMailOptions = {
      from: senderAddress,
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email via Resend",
      text: "This is a plain text email.",
    }

    const messageId = await resendProvider.send(mailOptions)
    expect(messageId).toBeDefined()
  })

  it("should send an email with HTML via Resend", async () => {
    const mailOptions: ProviderMailOptions = {
      from: senderAddress,
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email with HTML via Resend",
      text: "This is a plain text email.",
      html: "<p>This is an <strong>HTML</strong> email.</p>",
    }

    const messageId = await resendProvider.send(mailOptions)
    expect(messageId).toBeDefined()
  })
})
