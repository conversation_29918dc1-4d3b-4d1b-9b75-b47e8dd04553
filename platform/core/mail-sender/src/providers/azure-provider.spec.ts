/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { beforeEach, describe, expect, it } from "vitest"

import type { MailAddress, ProviderMailOptions } from "../core"
import { AzureProvider } from "./azure-provider"

const shouldRunTests = process.env.AZURE_COMMUNICATION_CONNECTION_STRING && process.env.AZURE_COMMUNICATION_FROM_EMAIL

describe.runIf(shouldRunTests)("AzureProvider", () => {
  let azureProvider: AzureProvider
  const senderAddress: MailAddress = { name: "Sender", email: process.env.AZURE_COMMUNICATION_FROM_EMAIL! }

  beforeEach(() => {
    azureProvider = new AzureProvider(process.env.AZURE_COMMUNICATION_CONNECTION_STRING!)
  })

  it("should send an email via Azure Communication Services", async () => {
    const mailOptions: ProviderMailOptions = {
      from: senderAddress,
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email via Azure Communication Services",
      text: "This is a plain text email.",
    }

    const messageId = await azureProvider.send(mailOptions)
    expect(messageId).toBeDefined()
  }, 10000)

  it("should send an email with HTML via Azure Communication Services", async () => {
    const mailOptions: ProviderMailOptions = {
      from: senderAddress,
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email with HTML",
      text: "This is a plain text email.",
      html: "<p>This is an <strong>HTML</strong> email.</p>",
    }

    const messageId = await azureProvider.send(mailOptions)
    expect(messageId).toBeDefined()
  }, 10000)
})
