/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// We provide this module as a way to selectively export components from the @react-email package, since the Tailwind CSS package is too big and tree-shaking doesn't seem to work

// Render API
export * from "@react-email/render"

// Components
export * from "@react-email/body"
export * from "@react-email/button"
export * from "@react-email/code-block"
export * from "@react-email/code-inline"
export * from "@react-email/column"
export * from "@react-email/container"
export * from "@react-email/font"
export * from "@react-email/head"
export * from "@react-email/heading"
export * from "@react-email/hr"
export * from "@react-email/html"
export * from "@react-email/img"
export * from "@react-email/link"
export * from "@react-email/markdown"
export * from "@react-email/preview"
export * from "@react-email/row"
export * from "@react-email/section"
export * from "@react-email/text"
