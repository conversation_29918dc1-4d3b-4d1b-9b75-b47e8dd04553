/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import type { MailOptions, MailSenderSPI } from "./core"
import { MailSender } from "./core"

vi.mock("./flags", () => {
  return {
    emailOverride: vi.fn().mockResolvedValue(null),
  }
})

describe("MailSender", () => {
  let mailSender: MailSender
  const mockSend = vi.fn().mockResolvedValue("mocked-message-id")
  const mockProvider: MailSenderSPI = {
    send: mockSend,
    sendBatch: vi.fn().mockResolvedValue(["mocked-message-id"]),
  }

  beforeEach(() => {
    mailSender = new MailSender(mockProvider, { name: "No Reply", email: "<EMAIL>" })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it("should convert HTML content to plain text and call provider's send method (HTML only)", async () => {
    const mailOptions: MailOptions = {
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email",
      html: "<p>This is a <strong>HTML</strong> email.</p>",
    }

    const messageId = await mailSender.send(mailOptions)

    expect(mockSend).toHaveBeenCalledWith({
      from: { name: "No Reply", email: "<EMAIL>" },
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email",
      text: "This is a HTML email.",
      html: "<p>This is a <strong>HTML</strong> email.</p>",
    })

    expect(messageId).toBe("mocked-message-id")
  })

  it("should call provider's send method with both HTML and text content when both are provided", async () => {
    const mailOptions: MailOptions = {
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email",
      html: "<p>This is a <strong>HTML</strong> email.</p>",
      text: "This is a plain text version of the email.",
    }

    const messageId = await mailSender.send(mailOptions)

    expect(mockSend).toHaveBeenCalledWith({
      from: { name: "No Reply", email: "<EMAIL>" },
      to: [{ name: "Recipient", email: "<EMAIL>" }],
      subject: "Test Email",
      text: "This is a plain text version of the email.",
      html: "<p>This is a <strong>HTML</strong> email.</p>",
    })

    expect(messageId).toBe("mocked-message-id")
  })
})
