/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { htmlToText } from "html-to-text"

import type { RequireAtLeastOne } from "@kreios/utils/types/require-at-least-one"

import { emailOverride } from "./flags"

/**
 * This file defines the core interface and types for the mail sending service.
 */

/**
 * Represents an email address with an optional display name.
 */
export type MailAddress = {
  name: string
  email: string
}

/**
 * Options for sending an email.
 * Either provide `text` or `html`, or both. If only `html` is provided, a plain-text version is generated.
 */
export type MailOptions = {
  to: MailAddress[]
  cc?: MailAddress[]
  subject: string
} & RequireAtLeastOne<{
  text: string
  html: string
}>

/**
 * ProviderMailOptions is the internal type used for sending emails to the provider.
 */
export type ProviderMailOptions = {
  from: MailAddress
  to: MailAddress[]
  cc?: MailAddress[]
  subject: string
  text: string
  html?: string
}

/**
 * MessageId is the type for the ID of an email message that has been sent.
 */
export type MessageId = string

/**
 * MailSenderSPI defines the Service Provider Interface (SPI) for mail services.
 * Implementations must handle sending emails with the given options.
 */
export interface MailSenderSPI {
  /**
   * Sends an email using the configured provider.
   * @param options The options for the email to be sent.
   * @returns The ID of the sent email.
   */
  send(options: ProviderMailOptions): Promise<MessageId>

  /**
   * Sends multiple emails in a batch using the configured provider.
   * @param options An array of email options to be sent in a batch.
   * @returns An array of message IDs for the sent emails.
   */
  sendBatch(options: ProviderMailOptions[]): Promise<MessageId[]>
}

/**
 * MailSender is the main class for sending emails. It uses a configured provider to handle email sending.
 */
export class MailSender {
  public readonly senderAddress: MailAddress
  private provider: MailSenderSPI

  /**
   * Constructs a new MailSender instance with the given provider and sender address.
   * @param provider The mail provider to use for sending emails.
   * @param senderAddress The email address to be used as the "from" address.
   */
  constructor(provider: MailSenderSPI, senderAddress: MailAddress) {
    this.senderAddress = senderAddress
    this.provider = provider
    console.info("MailSender initialized with provider:", this.provider.constructor.name)
  }

  /**
   * Sends an email using the configured provider. If only `html` is provided, it automatically generates
   * a plain-text version from the HTML content.
   * @param options The options for the email to be sent.
   */
  public async send(options: MailOptions): Promise<string> {
    const override = await emailOverride()
    const providerOptions = this.ensureTextContent(options)
    const messageId = await this.provider.send({
      ...providerOptions,
      from: this.senderAddress,
      ...(override ? { to: [{ name: override, email: override }] } : {}),
    })
    console.info(`Email sent successfully to: ${options.to.map((recipient) => recipient.email).join(", ")}`)
    return messageId
  }

  /**
   * Sends multiple emails in a batch using the configured provider.
   * This is more efficient than sending emails individually when sending to many recipients.
   *
   * @param optionsArray An array of email options to be sent in a batch.
   * @returns An array of message IDs for the sent emails.
   */
  public async sendBatch(optionsArray: MailOptions[]): Promise<string[]> {
    const override = await emailOverride()

    // Process each email in the batch
    const providerOptionsArray = optionsArray.map((options) => {
      const providerOptions = this.ensureTextContent(options)
      return {
        ...providerOptions,
        from: this.senderAddress,
        ...(override ? { to: [{ name: override, email: override }] } : {}),
      }
    })

    // Send the batch
    const messageIds = await this.provider.sendBatch(providerOptionsArray)

    console.info(`Batch of ${optionsArray.length} emails sent successfully`)
    return messageIds
  }

  /**
   * Ensures that the `text` content is always present. If `html` is provided, it generates a plain-text version.
   * @param options The options for the email.
   * @returns The email content with a guaranteed `text` field.
   */
  private ensureTextContent(options: MailOptions) {
    // If the options already have a text field, then return
    if (this.hasText(options)) {
      return { ...options, text: options.text }
    }

    // If not, then generate the plain-text content from the HTML
    const text = this.generateTextFromHtml(options.html)
    return { ...options, text, html: options.html }
  }

  /**
   * Type guard to check if options has a text field.
   */
  private hasText(options: { text?: string; html?: string }): options is { text: string; html?: string } {
    return "text" in options && options.text !== undefined
  }

  /**
   * Generates a plain-text version of the provided HTML content using the `html-to-text` library.
   * @param html The HTML content.
   * @returns The plain-text version of the HTML content.
   */
  private generateTextFromHtml(html: string) {
    return htmlToText(html, {
      wordwrap: 130,
    })
  }
}
