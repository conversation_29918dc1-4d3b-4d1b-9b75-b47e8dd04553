/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// import "server-only" comment out server-only for now since it caueses errors when trying to run via the cli
// when opting for the react-server condition mail-sender package fails since react-email will import a different react version of condition is set
// this may can be resolved when upgrading to react 19

import { unstable_flag as flag } from "@vercel/flags/next"

import { env } from "../env"

export const emailOverride = flag({
  key: "email-override",
  defaultValue: undefined,
  description: "Override the email address to which any email is sent",
  options: env.EMAIL_OVERRIDE?.map((email) => ({ label: email, value: email })) ?? [],
  decide: async () => {
    return null
  },
})
