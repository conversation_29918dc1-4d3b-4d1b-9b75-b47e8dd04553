{"name": "@kreios/mail-sender", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.ts", "./providers": "./src/providers/index.ts", "./utils": "./src/utils/index.ts", "./flags": "./src/flags.ts", "./env": "./env.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@azure/communication-email": "1.0.0", "@kreios/utils": "workspace:*", "@react-email/body": "0.0.10", "@react-email/button": "0.0.17", "@react-email/code-block": "0.0.9", "@react-email/code-inline": "0.0.4", "@react-email/column": "0.0.12", "@react-email/container": "0.0.14", "@react-email/font": "0.0.8", "@react-email/head": "0.0.11", "@react-email/heading": "0.0.14", "@react-email/hr": "0.0.10", "@react-email/html": "0.0.10", "@react-email/img": "0.0.10", "@react-email/link": "0.0.10", "@react-email/markdown": "0.0.12", "@react-email/preview": "0.0.11", "@react-email/render": "1.0.1", "@react-email/row": "0.0.10", "@react-email/section": "0.0.14", "@react-email/text": "0.0.10", "@t3-oss/env-core": "0.11.1", "@vercel/flags": "2.6.1", "html-to-text": "9.0.4", "resend": "4.0.0", "zod": "3.23.8"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/html-to-text": "9.0.4", "@types/node": "20.14.10", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}, "peerDependencies": {"react": "18.3.1", "react-dom": "18.3.1"}}