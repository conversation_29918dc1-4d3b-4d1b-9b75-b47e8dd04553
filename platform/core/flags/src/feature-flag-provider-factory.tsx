/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
"use client"

// Define the structure of a flag
import type { Flag } from "@vercel/flags/next"
import { useState } from "react"
import constate from "constate"

// Define the type for the flags object
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type FlagsObject = Record<string, Flag<any>>

// Create a type based on the passed flags
type FeatureFlags<T extends FlagsObject> = {
  [K in keyof T]: Awaited<ReturnType<T[K]>>
}

/**
 * A factory function to create scoped feature flag providers.
 * @param T - The type of the flags object.
 * @returns The feature flag provider and hooks.
 */
export function createFeatureFlagsProvider<T extends FlagsObject>() {
  return constate(
    ({ flags }: { flags: FeatureFlags<T> }) => {
      const [featureFlags, setFeatureFlags] = useState(() => flags)

      const setFeatureFlag = <K extends keyof FeatureFlags<T>>(flag: K, value: FeatureFlags<T>[K]) => {
        setFeatureFlags((prev) => ({ ...prev, [flag]: value }))
      }

      return { featureFlags, setFeatureFlag }
    },
    (value) => value.featureFlags,
    (value) => value.setFeatureFlag
  )
}
