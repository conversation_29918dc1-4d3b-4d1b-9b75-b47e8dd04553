{"name": "@kreios/flags", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {".": "./src/index.tsx", "./env": "./env.ts", "./*": "./src/*.tsx"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run --passWithNoTests", "test:watch": "vitest", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@kreios/prettier-config", "dependencies": {"@vercel/flags": "2.6.1", "constate": "3.3.2"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tailwind-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/react": "18.3.5", "eslint": "9.10.0", "prettier": "3.4.2", "tailwindcss": "3.4.10", "typescript": "5.6.2", "vitest": "2.1.9"}, "peerDependencies": {"react": "18.3.1"}}