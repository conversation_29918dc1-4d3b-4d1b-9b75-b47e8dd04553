{"name": "@kreios/utils", "version": "0.1.0", "private": true, "license": "LicenseRef-LICENSE", "type": "module", "exports": {"./*": "./src/*.ts"}, "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check . --ignore-path ../../../.gitignore --ignore-path ../../../.prettierignore", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "typecheck": "tsc --noEmit"}, "prettier": "@kreios/prettier-config", "dependencies": {"@date-fns/utc": "2.1.0", "@formatjs/intl-durationformat": "0.6.5", "chalk": "5.3.0", "cli-progress": "3.12.0", "dataloader": "2.2.2", "date-fns": "4.1.0", "lodash-es": "4.17.21", "nanoid": "5.0.9", "superjson": "2.2.1", "tinycolor2": "1.6.0"}, "devDependencies": {"@kreios/eslint-config": "workspace:*", "@kreios/prettier-config": "workspace:*", "@kreios/tsconfig": "workspace:*", "@types/cli-progress": "3.11.6", "@types/lodash-es": "4.17.12", "@types/node": "20.14.10", "@types/tinycolor2": "1.4.6", "@vercel/functions": "1.4.1", "eslint": "9.10.0", "prettier": "3.4.2", "typescript": "5.6.2", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}