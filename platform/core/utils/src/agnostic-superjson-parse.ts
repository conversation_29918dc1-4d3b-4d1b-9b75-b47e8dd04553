/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { SuperJSONResult } from "superjson"
import superjson from "superjson"

/**
 * If the payload is a SuperJSON object, it will be parsed using superjson.deserialize.
 * Otherwise, it will be parsed using JSON.parse.
 *
 * @param text - The string to parse.
 * @returns The parsed value.
 *
 * @example
 * const payload = agnosticSuperjsonParse<{ name: string }>('{"json": {"name": "<PERSON>"}}')
 * console.log(payload) // { name: "<PERSON>" }
 *
 * const payload = agnosticSuperjsonParse<{ name: string }>('{"name": "<PERSON>"}')
 * console.log(payload) // { name: "<PERSON>" }
 */
export const agnosticSuperjsonParse = <T>(text: string) => {
  const payload = JSON.parse(text) as unknown
  if (typeof payload === "object" && payload !== null && "json" in payload)
    return superjson.deserialize<T>(payload as SuperJSONResult)
  return payload as T
}
