/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { BatchLoadFn } from "dataloader"
import type { Mock, MockInstance } from "vitest"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { DataLoader } from "./dataloader" // Adjust the import path as needed

describe("Dataloader", () => {
  const originalEnv = process.env.NODE_ENV

  let consoleWarnSpy: MockInstance<typeof console.warn>
  let batchFunction: Mock<BatchLoadFn<string, string>>
  let loader: DataLoader<string, string>

  // @ts-expect-error - We need to access a private property for testing purposes
  const flushThrottledFunction = () => loader.logWarningThrottled.flush()

  beforeEach(() => {
    // Set NODE_ENV to development for testing
    process.env.NODE_ENV = "development"

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    consoleWarnSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

    batchFunction = vi.fn(async (keys: readonly string[]) => {
      return keys.map((key) => `Value for ${key}`)
    })

    Object.defineProperty(batchFunction, "name", { value: "batchFunction" })

    loader = new DataLoader(batchFunction)
  })

  afterEach(() => {
    // Restore the original NODE_ENV
    process.env.NODE_ENV = originalEnv
    consoleWarnSpy.mockRestore()
  })

  describe("Warning message", () => {
    it("should include the batch function name", async () => {
      await loader.load("key1")
      await loader.load("key2")

      flushThrottledFunction()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringMatching(/.*/),
        expect.stringContaining("batchFunction"),
        expect.stringMatching(/.*/)
      )
    })

    it("should include the number of times the batch function was called rapidly", async () => {
      await loader.load("key1")
      await loader.load("key2")
      await loader.load("key3")
      await loader.load("key4")

      flushThrottledFunction()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringMatching(/.*/),
        expect.stringMatching(/2 times/),
        expect.stringMatching(/.*/)
      )
    })

    it("should include the keys in the warning message", async () => {
      await loader.load("key1")
      await loader.load("key2")

      flushThrottledFunction()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringMatching(/.*/),
        expect.stringMatching(/.*/),
        expect.stringContaining("[key2]")
      )
    })

    it("should cut off keys after the 4th key and include total keys count", async () => {
      await loader.loadMany(["key0"])
      await loader.loadMany(["key1", "key2", "key3", "key4", "key5"])

      flushThrottledFunction()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringMatching(/.*/),
        expect.stringMatching(/.*/),
        expect.stringContaining("[key1,key2,key3, ... 2 more items]")
      )
    })
  })

  it("should log a warning if batch function is called rapidly", async () => {
    // Call the batch function twice rapidly
    await loader.load("key1")
    await loader.load("key2")

    flushThrottledFunction()

    expect(consoleWarnSpy).toHaveBeenCalledOnce()
  })

  it("should not log a warning if batch function is called with sufficient delay", async () => {
    // Call the batch function with sufficient delay
    await loader.load("key1")
    await new Promise((resolve) => setTimeout(resolve, 200)) // wait 200ms
    await loader.load("key2")

    await new Promise((resolve) => setTimeout(resolve, 200)) // wait 200ms
    await loader.load("key3")

    flushThrottledFunction()

    expect(consoleWarnSpy).not.toHaveBeenCalled()
  })

  it("should not log a warning in production environment", async () => {
    process.env.NODE_ENV = "production"

    // Reinitialize DataLoader in production environment
    loader = new DataLoader(batchFunction)

    // Call the batch function rapidly
    await loader.load("key1")
    await loader.load("key2")

    flushThrottledFunction()

    expect(consoleWarnSpy).not.toHaveBeenCalled()
  })

  it("should work correctly when no initial call has been made", async () => {
    // No initial call to loader

    flushThrottledFunction()
    expect(consoleWarnSpy).not.toHaveBeenCalled()
  })
})
