/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import { EventEmitter } from "events"

import { withResolvers } from "./with-resolvers"

type EventMap<T> = Record<keyof T, any[]> | DefaultEventMap
type DefaultEventMap = [never]
type Key<K, T> = T extends DefaultEventMap ? string | symbol : K | keyof T
type Listener1<K, T> = Listener<K, T, (...args: any[]) => void>
type Listener<K, T, F> = T extends DefaultEventMap
  ? F
  : K extends keyof T
    ? T[K] extends unknown[]
      ? (...args: T[K]) => void
      : never
    : never
type AnyRest = [...args: any[]]
type Args<K, T> = T extends DefaultEventMap ? AnyRest : K extends keyof T ? T[K] : never

export class ReplayEventEmitter<T extends EventMap<T> = DefaultEventMap> extends EventEmitter<T> {
  cache = new Map<unknown, any[][]>()

  emit<K>(eventName: Key<K, T>, ...args: Args<K, T>): boolean {
    if (this.cache.has(eventName)) this.cache.get(eventName)!.push(args)
    else this.cache.set(eventName, [args])

    return super.emit(eventName, ...args)
  }

  addListener<K>(eventName: Key<K, T>, listener: Listener1<K, T>): this {
    return this.on(eventName, listener)
  }

  on<K>(eventName: Key<K, T>, listener: Listener1<K, T>): this {
    for (const args of this.cache.get(eventName) ?? []) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      listener(...args)
    }
    return super.on(eventName, listener)
  }
}

export function createEventEmitterFromAsyncGenerator<T, TReturn = any, TNext = unknown>(
  generator: AsyncGenerator<T, TReturn, TNext>
): ReplayEventEmitter {
  const emitter = new ReplayEventEmitter()

  void (async () => {
    try {
      let result: IteratorResult<T, TReturn>
      while (!(result = await generator.next()).done) {
        emitter.emit("data", result.value)
      }
      emitter.emit("done", result.value)
    } catch (err) {
      emitter.emit("error", err as Error)
    } finally {
      emitter.emit("end")
    }
  })()

  return emitter
}

export async function* createAsyncGeneratorFromEventEmitter<T, TReturn = any, TNext = unknown>(
  emitter: EventEmitter
): AsyncGenerator<T, TReturn, TNext> {
  const eventQueue: T[] = []
  let done = false
  let returnValue: TReturn | undefined
  let error: Error | undefined

  let { promise: nextPromise, resolve: nextResolve } = withResolvers<void>()

  // Register event listeners on the emitter
  const onData = (value: T) => {
    eventQueue.push(value)
    nextResolve()
  }

  const onEnd = () => {
    done = true
    nextResolve()
  }

  const onDone = (value: TReturn) => {
    done = true
    returnValue = value
    nextResolve()
  }

  const onError = (err: Error) => {
    error = err
    nextResolve()
  }

  emitter.on("data", onData)
  emitter.once("done", onDone)
  emitter.once("error", onError)
  emitter.once("end", onEnd)

  try {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    while (!done || eventQueue.length > 0) {
      if (eventQueue.length > 0) {
        yield eventQueue.shift() as T
      } else if (error) {
        throw error
      } else {
        // Wait for new events or completion
        await nextPromise
        // Reset the promise for the next iteration
        ;({ promise: nextPromise, resolve: nextResolve } = withResolvers<void>())
      }
    }
  } finally {
    // Clean up listeners
    emitter.off("data", onData)
    emitter.off("done", onDone)
    emitter.off("error", onError)
    emitter.off("end", onEnd)
  }

  if (error) throw error

  return returnValue as TReturn
}

export function createReplayAsyncGenerator<T = unknown, TReturn = any, TNext = unknown>(
  generator: AsyncGenerator<T, TReturn, TNext>
): { [Symbol.asyncIterator]: () => AsyncGenerator<T, TReturn, TNext> } {
  const eventEmitter = createEventEmitterFromAsyncGenerator(generator)

  const create = () => createAsyncGeneratorFromEventEmitter<T, TReturn, TNext>(eventEmitter)

  return {
    [Symbol.asyncIterator]: create,
  }
}
