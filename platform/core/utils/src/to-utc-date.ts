/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { UTCDate } from "@date-fns/utc"
import { parseISO } from "date-fns"

/**
 * Converts a GraphQL date string to a UTC Date object.
 *
 * @param dateString - The GraphQL date string to convert.
 * @returns A UTC Date object, or undefined if the input is null or undefined.
 */
export function toUTCDate(dateString: string | null | undefined): Date | undefined {
  if (!dateString) return undefined
  return new UTCDate(parseISO(dateString))
}
