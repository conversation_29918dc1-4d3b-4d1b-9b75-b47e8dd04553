/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * This function takes an object of promises and returns an object with the same keys and the resolved values of the promises.
 * It functions the same as Promise.all, but for an object of promises.
 * @param values - An object of promises.
 * @returns An object with the same keys and the resolved values of the promises.
 */
export async function promiseObjectAll<T extends Record<string, Promise<unknown>>>(
  values: T
): Promise<{
  [P in keyof T]: Awaited<T[P]>
}> {
  return Object.fromEntries(
    await Promise.all(Object.entries(values).map(([key, value]) => value.then((data) => [key, data])))
  ) as {
    [P in keyof T]: Awaited<T[P]>
  }
}
