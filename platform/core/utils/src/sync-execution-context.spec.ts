/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { waitUntil } from "@vercel/functions"
import { describe, expect, it } from "vitest"

import { isAsyncContext, isSyncContext, runAsync, runSync } from "./sync-execution-context"

describe("runSync", () => {
  it("should execute the provided function", async () => {
    const result = await runSync(async () => "test")
    expect(result).toBe("test")
  })

  it("should set sync context to true", async () => {
    await runSync(async () => {
      expect(isSyncContext()).toBe(true)
      expect(isAsyncContext()).toBe(false)
    })
  })

  it("should handle errors properly", async () => {
    const error = new Error("Test error")

    await expect(
      runSync(async () => {
        throw error
      })
    ).rejects.toThrow(error)
  })

  it("should handle nested calls", async () => {
    const result = await runSync(async () => {
      expect(isSyncContext()).toBe(true)
      const innerResult = await runSync(async () => {
        expect(isSyncContext()).toBe(true)
        return "inner"
      })
      return `outer ${innerResult}`
    })

    expect(result).toBe("outer inner")
  })
})

describe("runAsync", () => {
  it("should set sync context to false", async () => {
    await runAsync(async () => {
      expect(isSyncContext()).toBe(false)
      expect(isAsyncContext()).toBe(true)
    })
  })

  it("should not await waitUntil", async () => {
    await runAsync(async () => {
      expect(isAsyncContext()).toBe(true)
      waitUntil(new Promise((resolve) => setTimeout(() => resolve("test"), 100)))
      expect(isAsyncContext()).toBe(true)
    })
  })

  it("should maintain async state in nested calls", async () => {
    await runAsync(async () => {
      expect(isAsyncContext()).toBe(true)
      await runAsync(async () => {
        expect(isAsyncContext()).toBe(true)
      })
      expect(isAsyncContext()).toBe(true)
    })
  })
})
