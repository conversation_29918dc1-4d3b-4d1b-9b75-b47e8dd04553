/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { AsyncLocalStorage } from "node:async_hooks"

const asyncLocalStorage = new AsyncLocalStorage<boolean>()

/**
 * Check if the current execution is in sync mode
 * @returns true if the current execution is in sync mode, false otherwise
 */
export const isSyncContext = () => asyncLocalStorage.getStore() ?? false

/**
 * Check if the current execution is in async mode
 * @returns true if the current execution is in async mode, false otherwise
 */
export const isAsyncContext = () => !isSyncContext()

/**
 * Execute a function in sync scope meaning all background jobs will be awaited
 * @param fn - the function to be excuted
 * @returns - returns the result of the function passed in
 * @example
 * import { waitUntil } from "@vercel/functions"
 * await runSync(async () => {
 *  const promise = new Promise((resolve) => setTimeout(resolve, 1000))
 *  if (isSyncContext()) await promise
 *  else waitUntil(promise)
 * })
 * // promise will be awaited
 */
export const runSync = <T>(fn: () => Promise<T>): Promise<T> => asyncLocalStorage.run(true, fn)

/**
 * Execute a function in async scope meaning all background jobs will be awaited
 * @param fn - the function to be excuted
 * @returns - returns the result of the function passed in
 * @example
 * import { waitUntil } from "@vercel/functions"
 * await runAsync(async () => {
 *  const promise = new Promise((resolve) => setTimeout(resolve, 1000))
 *  if (isSyncContext()) await promise
 *  else waitUntil(promise)
 * })
 * // promise will not be awaited
 */
export const runAsync = <T>(fn: () => Promise<T>): Promise<T> => asyncLocalStorage.run(false, fn)
