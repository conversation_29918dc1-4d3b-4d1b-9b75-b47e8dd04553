/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { describe, expect, it } from "vitest"

import {
  createAsyncGeneratorFromEventEmitter,
  createEventEmitterFromAsyncGenerator,
  createReplayAsyncGenerator,
  ReplayEventEmitter,
} from "./create-replay-async-generator"

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

type GeneratorExpectation<T, TReturn> = (T | Error | TReturn)[]

expect.extend({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async toMatchGeneratorOutput<T = unknown, TReturn = any, TNext = unknown>(
    received: AsyncGenerator<T, TReturn, TNext>,
    expected: GeneratorExpectation<T, TReturn>
  ) {
    const generator = received[Symbol.asyncIterator]()
    const results: Array<T | TReturn | Error> = []
    let actualError: Error | undefined

    try {
      let result: IteratorResult<T, TReturn>
      while (!(result = await generator.next()).done) {
        results.push(result.value)
      }
      results.push(result.value)
    } catch (err) {
      actualError = err as Error
      results.push(err as Error)
    }

    const pass = expected.every((exp, index) => {
      if (exp instanceof Error && actualError) {
        return actualError.message === exp.message
      } else if (exp instanceof Error) {
        return false
      } else if (exp === results[index]) {
        return true
      }
      return false
    })

    if (pass) {
      return {
        pass: true,
        message: () => `expected generator output not to match`,
      }
    } else {
      return {
        pass: false,
        message: () =>
          `expected generator output to match:\n` +
          `Expected: ${JSON.stringify(expected)}\n` +
          `Received: ${JSON.stringify(results)}`,
      }
    }
  },
})

declare module "vitest" {
  interface Assertion {
    toMatchGeneratorOutput<T, TReturn>(expectation: GeneratorExpectation<T, TReturn>): Promise<void>
  }
}

describe("ReplayEventEmitter", () => {
  it("should replay events after they are emitted", async () => {
    const replayEmitter = new ReplayEventEmitter()

    replayEmitter.emit("data", 1)
    replayEmitter.emit("data", 2)
    replayEmitter.emit("data", 3)

    const result: number[] = []
    replayEmitter.on("data", (value: number) => {
      result.push(value)
    })

    expect(result).toEqual([1, 2, 3])
  })
})

describe("createEventEmitterFromAsyncGenerator", () => {
  it("should emit events for each yielded value and the return value", async () => {
    async function* exampleGenerator() {
      yield 1
      yield 2
      yield 3
      return 42
    }

    const emitter = createEventEmitterFromAsyncGenerator(exampleGenerator())

    const result: number[] = []
    let doneValue: number | undefined

    emitter.on("data", (value: number) => {
      result.push(value)
    })

    emitter.on("done", (value: number) => {
      doneValue = value
    })

    await new Promise((resolve) => emitter.on("end", resolve))

    expect(result).toEqual([1, 2, 3])
    expect(doneValue).toBe(42)
  })

  it("should emit error events if the generator throws", async () => {
    async function* errorGenerator() {
      yield 1
      throw new Error("Test error")
    }

    const emitter = createEventEmitterFromAsyncGenerator(errorGenerator())

    const result: number[] = []
    let error: Error | undefined

    emitter.on("data", (value: number) => {
      result.push(value)
    })

    emitter.on("error", (err: Error) => {
      error = err
    })

    await new Promise((resolve) => emitter.on("end", resolve))

    expect(result).toEqual([1])
    expect(error).toBeDefined()
    expect(error!.message).toBe("Test error")
  })
})

describe("createAsyncGeneratorFromEventEmitter", () => {
  it("should convert an event emitter back to an async generator", async () => {
    async function* exampleGenerator() {
      yield 1
      yield 2
      yield 3
      return 42
    }

    const emitter = createEventEmitterFromAsyncGenerator(exampleGenerator())
    const generator = createAsyncGeneratorFromEventEmitter<number, number>(emitter)

    await expect(generator).toMatchGeneratorOutput([1, 2, 3, 42])
  })

  it("should handle errors from the event emitter", async () => {
    async function* errorGenerator() {
      yield 1
      throw new Error("Test error")
    }

    const emitter = createEventEmitterFromAsyncGenerator(errorGenerator())
    const generator = createAsyncGeneratorFromEventEmitter<number, number>(emitter)

    await expect(generator).toMatchGeneratorOutput([1, new Error("Test error")])
  })

  it("should handle completion without return value (end event)", async () => {
    async function* exampleGenerator() {
      yield 1
      yield 2
      yield 3
    }

    const emitter = createEventEmitterFromAsyncGenerator(exampleGenerator())
    const generator = createAsyncGeneratorFromEventEmitter<number>(emitter)

    await expect(generator).toMatchGeneratorOutput([1, 2, 3])
  })
})

describe("createReplayableProxy", () => {
  it("should replay all values from the generator", async () => {
    async function* exampleGenerator() {
      yield 1
      yield 2
      yield 3
      return 42
    }

    const replayableGen = createReplayAsyncGenerator(exampleGenerator())

    await expect(replayableGen).toMatchGeneratorOutput([1, 2, 3, 42])
    await expect(replayableGen).toMatchGeneratorOutput([1, 2, 3, 42])
  })

  it("should handle generators that throw errors", async () => {
    async function* errorGenerator() {
      yield 1
      yield 2
      throw new Error("Test error")
    }

    const replayableGen = createReplayAsyncGenerator(errorGenerator())

    await expect(replayableGen).toMatchGeneratorOutput([1, 2, new Error("Test error")])
    await expect(replayableGen).toMatchGeneratorOutput([1, 2, new Error("Test error")])
  })

  it("should not cause race conditions when accessed concurrently", async () => {
    async function* exampleGenerator() {
      yield 1
      await delay(25)
      yield 2
      yield 3
      return 42
    }

    const replayableGen = createReplayAsyncGenerator(exampleGenerator())

    const result1: number[] = []
    const result2: number[] = []

    await Promise.all([
      (async () => {
        for await (const value of replayableGen) {
          await delay(50)
          result1.push(value)
        }
      })(),
      (async () => {
        for await (const value of replayableGen) {
          result2.push(value)
        }
      })(),
    ])

    expect(result1).toEqual([1, 2, 3])
    expect(result2).toEqual([1, 2, 3])
  })

  it("should work with generators that have only a return value", async () => {
    // eslint-disable-next-line require-yield
    async function* returnOnlyGenerator() {
      return 42
    }

    const replayableGen = createReplayAsyncGenerator(returnOnlyGenerator())

    await expect(replayableGen).toMatchGeneratorOutput([42])
    await expect(replayableGen).toMatchGeneratorOutput([42])
  })

  it("should work with generators that yield only once and have a return value", async () => {
    async function* singleYieldGenerator() {
      yield 1
      return 42
    }

    const replayableGen = createReplayAsyncGenerator(singleYieldGenerator())

    await expect(replayableGen).toMatchGeneratorOutput([1, 42])
    await expect(replayableGen).toMatchGeneratorOutput([1, 42])
  })

  it("should work with generators that have no return value", async () => {
    async function* noReturnGenerator() {
      yield 1
      yield 2
      yield 3
    }

    const replayableGen = createReplayAsyncGenerator(noReturnGenerator())

    await expect(replayableGen).toMatchGeneratorOutput([1, 2, 3])
    await expect(replayableGen).toMatchGeneratorOutput([1, 2, 3])
  })
})
