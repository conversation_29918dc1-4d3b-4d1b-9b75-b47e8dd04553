/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { DurationInput } from "@formatjs/intl-durationformat/src/types"
import type { GenericBar, Options } from "cli-progress"
import { DurationFormat } from "@formatjs/intl-durationformat"
import chalk from "chalk"
import cliProgress from "cli-progress"
import { throttle } from "lodash-es"

export * from "cli-progress"

const numberFormatter = new Intl.NumberFormat("en-US", {
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
  notation: "compact",
  compactDisplay: "short",
})

const durationFormatter = new DurationFormat("en-US", {
  style: "narrow",
  secondsDisplay: "always",
})

/**
 * Convert seconds to a duration object
 * @param seconds - The number of seconds to convert
 * @returns The duration object
 */
const secondsToDuration = (seconds: number): DurationInput => {
  const years = Math.floor(seconds / (365 * 24 * 60 * 60))
  seconds = seconds % (365 * 24 * 60 * 60)

  const months = Math.floor(seconds / (30 * 24 * 60 * 60))
  seconds = seconds % (30 * 24 * 60 * 60)

  const days = Math.floor(seconds / (24 * 60 * 60))
  seconds = seconds % (24 * 60 * 60)

  const hours = Math.floor(seconds / (60 * 60))
  seconds = seconds % (60 * 60)

  const minutes = Math.floor(seconds / 60)
  seconds = seconds % 60

  return {
    years,
    months,
    days,
    hours,
    minutes,
    seconds: Math.floor(seconds),
  }
}

const percentageFormatter = new Intl.NumberFormat("en-US", {
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
  notation: "compact",
  compactDisplay: "short",
  style: "percent",
})

export const createMultiBar = (options?: Omit<Options, "format">) => {
  let maxTitleLength = 0
  const multibar = new cliProgress.MultiBar(
    {
      hideCursor: true,
      forceRedraw: true,
      clearOnComplete: false,
      barGlue: "\u001b[90m",
      format: (options, params, payload: { title: string; message: string }) => {
        // custom bar format set ?
        const formatBar = options.formatBar ?? cliProgress.Format.BarFormat

        // bar stopped and stopTime set ?
        const stopTime = params.stopTime ?? Date.now()

        // calculate elapsed time
        const elapsedTime = Math.round((stopTime - params.startTime) / 1000)

        const bar = formatBar(params.progress, options)
        const percentage = percentageFormatter.format(params.progress)
        const total = numberFormatter.format(params.total)
        const value = numberFormatter.format(params.value)
        const duration = durationFormatter.format(secondsToDuration(elapsedTime))

        // We found an issue on multibar when running on CI/CD environment
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        return `${chalk.bold(payload.title?.padEnd(maxTitleLength))} \u001b[32m${bar}\u001b[0m ${percentage.padStart(4)} | ${`${value}/${total}`.padStart(9)} | ${duration.padStart(6)} | ${chalk.gray(payload.message)}`
      },
      ...options,
    },
    cliProgress.Presets.shades_classic
  )

  const sync = throttle(() => multibar.update(), 100)

  /**
   * Sort bars by total from lowest to highest
   */
  const syncBarOrder = () => {
    ;(multibar as unknown as { bars: GenericBar[] }).bars.sort((a, b) => a.getTotal() - b.getTotal())
    multibar.update()
  }

  /**
   * Create a progress bar inside the multibar
   * @param title - The title of the progress bar
   * @param total - The total number of steps
   * @param initMessage - The message to display when the progress bar is initialized
   * @returns The progress bar
   */
  const create = (title: string, total: number, initMessage = "Initializing...") => {
    const bar = multibar.create(total, 0, { title, message: initMessage })
    syncBarOrder()
    maxTitleLength = Math.max(maxTitleLength, title.length)

    /**
     * Updates the bar with the given parameters but keeps payload values which are not overwritten
     */
    const update = ({ total, value, message }: { total?: number; value?: number; message?: string }) => {
      if (total !== undefined) {
        bar.setTotal(total)
        syncBarOrder()
      }
      const payload = "payload" in bar && typeof bar.payload === "object" ? bar.payload : {}
      if (value !== undefined && message !== undefined) bar.update(value, { ...payload, message })
      else if (message !== undefined) bar.update({ ...payload, message })
      else if (value !== undefined) bar.update(value)
    }

    return {
      /**
       * To be used as short hand for incrementing the progress bar inside a promise chain as side effect but forwarding the data
       * @param step - The number of steps to increment
       * @returns The data
       */
      incrementHandler:
        (step?: number) =>
        <T>(data: T): T => {
          bar.increment(step)
          sync()
          return data
        },
      /**
       * Get the total number of steps
       * @returns The total number of steps
       */
      getTotal: () => bar.getTotal(),
      /**
       * Stop the progress bar and update the message
       * @param message - The message to update the progress bar with
       */
      stop: (message = "Done") => {
        bar.update({ message })
        bar.stop()
        multibar.update()
      },
      /**
       * Increment the progress bar
       * @param step - The number of steps to increment
       */
      increment: (step?: number) => {
        bar.increment(step)
        sync()
      },
      /**
       * Update the progress bar with the given options keeping the options which are not overwritten
       * @param options - The options to update the progress bar with
       */
      update: ({ total, value, message }: { total?: number; value?: number; message?: string }) => {
        update({ total, value, message })
        multibar.update()
      },
    }
  }

  return {
    create,
    /**
     * Stop the multibar
     */
    stop: () => {
      sync.cancel()
      multibar.stop()
    },
  }
}
