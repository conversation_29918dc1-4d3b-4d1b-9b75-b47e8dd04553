/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

const base64Regex = /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/

/**
 * Checks if a string is a valid base64 encoded string.
 *
 * @param str - The string to check.
 * @returns A boolean indicating if the string is a valid base64 encoded string.
 */
export const isBase64 = (str: string): boolean => {
  try {
    // Check if string matches base64 pattern

    if (!base64Regex.test(str)) {
      return false
    }

    // Try to decode and encode back to verify
    const decoded = atob(str)
    const encoded = btoa(decoded)
    return encoded === str
  } catch {
    return false
  }
}
