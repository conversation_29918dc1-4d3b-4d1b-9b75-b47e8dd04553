/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2025 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

const PROMISE_PENDING = "pending"
const PROMISE_RESOLVED = "resolved"
const PROMISE_REJECTED = "rejected"

type PromiseStatuses = typeof PROMISE_PENDING | typeof PROMISE_RESOLVED | typeof PROMISE_REJECTED

export type PromisePendingState = {
  status: typeof PROMISE_PENDING
}

export type PromiseResolvedState<T = unknown> = {
  status: typeof PROMISE_RESOLVED
  value: T
}

export type PromiseRejectedState<T = unknown> = {
  status: typeof PROMISE_REJECTED
  reason: T
}

const pendingState: PromisePendingState = {
  status: PROMISE_PENDING,
}

/**
 * Get the status of a promise of either resolved, rejected or pending
 * @param promise - The promise to get the status of
 * @returns The status of the promise
 */
export const promiseStatus = async <T>(promise: Promise<T>): Promise<PromiseStatuses> =>
  Promise.race([promise, pendingState]).then(
    (value) => (value === pendingState ? pendingState.status : PROMISE_RESOLVED),
    () => PROMISE_REJECTED
  )
