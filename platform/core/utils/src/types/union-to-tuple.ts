/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Contra<T> = T extends any ? (arg: T) => void : never

type InferContra<T> = [T] extends [(arg: infer I) => void] ? I : never

type PickOne<T> = InferContra<InferContra<Contra<Contra<T>>>>

export type UnionToTuple<T> =
  PickOne<T> extends infer U // assign PickOne<T> to U
    ? Exclude<T, U> extends never // T and U are the same
      ? [T]
      : [...UnionToTuple<Exclude<T, U>>, U] // recursion
    : never
