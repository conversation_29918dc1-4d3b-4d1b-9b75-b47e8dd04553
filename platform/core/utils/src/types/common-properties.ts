/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Extracts common properties from two types that have the same key and value type.
 *
 * @template T - The first type to compare.
 * @template U - The second type to compare.
 *
 * @remarks
 * This type uses conditional types and mapped types to create a new type
 * that only includes properties that exist in both T and U with the same types.
 *
 * @example
 * ```typescript
 * type A = { x: number, y: string, z: boolean };
 * type B = { x: number, y: string, w: number };
 * type Common = CommonProperties<A, B>; // { x: number, y: string }
 * ```
 */
export type CommonProperties<T, U> = Omit<
  T,
  {
    [K in keyof T]: K extends keyof U ? (T[K] extends U[K] ? (U[K] extends T[K] ? never : K) : K) : K
  }[keyof T]
>
