/**
 * This file is part of the Kreios platform.
 *
 * Copyright (c) 2024 KREIOS S.A.R.L
 * Licensed under the MIT License (Expat). You may obtain a copy of the License
 * in the LICENSE file in the root directory of this source tree.
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * Utility type that ensures at least one property of the given type is required.
 * @template T - The type to modify.
 * @example
 * type Email = {
 *   text: string;
 *   html: string;
 * };
 *
 * type RequiredEmail = RequireAtLeastOne<Email>;
 * // { text: string; html?: string; } | { text?: string; html: string; }
 * const email1: RequiredEmail = {
 *   text: "Hello",
 *   html: "<p>Hello</p>"
 * };
 *
 * const email2: RequiredEmail = {
 *   text: "Hi"
 * };
 */
export type RequireAtLeastOne<T> = {
  [K in keyof T]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<keyof T, K>>>
}[keyof T]
