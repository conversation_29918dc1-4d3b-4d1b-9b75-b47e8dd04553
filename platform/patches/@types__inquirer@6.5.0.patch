diff --git a/index.d.ts b/index.d.ts
index db3ff4b8706f122b6b307258dec872fc024fc0de..e06a356ba3e4da150d2adf0bcf2965dc70427754 100644
--- a/index.d.ts
+++ b/index.d.ts
@@ -248,7 +248,7 @@ declare namespace inquirer {
         /**
          * The type of the question.
          */
-        type?: string;
+        type?: string & {};
 
         /**
          * The key to save the answer to the answers-hash.
@@ -310,7 +310,7 @@ declare namespace inquirer {
         /**
          * The type of the choice.
          */
-        type?: string;
+        type?: string & {};
     }
 
     /**
@@ -899,7 +899,7 @@ declare namespace inquirer {
             /**
              * The type of the question.
              */
-            type: string;
+            type: string & {};
 
             /**
              * The message to show to the user.
