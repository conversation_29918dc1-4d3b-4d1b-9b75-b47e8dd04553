diff --git a/esm/parquet_wasm.js b/esm/parquet_wasm.js
index 1a6fa5760bdcbf2f6169dbb2c9a3a66fa52b449b..03e0461c1407993828a1317e7dc2a2fc52375421 100644
--- a/esm/parquet_wasm.js
+++ b/esm/parquet_wasm.js
@@ -3307,6 +3307,13 @@ function initSync(module) {
     return __wbg_finalize_init(instance, module);
 }
 
+// Adapt loading the parquet_wasm_bg.wasm file for node.js 
+// reference https://github.com/electric-sql/pglite/blob/ea110d1cb87a221e8341326ffa2f6f12dc243404/packages/pglite/src/utils.ts#L43-L45
+const IN_NODE =
+  typeof process === 'object' &&
+  typeof process.versions === 'object' &&
+  typeof process.versions.node === 'string'
+
 async function __wbg_init(input) {
     if (wasm !== undefined) return wasm;
 
@@ -3315,7 +3322,10 @@ async function __wbg_init(input) {
     }
     const imports = __wbg_get_imports();
 
-    if (typeof input === 'string' || (typeof Request === 'function' && input instanceof Request) || (typeof URL === 'function' && input instanceof URL)) {
+    if(IN_NODE) {
+        const {readFile} = await import('fs/promises')
+        input = await readFile(input)
+    } else if (typeof input === 'string' || (typeof Request === 'function' && input instanceof Request) || (typeof URL === 'function' && input instanceof URL)) {
         input = fetch(input);
     }
 
