diff --git a/dist/leaflet.css b/dist/leaflet.css
index 2961b7618a57617d1015d31d2094c425c3d86ce9..e0103ddfdf1bf68d3811b4d69271e92de1011619 100644
--- a/dist/leaflet.css
+++ b/dist/leaflet.css
@@ -551,9 +551,6 @@ svg.leaflet-image-layer.leaflet-interactive path {
 .leaflet-oldie .leaflet-popup-tip {
 	width: 24px;
 	margin: 0 auto;
-
-	-ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)";
-	filter: progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678);
 	}
 
 .leaflet-oldie .leaflet-control-zoom,
