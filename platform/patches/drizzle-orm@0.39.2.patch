diff --git a/sql/sql.js b/sql/sql.js
index 1dfeb9464f47f3cd60d8ea2bc37ed3f93c20ba9b..9c1743179da1f7b76a3d5843051e77a90a54bf58 100644
--- a/sql/sql.js
+++ b/sql/sql.js
@@ -15,12 +15,18 @@ function mergeQueries(queries) {
   const result = { sql: "", params: [] };
   for (const query of queries) {
     result.sql += query.sql;
-    result.params.push(...query.params);
+    const paramsLength = query.params.length;
+    for (let i = 0; i < paramsLength; i++) {
+        result.params.push(query.params[i]);
+    }
     if (query.typings?.length) {
       if (!result.typings) {
         result.typings = [];
       }
-      result.typings.push(...query.typings);
+      const typingsLength = query.typings.length;
+      for (let i = 0; i < typingsLength; i++) {
+          result.typings.push(query.typings[i]);
+      }
     }
   }
   return result;
