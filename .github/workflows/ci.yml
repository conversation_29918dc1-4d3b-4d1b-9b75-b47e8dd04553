name: CI

on:
  pull_request:
  push:
    branches: ["main"]
  merge_group:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

env:
  FORCE_COLOR: 3
  TURBO_TEAM: farooq
  TURBO_TOKEN: ${{ secrets.VERCEL_TOKEN }}

jobs:
  validate:
    name: ${{ matrix.task.name }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        task:
          - name: lint
            run: pnpm lint && pnpm lint:ws
          - name: format
            run: pnpm format
          - name: typecheck
            run: pnpm typecheck
          - name: test
            run: pnpm test

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Setup
        uses: ./platform/tools/github/setup

      # Install system dependencies for SQLite
      - name: Install SQLite dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y sqlite3 libsqlite3-dev

      # Rebuild SQLite bindings
      - name: Rebuild SQLite bindings
        run: cd platform/core/eventsourcing && pnpm rebuild better-sqlite3

      - name: Copy env
        shell: bash
        run: cp .env.example .env

      - name: Run ${{ matrix.task.name }}
        run: ${{ matrix.task.run }}
