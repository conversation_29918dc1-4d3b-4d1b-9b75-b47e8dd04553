name: Vercel deploy
on:
  push:
    branches:
      - master
      - main
  pull_request:
  workflow_dispatch:

jobs:
  deploy-to-vercel:
    if: ${{ github.event_name == 'workflow_dispatch' || !contains(fromJSON(vars.VERCEL_USERS_GITHUB_USERS), github.actor) }}
    runs-on: ubuntu-latest
    permissions:
      actions: write
      checks: write
      contents: write
      deployments: write
      repository-projects: write
      statuses: write
      pull-requests: write
    steps:
      - uses: actions/checkout@v3
      - uses: amondnet/vercel-action@v25 #deploy
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          vercel-args: ${{ (endsWith(github.ref,'refs/heads/main') || endsWith(github.ref,'refs/heads/master')) && '--prod' || '' }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID}}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID}}
          scope: farooq
          github-comment: true
          github-deployment: true
