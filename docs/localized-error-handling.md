# Localized Error Handling

This document describes how error handling is implemented in the application with support for localization.

## Overview

The application uses a centralized approach to error handling, where all error messages are stored in language-specific JSON files and referenced by keys. This allows for consistent error messages across the application and easy translation to different languages.

## Backend Error Handling

### Creating Localized Errors

In the backend, we use a utility function `createLocalizedError` to create tRPC errors with localized error messages. This function is defined in `packages/api/src/utils/localized-error.ts`.

```typescript
import { TRPCError } from "@trpc/server"

/**
 * Creates a localized error with a translation key as the message
 * The frontend will use this key to display the error message in the user's language
 *
 * @param code The tRPC error code
 * @param translationKey The key for the error message in the translation files
 * @param cause Optional cause of the error
 * @returns A TRPCError with the translation key as the message
 */
export function createLocalizedError(
  code: TRPCError["code"],
  translationKey: string,
  cause?: unknown
): TRPCError {
  return new TRPCError({
    code,
    message: translationKey,
    cause,
  })
}
```

### Error Translation Keys

All error translation keys are defined in the `ErrorTranslationKeys` enum in the same file:

```typescript
export const ErrorTranslationKeys = {
  // Common errors
  UNAUTHORIZED: "errors.common.unauthorized",
  FORBIDDEN: "errors.common.forbidden",
  NOT_FOUND: "errors.common.notFound",
  BAD_REQUEST: "errors.common.badRequest",
  INTERNAL_SERVER_ERROR: "errors.common.serverError",
  TIMEOUT: "errors.common.timeout",

  // Auth errors
  INVALID_CREDENTIALS: "errors.auth.invalidCredentials",
  SESSION_EXPIRED: "errors.auth.sessionExpired",
  EMAIL_NOT_VERIFIED: "errors.auth.emailNotVerified",
  USER_NOT_FOUND: "errors.auth.userNotFound",

  // Survey errors
  SURVEY_CREATE_FAILED: "errors.surveys.createFailed",
  SURVEY_UPDATE_FAILED: "errors.surveys.updateFailed",
  SURVEY_DELETE_FAILED: "errors.surveys.deleteFailed",
  SURVEY_SEND_FAILED: "errors.surveys.sendFailed",
  SURVEY_NOT_COMPLETE: "errors.surveys.notComplete",
  SURVEY_ALREADY_SUBMITTED: "errors.surveys.alreadySubmitted",
  SURVEY_PAST_DEADLINE: "errors.surveys.pastDeadline",
  SURVEY_NOT_FOUND: "errors.surveys.notFound",
  SURVEY_BATCH_NOT_FOUND: "errors.surveys.batchNotFound",

  // Company errors
  COMPANY_NOT_FOUND: "errors.companies.notFound",

  // Portal user errors
  PORTAL_USER_UPDATE_FAILED: "errors.portalUsers.updateFailed",
  PORTAL_USER_NOT_FOUND: "errors.portalUsers.notFound",

  // Watchlist errors
  WATCHLIST_NOT_FOUND: "errors.watchlists.notFound",
  WATCHLIST_CREATE_FAILED: "errors.watchlists.createFailed",
  WATCHLIST_UPDATE_FAILED: "errors.watchlists.updateFailed",
  WATCHLIST_DELETE_FAILED: "errors.watchlists.deleteFailed",

  // Evaluation request errors
  EVALUATION_REQUEST_NOT_FOUND: "errors.evaluationRequests.notFound",
  EVALUATION_REQUEST_CREATE_FAILED: "errors.evaluationRequests.createFailed",
  EVALUATION_REQUEST_UPDATE_FAILED: "errors.evaluationRequests.updateFailed",
  EVALUATION_REQUEST_DELETE_FAILED: "errors.evaluationRequests.deleteFailed",
}
```

### Usage in tRPC Procedures

Here are examples of how to use the `createLocalizedError` function in tRPC procedures:

#### Survey Example

```typescript
import { createLocalizedError, ErrorTranslationKeys } from "@/utils/localized-error"

// Example tRPC procedure for surveys
getSurvey: protectedProcedure.input(z.string()).query(async ({ input: id }) => {
  const { aggregate: survey } = await surveyEventStore.getAggregate(id)
  if (!survey) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.SURVEY_NOT_FOUND)

  // Get company details
  const company = await gateway.findFirst("companies", {
    match: { aggregateId: survey.companyId },
  })
  if (!company) throw createLocalizedError("NOT_FOUND", ErrorTranslationKeys.COMPANY_NOT_FOUND)

  // Rest of the procedure...
})
```

#### Portal User Example

```typescript
import { createLocalizedError, ErrorTranslationKeys } from "@/utils/localized-error"

// Example tRPC procedure for portal users
get: protectedProcedure.query(async ({ ctx: { portalUser } }) => {
  try {
    const { aggregate } = await portalUserEventStore.getExistingAggregate(portalUser.aggregateId)

    // Rest of the procedure...
    return aggregate
  } catch (error) {
    console.error("Failed to get portal user:", portalUser.aggregateId, error)
    throw createLocalizedError(
      "NOT_FOUND",
      ErrorTranslationKeys.PORTAL_USER_NOT_FOUND
    )
  }
})

update: protectedProcedure
  .input(z.object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
  }))
  .mutation(async ({ ctx: { portalUser }, input }) => {

    if (!updateUserResult.updatePortalUser?.portalUser) {
      console.error("Failed to update portal user:", portalUser.aggregateId)
      throw createLocalizedError(
        "INTERNAL_SERVER_ERROR",
        ErrorTranslationKeys.PORTAL_USER_UPDATE_FAILED
      )
    }

  })
```

#### Order Example

```typescript
import { createLocalizedError, ErrorTranslationKeys } from "@/utils/localized-error"

// Example tRPC procedure for orders
submitOrder: publicProcedure.input(orderProfileSchema).mutation(async ({ input }) => {
  const { email, companyId, companyName, name } = input

  if (!createUserResult.createPortalUser?.portalUser) {
    console.error("Failed to create portal user:", email)
    throw createLocalizedError(
      "INTERNAL_SERVER_ERROR",
      ErrorTranslationKeys.PORTAL_USER_UPDATE_FAILED
    )
  }

  if (!createWatchlistResult.createWatchlist?.watchlist) {
    console.error("Failed to create watchlist for user:", portalUser.aggregateId)
    throw createLocalizedError(
      "INTERNAL_SERVER_ERROR",
      ErrorTranslationKeys.ORDER_SUBMIT_FAILED,
      "Failed to create watchlist"
    )
  }

  // Add entry to watchlist logic...
  if (!addEntryResult.addEntryToWatchlist?.entry) {
    console.error("Failed to add entry to watchlist:", companyId)
    throw createLocalizedError(
      "INTERNAL_SERVER_ERROR",
      ErrorTranslationKeys.ORDER_SUBMIT_FAILED,
      "Failed to add entry to watchlist"
    )
  }

})
```

#### Watchlist Example

```typescript
import { createLocalizedError, ErrorTranslationKeys } from "@/utils/localized-error"

// Example tRPC procedure for watchlists
history: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
  const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

  if (!(isAdmin || isClientAdmin))
    throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

  const { events } = await watchlistEventStore.getEvents(input)
  return events
}),

get: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
  const { aggregate } = await watchlistEventStore.getAggregate(input)
  if (!aggregate)
    throw createLocalizedError(
      "NOT_FOUND",
      ErrorTranslationKeys.WATCHLIST_NOT_FOUND
    )

  const portalUserId = portalUser.aggregateId

  if (portalUserId !== aggregate.portalUserId)
    throw createLocalizedError(
      "FORBIDDEN",
      ErrorTranslationKeys.FORBIDDEN
    )

})
```

#### Company Example

```typescript
import { createLocalizedError, ErrorTranslationKeys } from "@/utils/localized-error"

// Example tRPC procedure for companies
history: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
  const portalUserId = portalUser.aggregateId

  const watchlist = await gateway.findFirst("watchlists", {
    bool: {
      must: [{ term: { portalUserId: portalUserId } }],
    },
  })

  if (!watchlist)
    throw createLocalizedError(
      "NOT_FOUND",
      ErrorTranslationKeys.WATCHLIST_NOT_FOUND
    )

  const companyIds = watchlist.entries.map((entry) => entry.companyId)

  if (!companyIds.includes(input))
    throw createLocalizedError(
      "FORBIDDEN",
      ErrorTranslationKeys.FORBIDDEN
    )

}),

get: protectedProcedure.input(idSchema).query(async ({ input: id, ctx: { portalUser } }) => {
  const { isCompanyUser, isObserver } = getPortalUserRoles(portalUser)

  if ((isCompanyUser || isObserver) && portalUser.companyId !== id)
    throw createLocalizedError(
      "FORBIDDEN",
      ErrorTranslationKeys.FORBIDDEN
    )

  const { aggregate } = await companyEventStore.getAggregate(id)
  if (!aggregate)
    throw createLocalizedError(
      "NOT_FOUND",
      ErrorTranslationKeys.COMPANY_NOT_FOUND
    )

  // Rest of the procedure...
})
```

#### Evaluation Request Example

```typescript
import { createLocalizedError, ErrorTranslationKeys } from "@/utils/localized-error"

// Example tRPC procedure for evaluation requests
history: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
  const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

  if (!(isAdmin || isClientAdmin))
    throw createLocalizedError("FORBIDDEN", ErrorTranslationKeys.FORBIDDEN)

  const { events } = await evaluationRequestEventStore.getEvents(input)
  return events
}),

get: protectedProcedure.input(idSchema).query(async ({ input, ctx: { portalUser } }) => {
  const { isAdmin, isClientAdmin } = getPortalUserRoles(portalUser)

  if (!(isAdmin || isClientAdmin))
    throw createLocalizedError(
      "FORBIDDEN",
      ErrorTranslationKeys.FORBIDDEN
    )

  try {
    const { aggregate } = await evaluationRequestEventStore.getExistingAggregate(input)
    return aggregate
  } catch (error) {
    throw createLocalizedError(
      "NOT_FOUND",
      ErrorTranslationKeys.EVALUATION_REQUEST_NOT_FOUND,
      error
    )
  }
})
```

## Frontend Error Handling

### Translation Files

Error messages are stored in language-specific JSON files:

- English: `apps/app/src/messages/en.json`
- Estonian: `apps/app/src/messages/et.json`

Example structure:

```json
{
  "errors": {
    "common": {
      "default": "An error occurred. Please try again.",
      "unauthorized": "You are not authorized to perform this action.",
      "forbidden": "You do not have permission to access this resource.",
      "notFound": "The requested resource was not found.",
      "badRequest": "Invalid request. Please check your input.",
      "serverError": "A server error occurred. Please try again later.",
      "timeout": "The request timed out. Please try again."
    },
    "surveys": {
      "createFailed": "Failed to create survey. Please try again.",
      "updateFailed": "Failed to update survey. Please try again.",
      "notFound": "Survey not found. Please check the survey ID.",
      "batchNotFound": "Survey batch not found. Please check the batch ID."
    },
    "portalUsers": {
      "updateFailed": "Failed to update user profile. Please try again.",
      "notFound": "User profile not found. Please contact support."
    },
    "watchlists": {
      "notFound": "Watchlist not found. Please check the watchlist ID.",
      "createFailed": "Failed to create watchlist. Please try again.",
      "updateFailed": "Failed to update watchlist. Please try again.",
      "deleteFailed": "Failed to delete watchlist. Please try again."
    },
    "evaluationRequests": {
      "notFound": "Evaluation request not found. Please check the request ID.",
      "createFailed": "Failed to create evaluation request. Please try again.",
      "updateFailed": "Failed to update evaluation request. Please try again.",
      "deleteFailed": "Failed to delete evaluation request. Please try again."
    }
  }
}
```

### Handling tRPC Errors

We use a utility function `handleTRPCError` to handle tRPC errors on the frontend. This function is defined in `apps/app/src/utils/trpc-error-handler.ts`.

```typescript
import { TRPCClientError } from "@trpc/client"
import { toast } from "@kreios/ui/sonner"

/**
 * Handles tRPC errors by displaying a localized toast message
 * The backend sends error messages as translation keys, which are then
 * translated on the frontend using the next-intl translation function
 *
 * @param error The error object from tRPC
 * @param t The translation function from next-intl
 */
export function handleTRPCError(error: unknown, t: (key: string) => string): void {
  // Default error message key
  let messageKey = "errors.common.default"

  if (error instanceof TRPCClientError) {
    // The error message from the backend is already a translation key
    // We just need to use it to get the translated message
    messageKey = error.message
  }

  // Show the toast with the translated message
  toast.error(t(messageKey))
}

/**
 * Hook to handle tRPC errors
 * @param t The translation function from next-intl
 * @returns A function to handle tRPC errors
 */
export function useTRPCErrorHandler(t: (key: string) => string) {
  return (error: unknown) => handleTRPCError(error, t)
}
```

### Usage in Components

Here's an example of how to use the `useTRPCErrorHandler` hook in a React component:

```tsx
import { useTRPCErrorHandler } from "@/utils/trpc-error-handler"
import { useTranslations } from "next-intl"
import { api } from "@/lib/trpc-provider"
import { toast } from "@kreios/ui/sonner"

const MyComponent = () => {
  const t = useTranslations()
  const handleTRPCError = useTRPCErrorHandler(t)
  const updateSurvey = api.surveys.submitSurveyForm.useMutation()

  const handleSubmit = async () => {
    try {
      await updateSurvey.mutateAsync({
        id: surveyId,
        formData: {
          // Form data...
        },
      })
      toast.success(t("surveys.surveySubmittedSuccessfully"))
      // Handle success...
    } catch (error) {
      console.error("Error submitting survey:", error)
      handleTRPCError(error)
    }
  }

  // Rest of the component...
}
```

## Adding New Error Messages

To add a new error message:

1. Add a new key to the `ErrorTranslationKeys` enum in `packages/api/src/utils/localized-error.ts`
2. Add the corresponding error message to both `en.json` and `et.json` files
3. Use the new key with `createLocalizedError` in your tRPC procedures

## Best Practices

1. Always use the `createLocalizedError` function for tRPC errors
2. Always use the `handleTRPCError` function to handle tRPC errors on the frontend
3. Keep error messages consistent across the application
4. Make sure all error messages are translated to all supported languages
5. Use descriptive error keys that reflect the error condition
6. Group error keys by feature area (auth, surveys, companies, etc.)
