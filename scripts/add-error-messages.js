/**
 * This script adds custom error messages to the generated Zod schemas
 * for both English and Estonian languages.
 */
const fs = require("fs")
const path = require("path")

// Define paths to the generated schema files
const schemaSets = {
  universal: {
    en: path.resolve(__dirname, "../apps/app/src/schema/generated/universal-questionnaire-en.ts"),
    et: path.resolve(__dirname, "../apps/app/src/schema/generated/universal-questionnaire-et.ts"),
  },
  selfAssessment: {
    en: path.resolve(__dirname, "../apps/app/src/schema/self-assesment-generated/self-assesment-questionnaire-en.ts"),
    et: path.resolve(__dirname, "../apps/app/src/schema/self-assesment-generated/self-assesment-questionnaire-et.ts"),
  },
}
// Process each schema file
function processSchemaFile(filePath, language) {
  console.log(`Processing ${language} schema at ${filePath}...`)

  // Read the file
  let content = fs.readFileSync(filePath, "utf8")

  // Create a simpler approach - directly replace all validation methods with custom error messages
  // This is more reliable than trying to extract field names from the schema

  // Replace string validations
  content = content.replace(/z\.string\(\)/g, () => {
    return `z.string({ invalid_type_error: "${language === "en" ? "This field must be a valid text" : "See väli peab olema kehtiv tekst"}", required_error: "${language === "en" ? "This field is required" : "See väli on kohustuslik"}" })`
  })

  // Replace number validations
  content = content.replace(/z\.number\(\)/g, () => {
    return `z.number({ invalid_type_error: "${language === "en" ? "This field must be a valid number" : "See väli peab olema kehtiv number"}", required_error: "${language === "en" ? "This field is required" : "See väli on kohustuslik"}" })`
  })

  // Replace boolean validations
  content = content.replace(/z\.boolean\(\)/g, () => {
    return `z.boolean({ invalid_type_error: "${language === "en" ? "This field must be true or false" : "See väli peab olema tõene või väär"}", required_error: "${language === "en" ? "This field is required" : "See väli on kohustuslik"}" })`
  })

  // Replace date validations
  content = content.replace(/z\.date\(\)/g, () => {
    return `z.date({ invalid_type_error: "${language === "en" ? "This field must be a valid date" : "See väli peab olema kehtiv kuupäev"}", required_error: "${language === "en" ? "This field is required" : "See väli on kohustuslik"}" })`
  })

  // Replace refinements
  content = content.replace(/\.int\(\)/g, () => {
    return `.int({ message: "${language === "en" ? "This field must be a whole number" : "See väli peab olema täisarv"}" })`
  })

  content = content.replace(/\.positive\(\)/g, () => {
    return `.positive({ message: "${language === "en" ? "This field must be a positive number" : "See väli peab olema positiivne number"}" })`
  })

  content = content.replace(/\.email\(\)/g, () => {
    return `.email({ message: "${language === "en" ? "This field must be a valid email address" : "See väli peab olema kehtiv e-posti aadress"}" })`
  })

  content = content.replace(/\.min\(([^,)]+)\)/g, (_, p1) => {
    return `.min(${p1}, { message: "${language === "en" ? "This field value is too small" : "See väli väärtus on liiga väike"}" })`
  })

  content = content.replace(/\.max\(([^,)]+)\)/g, (_, p1) => {
    return `.max(${p1}, { message: "${language === "en" ? "This field value is too large" : "See väli väärtus on liiga suur"}" })`
  })

  content = content.replace(/\.gte\(([^,)]+)\)/g, (_, p1) => {
    return `.gte(${p1}, { message: "${language === "en" ? `This field must be greater than or equal to the minimum value (${p1})` : `See väli peab olema suurem või võrdne miinimumväärtusega (${p1})`}" })`
  })

  content = content.replace(/\.lte\(([^,)]+)\)/g, (_, p1) => {
    return `.lte(${p1}, { message: "${language === "en" ? `This field must be less than or equal to the maximum value (${p1})` : `See väli peab olema väiksem või võrdne maksimumväärtusega (${p1})`}" })`
  })

  content = content.replace(/\.optional\(\)/g, () => {
    return `.optional().nullable()`
  })

  content = content.replace(/z\.enum\(\[([^\]]*)\]\)/g, (_, options) => {
    return `z.enum([${options}], { invalid_type_error: "${language === "en" ? "This field must be a valid option" : "See väli peab olema kehtiv valik"}", required_error: "${language === "en" ? "This field is required" : "See väli on kohustuslik"}" })`
  })

  // Write the modified content back
  fs.writeFileSync(filePath, content)
  console.log(`Finished processing ${language} schema.`)
}

function processSchemas(schemaSetName) {
  const schemaSet = schemaSets[schemaSetName]
  if (!schemaSet) {
    throw new Error(`Schema set "${schemaSetName}" not found. Available sets: ${Object.keys(schemaSets).join(", ")}`)
  }

  // Process English schema
  processSchemaFile(schemaSet.en, "en")

  // Process Estonian schema
  processSchemaFile(schemaSet.et, "et")
}

// Process both schema files
try {
  const schemaSetName = process.argv[2]
  if (!schemaSetName) {
    throw new Error(
      'Please provide a schema set name (e.g., "universal" or "selfAssessment") as a command-line argument.'
    )
  }

  processSchemas(schemaSetName)
  console.log(`All schemas for "${schemaSetName}" processed successfully!`)
} catch (error) {
  console.error("Error processing schema files:", error)
  process.exit(1)
}
