#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// Function to get all keys from an object (including nested ones)
function getAllKeys(obj, prefix = '') {
  let keys = [];
  for (const key in obj) {
    const newPrefix = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      keys = [...keys, ...getAllKeys(obj[key], newPrefix)];
    } else {
      keys.push(newPrefix);
    }
  }
  return keys;
}

// Determine the base path based on current working directory
const cwd = process.cwd();
const isInAppDir = cwd.includes('/apps/app');
const basePath = isInAppDir ? '' : 'apps/app';

// Paths to the language files
const enPath = path.resolve(basePath, 'src/messages/en.json');
const etPath = path.resolve(basePath, 'src/messages/et.json');

// Read the language files
const enData = JSON.parse(fs.readFileSync(enPath, 'utf8'));
const etData = JSON.parse(fs.readFileSync(etPath, 'utf8'));

// Get all keys from both files
const enKeys = getAllKeys(enData).sort();
const etKeys = getAllKeys(etData).sort();

// Find keys that are in en.json but not in et.json
const missingInEt = enKeys.filter(key => !etKeys.includes(key));

// Find keys that are in et.json but not in en.json
const missingInEn = etKeys.filter(key => !enKeys.includes(key));

// Print the results
if (missingInEt.length > 0) {
  console.error('\x1b[31m%s\x1b[0m', 'Keys missing in et.json:');
  missingInEt.forEach(key => console.error(`  - ${key}`));
}

if (missingInEn.length > 0) {
  console.error('\x1b[31m%s\x1b[0m', 'Keys missing in en.json:');
  missingInEn.forEach(key => console.error(`  - ${key}`));
}

// Exit with an error code if there are any missing keys
if (missingInEt.length > 0 || missingInEn.length > 0) {
  console.error('\x1b[31m%s\x1b[0m', 'Translation files are not in sync!');
  process.exit(1);
} else {
  console.log('\x1b[32m%s\x1b[0m', 'All translation keys are in sync!');
}
