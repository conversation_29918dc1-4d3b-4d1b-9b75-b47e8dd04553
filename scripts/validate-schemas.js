#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Determine the base path based on current working directory
const cwd = process.cwd();
const isInAppDir = cwd.includes('/apps/app');
const basePath = isInAppDir ? '' : 'apps/app';

// Paths to the generated schema files
const enSchemaPath = path.resolve(basePath, 'src/schema/generated/universal-questionnaire-en.ts');
const etSchemaPath = path.resolve(basePath, 'src/schema/generated/universal-questionnaire-et.ts');

// Function to check if a file exists
function fileExists(filePath) {
  try {
    return fs.statSync(filePath).isFile();
  } catch (err) {
    return false;
  }
}

// Function to run the codegen command
function runCodegen() {
  return new Promise((resolve, reject) => {
    console.log('Running codegen command...');

    const command = isInAppDir ? 'pnpm codegen' : 'cd apps/app && pnpm codegen';

    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error running codegen: ${error.message}`);
        return reject(error);
      }

      if (stderr) {
        console.error(`Codegen stderr: ${stderr}`);
      }

      console.log(`Codegen stdout: ${stdout}`);
      resolve();
    });
  });
}

// Main function
async function main() {
  try {
    // Check if schema files exist
    const enExists = fileExists(enSchemaPath);
    const etExists = fileExists(etSchemaPath);

    if (!enExists || !etExists) {
      console.log('Schema files do not exist. Running codegen...');
      await runCodegen();
    }

    // Check if schema files exist after running codegen
    const enExistsAfter = fileExists(enSchemaPath);
    const etExistsAfter = fileExists(etSchemaPath);

    if (!enExistsAfter || !etExistsAfter) {
      console.error('\x1b[31m%s\x1b[0m', 'Failed to generate schema files!');
      process.exit(1);
    }

    // Read the schema files
    const enSchema = fs.readFileSync(enSchemaPath, 'utf8');
    const etSchema = fs.readFileSync(etSchemaPath, 'utf8');

    // Basic validation - check if files are not empty
    if (!enSchema.trim() || !etSchema.trim()) {
      console.error('\x1b[31m%s\x1b[0m', 'Schema files are empty!');
      process.exit(1);
    }

    // Check if the files contain Zod schemas
    if (!enSchema.includes('z.object') || !etSchema.includes('z.object')) {
      console.error('\x1b[31m%s\x1b[0m', 'Schema files do not contain valid Zod schemas!');
      process.exit(1);
    }

    console.log('\x1b[32m%s\x1b[0m', 'Schema validation successful!');
  } catch (error) {
    console.error('\x1b[31m%s\x1b[0m', `Error validating schemas: ${error.message}`);
    process.exit(1);
  }
}

main();
