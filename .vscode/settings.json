{
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "editor.formatOnSave": true,
  "eslint.useFlatConfig": true,
  "typescript.tsserver.watchOptions": "vscode",
  "search.exclude": {
    // Avoid polluting search results with lockfile content
    "pnpm-lock.yaml": true
  },
  // Ensure VSCode uses pnpm instead of npm
  "npm.packageManager": "pnpm",
  // For those using file-nesting, nest the new files. E.g.:
  "explorer.fileNesting.patterns": {
    "package.json": "pnpm-workspace.yaml, pnpm-lock.yaml"
  },
  "eslint.workingDirectories": [{ "pattern": "apps/*/" }, { "pattern": "packages/*/" }, { "pattern": "platform/*/*/" }],
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "tailwindCSS.experimental.configFile": "./platform/core/tailwind/web.ts",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"],
  "typescript.tsdk": "node_modules/typescript/lib",
  "[terraform]": {
    "editor.defaultFormatter": "hashicorp.terraform"
  },
  "graphql-config.dotEnvPath": ".env.local",
  "nix.enableLanguageServer": true,
  "nixEnvSelector.nixFile": "${workspaceRoot}/flake.nix",
  "direnv.enable": true
}
